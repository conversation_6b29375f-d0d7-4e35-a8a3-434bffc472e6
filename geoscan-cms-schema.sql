-- public.contract_updates definition

-- Drop table

-- DROP TABLE public.contract_updates;

CREATE TABLE public.contract_updates (
	organization_id int4 NULL,
	"name" varchar(64) NULL,
	eik varchar NULL,
	"start" varchar(8) NULL,
	еnd varchar(8) NULL,
	packages varchar(256) NULL,
	packages_ids _int4 NULL,
	start_date date NULL,
	end_date date NULL
);


-- public.cron_job definition

-- Drop table

-- DROP TABLE public.cron_job;

CREATE TABLE public.cron_job (
	id int4 NOT NULL,
	"name" varchar(191) NOT NULL,
	command varchar(1024) NOT NULL,
	schedule varchar(191) NOT NULL,
	description varchar(191) NOT NULL,
	enabled bool NOT NULL,
	CONSTRAINT cron_job_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX un_name ON public.cron_job USING btree (name);


-- public.ext_log_entries definition

-- Drop table

-- DROP TABLE public.ext_log_entries;

CREATE TABLE public.ext_log_entries (
	id int4 NOT NULL,
	"action" varchar(8) NOT NULL,
	logged_at timestamp(0) NOT NULL,
	object_id varchar(64) DEFAULT NULL::character varying NULL,
	object_class varchar(255) NOT NULL,
	"version" int4 NOT NULL,
	"data" text NULL,
	username varchar(255) DEFAULT NULL::character varying NULL,
	CONSTRAINT ext_log_entries_pkey PRIMARY KEY (id)
);
CREATE INDEX ext_log_entries_object_id_idx ON public.ext_log_entries USING btree (object_id);
CREATE INDEX log_class_lookup_idx ON public.ext_log_entries USING btree (object_class);
CREATE INDEX log_date_lookup_idx ON public.ext_log_entries USING btree (logged_at);
CREATE INDEX log_user_lookup_idx ON public.ext_log_entries USING btree (username);
CREATE INDEX log_version_lookup_idx ON public.ext_log_entries USING btree (object_id, object_class, version);


-- public.index_soil_meteo_helper definition

-- Drop table

-- DROP TABLE public.index_soil_meteo_helper;

CREATE TABLE public.index_soil_meteo_helper (
	id serial4 NOT NULL,
	"row_number" int8 NOT NULL,
	organization varchar(255) NOT NULL,
	organization_id int4 NOT NULL,
	customer_identification varchar(50) DEFAULT NULL::character varying NULL,
	"period" varchar(255) NOT NULL,
	"type" varchar(255) NOT NULL,
	"year" int4 NOT NULL,
	treatment varchar(255) NOT NULL,
	orders_plots json NOT NULL,
	contract_date timestamp(0) NOT NULL,
	area float8 NOT NULL,
	helper_type varchar(255) DEFAULT NULL::character varying NULL,
	for_delete bool NULL,
	CONSTRAINT index_soil_meteo_helper_pkey PRIMARY KEY (id)
);


-- public.lab_analysis_group definition

-- Drop table

-- DROP TABLE public.lab_analysis_group;

CREATE TABLE public.lab_analysis_group (
	id serial4 NOT NULL,
	"name" varchar(63) NOT NULL,
	CONSTRAINT lab_analysis_group_pkey PRIMARY KEY (id)
);


-- public.lab_elements_calculations definition

-- Drop table

-- DROP TABLE public.lab_elements_calculations;

CREATE TABLE public.lab_elements_calculations (
	id int4 DEFAULT nextval('lab_elements_calculations_seq'::regclass) NOT NULL,
	"element" public.elements_enum NOT NULL,
	coefficient float8 NOT NULL,
	operation varchar(27) NOT NULL,
	template_column varchar(27) DEFAULT NULL::character varying NULL,
	CONSTRAINT lab_elements_calculations_pkey PRIMARY KEY (id)
);


-- public.lab_interpretation_classes_config definition

-- Drop table

-- DROP TABLE public.lab_interpretation_classes_config;

CREATE TABLE public.lab_interpretation_classes_config (
	id serial4 NOT NULL,
	slug varchar(20) NULL,
	description varchar(255) NOT NULL,
	content_class public.content_class_enum NULL,
	CONSTRAINT lab_interpretation_classes_config_pkey PRIMARY KEY (id)
);


-- public.main_navigation definition

-- Drop table

-- DROP TABLE public.main_navigation;

CREATE TABLE public.main_navigation (
	id serial4 NOT NULL,
	"path" public.ltree NULL,
	label_en varchar(255) NOT NULL,
	url varchar(255) NULL,
	no_data_url varchar(255) NULL,
	icon xml NULL,
	label_bg varchar(255) NOT NULL,
	label_ro varchar(255) NOT NULL,
	label_ua varchar(255) NOT NULL,
	label_it varchar(255) NULL,
	"instance" public.main_navigation_instance_enum DEFAULT 'web'::main_navigation_instance_enum NOT NULL,
	CONSTRAINT main_navigation_pkey PRIMARY KEY (id)
);


-- public.meta_groups definition

-- Drop table

-- DROP TABLE public.meta_groups;

CREATE TABLE public.meta_groups (
	id int4 DEFAULT nextval('meta_groups_seq'::regclass) NOT NULL,
	"name" varchar(255) NOT NULL,
	CONSTRAINT meta_groups_pkey PRIMARY KEY (id)
);


-- public.migration_versions definition

-- Drop table

-- DROP TABLE public.migration_versions;

CREATE TABLE public.migration_versions (
	"version" varchar(192) NOT NULL,
	executed_at timestamp(0) NULL,
	execution_time int4 NULL,
	CONSTRAINT migration_versions_pkey PRIMARY KEY (version)
);


-- public.org_gs definition

-- Drop table

-- DROP TABLE public.org_gs;

CREATE TABLE public.org_gs (
	current_eik varchar(254) NULL,
	real_eik varchar(254) NULL,
	real_name varchar(254) NULL
);


-- public.protocol definition

-- Drop table

-- DROP TABLE public.protocol;

CREATE TABLE public.protocol (
	id int4 NOT NULL,
	"date" timestamp(0) NOT NULL,
	user_id int4 NOT NULL,
	CONSTRAINT protocol_pkey PRIMARY KEY (id)
);


-- public.sampling_type definition

-- Drop table

-- DROP TABLE public.sampling_type;

CREATE TABLE public.sampling_type (
	id serial4 NOT NULL,
	"type" varchar(50) NOT NULL,
	CONSTRAINT sampling_type_pkey PRIMARY KEY (id)
);


-- public.service_provider definition

-- Drop table

-- DROP TABLE public.service_provider;

CREATE TABLE public.service_provider (
	id serial4 NOT NULL,
	"name" varchar(100) NOT NULL,
	slug varchar(100) NOT NULL,
	from_sync bool NOT NULL,
	last_sync timestamp(0) DEFAULT NULL::timestamp without time zone NULL,
	country_code varchar(15) DEFAULT 'BG'::character varying NOT NULL,
	CONSTRAINT service_provider_pkey PRIMARY KEY (id)
);


-- public.weather_station_helper definition

-- Drop table

-- DROP TABLE public.weather_station_helper;

CREATE TABLE public.weather_station_helper (
	id serial4 NOT NULL,
	station_id int4 NOT NULL,
	station_name varchar(255) NOT NULL,
	organization_id int4 NOT NULL,
	organization_name varchar(255) NOT NULL,
	organization_ident_number varchar(255) DEFAULT NULL::character varying NULL,
	station_install_date date NULL,
	station_start_date date NULL,
	station_end_date date NULL,
	station_period varchar(255) DEFAULT NULL::character varying NULL,
	CONSTRAINT weather_station_helper_pkey PRIMARY KEY (id)
);


-- public.activity definition

-- Drop table

-- DROP TABLE public.activity;

CREATE TABLE public.activity (
	id int4 NOT NULL,
	slug varchar(20) NOT NULL,
	service_provider_id int4 NULL,
	CONSTRAINT activity_pkey PRIMARY KEY (id),
	CONSTRAINT fk_ac74095ac6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id)
);
CREATE INDEX idx_ac74095ac6c98e06 ON public.activity USING btree (service_provider_id);


-- public.cron_report definition

-- Drop table

-- DROP TABLE public.cron_report;

CREATE TABLE public.cron_report (
	id int4 NOT NULL,
	job_id int4 NULL,
	run_at timestamp(0) NOT NULL,
	run_time float8 NOT NULL,
	exit_code int4 NOT NULL,
	"output" text NOT NULL,
	error varchar NULL,
	CONSTRAINT cron_report_pkey PRIMARY KEY (id),
	CONSTRAINT fk_b6c6a7f5be04ea9 FOREIGN KEY (job_id) REFERENCES public.cron_job(id) ON DELETE CASCADE
);
CREATE INDEX idx_b6c6a7f5be04ea9 ON public.cron_report USING btree (job_id);


-- public.currency definition

-- Drop table

-- DROP TABLE public.currency;

CREATE TABLE public.currency (
	id int4 NOT NULL,
	slug varchar(255) NOT NULL,
	service_provider_id int4 NULL,
	CONSTRAINT currency_pkey PRIMARY KEY (id),
	CONSTRAINT fk_6956883fc6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id)
);
CREATE INDEX idx_6956883fc6c98e06 ON public.currency USING btree (service_provider_id);


-- public.duration_type definition

-- Drop table

-- DROP TABLE public.duration_type;

CREATE TABLE public.duration_type (
	id int4 NOT NULL,
	slug varchar(20) NOT NULL,
	service_provider_id int4 NULL,
	is_calendar_period bool NULL,
	start_month int4 NULL,
	end_month int4 NULL,
	start_day int4 NULL,
	end_day int4 NULL,
	format varchar(20) DEFAULT NULL::character varying NULL,
	CONSTRAINT duration_type_pkey PRIMARY KEY (id),
	CONSTRAINT fk_df8e2f92c6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id)
);
CREATE INDEX idx_df8e2f92c6c98e06 ON public.duration_type USING btree (service_provider_id);


-- public.lab_analysis_group_element definition

-- Drop table

-- DROP TABLE public.lab_analysis_group_element;

CREATE TABLE public.lab_analysis_group_element (
	id int4 DEFAULT nextval('lab_analysis_group_element_seq'::regclass) NOT NULL,
	lab_analysis_group_id int4 NOT NULL,
	"element" public.elements_enum NOT NULL,
	unit varchar(50) DEFAULT NULL::character varying NULL,
	"method" varchar(50) DEFAULT NULL::character varying NULL,
	has_soil_map bool DEFAULT false NULL,
	color varchar(10) DEFAULT NULL::character varying NULL,
	CONSTRAINT lab_analysis_group_element_pkey PRIMARY KEY (id),
	CONSTRAINT lage_unique_fields UNIQUE (lab_analysis_group_id, element),
	CONSTRAINT fk_2e93f282fc8e2340 FOREIGN KEY (lab_analysis_group_id) REFERENCES public.lab_analysis_group(id)
);
CREATE INDEX idx_2e93f282fc8e2340 ON public.lab_analysis_group_element USING btree (lab_analysis_group_id);


-- public.lab_analysis_group_element_visual_order definition

-- Drop table

-- DROP TABLE public.lab_analysis_group_element_visual_order;

CREATE TABLE public.lab_analysis_group_element_visual_order (
	id serial4 NOT NULL,
	lab_analysis_group_element_id int4 NOT NULL,
	service_provider_id int4 NOT NULL,
	visual_order int4 NOT NULL,
	CONSTRAINT lab_analysis_group_element_visual_order_pkey PRIMARY KEY (id),
	CONSTRAINT fk_814f8c9ccb7c4b31 FOREIGN KEY (lab_analysis_group_element_id) REFERENCES public.lab_analysis_group_element(id)
);
CREATE INDEX idx_814f8c9ccb7c4b31 ON public.lab_analysis_group_element_visual_order USING btree (lab_analysis_group_element_id);


-- public.lab_analysis_uploads definition

-- Drop table

-- DROP TABLE public.lab_analysis_uploads;

CREATE TABLE public.lab_analysis_uploads (
	id int4 NOT NULL,
	user_id int4 NOT NULL,
	"date" timestamp(0) NOT NULL,
	file_path varchar(255) NOT NULL,
	state public.analysis_uploads_states_enum DEFAULT 'Success'::analysis_uploads_states_enum NOT NULL,
	records int4 NULL,
	service_provider_id int4 NULL,
	CONSTRAINT lab_analysis_uploads_pkey PRIMARY KEY (id),
	CONSTRAINT fk_217872dbc6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id)
);
CREATE INDEX idx_217872dbc6c98e06 ON public.lab_analysis_uploads USING btree (service_provider_id);


-- public.lab_element_aggregation_area_value_config definition

-- Drop table

-- DROP TABLE public.lab_element_aggregation_area_value_config;

CREATE TABLE public.lab_element_aggregation_area_value_config (
	id serial4 NOT NULL,
	service_provider_id int4 NOT NULL,
	area_treshold float8 NOT NULL,
	CONSTRAINT lab_element_aggregation_area_value_config_pkey PRIMARY KEY (id),
	CONSTRAINT fk_cc2ed0d5c6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id)
);
CREATE UNIQUE INDEX uniq_cc2ed0d5c6c98e06 ON public.lab_element_aggregation_area_value_config USING btree (service_provider_id);


-- public.lab_element_interpretations_config definition

-- Drop table

-- DROP TABLE public.lab_element_interpretations_config;

CREATE TABLE public.lab_element_interpretations_config (
	id serial4 NOT NULL,
	element_id int4 NOT NULL,
	service_provider_id int4 NOT NULL,
	class_id int4 NOT NULL,
	"range" numrange NOT NULL,
	color varchar(10) DEFAULT NULL::character varying NULL,
	CONSTRAINT lab_element_interpretations_config_pkey PRIMARY KEY (id),
	CONSTRAINT fk_208a18451f1f2a24 FOREIGN KEY (element_id) REFERENCES public.lab_analysis_group_element(id),
	CONSTRAINT fk_208a1845c6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id),
	CONSTRAINT fk_208a1845ea000b10 FOREIGN KEY (class_id) REFERENCES public.lab_interpretation_classes_config(id)
);
CREATE INDEX idx_208a18451f1f2a24 ON public.lab_element_interpretations_config USING btree (element_id);
CREATE INDEX idx_208a1845c6c98e06 ON public.lab_element_interpretations_config USING btree (service_provider_id);
CREATE INDEX idx_208a1845ea000b10 ON public.lab_element_interpretations_config USING btree (class_id);


-- public.lab_elements_results_raw definition

-- Drop table

-- DROP TABLE public.lab_elements_results_raw;

CREATE TABLE public.lab_elements_results_raw (
	id int4 NOT NULL,
	lab_analisys_uploads_id int4 NOT NULL,
	lab_number varchar(27) NOT NULL,
	"element" public.elements_enum NULL,
	value float8 NOT NULL,
	CONSTRAINT lab_elements_results_raw_pkey PRIMARY KEY (id),
	CONSTRAINT fk_d0be8251542fc42f FOREIGN KEY (lab_analisys_uploads_id) REFERENCES public.lab_analysis_uploads(id)
);
CREATE INDEX idx_d0be8251542fc42f ON public.lab_elements_results_raw USING btree (lab_analisys_uploads_id);
CREATE INDEX lab_elements_results_raw_lab_number_idx ON public.lab_elements_results_raw USING btree (lab_number);


-- public.meta_elements_groups definition

-- Drop table

-- DROP TABLE public.meta_elements_groups;

CREATE TABLE public.meta_elements_groups (
	id int4 DEFAULT nextval('meta_elements_groups_seq'::regclass) NOT NULL,
	group_id int4 NULL,
	"element" public.elements_enum NOT NULL,
	CONSTRAINT meta_elements_groups_pkey PRIMARY KEY (id),
	CONSTRAINT fk_3409e0bffe54d947 FOREIGN KEY (group_id) REFERENCES public.meta_groups(id)
);
CREATE INDEX idx_3409e0bffe54d947 ON public.meta_elements_groups USING btree (group_id);


-- public."number" definition

-- Drop table

-- DROP TABLE public."number";

CREATE TABLE public."number" (
	id int4 NOT NULL,
	last_number int4 NOT NULL,
	service_provider_id int4 NULL,
	CONSTRAINT number_pkey PRIMARY KEY (id),
	CONSTRAINT fk_96901f54c6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id)
);
CREATE INDEX idx_96901f54c6c98e06 ON public.number USING btree (service_provider_id);


-- public.package definition

-- Drop table

-- DROP TABLE public.package;

CREATE TABLE public.package (
	id serial4 NOT NULL,
	slug varchar(30) NOT NULL,
	service_provider_id int4 NULL,
	is_active bool DEFAULT true NOT NULL,
	contain_fields bool DEFAULT false NULL,
	slug_short varchar(10) DEFAULT NULL::character varying NULL,
	is_sampling bool DEFAULT false NULL,
	has_station bool DEFAULT false NULL,
	integration public.integration_type_enum NULL,
	is_vra bool DEFAULT false NULL,
	is_full_sampling bool DEFAULT false NULL,
	is_satellite bool DEFAULT false NULL,
	parent_id int4 NULL,
	"style" jsonb DEFAULT '{"color": "#000000", "backgroundColor": "#fafafa"}'::jsonb NOT NULL,
	is_countable bool DEFAULT false NOT NULL,
	"attributes" jsonb NULL,
	CONSTRAINT package_pkey PRIMARY KEY (id),
	CONSTRAINT fk_de686795727aca70 FOREIGN KEY (parent_id) REFERENCES public.package(id) ON DELETE SET NULL,
	CONSTRAINT fk_de686795c6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id)
);
CREATE INDEX idx_de686795727aca70 ON public.package USING btree (parent_id);
CREATE INDEX idx_de686795c6c98e06 ON public.package USING btree (service_provider_id);


-- public.package_grid_points definition

-- Drop table

-- DROP TABLE public.package_grid_points;

CREATE TABLE public.package_grid_points (
	id int4 NOT NULL,
	sample_id int4 NOT NULL,
	package_id int4 NOT NULL,
	package_type varchar(63) NOT NULL,
	grid_type varchar(63) DEFAULT NULL::character varying NULL,
	barcode varchar(127) DEFAULT NULL::character varying NULL,
	lab_number varchar(63) DEFAULT NULL::character varying NULL,
	state public.point_states_enum DEFAULT 'Pending'::point_states_enum NOT NULL,
	state_updated_at timestamp(0) DEFAULT NULL::timestamp without time zone NULL,
	point_uuid varchar(63) DEFAULT NULL::character varying NULL,
	plot_uuid varchar(63) DEFAULT NULL::character varying NULL,
	sampling_type_id int4 NOT NULL,
	CONSTRAINT package_grid_points_pkey PRIMARY KEY (id),
	CONSTRAINT fk_ca8b52fe45c8b25e FOREIGN KEY (sampling_type_id) REFERENCES public.sampling_type(id)
);
CREATE INDEX idx_ca8b52fe45c8b25e ON public.package_grid_points USING btree (sampling_type_id);
CREATE INDEX package_grid_points_lab_number_idx ON public.package_grid_points USING btree (lab_number);
CREATE INDEX package_grid_points_package_id_idx ON public.package_grid_points USING btree (package_id);


-- public.package_main_navigation definition

-- Drop table

-- DROP TABLE public.package_main_navigation;

CREATE TABLE public.package_main_navigation (
	id serial4 NOT NULL,
	package_id int4 NOT NULL,
	main_navigation_id int4 NOT NULL,
	visual_order int4 NOT NULL,
	target public.link_target_enum DEFAULT '_self'::link_target_enum NOT NULL,
	CONSTRAINT package_main_navigation_pkey PRIMARY KEY (id),
	CONSTRAINT fk_f9279dfc8b532ef0 FOREIGN KEY (main_navigation_id) REFERENCES public.main_navigation(id),
	CONSTRAINT fk_f9279dfcf44cabff FOREIGN KEY (package_id) REFERENCES public.package(id)
);
CREATE INDEX idx_f9279dfc8b532ef0 ON public.package_main_navigation USING btree (main_navigation_id);
CREATE INDEX idx_f9279dfcf44cabff ON public.package_main_navigation USING btree (package_id);


-- public.package_sampling_type definition

-- Drop table

-- DROP TABLE public.package_sampling_type;

CREATE TABLE public.package_sampling_type (
	package_id int4 NOT NULL,
	sampling_type_id int4 NOT NULL,
	CONSTRAINT package_sampling_type_pkey PRIMARY KEY (package_id, sampling_type_id),
	CONSTRAINT fk_b423c1dd45c8b25e FOREIGN KEY (sampling_type_id) REFERENCES public.sampling_type(id) ON DELETE CASCADE,
	CONSTRAINT fk_b423c1ddf44cabff FOREIGN KEY (package_id) REFERENCES public.package(id) ON DELETE CASCADE
);
CREATE INDEX idx_b423c1dd45c8b25e ON public.package_sampling_type USING btree (sampling_type_id);
CREATE INDEX idx_b423c1ddf44cabff ON public.package_sampling_type USING btree (package_id);


-- public.protocol_package_field definition

-- Drop table

-- DROP TABLE public.protocol_package_field;

CREATE TABLE public.protocol_package_field (
	id int4 NOT NULL,
	protocol_id int4 NOT NULL,
	package_field_id int4 NOT NULL,
	package_field_type varchar(255) NOT NULL,
	CONSTRAINT protocol_package_field_pkey PRIMARY KEY (id),
	CONSTRAINT fk_7c8a00abccd59258 FOREIGN KEY (protocol_id) REFERENCES public.protocol(id)
);
CREATE INDEX idx_7c8a00abccd59258 ON public.protocol_package_field USING btree (protocol_id);


-- public.recommendation_calc_model_config definition

-- Drop table

-- DROP TABLE public.recommendation_calc_model_config;

CREATE TABLE public.recommendation_calc_model_config (
	id serial4 NOT NULL,
	calc_model_id int4 NOT NULL,
	result_element public.result_element_enum NOT NULL,
	element_id int4 NULL,
	params public._recommendation_crop_model_parameter_enum NULL,
	visual_order int4 NULL,
	CONSTRAINT recommendation_calc_model_config_pkey PRIMARY KEY (id),
	CONSTRAINT fk_282dfb371f1f2a24 FOREIGN KEY (element_id) REFERENCES public.lab_analysis_group_element(id)
);
CREATE INDEX idx_282dfb371f1f2a24 ON public.recommendation_calc_model_config USING btree (element_id);


-- public.recommendation_models_config definition

-- Drop table

-- DROP TABLE public.recommendation_models_config;

CREATE TABLE public.recommendation_models_config (
	id serial4 NOT NULL,
	service_provider_id int4 NOT NULL,
	"name" varchar(255) NOT NULL,
	calc_model_id int4 NOT NULL,
	CONSTRAINT recommendation_models_config_pkey PRIMARY KEY (id),
	CONSTRAINT fk_cdca80fdc6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id)
);
CREATE INDEX idx_cdca80fdc6c98e06 ON public.recommendation_models_config USING btree (service_provider_id);


-- public.recommendations definition

-- Drop table

-- DROP TABLE public.recommendations;

CREATE TABLE public.recommendations (
	id serial4 NOT NULL,
	package_id int4 NOT NULL,
	package_type varchar(63) NOT NULL,
	model_id int4 NULL,
	plot_uuid varchar(63) NOT NULL,
	plot_name varchar(255) NOT NULL,
	crop_id int4 NULL,
	status public.recommendation_status_enum DEFAULT 'For approve'::recommendation_status_enum NOT NULL,
	target_yield float8 NULL,
	created_at timestamp(0) NOT NULL,
	modified_at timestamp(0) NOT NULL,
	humus float8 DEFAULT 1.00 NOT NULL,
	sampling_type_ids _int4 NOT NULL,
	valid_from date NOT NULL,
	decline_reason text NULL,
	CONSTRAINT recommendations_pkey PRIMARY KEY (id),
	CONSTRAINT fk_5f42d6c97975b7e7 FOREIGN KEY (model_id) REFERENCES public.recommendation_models_config(id)
);
CREATE INDEX idx_5f42d6c97975b7e7 ON public.recommendations USING btree (model_id);
CREATE INDEX idx_5f42d6c9f44cabff ON public.recommendations USING btree (package_id);


-- public.recommendations_vra_orders definition

-- Drop table

-- DROP TABLE public.recommendations_vra_orders;

CREATE TABLE public.recommendations_vra_orders (
	id serial4 NOT NULL,
	recommendation_id int4 NOT NULL,
	vra_order_id int4 NOT NULL,
	vra_order_type public.vra_order_type_enum DEFAULT 'soil_vra'::vra_order_type_enum NOT NULL,
	CONSTRAINT recommendations_vra_orders_pkey PRIMARY KEY (id),
	CONSTRAINT fk_73904ed72576a2dd FOREIGN KEY (recommendation_id) REFERENCES public.recommendations(id)
);
CREATE INDEX idx_73904ed7f44cabbb ON public.recommendations_vra_orders USING btree (recommendation_id);


-- public.service definition

-- Drop table

-- DROP TABLE public.service;

CREATE TABLE public.service (
	id int4 NOT NULL,
	slug varchar(20) NOT NULL,
	service_provider_id int4 NULL,
	is_active bool DEFAULT true NOT NULL,
	CONSTRAINT service_pkey PRIMARY KEY (id),
	CONSTRAINT fk_e19d9ad2c6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id)
);
CREATE INDEX idx_e19d9ad2c6c98e06 ON public.service USING btree (service_provider_id);


-- public.service_activity definition

-- Drop table

-- DROP TABLE public.service_activity;

CREATE TABLE public.service_activity (
	service_id int4 NOT NULL,
	activity_id int4 NOT NULL,
	CONSTRAINT service_activity_pkey PRIMARY KEY (service_id, activity_id),
	CONSTRAINT fk_5502526781c06096 FOREIGN KEY (activity_id) REFERENCES public.activity(id) ON DELETE CASCADE,
	CONSTRAINT fk_55025267ed5ca9e6 FOREIGN KEY (service_id) REFERENCES public.service(id) ON DELETE CASCADE
);
CREATE INDEX idx_5502526781c06096 ON public.service_activity USING btree (activity_id);
CREATE INDEX idx_55025267ed5ca9e6 ON public.service_activity USING btree (service_id);


-- public.service_package definition

-- Drop table

-- DROP TABLE public.service_package;

CREATE TABLE public.service_package (
	service_id int4 NOT NULL,
	package_id int4 NOT NULL,
	CONSTRAINT service_package_pkey PRIMARY KEY (service_id, package_id),
	CONSTRAINT fk_11ec3509ed5ca9e6 FOREIGN KEY (service_id) REFERENCES public.service(id) ON DELETE CASCADE,
	CONSTRAINT fk_11ec3509f44cabff FOREIGN KEY (package_id) REFERENCES public.package(id) ON DELETE CASCADE
);
CREATE INDEX idx_11ec3509ed5ca9e6 ON public.service_package USING btree (service_id);
CREATE INDEX idx_11ec3509f44cabff ON public.service_package USING btree (package_id);


-- public.service_statuses definition

-- Drop table

-- DROP TABLE public.service_statuses;

CREATE TABLE public.service_statuses (
	id int4 NOT NULL,
	slug varchar(20) NOT NULL,
	service_provider_id int4 NULL,
	CONSTRAINT service_statuses_pkey PRIMARY KEY (id),
	CONSTRAINT fk_b286452cc6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id)
);
CREATE INDEX idx_b286452cc6c98e06 ON public.service_statuses USING btree (service_provider_id);


-- public.contract definition

-- Drop table

-- DROP TABLE public.contract;

CREATE TABLE public.contract (
	id int4 NOT NULL,
	currency_id int4 NOT NULL,
	parent_id int4 NULL,
	contract_date date NULL,
	start_date timestamp(0) NOT NULL,
	end_date timestamp(0) NOT NULL,
	created_at timestamp(0) NOT NULL,
	modified_at timestamp(0) DEFAULT NULL::timestamp without time zone NULL,
	is_annex bool NOT NULL,
	"type" varchar(255) NOT NULL,
	"number" varchar(50) DEFAULT NULL::character varying NULL,
	area numeric(10, 4) DEFAULT NULL::numeric NULL,
	service_provider_id int4 NULL,
	customer_identification varchar(50) DEFAULT NULL::character varying NULL,
	status public.contract_statuses_enum DEFAULT 'New'::contract_statuses_enum NOT NULL,
	organization_id int4 NULL,
	CONSTRAINT contract_pkey PRIMARY KEY (id),
	CONSTRAINT fk_e98f285938248176 FOREIGN KEY (currency_id) REFERENCES public.currency(id),
	CONSTRAINT fk_e98f2859727aca70 FOREIGN KEY (parent_id) REFERENCES public.contract(id),
	CONSTRAINT fk_e98f2859c6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id)
);
CREATE INDEX contract_customer_identification_idx ON public.contract USING btree (customer_identification);
CREATE INDEX idx_e98f285938248176 ON public.contract USING btree (currency_id);
CREATE INDEX idx_e98f2859727aca70 ON public.contract USING btree (parent_id);
CREATE INDEX idx_e98f2859c6c98e06 ON public.contract USING btree (service_provider_id);


-- public.lab_aggregated_element_interpetations_config definition

-- Drop table

-- DROP TABLE public.lab_aggregated_element_interpetations_config;

CREATE TABLE public.lab_aggregated_element_interpetations_config (
	id serial4 NOT NULL,
	element_id int4 NOT NULL,
	service_provider_id int4 NOT NULL,
	class_ids _int4 NULL,
	comment_text text NULL,
	CONSTRAINT lab_aggregated_element_interpetations_config_pkey PRIMARY KEY (id),
	CONSTRAINT fk_6e577c171f1f2a24 FOREIGN KEY (element_id) REFERENCES public.lab_analysis_group_element(id),
	CONSTRAINT fk_6e577c17c6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id)
);
CREATE INDEX idx_6e577c171f1f2a24 ON public.lab_aggregated_element_interpetations_config USING btree (element_id);
CREATE INDEX idx_6e577c17c6c98e06 ON public.lab_aggregated_element_interpetations_config USING btree (service_provider_id);


-- public.lab_analysis_package_group definition

-- Drop table

-- DROP TABLE public.lab_analysis_package_group;

CREATE TABLE public.lab_analysis_package_group (
	id int4 DEFAULT nextval('lab_analysis_package_group_seq'::regclass) NOT NULL,
	package_id int4 NOT NULL,
	lab_analysis_group_id int4 NOT NULL,
	package_type varchar(63) NOT NULL,
	sampling_type_id int4 NULL,
	CONSTRAINT lab_analysis_package_group_pkey PRIMARY KEY (id),
	CONSTRAINT lab_analysis_package_group_unique_columns UNIQUE (package_id, package_type, lab_analysis_group_id, sampling_type_id),
	CONSTRAINT fk_1117bb9545c8b25e FOREIGN KEY (sampling_type_id) REFERENCES public.sampling_type(id),
	CONSTRAINT fk_1117bb95f44cabff FOREIGN KEY (package_id) REFERENCES public.package(id),
	CONSTRAINT fk_1117bb95fc8e2340 FOREIGN KEY (lab_analysis_group_id) REFERENCES public.lab_analysis_group(id)
);
CREATE INDEX idx_1117bb9545c8b25e ON public.lab_analysis_package_group USING btree (sampling_type_id);
CREATE INDEX idx_1117bb95f44cabff ON public.lab_analysis_package_group USING btree (package_id);
CREATE INDEX idx_1117bb95fc8e2340 ON public.lab_analysis_package_group USING btree (lab_analysis_group_id);


-- public.lab_element_group definition

-- Drop table

-- DROP TABLE public.lab_element_group;

CREATE TABLE public.lab_element_group (
	id serial4 NOT NULL,
	package_id int4 NOT NULL,
	package_type varchar(63) NOT NULL,
	state public.element_group_states_enum DEFAULT 'Pending'::element_group_states_enum NOT NULL,
	state_updated_at timestamp(0) NOT NULL,
	plot_uuid varchar(63) DEFAULT NULL::character varying NULL,
	lab_analysis_package_group_id int4 NULL,
	CONSTRAINT lab_element_group_pkey PRIMARY KEY (id),
	CONSTRAINT fk_858a2f33197b4d8e FOREIGN KEY (lab_analysis_package_group_id) REFERENCES public.lab_analysis_package_group(id)
);
CREATE INDEX idx_858a2f33197b4d8e ON public.lab_element_group USING btree (lab_analysis_package_group_id);


-- public.lab_elements_results definition

-- Drop table

-- DROP TABLE public.lab_elements_results;

CREATE TABLE public.lab_elements_results (
	id int4 NOT NULL,
	lab_element_group_id int4 NOT NULL,
	"element" public.elements_enum NULL,
	value float8 NULL,
	state public.element_result_states_enum DEFAULT 'Pending'::element_result_states_enum NULL,
	state_updated_at timestamp(0) NOT NULL,
	package_grid_points_id int4 NOT NULL,
	lab_elements_results_raw_id int4 NULL,
	CONSTRAINT lab_elements_results_pkey PRIMARY KEY (id),
	CONSTRAINT fk_2e6761b62bfcfe5e FOREIGN KEY (package_grid_points_id) REFERENCES public.package_grid_points(id) ON DELETE CASCADE,
	CONSTRAINT fk_2e6761b65f1dcc2f FOREIGN KEY (lab_element_group_id) REFERENCES public.lab_element_group(id),
	CONSTRAINT fk_2e6761b6dedf4c09 FOREIGN KEY (lab_elements_results_raw_id) REFERENCES public.lab_elements_results_raw(id)
);
CREATE INDEX idx_2e6761b62bfcfe5e ON public.lab_elements_results USING btree (package_grid_points_id);
CREATE INDEX idx_2e6761b62e53e525 ON public.lab_elements_results USING btree (lab_elements_results_raw_id);
CREATE INDEX idx_2e6761b65f1dcc2f ON public.lab_elements_results USING btree (lab_element_group_id);
CREATE INDEX lab_elements_results_state_idx ON public.lab_elements_results USING btree (state);


-- public.price definition

-- Drop table

-- DROP TABLE public.price;

CREATE TABLE public.price (
	id int4 NOT NULL,
	contract_id int4 NOT NULL,
	"period" int4 NULL,
	amount numeric(10, 2) DEFAULT NULL::numeric NULL,
	service_provider_id int4 NULL,
	status public.price_status_enum DEFAULT 'None'::price_status_enum NOT NULL,
	CONSTRAINT price_pkey PRIMARY KEY (id),
	CONSTRAINT fk_cac822d92576e0fd FOREIGN KEY (contract_id) REFERENCES public.contract(id) ON DELETE CASCADE,
	CONSTRAINT fk_cac822d9c6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id)
);
CREATE INDEX idx_cac822d92576e0fd ON public.price USING btree (contract_id);
CREATE INDEX idx_cac822d99a1887dc ON public.price USING btree (contract_id);
CREATE INDEX idx_cac822d9c6c98e06 ON public.price USING btree (service_provider_id);


-- public.recommendation_comment definition

-- Drop table

-- DROP TABLE public.recommendation_comment;

CREATE TABLE public.recommendation_comment (
	id serial4 NOT NULL,
	recommendation_id int4 NOT NULL,
	comment_type public.recommendation_comment_type_enum NOT NULL,
	param public.result_element_enum NULL,
	value text NULL,
	CONSTRAINT recommendation_comment_pkey PRIMARY KEY (id),
	CONSTRAINT fk_80a31ae0d173940b FOREIGN KEY (recommendation_id) REFERENCES public.recommendations(id)
);
CREATE INDEX idx_80a31ae0d173940b ON public.recommendation_comment USING btree (recommendation_id);


-- public.recommendation_crop_model_config_values definition

-- Drop table

-- DROP TABLE public.recommendation_crop_model_config_values;

CREATE TABLE public.recommendation_crop_model_config_values (
	id serial4 NOT NULL,
	model_id int4 NOT NULL,
	crop_ids _int4 NOT NULL,
	"parameter" public.recommendation_crop_model_parameter_enum NOT NULL,
	value float8 NULL,
	CONSTRAINT recommendation_crop_model_config_values_pkey PRIMARY KEY (id),
	CONSTRAINT fk_f3c737397975b7e7 FOREIGN KEY (model_id) REFERENCES public.recommendation_models_config(id)
);
CREATE INDEX idx_f3c737397975b7e7 ON public.recommendation_crop_model_config_values USING btree (model_id);


-- public.recommendation_element_comments_config definition

-- Drop table

-- DROP TABLE public.recommendation_element_comments_config;

CREATE TABLE public.recommendation_element_comments_config (
	id serial4 NOT NULL,
	model_id int4 NOT NULL,
	crop_ids _int4 NULL,
	result_element public.result_element_enum NULL,
	"range" numrange NOT NULL,
	fertiliser_type varchar(255) DEFAULT NULL::character varying NULL,
	comment_text text NULL,
	CONSTRAINT recommendation_element_comments_config_pkey PRIMARY KEY (id),
	CONSTRAINT fk_476b9d6c7975b7e7 FOREIGN KEY (model_id) REFERENCES public.recommendation_models_config(id)
);
CREATE INDEX idx_476b9d6c7975b7e7 ON public.recommendation_element_comments_config USING btree (model_id);


-- public.recommendation_elements_results definition

-- Drop table

-- DROP TABLE public.recommendation_elements_results;

CREATE TABLE public.recommendation_elements_results (
	id serial4 NOT NULL,
	recommendation_id int4 NOT NULL,
	result_element public.result_element_enum NOT NULL,
	value float8 NULL,
	CONSTRAINT recommendation_elements_results_pkey PRIMARY KEY (id),
	CONSTRAINT fk_f44e2df1d173940b FOREIGN KEY (recommendation_id) REFERENCES public.recommendations(id)
);
CREATE INDEX idx_f44e2df1d173940b ON public.recommendation_elements_results USING btree (recommendation_id);


-- public.recommendation_elements_susces definition

-- Drop table

-- DROP TABLE public.recommendation_elements_susces;

CREATE TABLE public.recommendation_elements_susces (
	id serial4 NOT NULL,
	recommendation_id int4 NOT NULL,
	"element" public.elements_enum NOT NULL,
	value bool NOT NULL,
	CONSTRAINT recommendation_elements_susces_pkey PRIMARY KEY (id),
	CONSTRAINT fk_f33a1df1d182840b FOREIGN KEY (recommendation_id) REFERENCES public.recommendations(id)
);
CREATE INDEX idx_f32e2df1d284951a ON public.recommendation_elements_susces USING btree (recommendation_id);


-- public.recommendation_fertiliser_comments_ph_config definition

-- Drop table

-- DROP TABLE public.recommendation_fertiliser_comments_ph_config;

CREATE TABLE public.recommendation_fertiliser_comments_ph_config (
	id serial4 NOT NULL,
	model_id int4 NOT NULL,
	fertiliser_type varchar(255) DEFAULT NULL::character varying NULL,
	result_element public.result_element_enum NOT NULL,
	"range" numrange NOT NULL,
	comment_text text NOT NULL,
	CONSTRAINT recommendation_fertiliser_comments_ph_config_pkey PRIMARY KEY (id),
	CONSTRAINT fk_99262df57975b7e7 FOREIGN KEY (model_id) REFERENCES public.recommendation_models_config(id)
);
CREATE INDEX idx_99262df57975b7e7 ON public.recommendation_fertiliser_comments_ph_config USING btree (model_id);


-- public.recommendation_leaf_fertiliser_comments_config definition

-- Drop table

-- DROP TABLE public.recommendation_leaf_fertiliser_comments_config;

CREATE TABLE public.recommendation_leaf_fertiliser_comments_config (
	id serial4 NOT NULL,
	model_id int4 NOT NULL,
	crop_ids _int4 NULL,
	result_elements public._result_element_enum NULL,
	"range" numrange NOT NULL,
	comment_text text NOT NULL,
	CONSTRAINT recommendation_leaf_fertiliser_comments_config_pkey PRIMARY KEY (id),
	CONSTRAINT fk_28736a8a7975b7e7 FOREIGN KEY (model_id) REFERENCES public.recommendation_models_config(id)
);
CREATE INDEX idx_28736a8a7975b7e7 ON public.recommendation_leaf_fertiliser_comments_config USING btree (model_id);


-- public.responsible_user definition

-- Drop table

-- DROP TABLE public.responsible_user;

CREATE TABLE public.responsible_user (
	id int4 NOT NULL,
	contract_id int4 NOT NULL,
	username varchar(255) NOT NULL,
	"role" varchar(255) NOT NULL,
	user_id int4 NULL,
	CONSTRAINT responsible_user_pkey PRIMARY KEY (id),
	CONSTRAINT fk_cc91c58f2576e0fd FOREIGN KEY (contract_id) REFERENCES public.contract(id) ON DELETE CASCADE
);
CREATE INDEX idx_cc91c58f2576e0fd ON public.responsible_user USING btree (contract_id);


-- public.service_contracts definition

-- Drop table

-- DROP TABLE public.service_contracts;

CREATE TABLE public.service_contracts (
	id int4 NOT NULL,
	CONSTRAINT service_contracts_pkey PRIMARY KEY (id),
	CONSTRAINT fk_51c19339bf396750 FOREIGN KEY (id) REFERENCES public.contract(id) ON DELETE CASCADE
);


-- public.subscription_contracts definition

-- Drop table

-- DROP TABLE public.subscription_contracts;

CREATE TABLE public.subscription_contracts (
	id int4 NOT NULL,
	duration_type_id int4 NOT NULL,
	duration int4 NOT NULL,
	CONSTRAINT subscription_contracts_pkey PRIMARY KEY (id),
	CONSTRAINT fk_3bef6bcf80ca3f3b FOREIGN KEY (duration_type_id) REFERENCES public.duration_type(id),
	CONSTRAINT fk_3bef6bcfbf396750 FOREIGN KEY (id) REFERENCES public.contract(id) ON DELETE CASCADE
);
CREATE INDEX idx_3bef6bcf80ca3f3b ON public.subscription_contracts USING btree (duration_type_id);


-- public.subscription_package definition

-- Drop table

-- DROP TABLE public.subscription_package;

CREATE TABLE public.subscription_package (
	id int4 NOT NULL,
	contract_id int4 NOT NULL,
	package_id int4 NOT NULL,
	"period" int4 NOT NULL,
	service_provider_id int4 NULL,
	status public.package_statuses_enum DEFAULT 'New'::package_statuses_enum NOT NULL,
	state public.package_states_enum DEFAULT 'New'::package_states_enum NOT NULL,
	amount numeric(10, 2) DEFAULT NULL::numeric NULL,
	start_date timestamp DEFAULT now() NOT NULL,
	end_date timestamp DEFAULT now() NOT NULL,
	CONSTRAINT subscription_package_pkey PRIMARY KEY (id),
	CONSTRAINT fk_ad7d870e2576e0fd FOREIGN KEY (contract_id) REFERENCES public.subscription_contracts(id) ON DELETE CASCADE,
	CONSTRAINT fk_ad7d870ec6c98e06 FOREIGN KEY (service_provider_id) REFERENCES public.service_provider(id),
	CONSTRAINT fk_ad7d870ef44cabff FOREIGN KEY (package_id) REFERENCES public.package(id)
);
CREATE INDEX idx_ad7d870e2576e0fd ON public.subscription_package USING btree (contract_id);
CREATE INDEX idx_ad7d870ec6c98e06 ON public.subscription_package USING btree (service_provider_id);
CREATE INDEX idx_ad7d870ef44cabff ON public.subscription_package USING btree (package_id);


-- public.subscription_package_field definition

-- Drop table

-- DROP TABLE public.subscription_package_field;

CREATE TABLE public.subscription_package_field (
	id int4 NOT NULL,
	subscription_package_id int4 NOT NULL,
	area numeric(20, 5) NOT NULL,
	field_state public.field_states_enum DEFAULT 'New'::field_states_enum NOT NULL,
	plot_uuid varchar(63) DEFAULT NULL::character varying NULL,
	order_uuid varchar(63) DEFAULT NULL::character varying NULL,
	parent_id int4 NULL,
	farm_id int4 NOT NULL,
	CONSTRAINT subscription_package_field_pkey PRIMARY KEY (id),
	CONSTRAINT fk_a0f4124436a9eb9a FOREIGN KEY (subscription_package_id) REFERENCES public.subscription_package(id),
	CONSTRAINT fk_a0f41244727aca70 FOREIGN KEY (parent_id) REFERENCES public.subscription_package_field(id) ON DELETE SET NULL
);
CREATE INDEX idx_a0f4124436a9eb9a ON public.subscription_package_field USING btree (subscription_package_id);
CREATE INDEX idx_a0f41244727aca70 ON public.subscription_package_field USING btree (parent_id);


-- public.package_service definition

-- Drop table

-- DROP TABLE public.package_service;

CREATE TABLE public.package_service (
	package_id int4 NOT NULL,
	service_id int4 NOT NULL,
	CONSTRAINT package_service_pkey PRIMARY KEY (package_id, service_id),
	CONSTRAINT fk_6ec6ff5eed5ca9e6 FOREIGN KEY (service_id) REFERENCES public.service_contracts(id) ON DELETE CASCADE,
	CONSTRAINT fk_6ec6ff5ef44cabff FOREIGN KEY (package_id) REFERENCES public.package(id) ON DELETE CASCADE
);
CREATE INDEX idx_6ec6ff5eed5ca9e6 ON public.package_service USING btree (service_id);
CREATE INDEX idx_6ec6ff5ef44cabff ON public.package_service USING btree (package_id);


-- public.payment definition

-- Drop table

-- DROP TABLE public.payment;

CREATE TABLE public.payment (
	id int4 NOT NULL,
	price_id int4 NOT NULL,
	paid numeric(10, 2) NOT NULL,
	date_paid timestamp(0) NOT NULL,
	created_at timestamp(0) NOT NULL,
	username varchar(127) DEFAULT NULL::character varying NULL,
	CONSTRAINT payment_pkey PRIMARY KEY (id),
	CONSTRAINT fk_6d28840dd614c7e7 FOREIGN KEY (price_id) REFERENCES public.price(id) ON DELETE CASCADE
);
CREATE INDEX idx_6d28840dd614c7e7 ON public.payment USING btree (price_id);


-- public.service_contract_packages definition

-- Drop table

-- DROP TABLE public.service_contract_packages;

CREATE TABLE public.service_contract_packages (
	id int4 NOT NULL,
	contract_id int4 NOT NULL,
	package_id int4 NOT NULL,
	status public.package_statuses_enum DEFAULT 'New'::package_statuses_enum NOT NULL,
	state public.package_states_enum DEFAULT 'New'::package_states_enum NOT NULL,
	amount numeric(10, 2) DEFAULT NULL::numeric NULL,
	CONSTRAINT service_contract_packages_pkey PRIMARY KEY (id),
	CONSTRAINT fk_6415be8d2576e0fd FOREIGN KEY (contract_id) REFERENCES public.service_contracts(id) ON DELETE CASCADE,
	CONSTRAINT fk_6415be8df44cabff FOREIGN KEY (package_id) REFERENCES public.package(id)
);
CREATE INDEX idx_6415be8d2576e0fd ON public.service_contract_packages USING btree (contract_id);
CREATE INDEX idx_6415be8df44cabff ON public.service_contract_packages USING btree (package_id);


-- public.service_package_field definition

-- Drop table

-- DROP TABLE public.service_package_field;

CREATE TABLE public.service_package_field (
	id int4 NOT NULL,
	service_package_id int4 NOT NULL,
	area numeric(20, 5) NOT NULL,
	plot_uuid varchar(63) DEFAULT NULL::character varying NULL,
	order_uuid varchar(63) DEFAULT NULL::character varying NULL,
	CONSTRAINT service_package_field_pkey PRIMARY KEY (id),
	CONSTRAINT fk_1e4ef83d621d924b FOREIGN KEY (service_package_id) REFERENCES public.service_contract_packages(id)
);
CREATE INDEX idx_1e4ef83d621d924b ON public.service_package_field USING btree (service_package_id);