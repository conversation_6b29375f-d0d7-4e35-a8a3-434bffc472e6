framework:
    workflows:
        contract_subscription_state:
            type: state_machine
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'status'
            supports:
                - App\Entity\Contract\Subscription
            initial_marking: New
            places:
                - New
                - Active
                - Expired
            transitions:
                activate_contract:
                    from: New
                    to: Active
                deactivate_contract:
                    from: Active
                    to: Expired