framework:
    workflows:
        subscription_package_status:
            type: state_machine
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'status'
            supports:
                - App\Entity\Contract\SubscriptionPackage
            initial_marking: New
            places:
                - New
                - Active
                - Expired
            transitions:
                activate_package:
                    from: New
                    to: Active
                deactivate_package:
                    from: Active
                    to: Expired
