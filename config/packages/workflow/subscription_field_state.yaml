framework:
    workflows:
        subscription_field_state:
            type: state_machine
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'fieldState'
            supports:
                - App\Entity\Contract\SubscriptionPackageField
            initial_marking: New
            places:
                - New
                - Active
                - Gridded
                - Cells selected
                - For sampling
                - Sampling
                - Sampled
                - In progress
                - For recommendation
                - Delivered
                - Expired
            transitions:
                plot_active:
                    from: New
                    to: Active
                plot_gridded:
                    from: [New, Cells selected]
                    to: Gridded
                plot_cells_selected:
                    from: Gridded
                    to: Cells selected
                plot_for_sampling:
                    from: [Gridded, Cells selected]
                    to: For sampling
                plot_sampling:
                    from: For sampling
                    to: Sampling
                plot_sampled:
                    from: Sampling
                    to: Sampled
                plot_in_progress:
                    from: Sampled
                    to: In progress
                plot_for_recommendation:
                    from: In progress
                    to: For recommendation
                plot_delivered:
                    from: For recommendation
                    to: Delivered
                plot_expired:
                    from: Active
                    to: Expired
                    metadata:
                        expire_from_package: ['Satellite imagery', 'Index VRA-N', 'Weather data']