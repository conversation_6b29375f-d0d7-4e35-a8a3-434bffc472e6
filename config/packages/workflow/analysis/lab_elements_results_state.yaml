framework:
    workflows:
        lab_elements_results_state:
            type: state_machine
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'state'
            supports:
                - App\Entity\Analysis\LabElementsResults
            initial_marking: Pending
            places:
                - Pending
                - For Analysis
                - Analysed
                - For reanalysis
                - Approved
                - Not analysed
            transitions:
                for_analysis:
                    from: Pending
                    to: For Analysis
                analysed:
                    from: For Analysis
                    to: Analysed
                approved:
                    from: Analysed
                    to: Approved
                for_reanalysis:
                    from: Analysed
                    to: For reanalysis
                analysed_again:
                    from: For reanalysis
                    to: Analysed
                not_analysed:
                    from: Pending
                    to: Not analysed
