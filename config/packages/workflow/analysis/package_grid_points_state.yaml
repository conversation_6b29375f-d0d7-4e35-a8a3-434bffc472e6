framework:
    workflows:
        package_grid_points_state:
            type: state_machine
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'state'
            supports:
                - App\Entity\Analysis\PackageGridPoints
            initial_marking: Pending
            places:
                - Pending
                - For sampling
                - Not sampled
                - Sampling
                - Sampled
                - ReceivedInLab
            transitions:
                not_sampled:
                    from: [Pending, For sampling, Sampling]
                    to: Not sampled
                pending:
                    from: Not sampled
                    to: Pending
                for_sampling:
                    from: [ Pending, Not sampled]
                    to: For sampling
                sampling:
                    from: [Pending, For sampling, Not sampled]
                    to: Sampling
                sampled:
                    from: Sampling
                    to: Sampled
                received_in_lab:
                    from: Sampled
                    to: ReceivedInLab
