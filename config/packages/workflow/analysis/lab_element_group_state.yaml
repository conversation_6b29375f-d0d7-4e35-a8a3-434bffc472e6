framework:
    workflows:
        lab_element_group_state:
            type: state_machine
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'state'
            supports:
                - App\Entity\Analysis\LabElementGroup
            initial_marking: Pending
            places:
                - Pending
                - In progress
                - For reanalysis
                - For approve
                - For recommendation
                - Delivered
            transitions:
                in_progress:
                    from: Pending
                    to: In progress
                for_approve:
                    from: In progress
                    to: For approve
                for_reanalysis:
                    from: For approve
                    to: For reanalysis
                in_progress_back:
                    from: For reanalysis
                    to: In progress
                for_recommendation:
                    from: For approve
                    to: For recommendation
                delivered:
                    from: For recommendation
                    to: Delivered
