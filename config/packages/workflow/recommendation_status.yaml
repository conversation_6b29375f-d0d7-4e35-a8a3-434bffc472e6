framework:
    workflows:
        recommendation_status:
            type: state_machine
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'status'
            supports:
                - App\Entity\Recommendation\Recommendation
            initial_marking: For approve
            places:
                - For approve
                - Declined
                - Delivered
            transitions:
                complete_recommendation:
                    from: For approve
                    to: Delivered
                decline_recommendation:
                    from: For approve
                    to: Declined
                for_approve_recommendation:
                    from: Declined
                    to: For approve
