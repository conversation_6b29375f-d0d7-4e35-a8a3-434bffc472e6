framework:
    workflows:
        subscription_package_states:
            type: state_machine
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'state'
            supports:
                - App\Entity\Contract\SubscriptionPackage
            initial_marking: New
            places:
                - New
                - Waiting for plots
                - Add stations
                - Add devices
                - In progress
                - Done
            transitions:
                waiting_for_plot_state:
                    from: [New, In progress]
                    to: Waiting for plots
                add_stations_state:
                    from: New
                    to: Add stations
                    metadata:
                        packages_add_stations_state: ['Weather stations']
                add_devices_state:
                    from: New
                    to: Add devices
                    metadata:
                        packages_add_devices_state: ['TF connect']
                in_progress_state:
                    from: [Waiting for plots, Add stations, Add devices]
                    to: In progress
                done_state:
                    from: [Add devices, Add stations, Waiting for plots, In progress]
                    to: Done
                    metadata:
                        done_packages_when_contact_exp: [
                                            'Satellite imagery',
                                            'Index VRA-N',
                                            'Weather data' ,
                                            'Weather stations' ,
                                            'TF connect'
                                            ]
