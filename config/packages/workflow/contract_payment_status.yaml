framework:
    workflows:
        contract_payment_status:
            type: 'workflow'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'status'
            supports:
                - App\Entity\Contract\Price
            initial_marking: None
            places:
                - None
                - Partial
                - Paid
            transitions:
                add_partial_payment:
                    from: None
                    to: Partial
                complete_payment:
                    from: Partial
                    to: Paid
                add_full_payment:
                    from: None
                    to: Paid
