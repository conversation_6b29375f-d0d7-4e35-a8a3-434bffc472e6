framework:
    secret: '%env(APP_SECRET)%'
    #default_locale: en
    csrf_protection: false
    #http_method_override: true
    ide: 'vscode://file/%%f:%%l&/var/www/html/app/>/Users/<USER>/projects/geoscan-contracts-api/'
    # ide: 'phpstorm://open?file=%%f&line=%%l&/var/www/html/app/>[REPLACE WITH ABS PATH]/geoscan-contracts-api/'
    trusted_headers: ['x-forwarded-for', 'x-forwarded-host', 'x-forwarded-proto', 'x-forwarded-port', 'x-forwarded-prefix']
    trusted_proxies: '************/24,REMOTE_ADDR'

    # Enables session support. Note that the session will ONLY be started if you read or write from it.
    # Remove or comment this section to explicitly disable session support.
    session:
        handler_id: ~
        cookie_secure: auto
        cookie_samesite: lax
        storage_factory_id: session.storage.factory.native

    #esi: true
    #fragments: true
    php_errors:
        log: true

    validation: { enable_annotations: true }
    messenger:
        reset_on_message: true
