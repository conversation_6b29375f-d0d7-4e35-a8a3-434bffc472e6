monolog:
    handlers:
        main:
            type: fingers_crossed
            action_level: error
            handler: nested
            excluded_http_codes:
                # regex: exclude all 404 errors from the logs
                - ^/
            channels: ["!event"]
        nested:
            type: stream
            path: 'php://stderr'
            level: debug
        console:
            type:   console
            process_psr_3_messages: false
            channels: ['!event', '!doctrine']