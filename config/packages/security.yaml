security:
    enable_authenticator_manager: true

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        auth:
            pattern: ^/api/auth/access_token
        api_doc:
            pattern: ^/api/doc
        api:
            pattern: ^/api
            stateless: true
            custom_authenticators:
                - App\Security\TokenAuthenticator
        system:
            pattern: ^/system
            stateless: true
            custom_authenticators:
                - App\Security\SystemTokenAuthenticator        

            # activate different ways to authenticate

            # http_basic: true
            # https://symfony.com/doc/current/security.html#a-configuring-how-your-users-will-authenticate

            # form_login: true
            # https://symfony.com/doc/current/security/form_login_setup.html


    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
#         - { path: ^/api, roles: ROLE_SUPER_ADMIN }
