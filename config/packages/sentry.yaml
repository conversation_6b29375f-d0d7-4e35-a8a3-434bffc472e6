sentry:
    dsn: '%env(resolve:SENTRY_CMS_DSN)%'
    register_error_listener: false # Disables the ErrorListener to avoid duplicated log in sentry
    register_error_handler: false # Disables the ErrorListener, ExceptionListener and FatalErrorListener integrations of the base PHP SDK
    options:
        traces_sample_rate: 1
        profiles_sample_rate: 1
monolog:
    handlers:
        sentry:
            type: sentry
            level: !php/const Monolog\Logger::ERROR
            hub_id: Sentry\State\HubInterface
