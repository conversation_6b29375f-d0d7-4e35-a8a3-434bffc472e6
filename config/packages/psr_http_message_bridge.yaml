services:
    _defaults:
        autowire: true
        autoconfigure: true

    Symfony\Bridge\PsrHttpMessage\HttpFoundationFactoryInterface:
        '@Symfony\Bridge\PsrHttpMessage\Factory\HttpFoundationFactory'

#    Symfony\Bridge\PsrHttpMessage\HttpMessageFactoryInterface:
#        '@Symfony\Bridge\PsrHttpMessage\Factory\PsrHttpFactory'

    Symfony\Bridge\PsrHttpMessage\Factory\HttpFoundationFactory: null
    Symfony\Bridge\PsrHttpMessage\Factory\PsrHttpFactory: null

    # Uncomment the following line to allow controllers to receive a
    # PSR-7 server request object instead of an HttpFoundation request
    #Symfony\Bridge\PsrHttpMessage\ArgumentValueResolver\PsrServerRequestResolver: null

    # Uncomment the following line to allow controllers to return a
    # PSR-7 response object instead of an HttpFoundation response
    #Symfony\Bridge\PsrHttpMessage\EventListener\PsrResponseListener: null
