# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    locale: "en"

    geoscan_api_base_url: "%env(resolve:GEOSCAN_API_BASE_URL)%"
    geoscan_api_client_id: "%env(resolve:GEOSCAN_API_CLIENT_ID)%"
    geoscan_api_client_secret: "%env(resolve:GEOSCAN_API_CLIENT_SECRET)%"
    analysis_directory: "%env(resolve:ANALYSIS_DIRECTORY)%"
    keycloak_base_url: "%env(resolve:KEYCLOAK_BASE_URL)%"
    keycloak_alg: "%env(resolve:KEYCLOAK_ALG)%"
    keycloak_certs_url: "%env(resolve:KEYCLOAK_CERTS_URI)%"
    tf_api_base_url: "%env(resolve:TF_API_BASE_URL)%"
    app.UUID_V4: "[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}"
services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            $userAuthenticator: "@security.authenticator.manager.auth"

    Redis:
        # you can also use \RedisArray, \RedisCluster or \Predis\Client classes
        class: Redis
        calls:
            - connect:
                  - "%env(REDIS_HOST)%"
                  - "%env(int:REDIS_PORT)%"
    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: "../src/*"
        exclude: "../src/{DependencyInjection,Entity,Migrations,Tests,Kernel.php}"

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\Controller\:
        resource: "../src/Controller"
        tags: ["controller.service_arguments"]

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
    before_request_listener:
        class: App\Listener\BeforeRequestListener
        tags:
            - name: kernel.event_listener
              event: kernel.request
              method: onKernelRequest

    # Exception listener for detailed error reporting in development
    App\Listener\ExceptionListener:
        arguments:
            - "@parameter_bag"
            - "@logger"
        tags:
            - name: kernel.event_listener
              event: kernel.exception
              method: onKernelException
              priority: 10
    pre_persist_listener:
        class: App\Listener\DoctrinePrePersistListener
        tags:
            - name: doctrine.event_listener
              event: prePersist

    # Normalizers
    error_normalizer:
        class: App\Serializer\ErrorNormalizer
        arguments:
            - "@parameter_bag"
        tags:
            - { name: serializer.normalizer.problem }

    # workflow guards:
    App\Listener\WorkflowGuard\ActivateSubscriptionContractGuard:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowGuard\ActivateSubscriptionPackageGuard:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowGuard\WaitingForPlotStateSubscriptionPackageGuard:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowGuard\DoneSubscriptionPackageGuard:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowGuard\Analysis\LabElementsGroupForRecommendationGuard:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowGuard\SubscriptionPackageField\SubscriptionPackageFieldInProgressGuard:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowGuard\SubscriptionPackageField\SubscriptionPackageFieldActivateGuard:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowGuard\SubscriptionPackageField\SubscriptionPackageFieldGriddedGuard:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowGuard\SubscriptionPackageField\SubscriptionPackageFieldCellsSelectedGuard:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowGuard\SubscriptionPackageField\SubscriptionPackageFieldSampledGuard:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowGuard\SubscriptionPackageField\SubscriptionPackageFieldForSamplingGuard:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowGuard\Analysis\PackageGridPointSamplingGuard:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowGuard\Analysis\PackageGridPointPendingGuard:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowGuard\Analysis\PackageGridPointForSamplingGuard:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowGuard\Analysis\PackageGridPointNotSampledGuard:
        tags:
            - { name: kernel.event_subscriber }

    # workflow entered:
    App\Listener\WorkflowEntered\ActivateSubscriptionPackageListener:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowEntered\DeactivateSubscriptionPackageListener:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowEntered\WaitingForPlotStateSubscriptionPackageListener:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowEntered\SubsPackExpiredAndSubsFieldPackDeliverListener:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowEntered\Analysis\PackageGridPointReceivedInLabListener:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowEntered\ExpiredSubscriptionPackageFieldsListener:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowEntered\SubscriptionPackageDoneListener:
        tags:
            - { name: kernel.event_subscriber }

    # workflow completed:
    App\Listener\WorkflowCompleted\Analysis\PackageGridPointPendingListener:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowCompleted\Analysis\PackageGridPointForSamplingAndNotSampledListener:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowCompleted\SubscriptionPackageFields\SubsPackFieldForSampling:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowEnter\SubscriptionPackageFields\SubsPackFieldSamplingListener:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowCompleted\Analysis\PackageGridPointReceivedInLabListener:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowCompleted\Analysis\PackageGridPointSampledListener:
        tags:
            - { name: kernel.event_subscriber }
    App\Listener\WorkflowCompleted\Analysis\PackageGridPointNotSampledListener:
        tags:
            - { name: kernel.event_subscriber }

    App\Service\Analysis\LabAnalysisUploadService:
        arguments:
            $targetDirectory: "%analysis_directory%"
            $templateUploadElements: '/(?:pH|\bC\b|\bN\b|\bB\b|Cu|Fe|Mn|Zn|\bP\b|K\+?|\bCa\b|\bMg\b|Na\+?|S|NO3|NH4|TMN|Organic Matter|CEC|Base Saturation|K\+|Ca2\+|Mg2\+|H\+|Dumas|Total Nitrogen|Bulk Density|Organic Carbon|Inorganic Carbon|Active Carbonates|EC|Active Carbon|Molibdenum|Humus|Kal|IN\(N indicator\)|Pal)/m'

    App\Security\OAuthClient\Provider\KeycloakMachineToMachine:
        arguments:
            - {
                  authServerUrl: "%env(resolve:KEYCLOAK_BASE_URL)%",
                  realm: "%env(resolve:KEYCLOAK_REALM)%",
                  clientId: "%env(resolve:KEYCLOAK_M2M_CLIENT_ID)%",
                  clientSecret: "%env(resolve:KEYCLOAK_M2M_CLIENT_SECRET)%",
              }
    App\Security\OAuthClient\Provider\Keycloak:
        arguments:
            - {
                  authServerUrl: "%env(resolve:KEYCLOAK_BASE_URL)%",
                  realm: "%env(resolve:KEYCLOAK_REALM)%",
                  clientId: "%env(resolve:KEYCLOAK_CLIENT_ID)%",
                  clientSecret: "%env(resolve:KEYCLOAK_CLIENT_SECRET)%",
                  redirectUri: "%env(resolve:KEYCLOAK_REDIRECT_URI)%",
              }
    App\Security\OAuthClient\Provider\KeycloakAdminProvider:
        arguments:
            - {
                  authServerUrl: "%env(resolve:KEYCLOAK_BASE_URL)%",
                  realm: "%env(resolve:KEYCLOAK_REALM)%",
                  clientId: "%env(resolve:KEYCLOAK_ADMIN_CLIENT_ID)%",
                  clientSecret: "%env(resolve:KEYCLOAK_ADMIN_CLIENT_SECRET)%",
              }

imports:
    - { resource: services/recommendation.yaml }
    - { resource: services/gs_main_db.yaml }
