image: atlassian/default-image:3

options:
    docker: true
    size: 2x

definitions:
    services:
        docker:
            memory: 7168
    steps:
        - step: &build
              name: "Build"
              runs-on:
                  - "self.hosted"
                  - "linux"
                  - "cms"
                  - "build"
              services:
                  - docker
              script:
                  - echo "Building the image and push to docker hub registry"
                  - docker login -u $DOCKERHUB_USER -p $DOCKERHUB_PASSWORD
                  - docker-compose -f docker-compose.cd.yml build --no-cache --force-rm
                  - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                  - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                  - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                  - docker tag ${BITBUCKET_REPO_SLUG}:${BITBUCKET_COMMIT} ${DOCKERHUB_REPO_NAME}:${IMAGE_TAG_NAME}
                  - docker tag ${BITBUCKET_REPO_SLUG}-nginx:${BITBUCKET_COMMIT} ${DOCKERHUB_REPO_NAME}-nginx:${IMAGE_TAG_NAME}
                  - docker push ${DOCKERHUB_REPO_NAME}:${IMAGE_TAG_NAME}
                  - docker push ${DOCKERHUB_REPO_NAME}-nginx:${IMAGE_TAG_NAME}
                  - docker logout
                  - echo ${DOCKERHUB_REPO_NAME}:${IMAGE_TAG_NAME} "is pushed to Docker Hub"
        - step: &deploy
              name: "Deploy"
              runs-on:
                  - "self.hosted"
                  - "linux"
                  - "cms"
                  - "build"
              image: willhallonline/ansible:2.15-alpine-3.16
              services:
                  - docker
              script:
                  - export ANSIBLE_HOST_KEY_CHECKING="False"
                  - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                  - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                  - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                  - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                  - export CONTAINER_NAME=$CONTAINER_NAME
                  - export EXTERNAL_PORT=$EXTERNAL_PORT
                  - export KEY_DATA_VOLUME_NAME=$KEY_DATA_VOLUME_NAME
                  - export TF_API_BASE_URL=$TF_API_BASE_URL
                  - export KEYCLOAK_ADMIN_CLIENT_ID=$KEYCLOAK_ADMIN_CLIENT_ID
                  - export KEYCLOAK_ADMIN_CLIENT_SECRET=$KEYCLOAK_ADMIN_CLIENT_SECRET
                  - export KEYCLOAK_CLIENT_ID=$KEYCLOAK_CLIENT_ID
                  - export KEYCLOAK_CLIENT_SECRET=$KEYCLOAK_CLIENT_SECRET
                  - export KEYCLOAK_M2M_CLIENT_ID=$KEYCLOAK_M2M_CLIENT_ID
                  - export KEYCLOAK_M2M_CLIENT_SECRET=$KEYCLOAK_M2M_CLIENT_SECRET
                  - export TF_MAIN_DB_HOST=$TF_MAIN_DB_HOST
                  - export TF_MAIN_DB_PORT=$TF_MAIN_DB_PORT
                  - export TF_MAIN_DB_NAME=$TF_MAIN_DB_NAME
                  - export TF_MAIN_DB_USER=$TF_MAIN_DB_USER
                  - export TF_MAIN_DB_PASS=$TF_MAIN_DB_PASS
                  - ansible-galaxy collection install community.docker
                  - ansible-playbook -i .ansible/hosts ansible-deploy.yml -l staging
pipelines:
    branches:
        "master":
            - step: *build
            - step:
                  <<: *deploy
                  deployment: staging
    custom:
        "Keycloak":
            - step: *build
            - step:
                  <<: *deploy
                  deployment: keycloak
        "Agrimi deployment":
            - parallel:
                  - step: *build
                  - step:
                        name: "Deploy to kubernetes"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: atlassian/pipelines-kubectl
                        deployment: agrimi
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=1
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export DATABASE_URL=$DATABASE_URL
                            - export GEOSCAN_DATABASE_URL=$GEOSCAN_DATABASE_URL
                            - export GEOSCAN_API_BASE_URL=$GEOSCAN_API_BASE_URL
                            - export COUCH_DB_PASS=$COUCH_DB_PASS
                            - export GEOSCAN_APP_BASE_URL=$GEOSCAN_APP_BASE_URL
                            - export APP_ENV=$APP_ENV
                            - export SUSI_MAIN_DB_HOST=$SUSI_MAIN_DB_HOST
                            - export SUSI_MAIN_DB_PORT=$SUSI_MAIN_DB_PORT
                            - export SUSI_MAIN_DB_USER=$SUSI_MAIN_DB_USER
                            - export SUSI_MAIN_DB_PASS=$SUSI_MAIN_DB_PASS
                            - export EXTERNAL_PORT=$EXTERNAL_PORT
                            - export ANALYSIS_DIRECTORY=$ANALYSIS_DIRECTORY
                            - export NFS_DIR_ANALYSES=$NFS_DIR_ANALYSES
                            - export NFS_DIR_LOG=$NFS_DIR_LOG
                            - export MAILER_DSN=$MAILER_DSN
                            - export SENTRY_CMS_DSN=$SENTRY_CMS_DSN
                            - export CONFIGMAP=$CONFIGMAP
                            - export CONFIGMAPFILES=$CONFIGMAPFILES
                            - export REDIS_HOST=$REDIS_HOST
                            - export REDIS_PORT=$REDIS_PORT
                            - export NFS_SERVER=$NFS_SERVER
                            - export NFS_DIR_LOG=$NFS_DIR_LOG
                            - export NFS_DIR_ANALYSES=$NFS_DIR_ANALYSES
                            - export KEYCLOAK_BASE_URL=$KEYCLOAK_BASE_URL
                            - export KEYCLOAK_ALG=$KEYCLOAK_ALG
                            - export KEYCLOAK_REALM=$KEYCLOAK_REALM
                            - export KEYCLOAK_CERTS_URI=$KEYCLOAK_CERTS_URI
                            - export KUBE_TOKEN=$KUBE_TOKEN
                            - export KUBE_CA=$KUBE_CA
                            - export TF_API_BASE_URL=$TF_API_BASE_URL
                            - export KEYCLOAK_ADMIN_CLIENT_ID=$KEYCLOAK_ADMIN_CLIENT_ID
                            - export KEYCLOAK_ADMIN_CLIENT_SECRET=$KEYCLOAK_ADMIN_CLIENT_SECRET
                            - export KEYCLOAK_CLIENT_ID=$KEYCLOAK_CLIENT_ID
                            - export KEYCLOAK_CLIENT_SECRET=$KEYCLOAK_CLIENT_SECRET
                            - export KEYCLOAK_M2M_CLIENT_ID=$KEYCLOAK_M2M_CLIENT_ID
                            - export KEYCLOAK_M2M_CLIENT_SECRET=$KEYCLOAK_M2M_CLIENT_SECRET
                            - echo $KUBE_CONFIG | base64 -d > kubeconfig.yml
                            - sed -i "s|REPLACE_CONTAINER_NAME|${CONTAINER_NAME}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DATABASE_URL|${DATABASE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_GEOSCAN_DATABASE_URL|${GEOSCAN_DATABASE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_GEOSCAN_API_BASE_URL|${GEOSCAN_API_BASE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_GEOSCAN_APP_BASE_URL|${GEOSCAN_APP_BASE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_APP_ENV|${APP_ENV}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_SUSI_MAIN_DB_HOST|${SUSI_MAIN_DB_HOST}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_SUSI_MAIN_DB_PORT|${SUSI_MAIN_DB_PORT}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_SUSI_MAIN_DB_USER|${SUSI_MAIN_DB_USER}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_SUSI_MAIN_DB_PASS|${SUSI_MAIN_DB_PASS}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_EXTERNAL_PORT|${EXTERNAL_PORT}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_ANALYSIS_DIRECTORY|${ANALYSIS_DIRECTORY}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MAILER_DSN|${MAILER_DSN}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_SENTRY_CMS_DSN|${SENTRY_CMS_DSN}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_BASE_URL|${KEYCLOAK_BASE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_ALG|${KEYCLOAK_ALG}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_REALM|${KEYCLOAK_REALM}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_CERTS_URI|${KEYCLOAK_CERTS_URI}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_REDIS_HOST|${REDIS_HOST}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_REDIS_PORT|${REDIS_PORT}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_TF_API_BASE_URL|${TF_API_BASE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_ADMIN_CLIENT_ID|${KEYCLOAK_ADMIN_CLIENT_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_ADMIN_CLIENT_SECRET|${KEYCLOAK_ADMIN_CLIENT_SECRET}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_CLIENT_ID|${KEYCLOAK_CLIENT_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_CLIENT_SECRET|${KEYCLOAK_CLIENT_SECRET}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_M2M_CLIENT_ID|${KEYCLOAK_M2M_CLIENT_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_M2M_CLIENT_SECRET|${KEYCLOAK_M2M_CLIENT_SECRET}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_TF_MAIN_DB_HOST|${TF_MAIN_DB_HOST}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_TF_MAIN_DB_PORT|${TF_MAIN_DB_PORT}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_TF_MAIN_DB_NAME|${TF_MAIN_DB_NAME}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_TF_MAIN_DB_USER|${TF_MAIN_DB_USER}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_TF_MAIN_DB_PASS|${TF_MAIN_DB_PASS}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_COUCH_DB_PASS|${COUCH_DB_PASS}|g" .kube/configmap-env-file
                            - echo $KUBE_TOKEN | base64 -d > ./kube_token
                            - echo $KUBE_CA | base64 -d > ./kube_ca
                            - kubectl config set-cluster $KUBE_CLUSTER --server=$KUBE_SERVER --certificate-authority="$(pwd)/kube_ca"
                            - kubectl config set-credentials $KUBE_SA --token="$(cat ./kube_token)"
                            - kubectl config set-context $KUBE_NAMESPACE --cluster=$KUBE_CLUSTER --user=$KUBE_SA
                            - kubectl config use-context $KUBE_NAMESPACE
                            - kubectl create configmap ${CONFIGMAP} --from-env-file=.kube/configmap-env-file --dry-run=client -o yaml | kubectl -n $KUBE_NAMESPACE apply -f -
                            #Create SSL secret
                            - export AGRIMI_SSL_CRT=$AGRIMI_SSL_CRT
                            - export AGRIMI_SSL_KEY=$AGRIMI_SSL_KEY
                            - export KUBE_INGRESS_HOSTNAME=$KUBE_INGRESS_HOSTNAME
                            - echo $AGRIMI_SSL_CRT | base64 -d > .kube/${KUBE_INGRESS_HOSTNAME}.crt
                            - echo $AGRIMI_SSL_KEY | base64 -d > .kube/${KUBE_INGRESS_HOSTNAME}.key
                            - kubectl create secret tls ${KUBE_INGRESS_HOSTNAME} --cert=.kube/${KUBE_INGRESS_HOSTNAME}.crt --key=.kube/${KUBE_INGRESS_HOSTNAME}.key --dry-run=client -o yaml | kubectl -n $KUBE_NAMESPACE apply -f -
                            #End SSL
                            - sed -i "s|REPLACE_REDIS_HOST|${REDIS_HOST}|g" .kube/php.ini
                            - sed -i "s|REPLACE_REDIS_PORT|${REDIS_PORT}|g" .kube/php.ini
                            - kubectl create configmap ${CONFIGMAPFILES} --from-file=.kube/php.ini --from-file=.env.example --dry-run=client -o yaml | kubectl -n $KUBE_NAMESPACE apply -f -
                            - sed -i "s|REPLACE_CONFIG_MAP_FILES|${CONFIGMAPFILES}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_CONFIG_MAP|${CONFIGMAP}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_REPO|${DOCKERHUB_REPO_NAME}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_TAG|${IMAGE_TAG_NAME}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_SERVER|${NFS_SERVER}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_ANALYSES|${NFS_DIR_ANALYSES}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_LOG|${NFS_DIR_LOG}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_KUBE_NAMESPACE|${KUBE_NAMESPACE}|g" .kube/*.yaml
                            - sed -i "s|REPLACE_KUBE_INGRESS_HOSTNAME|${KUBE_INGRESS_HOSTNAME}|g" .kube/*.yaml
                            - sed -i "s|REPLACE_KUBE_REPLICAS|${KUBE_REPLICAS}|g" .kube/*.yaml
                            - sed -i "s|REPLACE_KUBE_SCHEDULER_REPLICAS|${KUBE_SCHEDULER_REPLICAS}|g" .kube/*.yaml
                            - kubectl apply -f .kube/deployment-geoscan-cms-api-prod.yaml
                            - kubectl rollout restart deployment/geoscan-cms-api-prod -n $KUBE_NAMESPACE
                            - kubectl apply -f .kube/deployment-geoscan-cms-api-prod-scheduler.yaml
                            - kubectl rollout restart deployment/geoscan-cms-api-prod-scheduler -n $KUBE_NAMESPACE
                            - kubectl apply -f .kube/service-geoscan-cms-api-prod.yaml
                            - kubectl apply -f .kube/deployment-geoscan-cms-api-prod-nginx.yaml
                            - kubectl apply -f .kube/service-geoscan-cms-api-prod-nginx.yaml
                            - kubectl rollout restart deployment/geoscan-cms-api-prod-nginx -n $KUBE_NAMESPACE
                            - kubectl apply -f .kube/ingress-geoscan-cms-api-prod.yaml

        "Agrimi A1 deployment":
            - parallel:
                  - step: *build
                  - step:
                        name: "Deploy to kubernetes - A1"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "a1"
                            - "agrimi"
                        image: atlassian/pipelines-kubectl
                        deployment: a1
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=1
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export DATABASE_URL=$DATABASE_URL
                            - export GEOSCAN_DATABASE_URL=$GEOSCAN_DATABASE_URL
                            - export GEOSCAN_API_BASE_URL=$GEOSCAN_API_BASE_URL
                            - export COUCH_DB_PASS=$COUCH_DB_PASS
                            - export GEOSCAN_APP_BASE_URL=$GEOSCAN_APP_BASE_URL
                            - export APP_ENV=$APP_ENV
                            - export SUSI_MAIN_DB_HOST=$SUSI_MAIN_DB_HOST
                            - export SUSI_MAIN_DB_PORT=$SUSI_MAIN_DB_PORT
                            - export SUSI_MAIN_DB_USER=$SUSI_MAIN_DB_USER
                            - export SUSI_MAIN_DB_PASS=$SUSI_MAIN_DB_PASS
                            - export EXTERNAL_PORT=$EXTERNAL_PORT
                            - export ANALYSIS_DIRECTORY=$ANALYSIS_DIRECTORY
                            - export NFS_DIR_ANALYSES=$NFS_DIR_ANALYSES
                            - export NFS_DIR_LOG=$NFS_DIR_LOG
                            - export MAILER_DSN=$MAILER_DSN
                            - export SENTRY_CMS_DSN=$SENTRY_CMS_DSN
                            - export CONFIGMAP=$CONFIGMAP
                            - export CONFIGMAPFILES=$CONFIGMAPFILES
                            - export REDIS_HOST=$REDIS_HOST
                            - export REDIS_PORT=$REDIS_PORT
                            - export NFS_SERVER=$NFS_SERVER
                            - export NFS_DIR_LOG=$NFS_DIR_LOG
                            - export NFS_DIR_ANALYSES=$NFS_DIR_ANALYSES
                            - export KEYCLOAK_BASE_URL=$KEYCLOAK_BASE_URL
                            - export KEYCLOAK_ALG=$KEYCLOAK_ALG
                            - export KEYCLOAK_REALM=$KEYCLOAK_REALM
                            - export KEYCLOAK_CERTS_URI=$KEYCLOAK_CERTS_URI
                            - export KUBE_TOKEN=$KUBE_TOKEN
                            - export KUBE_CA=$KUBE_CA
                            - export TF_API_BASE_URL=$TF_API_BASE_URL
                            - export KEYCLOAK_ADMIN_CLIENT_ID=$KEYCLOAK_ADMIN_CLIENT_ID
                            - export KEYCLOAK_ADMIN_CLIENT_SECRET=$KEYCLOAK_ADMIN_CLIENT_SECRET
                            - export KEYCLOAK_CLIENT_ID=$KEYCLOAK_CLIENT_ID
                            - export KEYCLOAK_CLIENT_SECRET=$KEYCLOAK_CLIENT_SECRET
                            - export KEYCLOAK_M2M_CLIENT_ID=$KEYCLOAK_M2M_CLIENT_ID
                            - export KEYCLOAK_M2M_CLIENT_SECRET=$KEYCLOAK_M2M_CLIENT_SECRET
                            - echo $KUBE_CONFIG | base64 -d > kubeconfig.yml
                            - sed -i "s|REPLACE_CONTAINER_NAME|${CONTAINER_NAME}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DATABASE_URL|${DATABASE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_GEOSCAN_DATABASE_URL|${GEOSCAN_DATABASE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_GEOSCAN_API_BASE_URL|${GEOSCAN_API_BASE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_GEOSCAN_APP_BASE_URL|${GEOSCAN_APP_BASE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_APP_ENV|${APP_ENV}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_SUSI_MAIN_DB_HOST|${SUSI_MAIN_DB_HOST}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_SUSI_MAIN_DB_PORT|${SUSI_MAIN_DB_PORT}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_SUSI_MAIN_DB_USER|${SUSI_MAIN_DB_USER}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_SUSI_MAIN_DB_PASS|${SUSI_MAIN_DB_PASS}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_EXTERNAL_PORT|${EXTERNAL_PORT}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_ANALYSIS_DIRECTORY|${ANALYSIS_DIRECTORY}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MAILER_DSN|${MAILER_DSN}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_SENTRY_CMS_DSN|${SENTRY_CMS_DSN}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_BASE_URL|${KEYCLOAK_BASE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_ALG|${KEYCLOAK_ALG}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_REALM|${KEYCLOAK_REALM}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_CERTS_URI|${KEYCLOAK_CERTS_URI}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_REDIS_HOST|${REDIS_HOST}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_REDIS_PORT|${REDIS_PORT}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_TF_API_BASE_URL|${TF_API_BASE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_ADMIN_CLIENT_ID|${KEYCLOAK_ADMIN_CLIENT_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_ADMIN_CLIENT_SECRET|${KEYCLOAK_ADMIN_CLIENT_SECRET}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_CLIENT_ID|${KEYCLOAK_CLIENT_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_CLIENT_SECRET|${KEYCLOAK_CLIENT_SECRET}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_M2M_CLIENT_ID|${KEYCLOAK_M2M_CLIENT_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_M2M_CLIENT_SECRET|${KEYCLOAK_M2M_CLIENT_SECRET}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_TF_MAIN_DB_HOST|${TF_MAIN_DB_HOST}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_TF_MAIN_DB_PORT|${TF_MAIN_DB_PORT}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_TF_MAIN_DB_NAME|${TF_MAIN_DB_NAME}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_TF_MAIN_DB_USER|${TF_MAIN_DB_USER}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_TF_MAIN_DB_PASS|${TF_MAIN_DB_PASS}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_COUCH_DB_PASS|${COUCH_DB_PASS}|g" .kube/configmap-env-file
                            - echo $KUBE_TOKEN | base64 -d > ./kube_token
                            - echo $KUBE_CA | base64 -d > ./kube_ca
                            - kubectl config set-cluster $KUBE_CLUSTER --server=$KUBE_SERVER --certificate-authority="$(pwd)/kube_ca"
                            - kubectl config set-credentials $KUBE_SA --token="$(cat ./kube_token)"
                            - kubectl config set-context $KUBE_NAMESPACE --cluster=$KUBE_CLUSTER --user=$KUBE_SA
                            - kubectl config use-context $KUBE_NAMESPACE
                            - kubectl create configmap ${CONFIGMAP} --from-env-file=.kube/configmap-env-file --dry-run=client -o yaml | kubectl -n $KUBE_NAMESPACE apply -f -
                            #Create SSL secret
                            - export AGRIMI_SSL_CRT=$AGRIMI_SSL_CRT
                            - export AGRIMI_SSL_KEY=$AGRIMI_SSL_KEY
                            - export KUBE_INGRESS_HOSTNAME=$KUBE_INGRESS_HOSTNAME
                            - echo $AGRIMI_SSL_CRT | base64 -d > .kube/${KUBE_INGRESS_HOSTNAME}.crt
                            - echo $AGRIMI_SSL_KEY | base64 -d > .kube/${KUBE_INGRESS_HOSTNAME}.key
                            - kubectl create secret tls ${KUBE_INGRESS_HOSTNAME} --cert=.kube/${KUBE_INGRESS_HOSTNAME}.crt --key=.kube/${KUBE_INGRESS_HOSTNAME}.key --dry-run=client -o yaml | kubectl -n $KUBE_NAMESPACE apply -f -
                            #End SSL
                            - sed -i "s|REPLACE_REDIS_HOST|${REDIS_HOST}|g" .kube/php.ini
                            - sed -i "s|REPLACE_REDIS_PORT|${REDIS_PORT}|g" .kube/php.ini
                            - kubectl create configmap ${CONFIGMAPFILES} --from-file=.kube/php.ini --from-file=.env.example --dry-run=client -o yaml | kubectl -n $KUBE_NAMESPACE apply -f -
                            - sed -i "s|REPLACE_CONFIG_MAP_FILES|${CONFIGMAPFILES}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_CONFIG_MAP|${CONFIGMAP}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_REPO|${DOCKERHUB_REPO_NAME}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_TAG|${IMAGE_TAG_NAME}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_SERVER|${NFS_SERVER}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_ANALYSES|${NFS_DIR_ANALYSES}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_LOG|${NFS_DIR_LOG}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_KUBE_NAMESPACE|${KUBE_NAMESPACE}|g" .kube/*.yaml
                            - sed -i "s|REPLACE_KUBE_INGRESS_HOSTNAME|${KUBE_INGRESS_HOSTNAME}|g" .kube/*.yaml
                            - sed -i "s|REPLACE_KUBE_REPLICAS|${KUBE_REPLICAS}|g" .kube/*.yaml
                            - sed -i "s|REPLACE_KUBE_SCHEDULER_REPLICAS|${KUBE_SCHEDULER_REPLICAS}|g" .kube/*.yaml
                            - kubectl apply -f .kube/deployment-geoscan-cms-api-prod.yaml
                            - kubectl rollout restart deployment/geoscan-cms-api-prod -n $KUBE_NAMESPACE
                            - kubectl apply -f .kube/deployment-geoscan-cms-api-prod-scheduler.yaml
                            - kubectl rollout restart deployment/geoscan-cms-api-prod-scheduler -n $KUBE_NAMESPACE
                            - kubectl apply -f .kube/service-geoscan-cms-api-prod.yaml
                            - kubectl apply -f .kube/deployment-geoscan-cms-api-prod-nginx.yaml
                            - kubectl apply -f .kube/service-geoscan-cms-api-prod-nginx.yaml
                            - kubectl rollout restart deployment/geoscan-cms-api-prod-nginx -n $KUBE_NAMESPACE
                            - kubectl apply -f .kube/ingress-geoscan-cms-api-prod.yaml