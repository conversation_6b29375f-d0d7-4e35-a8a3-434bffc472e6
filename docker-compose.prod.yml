version: "3.7"
services:
    php:
        image: technofarm/geoscan-cms-api:${IMAGE_TAG_NAME}
        container_name: "${CONTAINER_NAME}"
        user: "1000:1000"
        volumes:
            - keydata:/var/www/html/app/config/jwt
            - app:/var/www/html/app
            - analyses:/var/www/html/app/var/analyses
            - tmp:/tmp
        environment:
            - DATABASE_URL
            - GEOSCAN_DATABASE_URL
            - GEOSCAN_API_BASE_URL
            - COUCH_DB_PASS
            - GEOSCAN_APP_BASE_URL
            - DOCKERHUB_REPO_NAME
            - CONTAINER_NAME
            - IMAGE_TAG_NAME
            - APP_ENV
            - SUSI_MAIN_DB_HOST
            - SUSI_MAIN_DB_PORT
            - SUSI_MAIN_DB_USER
            - SUSI_MAIN_DB_PASS
            - MAILER_DSN
            - ANALYSIS_DIRECTORY
            - SENTRY_CMS_DSN
            - KEYCLOAK_BASE_URL
            - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_ALG
            - K<PERSON>YCLOA<PERSON>_REALM
            - KEYCLOA<PERSON>_CERTS_URI
            - TF_API_BASE_URL
            - KEYCLOAK_ADMIN_CLIENT_ID
            - KEYCLOAK_ADMIN_CLIENT_SECRET
            - KEYCLOAK_CLIENT_ID
            - KEYCLOAK_CLIENT_SECRET
            - KEYCLOAK_M2M_CLIENT_ID
            - KEYCLOAK_M2M_CLIENT_SECRET
            - TF_MAIN_DB_HOST
            - TF_MAIN_DB_PORT
            - TF_MAIN_DB_NAME
            - TF_MAIN_DB_USER
            - TF_MAIN_DB_PASS
        networks:
            - geoscan-net
        restart: always
        healthcheck:
            test: "exit 0"

    php-scheduler:
        image: technofarm/geoscan-cms-api:${IMAGE_TAG_NAME}
        container_name: "${CONTAINER_NAME}-scheduler"
        user: "1000:1000"
        volumes:
            - keydata:/var/www/html/app/config/jwt
            - app:/var/www/html/app
            - analyses:/var/www/html/app/var/analyses
            - tmp:/tmp
        environment:
            - CONTAINER_ROLE=scheduler
            - DATABASE_URL
            - GEOSCAN_DATABASE_URL
            - GEOSCAN_API_BASE_URL
            - COUCH_DB_PASS
            - GEOSCAN_APP_BASE_URL
            - DOCKERHUB_REPO_NAME
            - CONTAINER_NAME
            - IMAGE_TAG_NAME
            - APP_ENV
            - SUSI_MAIN_DB_HOST
            - SUSI_MAIN_DB_PORT
            - SUSI_MAIN_DB_USER
            - SUSI_MAIN_DB_PASS
            - MAILER_DSN
            - ANALYSIS_DIRECTORY
            - SENTRY_CMS_DSN
            - KEYCLOAK_BASE_URL
            - KEYCLOAK_ALG
            - KEYCLOAK_REALM
            - KEYCLOAK_CERTS_URI
            - TF_API_BASE_URL
            - KEYCLOAK_ADMIN_CLIENT_ID
            - KEYCLOAK_ADMIN_CLIENT_SECRET
            - KEYCLOAK_CLIENT_ID
            - KEYCLOAK_CLIENT_SECRET
            - KEYCLOAK_M2M_CLIENT_ID
            - KEYCLOAK_M2M_CLIENT_SECRET
            - TF_MAIN_DB_HOST
            - TF_MAIN_DB_PORT
            - TF_MAIN_DB_NAME
            - TF_MAIN_DB_USER
            - TF_MAIN_DB_PASS
        networks:
            - geoscan-net
        depends_on:
            - php
        restart: always
        healthcheck:
            test: "exit 0"

    web:
        image: technofarm/geoscan-cms-api-nginx:${IMAGE_TAG_NAME}
        container_name: ${CONTAINER_NAME}-nginx
        environment:
            - CONTAINER_NAME=${CONTAINER_NAME}
        ports:
            - ${EXTERNAL_PORT:-8099}:80
        volumes:
            - app:/var/www/html/app
            - analyses:/var/www/html/app/var/analyses
        networks:
            - geoscan-net
        depends_on:
            - php
        restart: always
        healthcheck:
            test: "exit 0"

networks:
    geoscan-net:
        external: true

volumes:
    keydata:
        name: ${KEY_DATA_VOLUME_NAME}
        external: true
    app:
        name: ${CONTAINER_NAME}
    analyses:
        name: ${CONTAINER_NAME}-analyses
        external: true
    tmp:
        name: ${CONTAINER_NAME}-tmp
