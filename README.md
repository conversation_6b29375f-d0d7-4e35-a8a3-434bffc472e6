# GeoSCAN Subscriptions API

## Requirements

You should have the following installed:

-   docker >= 18
-   docker-compose >= 1.22
-   git

## Installing

```
#clone the app from the repository
git clone <url> ./
cd aplication

#configre .env file

# create network
docker network create --subnet=************/24 geoscan-net

# create keydata volume for storing JWT keys (if it's not already created)
docker volume create keydata

#build the image
docker-compose -f docker-compose.dev.yml build

#start all service containers (demonized)
docker-compose -f docker-compose.dev.yml up -d

#stop all service containers
docker-compose -f docker-compose.dev.yml stop

#restart all service containers
docker-compose -f docker-compose.dev.yml restart
```

UNIT & INTEGRATION TESTS:

-   Create a test: php vendor/bin/codecept generate:test unit Example
-   To tun a test: php vendor/bin/codecept run unit ExampleTest
-   To run the whole set of unit tests: php vendor/bin/codecept run unit

https://stackoverflow.com/questions/1889559/git-diff-to-ignore-m
