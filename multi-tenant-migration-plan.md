# Multi-Tenant Architecture Migration Plan

## Current Architecture Analysis

### Current Single-Tenant Structure
- **Users**: Each `GlobalUser` has a single `service_provider_id` foreign key
- **Organizations**: Managed through external API calls, no direct DB relationship to service providers
- **Data Isolation**: Enforced via `ServiceProviderFilter` using single `service_provider_id`
- **Business Entities**: All implement `IProvided` interface with single service provider relationship

### Current Service Provider Relationships
**gs_main database:**
- `su_users.service_provider_id` → `service_providers.id`

**geoscan_cms database:**
- `activity.service_provider_id` → `service_provider.id`
- `contract.service_provider_id` → `service_provider.id`
- `currency.service_provider_id` → `service_provider.id`
- `duration_type.service_provider_id` → `service_provider.id`
- `lab_analysis_uploads.service_provider_id` → `service_provider.id`
- `number.service_provider_id` → `service_provider.id`
- `package.service_provider_id` → `service_provider.id`
- `price.service_provider_id` → `service_provider.id`
- `service.service_provider_id` → `service_provider.id`
- `service_statuses.service_provider_id` → `service_provider.id`
- `subscription_package.service_provider_id` → `service_provider.id`

## New Multi-Tenant Architecture Design

### 1. User Multi-Tenancy
**New Junction Table: `user_service_providers`**
```sql
CREATE TABLE user_service_providers (
    id SERIAL PRIMARY KEY,
    user_id INT NOT NULL,
    service_provider_id INT NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_usp_user FOREIGN KEY (user_id) REFERENCES su_users(id) ON DELETE CASCADE,
    CONSTRAINT fk_usp_service_provider FOREIGN KEY (service_provider_id) REFERENCES service_providers(id) ON DELETE CASCADE,
    CONSTRAINT unique_user_service_provider UNIQUE (user_id, service_provider_id)
);
```

### 2. Organization Multi-Tenancy
**New Junction Table: `organization_service_providers`**
```sql
CREATE TABLE organization_service_providers (
    id SERIAL PRIMARY KEY,
    organization_id INT NOT NULL,
    service_provider_id INT NOT NULL,
    relationship_type VARCHAR(50) DEFAULT 'client', -- client, partner, vendor
    is_active BOOLEAN DEFAULT true,
    contract_start_date DATE,
    contract_end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_org_service_provider UNIQUE (organization_id, service_provider_id)
);
```

### 3. Service Provider Context Management
**New Table: `user_sessions`**
```sql
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INT NOT NULL,
    active_service_provider_id INT NOT NULL,
    session_token VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    CONSTRAINT fk_us_user FOREIGN KEY (user_id) REFERENCES su_users(id) ON DELETE CASCADE,
    CONSTRAINT fk_us_service_provider FOREIGN KEY (active_service_provider_id) REFERENCES service_providers(id) ON DELETE CASCADE
);
```

### 4. Enhanced Business Entity Relationships
**Option A: Keep existing single service_provider_id (Recommended for backward compatibility)**
- Maintain current structure for primary ownership
- Add context-aware filtering in application layer
- Use junction tables for cross-provider access

**Option B: Convert to many-to-many for all entities**
- Create junction tables for each entity type
- More complex but fully flexible

## Migration Strategy

### Phase 1: Database Schema Updates
1. Create new junction tables
2. Add backward compatibility columns
3. Migrate existing data to junction tables
4. Add indexes and constraints

### Phase 2: Entity Model Updates
1. Update `IProvided` interface for multi-tenancy
2. Create new junction entities
3. Update existing entities with new relationships
4. Maintain backward compatibility methods

### Phase 3: Authentication & Authorization
1. Implement service provider context switching
2. Update `ServiceProviderFilter` for multi-tenant access
3. Add session management for active service provider
4. Update security configuration

### Phase 4: Business Logic Updates
1. Update services to handle service provider context
2. Modify repositories for multi-tenant queries
3. Update API controllers for context switching
4. Add organization-service provider management

### Phase 5: Testing & Validation
1. Create comprehensive test suite
2. Validate data isolation
3. Test context switching functionality
4. Ensure backward compatibility

## Backward Compatibility Strategy

### Data Migration
- Existing single relationships → Default entries in junction tables
- `is_default = true` for migrated relationships
- Preserve existing `service_provider_id` columns initially

### API Compatibility
- Maintain existing endpoints behavior
- Add new endpoints for multi-tenant operations
- Gradual deprecation of single-tenant methods

### Configuration
- Feature flag for multi-tenant mode
- Fallback to single-tenant behavior when disabled
- Per-service-provider configuration options