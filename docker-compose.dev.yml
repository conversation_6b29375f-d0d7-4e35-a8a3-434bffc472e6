version: "3.7"

services:
    php:
        env_file:
            - .env
        build:
            context: .
            dockerfile: .docker/Dockerfile
            args:
                - APP_ENV=dev
        image: ${CONTAINER_NAME}
        container_name: ${CONTAINER_NAME}
        user: "root:root"
        volumes:
            - keydata:/var/www/html/app/config/jwt
            - .:/var/www/html/app
            - analyses:/var/www/html/app/var/analyses
            - tmp:/tmp
        networks:
            - geoscan-net
        restart: always
        healthcheck:
            test: "exit 0"
    web:
        image: ${CONTAINER_NAME}-nginx
        build:
          context: .
          dockerfile: ./.docker/nginx/Dockerfile
        container_name: ${CONTAINER_NAME}-nginx
        environment:
            - CONTAINER_NAME=${CONTAINER_NAME}
        ports:
            - ${EXTERNAL_PORT:-8099}:80
        volumes:
            - .:/var/www/html/app
            - analyses:/var/www/html/app/var/analyses
        networks:
            - geoscan-net
        depends_on:
            - php
        restart: always
        healthcheck:
            test: "exit 0"
    redis:
        image: 'bitnami/redis:latest'
        container_name: ${CONTAINER_NAME}-redis
        platform: linux/amd64
        ports:
            - '6379:6379'
        environment:
            - ALLOW_EMPTY_PASSWORD=yes
        restart: always
        networks:
            - geoscan-net        
networks:
    geoscan-net:
        external: true

volumes:
    keydata:
        name: ${KEY_DATA_VOLUME_NAME}
        external: true
    app:
        name: ${CONTAINER_NAME}
    analyses:
        name: ${CONTAINER_NAME}-analyses
        external: true
    tmp:
        name: ${CONTAINER_NAME}-tmp