apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: REPLACE_KUBE_INGRESS_HOSTNAME
  namespace: REPLACE_KUBE_NAMESPACE
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/add-base-url: "true"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "Host              $http_host";
      more_set_headers "X-Forwarded-Proto $scheme";
      more_set_headers "X-Forwarded-For   $proxy_add_x_forwarded_for";
      more_set_headers "X-Forwarded-Host  $host";
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  tls:
    - hosts:
        - REPLACE_KUBE_INGRESS_HOSTNAME
      secretName: REPLACE_KUBE_INGRESS_HOSTNAME
  rules:
    - host: REPLA<PERSON>_KUBE_INGRESS_HOSTNAME
      http:
        paths:
          - path: /(|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: geoscan-cms-api-prod-nginx
                port:
                  number: 80
