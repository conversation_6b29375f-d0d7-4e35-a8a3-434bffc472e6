apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: geoscan-cms-api-prod-nginx
  name: geoscan-cms-api-prod-nginx
  namespace: REPLACE_KUBE_NAMESPACE
spec:
  replicas: REPLACE_KUBE_REPLICAS
  selector:
    matchLabels:
      app: geoscan-cms-api-prod-nginx
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: geoscan-cms-api-prod-nginx
    spec:
      serviceAccount: bitbucket
      containers:
      - image: REPLACE_REPO-nginx:REPLACE_TAG
        name: geoscan-cms-api-prod-nginx
        livenessProbe:
          httpGet:
            path: /healthcheck.html
            port: 80
            httpHeaders:
            - name: X-Health-Check
              value: Ok
          initialDelaySeconds: 3
          periodSeconds: 3
        readinessProbe:
          httpGet:
            path: /healthcheck.html
            port: 80
            httpHeaders:
            - name: X-Health-Check
              value: Ok
          initialDelaySeconds: 1
          periodSeconds: 5
        envFrom:
        - configMapRef:
            name: REPLACE_CONFIG_MAP
        imagePullPolicy: "Always"
        volumeMounts:
        - name: analyses
          mountPath: /var/www/html/app/var/analyses
        - name: log
          mountPath: /var/www/html/app/var/log
        - name: envfiles
          mountPath: /var/www/html/app/.env
          subPath: .env.example
          readOnly: false
        - name: envfiles
          mountPath: /usr/local/etc/php/php.ini
          subPath: php.ini
          readOnly: false 
      volumes:
      - name: analyses
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_ANALYSES
      - name: log
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_LOG
      - name: envfiles
        configMap:
          name: REPLACE_CONFIG_MAP_FILES
      imagePullSecrets:
      - name: docker-technofarm