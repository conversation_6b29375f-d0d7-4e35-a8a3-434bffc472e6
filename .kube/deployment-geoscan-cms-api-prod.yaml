apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: geoscan-cms-api-prod
  name: geoscan-cms-api-prod
  namespace: REPLACE_KUBE_NAMESPACE
spec:
  replicas: REPLACE_KUBE_REPLICAS
  selector:
    matchLabels:
      app: geoscan-cms-api-prod
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: geoscan-cms-api-prod
    spec:
      serviceAccount: bitbucket
      containers:
      - image: REPLACE_REPO:REPLACE_TAG
        name: geoscan-cms-api-prod
        # readinessProbe:
        #     exec:
        #         command:
        #             - php-fpm-healthcheck # a simple ping since this means it's ready to handle traffic
        #     initialDelaySeconds: 1
        #     periodSeconds: 5
        # livenessProbe:
        #     exec:
        #         command:
        #             - php-fpm-healthcheck
        #             - --listen-queue=10 # fails if there are more than 10 processes waiting in the fpm queue
        #     initialDelaySeconds: 0
        #     periodSeconds: 10
        envFrom:
        - configMapRef:
            name: REPLACE_CONFIG_MAP
        imagePullPolicy: "Always"
        volumeMounts:
        - name: analyses
          mountPath: /var/www/html/app/var/analyses
        - name: log
          mountPath: /var/www/html/app/var/log
        - name: envfiles
          mountPath: /var/www/html/app/.env
          subPath: .env.example
          readOnly: false
        - name: envfiles
          mountPath: /usr/local/etc/php/php.ini
          subPath: php.ini
          readOnly: false 
      volumes:
      - name: analyses
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_ANALYSES
      - name: log
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_LOG
      - name: envfiles
        configMap:
          name: REPLACE_CONFIG_MAP_FILES
      imagePullSecrets:
      - name: docker-technofarm