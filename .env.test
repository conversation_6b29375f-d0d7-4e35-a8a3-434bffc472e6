# define your env variables for the test env here
KERNEL_CLASS='App\Kernel'
APP_SECRET='$ecretf0rt3st'
SYMFONY_DEPRECATIONS_HELPER=999999

GEOSCAN_API_BASE_URL='http://api.geoscan.local'
DATABASE_URL=pgsql://postgres:6nuk23@**************:5432/geoscan_cms_georgi

GEOSCAN_API_CLIENT_ID=1
GEOSCAN_API_CLIENT_SECRET=HeswUfret8esTuYe

ANALYSIS_DIRECTORY=%kernel.project_dir%/public/uploads/analysis/

COUCH_DB_PASS=6nuk23

KEYCLOAK_BASE_URL=http://keycloak.geoscan.info:8081/
KEYCLOAK_ALG=RS256
KEYCLOAK_REALM=geotech
KEYCLOAK_CERTS_URI=${KEYCLOAK_BASE_URL}realms/${KEY<PERSON><PERSON>K_REALM}/protocol/openid-connect/certs

REDIS_HOST="geoscan-cms-api-redis"
REDIS_PORT="6379"
