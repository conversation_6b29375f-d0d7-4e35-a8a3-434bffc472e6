-- Multi-Tenant Migration for gs_main database
-- This migration adds support for many-to-many relationships between users and service providers

-- ============================================================================
-- STEP 1: Create new junction tables
-- ============================================================================

-- User-Service Provider many-to-many relationship
CREATE TABLE IF NOT EXISTS user_service_providers (
    id SERIAL PRIMARY KEY,
    user_id INT NOT NULL,
    service_provider_id INT NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Foreign key constraints
    CONSTRAINT fk_usp_user FOREIGN KEY (user_id) REFERENCES su_users(id) ON DELETE CASCADE,
    CONSTRAINT fk_usp_service_provider FOREIGN KEY (service_provider_id) REFERENCES service_providers(id) ON DELETE CASCADE,

    -- Unique constraint to prevent duplicate relationships
    CONSTRAINT unique_user_service_provider UNIQUE (user_id, service_provider_id)
);

-- Organization-Service Provider many-to-many relationship
-- Note: Organizations are managed externally, so organization_id is just an integer reference
CREATE TABLE IF NOT EXISTS organization_service_providers (
    id SERIAL PRIMARY KEY,
    organization_id INT NOT NULL,
    service_provider_id INT NOT NULL,
    relationship_type VARCHAR(50) DEFAULT 'client', -- client, partner, vendor
    is_active BOOLEAN DEFAULT true,
    contract_start_date DATE,
    contract_end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Foreign key constraint for service provider
    CONSTRAINT fk_osp_service_provider FOREIGN KEY (service_provider_id) REFERENCES service_providers(id) ON DELETE CASCADE,

    -- Unique constraint to prevent duplicate relationships
    CONSTRAINT unique_org_service_provider UNIQUE (organization_id, service_provider_id)
);

-- User sessions for service provider context management
CREATE TABLE IF NOT EXISTS user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INT NOT NULL,
    active_service_provider_id INT NOT NULL,
    session_token VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,

    -- Foreign key constraints
    CONSTRAINT fk_us_user FOREIGN KEY (user_id) REFERENCES su_users(id) ON DELETE CASCADE,
    CONSTRAINT fk_us_service_provider FOREIGN KEY (active_service_provider_id) REFERENCES service_providers(id) ON DELETE CASCADE
);

-- ============================================================================
-- STEP 2: Create indexes for performance
-- ============================================================================

-- Indexes for user_service_providers
CREATE INDEX IF NOT EXISTS idx_usp_user_id ON user_service_providers(user_id);
CREATE INDEX IF NOT EXISTS idx_usp_service_provider_id ON user_service_providers(service_provider_id);
CREATE INDEX IF NOT EXISTS idx_usp_is_active ON user_service_providers(is_active);
CREATE INDEX IF NOT EXISTS idx_usp_is_default ON user_service_providers(is_default);

-- Indexes for organization_service_providers
CREATE INDEX IF NOT EXISTS idx_osp_organization_id ON organization_service_providers(organization_id);
CREATE INDEX IF NOT EXISTS idx_osp_service_provider_id ON organization_service_providers(service_provider_id);
CREATE INDEX IF NOT EXISTS idx_osp_is_active ON organization_service_providers(is_active);
CREATE INDEX IF NOT EXISTS idx_osp_relationship_type ON organization_service_providers(relationship_type);

-- Indexes for user_sessions
CREATE INDEX IF NOT EXISTS idx_us_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_us_active_service_provider_id ON user_sessions(active_service_provider_id);
CREATE INDEX IF NOT EXISTS idx_us_session_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_us_expires_at ON user_sessions(expires_at);

-- ============================================================================
-- STEP 3: Migrate existing data
-- ============================================================================

-- Migrate existing user-service provider relationships
-- Create default entries in the junction table for all existing users
INSERT INTO user_service_providers (user_id, service_provider_id, is_default, is_active)
SELECT
    id as user_id,
    service_provider_id,
    true as is_default,
    true as is_active
FROM su_users
WHERE service_provider_id IS NOT NULL
ON CONFLICT (user_id, service_provider_id) DO NOTHING;

-- ============================================================================
-- STEP 4: Add backward compatibility columns (optional)
-- ============================================================================

-- Add a flag to track multi-tenant mode per service provider
ALTER TABLE service_providers
ADD COLUMN IF NOT EXISTS multi_tenant_enabled BOOLEAN DEFAULT false;

-- Add metadata for tracking migration status
ALTER TABLE service_providers
ADD COLUMN IF NOT EXISTS migration_completed_at TIMESTAMP NULL;