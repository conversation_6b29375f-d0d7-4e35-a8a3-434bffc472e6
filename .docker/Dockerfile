FROM php:7.4-fpm-alpine as backend

WORKDIR /var/www/html/app

ARG APP_ENV

RUN apk add --no-cache build-base autoconf libpq unixodbc-dev freetype libpng libjpeg-turbo freetype-dev libpng-dev libjpeg-turbo-dev libzip-dev zip openldap-dev libxml2-dev

RUN apk add --no-cache --repository http://dl-cdn.alpinelinux.org/alpine/v3.15/community php7-pear

RUN set -ex \
    apk --no-cache add postgresql-libs postgresql-dev \
    docker-php-ext-install pdo pgsql pdo_pgsql gd zip \
    docker-php-ext-configure pgsql \
    --with-pgsql \
    apk del postgresql-dev

RUN apk add --no-cache \
    icu-dev \
    git \
    autoconf \
    g++ \
    make \
    cmake \
    openssl-dev \
    postgresql-libs \
    postgresql-dev \
    && CPPFLAGS="-DHAVE_SYS_FILE_H" pecl install redis \
    && docker-php-ext-enable redis.so

RUN apk add --no-cache nodejs npm

RUN docker-php-ext-install pdo pdo_pgsql

RUN docker-php-ext-enable pdo_pgsql

RUN apk add \
        --repository http://dl-cdn.alpinelinux.org/alpine/v3.6/main \
        --no-cache \
        rabbitmq-c-dev \
        && pecl install amqp \
        && docker-php-ext-enable amqp

RUN  addgroup -g 1000 -S appgroup \
    && adduser -u 1000 -D -S appuser -G appgroup \
    #change the app folder permissions
    && chown -R appuser:appgroup /var/www/html/app \
    && chmod -R g+rwx /var/www/html/app \
    && docker-php-ext-install zip intl opcache gd

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

RUN apk add --no-cache pcre-dev $PHPIZE_DEPS

COPY ./.docker/php/php.ini /usr/local/etc/php/php.ini
COPY ./.docker/php/www.conf /usr/local/etc/php-fpm.d/www.conf
COPY ./.docker/docker-entrypoint.sh /usr/local/bin
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# K8s Health Check
RUN apk add --no-cache fcgi
COPY ./.docker/php/php-fpm-healthcheck /usr/local/bin/
RUN chmod +x /usr/local/bin/php-fpm-healthcheck
# K8s Health Check End


EXPOSE 9000

#change the user to execute any further composer commands with appuser
USER appuser

COPY ./composer.json composer.json
COPY ./composer.lock composer.lock
COPY --chown=appuser:appgroup . .
RUN mkdir -p /var/www/html/app/var

RUN chown -R appuser:appgroup /var/www/html/app/var

RUN composer install --no-suggest --no-scripts $([ "$APP_ENV" == "prod" ] && echo "--no-dev") && \
    npm install $([ "$APP_ENV" == "prod" ] && echo "--production --ignore-scripts")

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]

