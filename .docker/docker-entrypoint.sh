#! /bin/sh

role=${CONTAINER_ROLE:-app}

#initialize working environment
if [ $APP_ENV = 'dev' ];
then
    composer install --no-suggest --no-scripts
    composer install --working-dir=tools/php-cs-fixer
    
    php bin/console doctrine:migrations:migrate --allow-no-migration --no-interaction

    php bin/console cache:clear --no-warmup

    npm install
fi

if [ "$role" = "app" ]; then
    if [ $APP_ENV = 'prod' ];
    then
        php bin/console doctrine:migrations:migrate --allow-no-migration --no-interaction
        
        php bin/console cache:clear --no-warmup
    fi
    #start php
    exec php-fpm -F
    
elif [ "$role" = "scheduler" ]; then
    echo "Scheduler role"
    php bin/console cache:clear --no-warmup
    while [ true ]
    do
      php bin/console cron:run --verbose --no-interaction &
      # Sleep for 1 minute.
      sleep 60
    done
else
    echo "Could not match the container role \"$role\""
    exit 1
fi