{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "379d9031182f19e1a7abf9f295f08c04", "packages": [{"name": "beberlei/doctrineextensions", "version": "v1.2.6", "source": {"type": "git", "url": "https://github.com/beberlei/DoctrineExtensions.git", "reference": "af72c4a136b744f1268ca8bb4da47a2f8af78f86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/beberlei/DoctrineExtensions/zipball/af72c4a136b744f1268ca8bb4da47a2f8af78f86", "reference": "af72c4a136b744f1268ca8bb4da47a2f8af78f86", "shasum": ""}, "require": {"doctrine/orm": "^2.6", "php": "^7.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.14", "nesbot/carbon": "*", "phpunit/phpunit": "^7.0 || ^8.0", "symfony/yaml": "^4.2", "zf1/zend-date": "^1.12", "zf1/zend-registry": "^1.12"}, "type": "library", "autoload": {"psr-4": {"DoctrineExtensions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A set of extensions to Doctrine 2 that add support for additional query functions available in MySQL and Oracle.", "keywords": ["database", "doctrine", "orm"], "time": "2019-12-05T09:49:04+00:00"}, {"name": "behat/transliterator", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/Behat/Transliterator.git", "reference": "3c4ec1d77c3d05caa1f0bf8fb3aae4845005c7fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Transliterator/zipball/3c4ec1d77c3d05caa1f0bf8fb3aae4845005c7fc", "reference": "3c4ec1d77c3d05caa1f0bf8fb3aae4845005c7fc", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"chuyskywalker/rolling-curl": "^3.1", "php-yaoi/php-yaoi": "^1.0", "phpunit/phpunit": "^4.8.36|^6.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Behat\\Transliterator\\": "src/Behat/Transliterator"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Artistic-1.0"], "description": "String transliterator", "keywords": ["i18n", "slug", "transliterator"], "time": "2020-01-14T16:39:13+00:00"}, {"name": "cron/cron", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/Cron/Cron.git", "reference": "8156b9532ecc64ed5aa5c4ebdfc4346d2ef91c4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Cron/Cron/zipball/8156b9532ecc64ed5aa5c4ebdfc4346d2ef91c4d", "reference": "8156b9532ecc64ed5aa5c4ebdfc4346d2ef91c4d", "shasum": ""}, "require": {"php": "^7.1", "symfony/process": "^2.4|^3.0|^4.0|^5.0"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Cron\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Cronjobs", "time": "2019-11-23T12:08:49+00:00"}, {"name": "cron/cron-bundle", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/Cron/Symfony-Bundle.git", "reference": "c38ae847108f72367f2013246ed9ffc51450fc49"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Cron/Symfony-Bundle/zipball/c38ae847108f72367f2013246ed9ffc51450fc49", "reference": "c38ae847108f72367f2013246ed9ffc51450fc49", "shasum": ""}, "require": {"cron/cron": "^1.0.1", "doctrine/doctrine-bundle": "~1.4|~2.0", "doctrine/orm": "~2.4,>=2.4.5", "php": ">=7.2", "symfony/framework-bundle": "^3.4|^4.4|^5.0"}, "require-dev": {"phpunit/phpunit": "^8.0", "symfony/symfony": "^3.0|^4.0|^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Cron\\CronBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony cron", "time": "2020-03-21T21:10:14+00:00"}, {"name": "doctrine/annotations", "version": "v1.8.0", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "904dca4eb10715b92569fbcd79e201d5c349b6bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/904dca4eb10715b92569fbcd79e201d5c349b6bc", "reference": "904dca4eb10715b92569fbcd79e201d5c349b6bc", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "php": "^7.1"}, "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "^7.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "http://www.doctrine-project.org", "keywords": ["annotations", "doc<PERSON>", "parser"], "time": "2019-10-01T18:55:10+00:00"}, {"name": "doctrine/cache", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "382e7f4db9a12dc6c19431743a2b096041bcdd62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/382e7f4db9a12dc6c19431743a2b096041bcdd62", "reference": "382e7f4db9a12dc6c19431743a2b096041bcdd62", "shasum": ""}, "require": {"php": "~7.1"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "doctrine/coding-standard": "^6.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0", "predis/predis": "~1.0"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "time": "2019-11-29T15:36:20+00:00"}, {"name": "doctrine/collections", "version": "1.6.4", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "6b1e4b2b66f6d6e49983cebfe23a21b7ccc5b0d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/6b1e4b2b66f6d6e49983cebfe23a21b7ccc5b0d7", "reference": "6b1e4b2b66f6d6e49983cebfe23a21b7ccc5b0d7", "shasum": ""}, "require": {"php": "^7.1.3"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan-shim": "^0.9.2", "phpunit/phpunit": "^7.0", "vimeo/psalm": "^3.2.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "time": "2019-11-13T13:07:11+00:00"}, {"name": "doctrine/common", "version": "2.12.0", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "2053eafdf60c2172ee1373d1b9289ba1db7f1fc6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/2053eafdf60c2172ee1373d1b9289ba1db7f1fc6", "reference": "2053eafdf60c2172ee1373d1b9289ba1db7f1fc6", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/inflector": "^1.0", "doctrine/lexer": "^1.0", "doctrine/persistence": "^1.1", "doctrine/reflection": "^1.0", "php": "^7.1"}, "require-dev": {"doctrine/coding-standard": "^1.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpunit/phpunit": "^7.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.11.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, persistence interfaces, proxies, event system and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "time": "2020-01-10T15:49:25+00:00"}, {"name": "doctrine/couchdb", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/doctrine/couchdb-client.git", "reference": "2ca8e66343b6d00658a86ba9709fffcdb5965251"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/couchdb-client/zipball/2ca8e66343b6d00658a86ba9709fffcdb5965251", "reference": "2ca8e66343b6d00658a86ba9709fffcdb5965251", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"Doctrine\\CouchDB": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "CouchDB Client", "homepage": "http://www.doctrine-project.org", "keywords": ["couchdb", "persistence"], "time": "2018-09-13T02:05:51+00:00"}, {"name": "doctrine/dbal", "version": "v2.10.1", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "c2b8e6e82732a64ecde1cddf9e1e06cb8556e3d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/c2b8e6e82732a64ecde1cddf9e1e06cb8556e3d8", "reference": "c2b8e6e82732a64ecde1cddf9e1e06cb8556e3d8", "shasum": ""}, "require": {"doctrine/cache": "^1.0", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.2"}, "require-dev": {"doctrine/coding-standard": "^6.0", "jetbrains/phpstorm-stubs": "^2019.1", "phpstan/phpstan": "^0.11.3", "phpunit/phpunit": "^8.4.1", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.10.x-dev", "dev-develop": "3.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "time": "2020-01-04T12:56:21+00:00"}, {"name": "doctrine/doctrine-bundle", "version": "1.12.6", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineBundle.git", "reference": "08f944760ac471aa97a9b0386dfdb559db80b32d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineBundle/zipball/08f944760ac471aa97a9b0386dfdb559db80b32d", "reference": "08f944760ac471aa97a9b0386dfdb559db80b32d", "shasum": ""}, "require": {"doctrine/dbal": "^2.5.12", "doctrine/doctrine-cache-bundle": "~1.2", "doctrine/persistence": "^1.3.3", "jdorn/sql-formatter": "^1.2.16", "php": "^7.1", "symfony/cache": "^3.4.30|^4.3.3", "symfony/config": "^3.4.30|^4.3.3", "symfony/console": "^3.4.30|^4.3.3", "symfony/dependency-injection": "^3.4.30|^4.3.3", "symfony/doctrine-bridge": "^3.4.30|^4.3.3", "symfony/framework-bundle": "^3.4.30|^4.3.3", "symfony/service-contracts": "^1.1.1|^2.0"}, "conflict": {"doctrine/orm": "<2.6", "twig/twig": "<1.34|>=2.0,<2.4"}, "require-dev": {"doctrine/coding-standard": "^6.0", "doctrine/orm": "^2.6", "ocramius/proxy-manager": "^2.1", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^7.5", "symfony/phpunit-bridge": "^4.2", "symfony/property-info": "^3.4.30|^4.3.3", "symfony/twig-bridge": "^3.4|^4.1", "symfony/validator": "^3.4.30|^4.3.3", "symfony/web-profiler-bundle": "^3.4.30|^4.3.3", "symfony/yaml": "^3.4.30|^4.3.3", "twig/twig": "^1.34|^2.12"}, "suggest": {"doctrine/orm": "The Doctrine ORM integration is optional in the bundle.", "symfony/web-profiler-bundle": "To use the data collector."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.12.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}], "description": "Symfony DoctrineBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "dbal", "orm", "persistence"], "time": "2019-12-19T09:08:00+00:00"}, {"name": "doctrine/doctrine-cache-bundle", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineCacheBundle.git", "reference": "6bee2f9b339847e8a984427353670bad4e7bdccb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineCacheBundle/zipball/6bee2f9b339847e8a984427353670bad4e7bdccb", "reference": "6bee2f9b339847e8a984427353670bad4e7bdccb", "shasum": ""}, "require": {"doctrine/cache": "^1.4.2", "doctrine/inflector": "^1.0", "php": "^7.1", "symfony/doctrine-bridge": "^3.4|^4.0"}, "require-dev": {"instaclick/coding-standard": "~1.1", "instaclick/object-calisthenics-sniffs": "dev-master", "instaclick/symfony2-coding-standard": "dev-remaster", "phpunit/phpunit": "^7.0", "predis/predis": "~0.8", "satooshi/php-coveralls": "^1.0", "squizlabs/php_codesniffer": "~1.5", "symfony/console": "^3.4|^4.0", "symfony/finder": "^3.4|^4.0", "symfony/framework-bundle": "^3.4|^4.0", "symfony/phpunit-bridge": "^3.4|^4.0", "symfony/security-acl": "^2.8", "symfony/validator": "^3.4|^4.0", "symfony/yaml": "^3.4|^4.0"}, "suggest": {"symfony/security-acl": "For using this bundle to cache ACLs"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineCacheBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON><PERSON><EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}], "description": "Symfony Bundle for Doctrine Cache", "homepage": "https://www.doctrine-project.org", "keywords": ["cache", "caching"], "abandoned": true, "time": "2019-11-29T11:22:01+00:00"}, {"name": "doctrine/doctrine-migrations-bundle", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineMigrationsBundle.git", "reference": "856437e8de96a70233e1f0cc2352fc8dd15a899d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineMigrationsBundle/zipball/856437e8de96a70233e1f0cc2352fc8dd15a899d", "reference": "856437e8de96a70233e1f0cc2352fc8dd15a899d", "shasum": ""}, "require": {"doctrine/doctrine-bundle": "~1.0|~2.0", "doctrine/migrations": "^2.2", "php": "^7.1", "symfony/framework-bundle": "~3.4|~4.0|~5.0"}, "require-dev": {"doctrine/coding-standard": "^5.0", "mikey179/vfsstream": "^1.6", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-strict-rules": "^0.9", "phpunit/phpunit": "^6.4|^7.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\MigrationsBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony DoctrineMigrationsBundle", "homepage": "https://www.doctrine-project.org", "keywords": ["dbal", "migrations", "schema"], "time": "2019-11-13T12:57:41+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "629572819973f13486371cb611386eb17851e85c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/629572819973f13486371cb611386eb17851e85c", "reference": "629572819973f13486371cb611386eb17851e85c", "shasum": ""}, "require": {"php": "^7.1"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "time": "2019-11-10T09:48:07+00:00"}, {"name": "doctrine/inflector", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "ec3a55242203ffa6a4b27c58176da97ff0a7aec1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/ec3a55242203ffa6a4b27c58176da97ff0a7aec1", "reference": "ec3a55242203ffa6a4b27c58176da97ff0a7aec1", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Common String Manipulations with regard to casing and singular/plural rules.", "homepage": "http://www.doctrine-project.org", "keywords": ["inflection", "pluralize", "singularize", "string"], "time": "2019-10-30T19:59:35+00:00"}, {"name": "doctrine/instantiator", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "ae466f726242e637cebdd526a7d991b9433bacf1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/ae466f726242e637cebdd526a7d991b9433bacf1", "reference": "ae466f726242e637cebdd526a7d991b9433bacf1", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"doctrine/coding-standard": "^6.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-shim": "^0.11", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "time": "2019-10-21T16:45:58+00:00"}, {"name": "doctrine/lexer", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "5242d66dbeb21a30dd8a3e66bf7a73b66e05e1f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/5242d66dbeb21a30dd8a3e66bf7a73b66e05e1f6", "reference": "5242d66dbeb21a30dd8a3e66bf7a73b66e05e1f6", "shasum": ""}, "require": {"php": "^7.2"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "time": "2019-10-30T14:39:59+00:00"}, {"name": "doctrine/migrations", "version": "2.2.1", "source": {"type": "git", "url": "https://github.com/doctrine/migrations.git", "reference": "a3987131febeb0e9acb3c47ab0df0af004588934"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/migrations/zipball/a3987131febeb0e9acb3c47ab0df0af004588934", "reference": "a3987131febeb0e9acb3c47ab0df0af004588934", "shasum": ""}, "require": {"doctrine/dbal": "^2.9", "ocramius/package-versions": "^1.3", "ocramius/proxy-manager": "^2.0.2", "php": "^7.1", "symfony/console": "^3.4||^4.0||^5.0", "symfony/stopwatch": "^3.4||^4.0||^5.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "doctrine/orm": "^2.6", "ext-pdo_sqlite": "*", "jdorn/sql-formatter": "^1.1", "mikey179/vfsstream": "^1.6", "phpstan/phpstan": "^0.10", "phpstan/phpstan-deprecation-rules": "^0.10", "phpstan/phpstan-phpunit": "^0.10", "phpstan/phpstan-strict-rules": "^0.10", "phpunit/phpunit": "^7.0", "symfony/process": "^3.4||^4.0||^5.0", "symfony/yaml": "^3.4||^4.0||^5.0"}, "suggest": {"jdorn/sql-formatter": "Allows to generate formatted SQL with the diff command.", "symfony/yaml": "Allows the use of yaml for migration configuration files."}, "bin": ["bin/doctrine-migrations"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Migrations\\": "lib/Doctrine/Migrations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Migrations project offer additional functionality on top of the database abstraction layer (DBAL) for versioning your database schema and easily deploying changes to it. It is a very easy to use and a powerful tool.", "homepage": "https://www.doctrine-project.org/projects/migrations.html", "keywords": ["database", "dbal", "migrations", "php"], "time": "2019-12-04T06:09:14+00:00"}, {"name": "doctrine/orm", "version": "v2.7.2", "source": {"type": "git", "url": "https://github.com/doctrine/orm.git", "reference": "dafe298ce5d0b995ebe1746670704c0a35868a6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/orm/zipball/dafe298ce5d0b995ebe1746670704c0a35868a6a", "reference": "dafe298ce5d0b995ebe1746670704c0a35868a6a", "shasum": ""}, "require": {"doctrine/annotations": "^1.8", "doctrine/cache": "^1.9.1", "doctrine/collections": "^1.5", "doctrine/common": "^2.11", "doctrine/dbal": "^2.9.3", "doctrine/event-manager": "^1.1", "doctrine/instantiator": "^1.3", "doctrine/persistence": "^1.2", "ext-pdo": "*", "ocramius/package-versions": "^1.2", "php": "^7.1", "symfony/console": "^3.0|^4.0|^5.0"}, "require-dev": {"doctrine/coding-standard": "^5.0", "phpunit/phpunit": "^7.5", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/yaml": "If you want to use YAML Metadata Mapping Driver"}, "bin": ["bin/doctrine"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.7.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\ORM\\": "lib/Doctrine/ORM"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Object-Relational-Mapper for PHP", "homepage": "https://www.doctrine-project.org/projects/orm.html", "keywords": ["database", "orm"], "time": "2020-03-19T06:41:02+00:00"}, {"name": "doctrine/persistence", "version": "1.3.7", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "0af483f91bada1c9ded6c2cfd26ab7d5ab2094e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/0af483f91bada1c9ded6c2cfd26ab7d5ab2094e0", "reference": "0af483f91bada1c9ded6c2cfd26ab7d5ab2094e0", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2", "php": "^7.1"}, "conflict": {"doctrine/common": "<2.10@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common", "Doctrine\\Persistence\\": "lib/Doctrine/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "time": "2020-03-21T15:13:52+00:00"}, {"name": "doctrine/reflection", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/reflection.git", "reference": "b699ecc7f2784d1e49924fd9858cf1078db6b0e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/reflection/zipball/b699ecc7f2784d1e49924fd9858cf1078db6b0e2", "reference": "b699ecc7f2784d1e49924fd9858cf1078db6b0e2", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "ext-tokenizer": "*", "php": "^7.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^5.0", "doctrine/common": "^2.10", "phpstan/phpstan": "^0.11.0", "phpstan/phpstan-phpunit": "^0.11.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Reflection project is a simple library used by the various Doctrine projects which adds some additional functionality on top of the reflection functionality that comes with PHP. It allows you to get the reflection information about classes, methods and properties statically.", "homepage": "https://www.doctrine-project.org/projects/reflection.html", "keywords": ["reflection", "static"], "abandoned": "roave/better-reflection", "time": "2020-03-21T11:34:59+00:00"}, {"name": "egulias/email-validator", "version": "2.1.24", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "ca90a3291eee1538cd48ff25163240695bd95448"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ca90a3291eee1538cd48ff25163240695bd95448", "reference": "ca90a3291eee1538cd48ff25163240695bd95448", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2020-11-14T15:56:27+00:00"}, {"name": "exsyst/swagger", "version": "v0.4.1", "source": {"type": "git", "url": "https://github.com/GuilhemN/swagger.git", "reference": "a02984db5edacdce2b4e09dae5ba8fe17a0e449e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GuilhemN/swagger/zipball/a02984db5edacdce2b4e09dae5ba8fe17a0e449e", "reference": "a02984db5edacdce2b4e09dae5ba8fe17a0e449e", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.5"}, "type": "library", "autoload": {"psr-4": {"EXSyst\\Component\\Swagger\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A php library to manipulate Swagger specifications", "time": "2018-07-27T06:40:00+00:00"}, {"name": "firebase/php-jwt", "version": "v5.2.0", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "feb0e820b8436873675fd3aca04f3728eb2185cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/feb0e820b8436873675fd3aca04f3728eb2185cb", "reference": "feb0e820b8436873675fd3aca04f3728eb2185cb", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <=9"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "time": "2020-03-25T18:49:23+00:00"}, {"name": "gedmo/doctrine-extensions", "version": "v2.4.39", "source": {"type": "git", "url": "https://github.com/Atlantic18/DoctrineExtensions.git", "reference": "c549b40bff560380c53812283d25ce42ee0992e4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Atlantic18/DoctrineExtensions/zipball/c549b40bff560380c53812283d25ce42ee0992e4", "reference": "c549b40bff560380c53812283d25ce42ee0992e4", "shasum": ""}, "require": {"behat/transliterator": "~1.2", "doctrine/common": "~2.4", "php": ">=5.3.2"}, "conflict": {"doctrine/annotations": "<1.2", "doctrine/mongodb-odm": ">=2.0"}, "require-dev": {"doctrine/common": ">=2.5.0", "doctrine/mongodb-odm": ">=1.0.2 <2.0", "doctrine/orm": ">=2.5.0", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5", "symfony/yaml": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"doctrine/mongodb-odm": "to use the extensions with the MongoDB ODM", "doctrine/orm": "to use the extensions with the ORM"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4.x-dev"}}, "autoload": {"psr-4": {"Gedmo\\": "lib/G<PERSON>mo"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Doctrine2 behavioral extensions", "homepage": "http://gediminasm.org/", "keywords": ["Blameable", "behaviors", "doctrine2", "extensions", "gedmo", "loggable", "nestedset", "sluggable", "sortable", "timestampable", "translatable", "tree", "uploadable"], "time": "2020-01-18T06:26:05+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.2", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "43ece0e75098b7ecd8d13918293029e555a50f82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/43ece0e75098b7ecd8d13918293029e555a50f82", "reference": "43ece0e75098b7ecd8d13918293029e555a50f82", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2019-12-23T11:57:10+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "239400de7a173fe9901b9ac7c06497751f00727a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/239400de7a173fe9901b9ac7c06497751f00727a", "reference": "239400de7a173fe9901b9ac7c06497751f00727a", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.8"}, "suggest": {"zendframework/zend-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2019-07-01T23:21:34+00:00"}, {"name": "jdorn/sql-formatter", "version": "v1.2.17", "source": {"type": "git", "url": "https://github.com/jdorn/sql-formatter.git", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jdorn/sql-formatter/zipball/64990d96e0959dff8e059dfcdc1af130728d92bc", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["lib"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/jdorn/sql-formatter/", "keywords": ["highlight", "sql"], "time": "2014-01-12T16:20:24+00:00"}, {"name": "lcobucci/jwt", "version": "3.3.1", "source": {"type": "git", "url": "https://github.com/lcobucci/jwt.git", "reference": "a11ec5f4b4d75d1fcd04e133dede4c317aac9e18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/jwt/zipball/a11ec5f4b4d75d1fcd04e133dede4c317aac9e18", "reference": "a11ec5f4b4d75d1fcd04e133dede4c317aac9e18", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-openssl": "*", "php": "^5.6 || ^7.0"}, "require-dev": {"mikey179/vfsstream": "~1.5", "phpmd/phpmd": "~2.2", "phpunit/php-invoker": "~1.1", "phpunit/phpunit": "^5.7 || ^7.3", "squizlabs/php_codesniffer": "~2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Lcobucci\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "jwt"], "time": "2019-05-24T18:30:49+00:00"}, {"name": "league/period", "version": "4.10.0", "source": {"type": "git", "url": "https://github.com/thephpleague/period.git", "reference": "697960c1f07d79ebb7724797e432245af7180782"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/period/zipball/697960c1f07d79ebb7724797e432245af7180782", "reference": "697960c1f07d79ebb7724797e432245af7180782", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "infection/infection": "^0.13", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"League\\Period\\": "src"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "description": "Time range API for PHP", "homepage": "http://period.thephpleague.com", "keywords": ["boundaries", "calendar", "collection", "date", "dateinterval", "dateperiod", "datetime", "gap", "intersections", "interval", "period", "range", "schedule", "sequence", "time", "timeline", "unions"], "time": "2020-03-22T16:39:12+00:00"}, {"name": "lexik/jwt-authentication-bundle", "version": "v2.6.5", "source": {"type": "git", "url": "https://github.com/lexik/LexikJWTAuthenticationBundle.git", "reference": "448551fc08c6cff37aad9d8f27f6b9615cd28966"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lexik/LexikJWTAuthenticationBundle/zipball/448551fc08c6cff37aad9d8f27f6b9615cd28966", "reference": "448551fc08c6cff37aad9d8f27f6b9615cd28966", "shasum": ""}, "require": {"ext-openssl": "*", "lcobucci/jwt": "^3.2", "namshi/jose": "^7.2", "php": "^5.5|^7.0", "symfony/framework-bundle": "^3.4|^4.0|^5.0", "symfony/security-bundle": "^3.4|^4.0|^5.0"}, "require-dev": {"symfony/browser-kit": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/phpunit-bridge": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"gesdinet/jwt-refresh-token-bundle": "Implements a refresh token system over Json Web Tokens in Symfony", "spomky-labs/lexik-jose-bridge": "Provides a JWT Token encoder with encryption support"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Lexik\\Bundle\\JWTAuthenticationBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/jeremyb"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/slashfan"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cedric-g"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/lexik"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/chalasr"}, {"name": "Lexik Community", "homepage": "https://github.com/lexik/LexikJWTAuthenticationBundle/graphs/contributors"}], "description": "This bundle provides JWT authentication for your Symfony REST API", "homepage": "https://github.com/lexik/LexikJWTAuthenticationBundle", "keywords": ["Authentication", "JWS", "api", "bundle", "jwt", "rest", "symfony"], "time": "2019-11-22T14:22:26+00:00"}, {"name": "markbaker/complex", "version": "1.4.8", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "8eaa40cceec7bf0518187530b2e63871be661b72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/8eaa40cceec7bf0518187530b2e63871be661b72", "reference": "8eaa40cceec7bf0518187530b2e63871be661b72", "shasum": ""}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "2.*", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^4.8.35|^5.4.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "^3.4.0"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}, "files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "time": "2020-03-11T20:15:49+00:00"}, {"name": "markbaker/matrix", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "5348c5a67e3b75cd209d70103f916a93b1f1ed21"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/5348c5a67e3b75cd209d70103f916a93b1f1ed21", "reference": "5348c5a67e3b75cd209d70103f916a93b1f1ed21", "shasum": ""}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "dev-master", "phploc/phploc": "^4", "phpmd/phpmd": "dev-master", "phpunit/phpunit": "^5.7", "sebastian/phpcpd": "^3.0", "squizlabs/php_codesniffer": "^3.0@dev"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}, "files": ["classes/src/functions/adjoint.php", "classes/src/functions/antidiagonal.php", "classes/src/functions/cofactors.php", "classes/src/functions/determinant.php", "classes/src/functions/diagonal.php", "classes/src/functions/identity.php", "classes/src/functions/inverse.php", "classes/src/functions/minors.php", "classes/src/functions/trace.php", "classes/src/functions/transpose.php", "classes/src/operations/add.php", "classes/src/operations/directsum.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "time": "2019-10-06T11:29:25+00:00"}, {"name": "monolog/monolog", "version": "1.25.3", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "fa82921994db851a8becaf3787a9e73c5976b6f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/fa82921994db851a8becaf3787a9e73c5976b6f1", "reference": "fa82921994db851a8becaf3787a9e73c5976b6f1", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "jakub-onderka/php-parallel-lint": "0.9", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpunit/phpunit": "~4.5", "phpunit/phpunit-mock-objects": "2.3.0", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2019-12-20T14:15:16+00:00"}, {"name": "namshi/jose", "version": "7.2.3", "source": {"type": "git", "url": "https://github.com/namshi/jose.git", "reference": "89a24d7eb3040e285dd5925fcad992378b82bcff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/namshi/jose/zipball/89a24d7eb3040e285dd5925fcad992378b82bcff", "reference": "89a24d7eb3040e285dd5925fcad992378b82bcff", "shasum": ""}, "require": {"ext-date": "*", "ext-hash": "*", "ext-json": "*", "ext-pcre": "*", "ext-spl": "*", "php": ">=5.5", "symfony/polyfill-php56": "^1.0"}, "require-dev": {"phpseclib/phpseclib": "^2.0", "phpunit/phpunit": "^4.5|^5.0", "satooshi/php-coveralls": "^1.0"}, "suggest": {"ext-openssl": "Allows to use OpenSSL as crypto engine.", "phpseclib/phpseclib": "Allows to use Phpseclib as crypto engine, use version ^2.0."}, "type": "library", "autoload": {"psr-4": {"Namshi\\JOSE\\": "src/Namshi/JOSE/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON> (cirpo)", "email": "<EMAIL>"}], "description": "JSON Object Signing and Encryption library for PHP.", "keywords": ["JSON Web Signature", "JSON Web Token", "JWS", "json", "jwt", "token"], "time": "2016-12-05T07:27:31+00:00"}, {"name": "nelmio/api-doc-bundle", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/nelmio/NelmioApiDocBundle.git", "reference": "0fcf2d5c9a9b4e230e5bcf6b3f4ad469ba738ed2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nelmio/NelmioApiDocBundle/zipball/0fcf2d5c9a9b4e230e5bcf6b3f4ad469ba738ed2", "reference": "0fcf2d5c9a9b4e230e5bcf6b3f4ad469ba738ed2", "shasum": ""}, "require": {"exsyst/swagger": "^0.4.1", "php": "^7.1", "phpdocumentor/reflection-docblock": "^3.1|^4.0|^5.0", "symfony/framework-bundle": "^3.4|^4.0|^5.0", "symfony/options-resolver": "^3.4.4|^4.0|^5.0", "symfony/property-info": "^3.4|^4.0|^5.0", "zircote/swagger-php": "^2.0.9"}, "conflict": {"symfony/framework-bundle": "4.2.7"}, "require-dev": {"api-platform/core": "^2.1.2", "doctrine/annotations": "^1.2", "doctrine/common": "^2.4", "friendsofsymfony/rest-bundle": "^2.0", "jms/serializer": "^1.14|^3.0", "jms/serializer-bundle": "^2.3|^3.0", "sensio/framework-extra-bundle": "^3.0.13|^4.0|^5.0", "symfony/asset": "^3.4|^4.0|^5.0", "symfony/browser-kit": "^3.4|^4.0|^5.0", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/form": "^3.4|^4.0|^5.0", "symfony/phpunit-bridge": "^3.4.24|^4.0|^5.0", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/twig-bundle": "^3.4|^4.0|^5.0", "symfony/validator": "^3.4|^4.0|^5.0", "willdurand/hateoas-bundle": "^1.0|^2.0"}, "suggest": {"api-platform/core": "For using an API oriented framework.", "friendsofsymfony/rest-bundle": "For using the parameters annotations."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.6.x-dev"}}, "autoload": {"psr-4": {"Nelmio\\ApiDocBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Nelmio", "homepage": "http://nelm.io"}, {"name": "Symfony Community", "homepage": "https://github.com/nelmio/NelmioApiDocBundle/contributors"}], "description": "Generates documentation for your REST API from annotations", "keywords": ["api", "doc", "documentation", "rest"], "time": "2020-02-23T16:51:12+00:00"}, {"name": "ocramius/package-versions", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/Ocramius/PackageVersions.git", "reference": "44af6f3a2e2e04f2af46bcb302ad9600cba41c7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/44af6f3a2e2e04f2af46bcb302ad9600cba41c7d", "reference": "44af6f3a2e2e04f2af46bcb302ad9600cba41c7d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0.0", "php": "^7.1.0"}, "require-dev": {"composer/composer": "^1.6.3", "doctrine/coding-standard": "^5.0.1", "ext-zip": "*", "infection/infection": "^0.7.1", "phpunit/phpunit": "^7.5.17"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "time": "2019-11-15T16:17:10+00:00"}, {"name": "ocramius/proxy-manager", "version": "2.2.3", "source": {"type": "git", "url": "https://github.com/Ocramius/ProxyManager.git", "reference": "4d154742e31c35137d5374c998e8f86b54db2e2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/4d154742e31c35137d5374c998e8f86b54db2e2f", "reference": "4d154742e31c35137d5374c998e8f86b54db2e2f", "shasum": ""}, "require": {"ocramius/package-versions": "^1.1.3", "php": "^7.2.0", "zendframework/zend-code": "^3.3.0"}, "require-dev": {"couscous/couscous": "^1.6.1", "ext-phar": "*", "humbug/humbug": "1.0.0-RC.0@RC", "nikic/php-parser": "^3.1.1", "padraic/phpunit-accelerator": "dev-master@DEV", "phpbench/phpbench": "^0.12.2", "phpstan/phpstan": "dev-master#856eb10a81c1d27c701a83f167dc870fd8f4236a as 0.9.999", "phpstan/phpstan-phpunit": "dev-master#5629c0a1f4a9c417cb1077cf6693ad9753895761", "phpunit/phpunit": "^6.4.3", "squizlabs/php_codesniffer": "^2.9.1"}, "suggest": {"ocramius/generated-hydrator": "To have very fast object to array to object conversion for ghost objects", "zendframework/zend-json": "To have the JsonRpc adapter (Remote Object feature)", "zendframework/zend-soap": "To have the Soap adapter (Remote Object feature)", "zendframework/zend-xmlrpc": "To have the XmlRpc adapter (Remote Object feature)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-0": {"ProxyManager\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.io/"}], "description": "A library providing utilities to generate, instantiate and generally operate with Object Proxies", "homepage": "https://github.com/Ocramius/ProxyManager", "keywords": ["aop", "lazy loading", "proxy", "proxy pattern", "service proxies"], "time": "2019-08-10T08:37:15+00:00"}, {"name": "opsway/doctrine-dbal-postgresql", "version": "v0.8.1", "source": {"type": "git", "url": "https://github.com/opsway/doctrine-dbal-postgresql.git", "reference": "fda403a60653c09637403384485f6db3c2e4ff73"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opsway/doctrine-dbal-postgresql/zipball/fda403a60653c09637403384485f6db3c2e4ff73", "reference": "fda403a60653c09637403384485f6db3c2e4ff73", "shasum": ""}, "require": {"doctrine/dbal": "~2.4", "doctrine/orm": "~2.4", "php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "^6.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Opsway\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Extensions for support Postgres in Doctrine DBAL & DQL", "time": "2018-03-14T08:32:41+00:00"}, {"name": "oro/doctrine-extensions", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/oroinc/doctrine-extensions.git", "reference": "2dbedcc5ed2dbe3283a7f7cf9e3b4c16bc1abca4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/oroinc/doctrine-extensions/zipball/2dbedcc5ed2dbe3283a7f7cf9e3b4c16bc1abca4", "reference": "2dbedcc5ed2dbe3283a7f7cf9e3b4c16bc1abca4", "shasum": ""}, "require": {"doctrine/orm": ">=2.2.3", "php": ">=5.4.0"}, "require-dev": {"doctrine/data-fixtures": "^1.0", "doctrine/orm": "<2.5.0", "phpunit/phpunit": "4.*", "squizlabs/php_codesniffer": "2.8.*", "symfony/yaml": "2.*"}, "type": "library", "autoload": {"psr-0": {"Oro\\DBAL": "src/", "Oro\\ORM": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Oro, Inc", "homepage": "http://www.orocrm.com"}], "description": "Doctrine Extensions for MySQL and PostgreSQL.", "homepage": "https://github.com/orocrm/doctrine-extensions/", "keywords": ["database", "doctrine", "dql", "function", "mysql", "postgresql", "type"], "time": "2019-09-27T10:30:38+00:00"}, {"name": "pagerfanta/pagerfanta", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/BabDev/Pagerfanta.git", "reference": "a53ff01d521648d9dbca19b93ac6bc75a59b0972"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/BabDev/Pagerfanta/zipball/a53ff01d521648d9dbca19b93ac6bc75a59b0972", "reference": "a53ff01d521648d9dbca19b93ac6bc75a59b0972", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"doctrine/orm": "~2.3", "doctrine/phpcr-odm": "1.*", "jackalope/jackalope-doctrine-dbal": "1.*", "jmikola/geojson": "~1.0", "mandango/mandango": "~1.0@dev", "mandango/mondator": "~1.0@dev", "phpunit/phpunit": "^6.5", "propel/propel": "~2.0@dev", "propel/propel1": "~1.6", "ruflin/elastica": "~1.3", "solarium/solarium": "~3.1"}, "suggest": {"doctrine/mongodb-odm": "To use the DoctrineODMMongoDBAdapter.", "doctrine/orm": "To use the DoctrineORMAdapter.", "doctrine/phpcr-odm": "To use the DoctrineODMPhpcrAdapter. >= 1.1.0", "mandango/mandango": "To use the MandangoAdapter.", "propel/propel": "To use the Propel2Adapter", "propel/propel1": "To use the PropelAdapter", "solarium/solarium": "To use the SolariumAdapter."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Pagerfanta\\": "src/Pagerfanta/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Pagination for PHP", "keywords": ["page", "pagination", "paginator", "paging"], "time": "2019-07-17T20:56:16+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "63a995caa1ca9e5590304cd845c15ad6d482a62a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/63a995caa1ca9e5590304cd845c15ad6d482a62a", "reference": "63a995caa1ca9e5590304cd845c15ad6d482a62a", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "~6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2018-08-07T13:53:10+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "4.3.4", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "da3fd972d6bafd628114f7e7e036f45944b62e9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/da3fd972d6bafd628114f7e7e036f45944b62e9c", "reference": "da3fd972d6bafd628114f7e7e036f45944b62e9c", "shasum": ""}, "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0.0 || ^2.0.0", "phpdocumentor/type-resolver": "~0.4 || ^1.0.0", "webmozart/assert": "^1.0"}, "require-dev": {"doctrine/instantiator": "^1.0.5", "mockery/mockery": "^1.0", "phpdocumentor/type-resolver": "0.4.*", "phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2019-12-28T18:55:12+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "7462d5f123dfc080dfdf26897032a6513644fc95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/7462d5f123dfc080dfdf26897032a6513644fc95", "reference": "7462d5f123dfc080dfdf26897032a6513644fc95", "shasum": ""}, "require": {"php": "^7.2", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "^7.2", "mockery/mockery": "~1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "time": "2020-02-18T18:59:58+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "c2a205e82f9cf1cc9fab86b79e808d86dd680470"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/c2a205e82f9cf1cc9fab86b79e808d86dd680470", "reference": "c2a205e82f9cf1cc9fab86b79e808d86dd680470", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "markbaker/complex": "^1.4", "markbaker/matrix": "^1.2", "php": "^7.1", "psr/simple-cache": "^1.0"}, "require-dev": {"dompdf/dompdf": "^0.8.3", "friendsofphp/php-cs-fixer": "^2.16", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "time": "2020-03-02T13:09:03+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2020-03-23T09:12:05+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "sensio/framework-extra-bundle", "version": "v5.5.3", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioFrameworkExtraBundle.git", "reference": "98f0807137b13d0acfdf3c255a731516e97015de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioFrameworkExtraBundle/zipball/98f0807137b13d0acfdf3c255a731516e97015de", "reference": "98f0807137b13d0acfdf3c255a731516e97015de", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "php": ">=7.1.3", "symfony/config": "^4.3|^5.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/framework-bundle": "^4.3|^5.0", "symfony/http-kernel": "^4.3|^5.0"}, "conflict": {"doctrine/doctrine-cache-bundle": "<1.3.1"}, "require-dev": {"doctrine/doctrine-bundle": "^1.11|^2.0", "doctrine/orm": "^2.5", "nyholm/psr7": "^1.1", "symfony/browser-kit": "^4.3|^5.0", "symfony/dom-crawler": "^4.3|^5.0", "symfony/expression-language": "^4.3|^5.0", "symfony/finder": "^4.3|^5.0", "symfony/monolog-bridge": "^4.0|^5.0", "symfony/monolog-bundle": "^3.2", "symfony/phpunit-bridge": "^4.3.5|^5.0", "symfony/psr-http-message-bridge": "^1.1", "symfony/security-bundle": "^4.3|^5.0", "symfony/twig-bundle": "^4.3|^5.0", "symfony/yaml": "^4.3|^5.0", "twig/twig": "^1.34|^2.4|^3.0"}, "suggest": {"symfony/expression-language": "", "symfony/psr-http-message-bridge": "To use the PSR-7 converters", "symfony/security-bundle": ""}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "5.5.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\FrameworkExtraBundle\\": "src/"}, "exclude-from-classmap": ["/tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle provides a way to configure your controllers with annotations", "keywords": ["annotations", "controllers"], "time": "2019-12-27T08:57:19+00:00"}, {"name": "stof/doctrine-extensions-bundle", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/stof/StofDoctrineExtensionsBundle.git", "reference": "46db71ec7ffee9122eca3cdddd4ef8d84bae269c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stof/StofDoctrineExtensionsBundle/zipball/46db71ec7ffee9122eca3cdddd4ef8d84bae269c", "reference": "46db71ec7ffee9122eca3cdddd4ef8d84bae269c", "shasum": ""}, "require": {"gedmo/doctrine-extensions": "^2.3.4", "php": ">=5.3.2", "symfony/framework-bundle": "~2.7|~3.2|~4.0"}, "require-dev": {"symfony/phpunit-bridge": "^4.0", "symfony/security-bundle": "^2.7 || ^3.2 || ^4.0"}, "suggest": {"doctrine/doctrine-bundle": "to use the ORM extensions", "doctrine/mongodb-odm-bundle": "to use the MongoDB ODM extensions"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Stof\\DoctrineExtensionsBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Integration of the gedmo/doctrine-extensions with Symfony2", "homepage": "https://github.com/stof/StofDoctrineExtensionsBundle", "keywords": ["behaviors", "doctrine2", "extensions", "gedmo", "loggable", "nestedset", "sluggable", "sortable", "timestampable", "translatable", "tree"], "time": "2017-12-24T16:06:50+00:00"}, {"name": "symfony/amqp-pack", "version": "v1.0.12", "source": {"type": "git", "url": "https://github.com/symfony/amqp-pack.git", "reference": "2433282bd6e57a530d6149bd282442870712e871"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/amqp-pack/zipball/2433282bd6e57a530d6149bd282442870712e871", "reference": "2433282bd6e57a530d6149bd282442870712e871", "shasum": ""}, "require": {"ext-amqp": "*", "php": "^7.1", "symfony/messenger": "*", "symfony/serializer-pack": "*"}, "type": "symfony-pack", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A pack for using the AMQP transport with Symfony Messenger", "time": "2018-12-10T12:12:31+00:00"}, {"name": "symfony/asset", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "5c1afa35f0ff6a4fa5d037b4a5ef1bf60513e65b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/5c1afa35f0ff6a4fa5d037b4a5ef1bf60513e65b", "reference": "5c1afa35f0ff6a4fa5d037b4a5ef1bf60513e65b", "shasum": ""}, "require": {"php": "^7.1.3"}, "require-dev": {"symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/http-foundation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Asset Component", "homepage": "https://symfony.com", "time": "2020-02-24T13:10:00+00:00"}, {"name": "symfony/cache", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "28511cbd8c760a19f4b4b70961d2cd957733b3d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/28511cbd8c760a19f4b4b70961d2cd957733b3d9", "reference": "28511cbd8c760a19f4b4b70961d2cd957733b3d9", "shasum": ""}, "require": {"php": "^7.1.3", "psr/cache": "~1.0", "psr/log": "~1.0", "symfony/cache-contracts": "^1.1.7|^2", "symfony/service-contracts": "^1.1|^2", "symfony/var-exporter": "^4.2|^5.0"}, "conflict": {"doctrine/dbal": "<2.5", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/var-dumper": "<4.4"}, "provide": {"psr/cache-implementation": "1.0", "psr/simple-cache-implementation": "1.0", "symfony/cache-implementation": "1.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "~1.6", "doctrine/dbal": "~2.5", "predis/predis": "~1.1", "psr/simple-cache": "^1.0", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.1|^5.0", "symfony/var-dumper": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Cache component with PSR-6, PSR-16, and tags", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "time": "2020-02-20T16:31:44+00:00"}, {"name": "symfony/cache-contracts", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "23ed8bfc1a4115feca942cb5f1aacdf3dcdf3c16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/23ed8bfc1a4115feca942cb5f1aacdf3dcdf3c16", "reference": "23ed8bfc1a4115feca942cb5f1aacdf3dcdf3c16", "shasum": ""}, "require": {"php": "^7.2.5", "psr/cache": "^1.0"}, "suggest": {"symfony/cache-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2019-11-18T17:27:11+00:00"}, {"name": "symfony/config", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "cbfef5ae91ccd3b06621c18d58cd355c68c87ae9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/cbfef5ae91ccd3b06621c18d58cd355c68c87ae9", "reference": "cbfef5ae91ccd3b06621c18d58cd355c68c87ae9", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/filesystem": "^3.4|^4.0|^5.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<3.4"}, "require-dev": {"symfony/event-dispatcher": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/messenger": "^4.1|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Config Component", "homepage": "https://symfony.com", "time": "2020-02-04T09:32:40+00:00"}, {"name": "symfony/console", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "4fa15ae7be74e53f6ec8c83ed403b97e23b665e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/4fa15ae7be74e53f6ec8c83ed403b97e23b665e9", "reference": "4fa15ae7be74e53f6ec8c83ed403b97e23b665e9", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3|>=5", "symfony/lock": "<4.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^4.3", "symfony/lock": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^4.3|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2020-02-24T13:10:00+00:00"}, {"name": "symfony/debug", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "a980d87a659648980d89193fd8b7a7ca89d97d21"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/a980d87a659648980d89193fd8b7a7ca89d97d21", "reference": "a980d87a659648980d89193fd8b7a7ca89d97d21", "shasum": ""}, "require": {"php": "^7.1.3", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": "<3.4"}, "require-dev": {"symfony/http-kernel": "^3.4|^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "time": "2020-02-23T14:41:43+00:00"}, {"name": "symfony/dependency-injection", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "ebb2e882e8c9e2eb990aa61ddcd389848466e342"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/ebb2e882e8c9e2eb990aa61ddcd389848466e342", "reference": "ebb2e882e8c9e2eb990aa61ddcd389848466e342", "shasum": ""}, "require": {"php": "^7.1.3", "psr/container": "^1.0", "symfony/service-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<4.3|>=5.0", "symfony/finder": "<3.4", "symfony/proxy-manager-bridge": "<3.4", "symfony/yaml": "<3.4"}, "provide": {"psr/container-implementation": "1.0", "symfony/service-implementation": "1.0"}, "require-dev": {"symfony/config": "^4.3", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DependencyInjection Component", "homepage": "https://symfony.com", "time": "2020-02-29T09:50:10+00:00"}, {"name": "symfony/doctrine-bridge", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-bridge.git", "reference": "1b4564758fe91f5d53dfbdfd9007d15e43fa465f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-bridge/zipball/1b4564758fe91f5d53dfbdfd9007d15e43fa465f", "reference": "1b4564758fe91f5d53dfbdfd9007d15e43fa465f", "shasum": ""}, "require": {"doctrine/event-manager": "~1.0", "doctrine/persistence": "^1.3", "php": "^7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.4", "symfony/form": "<4.4", "symfony/http-kernel": "<4.3.7", "symfony/messenger": "<4.3", "symfony/security-core": "<4.4", "symfony/validator": "<4.4.2|<5.0.2,>=5.0"}, "require-dev": {"doctrine/annotations": "~1.7", "doctrine/cache": "~1.6", "doctrine/collections": "~1.0", "doctrine/data-fixtures": "1.0.*", "doctrine/dbal": "~2.4", "doctrine/orm": "^2.6.3", "doctrine/reflection": "~1.0", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/form": "^4.4|^5.0", "symfony/http-kernel": "^4.3.7", "symfony/messenger": "^4.4|^5.0", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/property-info": "^3.4|^4.0|^5.0", "symfony/proxy-manager-bridge": "^3.4|^4.0|^5.0", "symfony/security-core": "^4.4|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/translation": "^3.4|^4.0|^5.0", "symfony/validator": "^4.4.2|^5.0.2", "symfony/var-dumper": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/data-fixtures": "", "doctrine/dbal": "", "doctrine/orm": "", "symfony/form": "", "symfony/property-info": "", "symfony/validator": ""}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Doctrine Bridge", "homepage": "https://symfony.com", "time": "2020-02-24T17:16:47+00:00"}, {"name": "symfony/dotenv", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "9bba981ecb1f57c04520d4165b3e6a17ac49319f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/9bba981ecb1f57c04520d4165b3e6a17ac49319f", "reference": "9bba981ecb1f57c04520d4165b3e6a17ac49319f", "shasum": ""}, "require": {"php": "^7.1.3"}, "require-dev": {"symfony/process": "^3.4.2|^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "time": "2020-02-29T10:04:02+00:00"}, {"name": "symfony/error-handler", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "89aa4b9ac6f1f35171b8621b24f60477312085be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/89aa4b9ac6f1f35171b8621b24f60477312085be", "reference": "89aa4b9ac6f1f35171b8621b24f60477312085be", "shasum": ""}, "require": {"php": "^7.1.3", "psr/log": "~1.0", "symfony/debug": "^4.4.5", "symfony/var-dumper": "^4.4|^5.0"}, "require-dev": {"symfony/http-kernel": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ErrorHandler Component", "homepage": "https://symfony.com", "time": "2020-02-26T11:45:31+00:00"}, {"name": "symfony/event-dispatcher", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "4ad8e149799d3128621a3a1f70e92b9897a8930d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/4ad8e149799d3128621a3a1f70e92b9897a8930d", "reference": "4ad8e149799d3128621a3a1f70e92b9897a8930d", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/event-dispatcher-contracts": "^1.1"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "time": "2020-02-04T09:32:40+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.1.7", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "c43ab685673fb6c8d84220c77897b1d6cdbe1d18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/c43ab685673fb6c8d84220c77897b1d6cdbe1d18", "reference": "c43ab685673fb6c8d84220c77897b1d6cdbe1d18", "shasum": ""}, "require": {"php": "^7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2019-09-17T09:54:03+00:00"}, {"name": "symfony/filesystem", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "266c9540b475f26122b61ef8b23dd9198f5d1cfd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/266c9540b475f26122b61ef8b23dd9198f5d1cfd", "reference": "266c9540b475f26122b61ef8b23dd9198f5d1cfd", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "time": "2020-01-21T08:20:44+00:00"}, {"name": "symfony/finder", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "ea69c129aed9fdeca781d4b77eb20b62cf5d5357"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/ea69c129aed9fdeca781d4b77eb20b62cf5d5357", "reference": "ea69c129aed9fdeca781d4b77eb20b62cf5d5357", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2020-02-14T07:42:58+00:00"}, {"name": "symfony/flex", "version": "v1.6.2", "source": {"type": "git", "url": "https://github.com/symfony/flex.git", "reference": "e4f5a2653ca503782a31486198bd1dd1c9a47f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/flex/zipball/e4f5a2653ca503782a31486198bd1dd1c9a47f83", "reference": "e4f5a2653ca503782a31486198bd1dd1c9a47f83", "shasum": ""}, "require": {"composer-plugin-api": "^1.0", "php": "^7.0"}, "require-dev": {"composer/composer": "^1.0.2", "symfony/dotenv": "^3.4|^4.0|^5.0", "symfony/phpunit-bridge": "^3.4.19|^4.1.8|^5.0", "symfony/process": "^2.7|^3.0|^4.0|^5.0"}, "type": "composer-plugin", "extra": {"branch-alias": {"dev-master": "1.5-dev"}, "class": "Symfony\\Flex\\Flex"}, "autoload": {"psr-4": {"Symfony\\Flex\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin for Symfony", "time": "2020-01-30T12:06:45+00:00"}, {"name": "symfony/form", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/form.git", "reference": "677927dad3b9f93117db62fc1df4de82027dc282"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/form/zipball/677927dad3b9f93117db62fc1df4de82027dc282", "reference": "677927dad3b9f93117db62fc1df4de82027dc282", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/event-dispatcher": "^4.3", "symfony/intl": "^4.4|^5.0", "symfony/options-resolver": "~4.3|^5.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<4.3", "symfony/dependency-injection": "<3.4", "symfony/doctrine-bridge": "<3.4", "symfony/framework-bundle": "<3.4", "symfony/http-kernel": "<4.4", "symfony/intl": "<4.3", "symfony/translation": "<4.2", "symfony/twig-bridge": "<3.4.5|<4.0.5,>=4.0"}, "require-dev": {"doctrine/collections": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^4.3|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/security-csrf": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/validator": "^3.4.31|^4.3.4|^5.0", "symfony/var-dumper": "^4.3|^5.0"}, "suggest": {"symfony/security-csrf": "For protecting forms against CSRF attacks.", "symfony/twig-bridge": "For templating with <PERSON><PERSON>.", "symfony/validator": "For form validation."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Form\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Form Component", "homepage": "https://symfony.com", "time": "2020-02-29T10:05:28+00:00"}, {"name": "symfony/framework-bundle", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "df5528926e6e1954975f3d73a91f029ba3d9c76b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/df5528926e6e1954975f3d73a91f029ba3d9c76b", "reference": "df5528926e6e1954975f3d73a91f029ba3d9c76b", "shasum": ""}, "require": {"ext-xml": "*", "php": "^7.1.3", "symfony/cache": "^4.4|^5.0", "symfony/config": "^4.3.4|^5.0", "symfony/dependency-injection": "^4.4.1|^5.0.1", "symfony/error-handler": "^4.4.1|^5.0.1", "symfony/filesystem": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/http-kernel": "^4.4", "symfony/polyfill-mbstring": "~1.0", "symfony/routing": "^4.4|^5.0"}, "conflict": {"doctrine/persistence": "<1.3", "phpdocumentor/reflection-docblock": "<3.0", "phpdocumentor/type-resolver": "<0.2.1", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/asset": "<3.4", "symfony/browser-kit": "<4.3", "symfony/console": "<4.3", "symfony/dom-crawler": "<4.3", "symfony/dotenv": "<4.3.6", "symfony/form": "<4.3.5", "symfony/http-client": "<4.4", "symfony/lock": "<4.4", "symfony/mailer": "<4.4", "symfony/messenger": "<4.4", "symfony/mime": "<4.4", "symfony/property-info": "<3.4", "symfony/security-bundle": "<4.4", "symfony/serializer": "<4.4", "symfony/stopwatch": "<3.4", "symfony/translation": "<4.4", "symfony/twig-bridge": "<4.1.1", "symfony/twig-bundle": "<4.4", "symfony/validator": "<4.4", "symfony/web-profiler-bundle": "<4.4", "symfony/workflow": "<4.3.6"}, "require-dev": {"doctrine/annotations": "~1.7", "doctrine/cache": "~1.0", "paragonie/sodium_compat": "^1.8", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/asset": "^3.4|^4.0|^5.0", "symfony/browser-kit": "^4.3|^5.0", "symfony/console": "^4.3.4|^5.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dom-crawler": "^4.3|^5.0", "symfony/dotenv": "^4.3.6|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/form": "^4.3.5|^5.0", "symfony/http-client": "^4.4|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/mailer": "^4.4|^5.0", "symfony/messenger": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/property-info": "^3.4|^4.0|^5.0", "symfony/security-csrf": "^3.4|^4.0|^5.0", "symfony/security-http": "^3.4|^4.0|^5.0", "symfony/serializer": "^4.4|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.4|^5.0", "symfony/twig-bundle": "^4.4|^5.0", "symfony/validator": "^4.4|^5.0", "symfony/web-link": "^4.4|^5.0", "symfony/workflow": "^4.3.6|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0", "twig/twig": "^1.41|^2.10|^3.0"}, "suggest": {"ext-apcu": "For best performance of the system caches", "symfony/console": "For using the console commands", "symfony/form": "For using forms", "symfony/property-info": "For using the property_info service", "symfony/serializer": "For using the serializer service", "symfony/validator": "For using validation", "symfony/web-link": "For using web links, features such as preloading, prefetching or prerendering", "symfony/yaml": "For using the debug:config and lint:yaml commands"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony FrameworkBundle", "homepage": "https://symfony.com", "time": "2020-02-28T13:15:16+00:00"}, {"name": "symfony/http-foundation", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "7e41b4fcad4619535f45f8bfa7744c4f384e1648"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/7e41b4fcad4619535f45f8bfa7744c4f384e1648", "reference": "7e41b4fcad4619535f45f8bfa7744c4f384e1648", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"predis/predis": "~1.0", "symfony/expression-language": "^3.4|^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "time": "2020-02-13T19:40:01+00:00"}, {"name": "symfony/http-kernel", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "8c8734486dada83a6041ab744709bdc1651a8462"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/8c8734486dada83a6041ab744709bdc1651a8462", "reference": "8c8734486dada83a6041ab744709bdc1651a8462", "shasum": ""}, "require": {"php": "^7.1.3", "psr/log": "~1.0", "symfony/error-handler": "^4.4", "symfony/event-dispatcher": "^4.4", "symfony/http-foundation": "^4.4|^5.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9"}, "conflict": {"symfony/browser-kit": "<4.3", "symfony/config": "<3.4", "symfony/console": ">=5", "symfony/dependency-injection": "<4.3", "symfony/translation": "<4.2", "twig/twig": "<1.34|<2.4,>=2"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "~1.0", "symfony/browser-kit": "^4.3|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^1.34|^2.4|^3.0"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpKernel Component", "homepage": "https://symfony.com", "time": "2020-02-29T10:31:38+00:00"}, {"name": "symfony/inflector", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/inflector.git", "reference": "f419ab2853cc00471ffd7fc18e544b5f5a90adb1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/inflector/zipball/f419ab2853cc00471ffd7fc18e544b5f5a90adb1", "reference": "f419ab2853cc00471ffd7fc18e544b5f5a90adb1", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Inflector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Inflector Component", "homepage": "https://symfony.com", "keywords": ["inflection", "pluralize", "singularize", "string", "symfony", "words"], "time": "2020-01-04T13:00:46+00:00"}, {"name": "symfony/intl", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/intl.git", "reference": "f675f139e20b9e0ff05bac662c081fe9ef7b2f88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/intl/zipball/f675f139e20b9e0ff05bac662c081fe9ef7b2f88", "reference": "f675f139e20b9e0ff05bac662c081fe9ef7b2f88", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-intl-icu": "~1.0"}, "require-dev": {"symfony/filesystem": "^3.4|^4.0|^5.0"}, "suggest": {"ext-intl": "to use the component with locales other than \"en\""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Intl\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A PHP replacement layer for the C intl extension that includes additional data from the ICU library.", "homepage": "https://symfony.com", "keywords": ["i18n", "icu", "internationalization", "intl", "l10n", "localization"], "time": "2020-02-04T09:32:40+00:00"}, {"name": "symfony/mailer", "version": "v4.4.17", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "ee9d1337d46d46d9022da7711c2f80614e3d9cda"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/ee9d1337d46d46d9022da7711c2f80614e3d9cda", "reference": "ee9d1337d46d46d9022da7711c2f80614e3d9cda", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10", "php": ">=7.1.3", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.3", "symfony/mime": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"symfony/http-kernel": "<4.4", "symfony/sendgrid-mailer": "<4.4"}, "require-dev": {"symfony/amazon-mailer": "^4.4|^5.0", "symfony/google-mailer": "^4.4|^5.0", "symfony/http-client-contracts": "^1.1|^2", "symfony/mailchimp-mailer": "^4.4|^5.0", "symfony/mailgun-mailer": "^4.4|^5.0", "symfony/messenger": "^4.4|^5.0", "symfony/postmark-mailer": "^4.4|^5.0", "symfony/sendgrid-mailer": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Mailer Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-28T20:42:29+00:00"}, {"name": "symfony/messenger", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/messenger.git", "reference": "7efb10dda368b6f056082eefa0d6dc9ee671ccef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/messenger/zipball/7efb10dda368b6f056082eefa0d6dc9ee671ccef", "reference": "7efb10dda368b6f056082eefa0d6dc9ee671ccef", "shasum": ""}, "require": {"php": "^7.1.3", "psr/log": "~1.0"}, "conflict": {"doctrine/persistence": "<1.3", "symfony/event-dispatcher": "<4.3", "symfony/framework-bundle": "<4.4", "symfony/http-kernel": "<4.4"}, "require-dev": {"doctrine/dbal": "^2.6", "doctrine/persistence": "^1.3", "psr/cache": "~1.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4.19|^4.1.8|^5.0", "symfony/event-dispatcher": "^4.3|^5.0", "symfony/http-kernel": "^4.4", "symfony/process": "^3.4|^4.0|^5.0", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/serializer": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/validator": "^3.4|^4.0|^5.0"}, "suggest": {"enqueue/messenger-adapter": "For using the php-enqueue library as a transport."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Messenger Component", "homepage": "https://symfony.com", "time": "2020-02-24T13:12:43+00:00"}, {"name": "symfony/mime", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "304db017bafd71c122cd5223a9ac2d03dc24da32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/304db017bafd71c122cd5223a9ac2d03dc24da32", "reference": "304db017bafd71c122cd5223a9ac2d03dc24da32", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10", "symfony/dependency-injection": "^3.4|^4.1|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A library to manipulate MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "time": "2020-02-04T09:32:40+00:00"}, {"name": "symfony/monolog-bridge", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bridge.git", "reference": "b14ee2f0488b3ad879fd000bc3dcce1cd2f6cfff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bridge/zipball/b14ee2f0488b3ad879fd000bc3dcce1cd2f6cfff", "reference": "b14ee2f0488b3ad879fd000bc3dcce1cd2f6cfff", "shasum": ""}, "require": {"monolog/monolog": "^1.25.1", "php": "^7.1.3", "symfony/http-kernel": "^4.3", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"symfony/console": "<3.4", "symfony/http-foundation": "<3.4"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0", "symfony/http-client": "^4.4|^5.0", "symfony/security-core": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/console": "For the possibility to show log messages in console commands depending on verbosity settings.", "symfony/http-kernel": "For using the debugging handlers together with the response life cycle of the HTTP kernel.", "symfony/var-dumper": "For using the debugging handlers like the console handler or the log server handler."}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Monolog\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Monolog Bridge", "homepage": "https://symfony.com", "time": "2020-02-04T09:32:40+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "dd80460fcfe1fa2050a7103ad818e9d0686ce6fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/dd80460fcfe1fa2050a7103ad818e9d0686ce6fd", "reference": "dd80460fcfe1fa2050a7103ad818e9d0686ce6fd", "shasum": ""}, "require": {"monolog/monolog": "~1.22 || ~2.0", "php": ">=5.6", "symfony/config": "~3.4 || ~4.0 || ^5.0", "symfony/dependency-injection": "~3.4.10 || ^4.0.10 || ^5.0", "symfony/http-kernel": "~3.4 || ~4.0 || ^5.0", "symfony/monolog-bridge": "~3.4 || ~4.0 || ^5.0"}, "require-dev": {"symfony/console": "~3.4 || ~4.0 || ^5.0", "symfony/phpunit-bridge": "^3.4.19 || ^4.0 || ^5.0", "symfony/yaml": "~3.4 || ~4.0 || ^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "http://symfony.com", "keywords": ["log", "logging"], "time": "2019-11-13T13:11:14+00:00"}, {"name": "symfony/options-resolver", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "9a02d6662660fe7bfadad63b5f0b0718d4c8b6b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/9a02d6662660fe7bfadad63b5f0b0718d4c8b6b0", "reference": "9a02d6662660fe7bfadad63b5f0b0718d4c8b6b0", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony OptionsResolver Component", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "time": "2020-01-04T13:00:46+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.14.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "727b3bb5bfa7ca9eeb86416784cf1c08a6289b86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/727b3bb5bfa7ca9eeb86416784cf1c08a6289b86", "reference": "727b3bb5bfa7ca9eeb86416784cf1c08a6289b86", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/intl": "~2.3|~3.0|~4.0|~5.0"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.14-dev"}}, "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "time": "2020-01-13T11:15:53+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.14.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "6842f1a39cf7d580655688069a03dd7cd83d244a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/6842f1a39cf7d580655688069a03dd7cd83d244a", "reference": "6842f1a39cf7d580655688069a03dd7cd83d244a", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.14-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "time": "2020-01-17T12:01:36+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.14.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "34094cfa9abe1f0f14f48f490772db7a775559f2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/34094cfa9abe1f0f14f48f490772db7a775559f2", "reference": "34094cfa9abe1f0f14f48f490772db7a775559f2", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.14-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2020-01-13T11:15:53+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.14.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "46ecacf4751dd0dc81e4f6bf01dbf9da1dc1dadf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/46ecacf4751dd0dc81e4f6bf01dbf9da1dc1dadf", "reference": "46ecacf4751dd0dc81e4f6bf01dbf9da1dc1dadf", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.14-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-01-13T11:15:53+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.14.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "5e66a0fa1070bf46bec4bea7962d285108edd675"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/5e66a0fa1070bf46bec4bea7962d285108edd675", "reference": "5e66a0fa1070bf46bec4bea7962d285108edd675", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.14-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-01-13T11:15:53+00:00"}, {"name": "symfony/process", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "bf9166bac906c9e69fb7a11d94875e7ced97bcd7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/bf9166bac906c9e69fb7a11d94875e7ced97bcd7", "reference": "bf9166bac906c9e69fb7a11d94875e7ced97bcd7", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2020-02-07T20:06:44+00:00"}, {"name": "symfony/property-access", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "090b4bc92ded1ec512f7e2ed1691210769dffdb3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/090b4bc92ded1ec512f7e2ed1691210769dffdb3", "reference": "090b4bc92ded1ec512f7e2ed1691210769dffdb3", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/inflector": "^3.4|^4.0|^5.0"}, "require-dev": {"symfony/cache": "^3.4|^4.0|^5.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony PropertyAccess Component", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property path", "reflection"], "time": "2020-01-04T13:00:46+00:00"}, {"name": "symfony/property-info", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "e6355ba81c738be31c3c3b3cd7929963f98da576"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/e6355ba81c738be31c3c3b3cd7929963f98da576", "reference": "e6355ba81c738be31c3c3b3cd7929963f98da576", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/inflector": "^3.4|^4.0|^5.0"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.0||>=3.2.0,<3.2.2", "phpdocumentor/type-resolver": "<0.3.0", "symfony/dependency-injection": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.7", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/serializer": "^3.4|^4.0|^5.0"}, "suggest": {"phpdocumentor/reflection-docblock": "To use the PHPDoc", "psr/cache-implementation": "To cache results", "symfony/doctrine-bridge": "To use Doctrine metadata", "symfony/serializer": "To use Serializer metadata"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Property Info Component", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "time": "2020-01-04T13:00:46+00:00"}, {"name": "symfony/routing", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "4124d621d0e445732520037f888a0456951bde8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/4124d621d0e445732520037f888a0456951bde8c", "reference": "4124d621d0e445732520037f888a0456951bde8c", "shasum": ""}, "require": {"php": "^7.1.3"}, "conflict": {"symfony/config": "<4.2", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.2", "psr/log": "~1.0", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Routing Component", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "time": "2020-02-25T12:41:09+00:00"}, {"name": "symfony/security-bundle", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/security-bundle.git", "reference": "21c4270b37f76e6c3c22f92597f397593ed1c9db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-bundle/zipball/21c4270b37f76e6c3c22f92597f397593ed1c9db", "reference": "21c4270b37f76e6c3c22f92597f397593ed1c9db", "shasum": ""}, "require": {"ext-xml": "*", "php": "^7.1.3", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4", "symfony/security-core": "^4.4", "symfony/security-csrf": "^4.2|^5.0", "symfony/security-guard": "^4.2|^5.0", "symfony/security-http": "^4.4.5"}, "conflict": {"symfony/browser-kit": "<4.2", "symfony/console": "<3.4", "symfony/framework-bundle": "<4.4", "symfony/ldap": "<4.4", "symfony/twig-bundle": "<4.4"}, "require-dev": {"doctrine/doctrine-bundle": "^1.5|^2.0", "symfony/asset": "^3.4|^4.0|^5.0", "symfony/browser-kit": "^4.2|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/form": "^3.4|^4.0|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/serializer": "^4.4|^5.0", "symfony/translation": "^3.4|^4.0|^5.0", "symfony/twig-bridge": "^3.4|^4.0|^5.0", "symfony/twig-bundle": "^4.4|^5.0", "symfony/validator": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0", "twig/twig": "^1.41|^2.10|^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SecurityBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony SecurityBundle", "homepage": "https://symfony.com", "time": "2020-02-26T10:27:30+00:00"}, {"name": "symfony/security-core", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/security-core.git", "reference": "6251c8e432640106e6f2f045ac3b365f1af36d44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-core/zipball/6251c8e432640106e6f2f045ac3b365f1af36d44", "reference": "6251c8e432640106e6f2f045ac3b365f1af36d44", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/event-dispatcher-contracts": "^1.1|^2", "symfony/service-contracts": "^1.1.6|^2"}, "conflict": {"symfony/event-dispatcher": "<4.3|>=5", "symfony/ldap": "<4.4", "symfony/security-guard": "<4.3"}, "require-dev": {"psr/container": "^1.0", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.3", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/ldap": "^4.4|^5.0", "symfony/validator": "^3.4.31|^4.3.4|^5.0"}, "suggest": {"psr/container-implementation": "To instantiate the Security class", "symfony/event-dispatcher": "", "symfony/expression-language": "For using the expression voter", "symfony/http-foundation": "", "symfony/ldap": "For using LDAP integration", "symfony/validator": "For using the user password constraint"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Security\\Core\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Core Library", "homepage": "https://symfony.com", "time": "2020-02-24T13:10:00+00:00"}, {"name": "symfony/security-csrf", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/security-csrf.git", "reference": "da4664d94164e2b50ce75f2453724c6c33222505"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-csrf/zipball/da4664d94164e2b50ce75f2453724c6c33222505", "reference": "da4664d94164e2b50ce75f2453724c6c33222505", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/security-core": "^3.4|^4.0|^5.0"}, "conflict": {"symfony/http-foundation": "<3.4"}, "require-dev": {"symfony/http-foundation": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/http-foundation": "For using the class SessionTokenStorage."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Security\\Csrf\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - CSRF Library", "homepage": "https://symfony.com", "time": "2020-01-04T13:00:46+00:00"}, {"name": "symfony/security-guard", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/security-guard.git", "reference": "7dcc99e3fa89387a1914496118b4aa0927f282e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-guard/zipball/7dcc99e3fa89387a1914496118b4aa0927f282e1", "reference": "7dcc99e3fa89387a1914496118b4aa0927f282e1", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/security-core": "^3.4.22|^4.2.3|^5.0", "symfony/security-http": "^4.4.1"}, "require-dev": {"psr/log": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Security\\Guard\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Guard", "homepage": "https://symfony.com", "time": "2020-02-24T13:10:00+00:00"}, {"name": "symfony/security-http", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/security-http.git", "reference": "c38fe1cd72360dfaa39c82fcb9e4666b041f8834"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-http/zipball/c38fe1cd72360dfaa39c82fcb9e4666b041f8834", "reference": "c38fe1cd72360dfaa39c82fcb9e4666b041f8834", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/security-core": "^4.4"}, "conflict": {"symfony/event-dispatcher": ">=5", "symfony/security-csrf": "<3.4.11|~4.0,<4.0.11"}, "require-dev": {"psr/log": "~1.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/security-csrf": "^3.4.11|^4.0.11|^5.0"}, "suggest": {"symfony/routing": "For using the HttpUtils class to create sub-requests, redirect the user, and match URLs", "symfony/security-csrf": "For using tokens to protect authentication/logout attempts"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Security\\Http\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - HTTP Integration", "homepage": "https://symfony.com", "time": "2020-02-26T10:27:30+00:00"}, {"name": "symfony/serializer", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "bc76d242fea5ff09c3ca34c90f3bb9341a2470a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/bc76d242fea5ff09c3ca34c90f3bb9341a2470a4", "reference": "bc76d242fea5ff09c3ca34c90f3bb9341a2470a4", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"phpdocumentor/type-resolver": "<0.2.1", "symfony/dependency-injection": "<3.4", "symfony/property-access": "<3.4", "symfony/property-info": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "phpdocumentor/reflection-docblock": "^3.2|^4.0", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "^4.4|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/property-info": "^3.4.13|~4.0|^5.0", "symfony/validator": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader and metadata cache.", "psr/cache-implementation": "For using the metadata cache.", "symfony/config": "For using the XML mapping loader.", "symfony/http-foundation": "For using a MIME type guesser within the DataUriNormalizer.", "symfony/property-access": "For using the ObjectNormalizer.", "symfony/property-info": "To deserialize relations.", "symfony/yaml": "For using the default YAML mapping loader."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Serializer Component", "homepage": "https://symfony.com", "time": "2020-02-29T10:05:28+00:00"}, {"name": "symfony/serializer-pack", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/symfony/serializer-pack.git", "reference": "c5f18ba4ff989a42d7d140b7f85406e77cd8c4b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer-pack/zipball/c5f18ba4ff989a42d7d140b7f85406e77cd8c4b2", "reference": "c5f18ba4ff989a42d7d140b7f85406e77cd8c4b2", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "php": "^7.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/property-access": "*", "symfony/property-info": "*", "symfony/serializer": "*"}, "type": "symfony-pack", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A pack for the Symfony serializer", "time": "2018-12-10T12:14:14+00:00"}, {"name": "symfony/service-contracts", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "144c5e51266b281231e947b51223ba14acf1a749"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/144c5e51266b281231e947b51223ba14acf1a749", "reference": "144c5e51266b281231e947b51223ba14acf1a749", "shasum": ""}, "require": {"php": "^7.2.5", "psr/container": "^1.0"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2019-11-18T17:27:11+00:00"}, {"name": "symfony/stopwatch", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "abc08d7c48987829bac301347faa10f7e8bbf4fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/abc08d7c48987829bac301347faa10f7e8bbf4fb", "reference": "abc08d7c48987829bac301347faa10f7e8bbf4fb", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/service-contracts": "^1.0|^2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Stopwatch Component", "homepage": "https://symfony.com", "time": "2020-01-04T13:00:46+00:00"}, {"name": "symfony/translation", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "0a19a77fba20818a969ef03fdaf1602de0546353"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/0a19a77fba20818a969ef03fdaf1602de0546353", "reference": "0a19a77fba20818a969ef03fdaf1602de0546353", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "time": "2020-02-04T09:32:40+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "8cc682ac458d75557203b2f2f14b0b92e1c744ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/8cc682ac458d75557203b2f2f14b0b92e1c744ed", "reference": "8cc682ac458d75557203b2f2f14b0b92e1c744ed", "shasum": ""}, "require": {"php": "^7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2019-11-18T17:27:11+00:00"}, {"name": "symfony/twig-bridge", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "e76e963d8aeb1370a1236ca237a8028141402fc0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/e76e963d8aeb1370a1236ca237a8028141402fc0", "reference": "e76e963d8aeb1370a1236ca237a8028141402fc0", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^1.41|^2.10|^3.0"}, "conflict": {"symfony/console": "<3.4", "symfony/form": "<4.4", "symfony/http-foundation": "<4.3", "symfony/translation": "<4.2", "symfony/workflow": "<4.3"}, "require-dev": {"egulias/email-validator": "^2.1.10", "symfony/asset": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "^4.4|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/form": "^4.3.5", "symfony/http-foundation": "^4.3|^5.0", "symfony/http-kernel": "^4.4", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/security-acl": "^2.8|^3.0", "symfony/security-core": "^3.0|^4.0|^5.0", "symfony/security-csrf": "^3.4|^4.0|^5.0", "symfony/security-http": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2.1|^5.0", "symfony/web-link": "^4.4|^5.0", "symfony/workflow": "^4.3|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0", "twig/cssinliner-extra": "^2.12", "twig/inky-extra": "^2.12", "twig/markdown-extra": "^2.12"}, "suggest": {"symfony/asset": "For using the AssetExtension", "symfony/expression-language": "For using the ExpressionExtension", "symfony/finder": "", "symfony/form": "For using the FormExtension", "symfony/http-kernel": "For using the HttpKernelExtension", "symfony/routing": "For using the RoutingExtension", "symfony/security-core": "For using the SecurityExtension", "symfony/security-csrf": "For using the CsrfExtension", "symfony/security-http": "For using the LogoutUrlExtension", "symfony/stopwatch": "For using the StopwatchExtension", "symfony/templating": "For using the TwigEngine", "symfony/translation": "For using the TranslationExtension", "symfony/var-dumper": "For using the DumpExtension", "symfony/web-link": "For using the WebLinkExtension", "symfony/yaml": "For using the YamlExtension"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Twig Bridge", "homepage": "https://symfony.com", "time": "2020-02-14T08:28:51+00:00"}, {"name": "symfony/twig-bundle", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "d3e3e46e9e683e946746219570299ba07506260a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/d3e3e46e9e683e946746219570299ba07506260a", "reference": "d3e3e46e9e683e946746219570299ba07506260a", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/http-foundation": "^4.3|^5.0", "symfony/http-kernel": "^4.4", "symfony/polyfill-ctype": "~1.8", "symfony/twig-bridge": "^4.4|^5.0", "twig/twig": "^1.41|^2.10|^3.0"}, "conflict": {"symfony/dependency-injection": "<4.1", "symfony/framework-bundle": "<4.4", "symfony/translation": "<4.2"}, "require-dev": {"doctrine/annotations": "~1.7", "doctrine/cache": "~1.0", "symfony/asset": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^4.2.5|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/form": "^3.4|^4.0|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/web-link": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony TwigBundle", "homepage": "https://symfony.com", "time": "2020-01-04T13:00:46+00:00"}, {"name": "symfony/validator", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "3a3a07fe2f42492eccca6771f1a460c9900cd851"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/3a3a07fe2f42492eccca6771f1a460c9900cd851", "reference": "3a3a07fe2f42492eccca6771f1a460c9900cd851", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1|^2"}, "conflict": {"doctrine/lexer": "<1.0.2", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/intl": "<4.3", "symfony/translation": ">=5.0", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.7", "doctrine/cache": "~1.0", "egulias/email-validator": "^2.1.10", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-client": "^4.3|^5.0", "symfony/http-foundation": "^4.1|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^4.3|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/property-info": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader.", "egulias/email-validator": "Strict (RFC compliant) email validation", "psr/cache-implementation": "For using the mapping cache.", "symfony/config": "", "symfony/expression-language": "For using the Expression validator", "symfony/http-foundation": "", "symfony/intl": "", "symfony/property-access": "For accessing properties within comparison constraints", "symfony/property-info": "To automatically add NotNull and Type constraints", "symfony/translation": "For translating validation errors.", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Validator Component", "homepage": "https://symfony.com", "time": "2020-02-29T10:05:28+00:00"}, {"name": "symfony/var-dumper", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "2572839911702b0405479410ea7a1334bfab0b96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/2572839911702b0405479410ea7a1334bfab0b96", "reference": "2572839911702b0405479410ea7a1334bfab0b96", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^3.4|^4.0|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^1.34|^2.4|^3.0"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "time": "2020-02-24T13:10:00+00:00"}, {"name": "symfony/var-exporter", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "1a76a943f2af334da13bc9f33f49392fa530eec9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/1a76a943f2af334da13bc9f33f49392fa530eec9", "reference": "1a76a943f2af334da13bc9f33f49392fa530eec9", "shasum": ""}, "require": {"php": "^7.1.3"}, "require-dev": {"symfony/var-dumper": "^4.1.1|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A blend of var_export() + serialize() to turn any serializable data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "serialize"], "time": "2020-01-04T13:00:46+00:00"}, {"name": "symfony/workflow", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/workflow.git", "reference": "d0ed681eb73f86123c7e1dbfc530bd1794b93d2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/workflow/zipball/d0ed681eb73f86123c7e1dbfc530bd1794b93d2e", "reference": "d0ed681eb73f86123c7e1dbfc530bd1794b93d2e", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/property-access": "^3.4|^4.3|^5.0"}, "conflict": {"symfony/event-dispatcher": "<4.3|>=5", "symfony/security-core": ">=5"}, "require-dev": {"psr/log": "~1.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^4.3", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/security-core": "^3.4|^4.0", "symfony/validator": "^3.4|^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Workflow\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Workflow Component", "homepage": "https://symfony.com", "keywords": ["petrinet", "place", "state", "statemachine", "transition", "workflow"], "time": "2020-02-24T13:10:00+00:00"}, {"name": "symfony/yaml", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "94d005c176db2080e98825d98e01e8b311a97a88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/94d005c176db2080e98825d98e01e8b311a97a88", "reference": "94d005c176db2080e98825d98e01e8b311a97a88", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2020-02-03T10:46:43+00:00"}, {"name": "twig/twig", "version": "v3.0.3", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "3b88ccd180a6b61ebb517aea3b1a8906762a1dc2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/3b88ccd180a6b61ebb517aea3b1a8906762a1dc2", "reference": "3b88ccd180a6b61ebb517aea3b1a8906762a1dc2", "shasum": ""}, "require": {"php": "^7.2.5", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "time": "2020-02-11T15:33:47+00:00"}, {"name": "webmozart/assert", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/webmozart/assert.git", "reference": "aed98a490f9a8f78468232db345ab9cf606cf598"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/assert/zipball/aed98a490f9a8f78468232db345ab9cf606cf598", "reference": "aed98a490f9a8f78468232db345ab9cf606cf598", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"vimeo/psalm": "<3.6.0"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^7.5.13"}, "type": "library", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2020-02-14T12:15:55+00:00"}, {"name": "white-october/pagerfanta-bundle", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/sampart/WhiteOctoberPagerfantaBundle.git", "reference": "6df560869b5e09a3acf920890ab40598998b30ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sampart/WhiteOctoberPagerfantaBundle/zipball/6df560869b5e09a3acf920890ab40598998b30ae", "reference": "6df560869b5e09a3acf920890ab40598998b30ae", "shasum": ""}, "require": {"pagerfanta/pagerfanta": "^1.1.0|^2.0.0", "php": ">=5.3.3", "symfony/framework-bundle": "~2.3|~3.0|~4.0", "symfony/property-access": "~2.3|~3.0|~4.0", "symfony/translation": "~2.3|~3.0|~4.0", "symfony/twig-bundle": "~2.3|~3.0|~4.0"}, "conflict": {"twig/twig": "<1.34|>=2.0,<2.4"}, "require-dev": {"phpunit/phpunit": "~3.7|~4.0|^5.0", "symfony/symfony": "~2.3|~3.0|~4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"WhiteOctober\\PagerfantaBundle\\": ""}, "exclude-from-classmap": ["Tests/", "TestsProject/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Bundle to use Pagerfanta with Symfony2", "keywords": ["page", "paging"], "abandoned": "babdev/pagerfanta-bundle", "time": "2019-12-02T14:19:37+00:00"}, {"name": "yectep/phpspreadsheet-bundle", "version": "v0.0.6", "source": {"type": "git", "url": "https://github.com/yectep/phpspreadsheet-bundle.git", "reference": "d2cdc11ebfe0289a0688ef768fb7d2941366cb0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yectep/phpspreadsheet-bundle/zipball/d2cdc11ebfe0289a0688ef768fb7d2941366cb0a", "reference": "d2cdc11ebfe0289a0688ef768fb7d2941366cb0a", "shasum": ""}, "require": {"php": ">=5.6", "phpoffice/phpspreadsheet": "~1.1", "symfony/framework-bundle": "~2.7|~3.0|~4.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Yectep\\PhpSpreadsheetBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Chester Li", "email": "<EMAIL>"}], "description": "A Symfony bundle to integrate with PHPOffice's PhpSpreadsheet library", "time": "2019-05-20T06:13:24+00:00"}, {"name": "zendframework/zend-code", "version": "3.4.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-code.git", "reference": "268040548f92c2bfcba164421c1add2ba43abaaa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-code/zipball/268040548f92c2bfcba164421c1add2ba43abaaa", "reference": "268040548f92c2bfcba164421c1add2ba43abaaa", "shasum": ""}, "require": {"php": "^7.1", "zendframework/zend-eventmanager": "^2.6 || ^3.0"}, "conflict": {"phpspec/prophecy": "<1.9.0"}, "require-dev": {"doctrine/annotations": "^1.7", "ext-phar": "*", "phpunit/phpunit": "^7.5.16 || ^8.4", "zendframework/zend-coding-standard": "^1.0", "zendframework/zend-stdlib": "^2.7 || ^3.0"}, "suggest": {"doctrine/annotations": "Doctrine\\Common\\Annotations >=1.0 for annotation features", "zendframework/zend-stdlib": "Zend\\Stdlib component"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4.x-dev", "dev-develop": "3.5.x-dev", "dev-dev-4.0": "4.0.x-dev"}}, "autoload": {"psr-4": {"Zend\\Code\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Extensions to the PHP Reflection API, static code scanning, and code generation", "keywords": ["ZendFramework", "code", "zf"], "abandoned": "laminas/laminas-code", "time": "2019-12-10T19:21:15+00:00"}, {"name": "zendframework/zend-eventmanager", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-eventmanager.git", "reference": "a5e2583a211f73604691586b8406ff7296a946dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-eventmanager/zipball/a5e2583a211f73604691586b8406ff7296a946dd", "reference": "a5e2583a211f73604691586b8406ff7296a946dd", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"athletic/athletic": "^0.1", "container-interop/container-interop": "^1.1.0", "phpunit/phpunit": "^5.7.27 || ^6.5.8 || ^7.1.2", "zendframework/zend-coding-standard": "~1.0.0", "zendframework/zend-stdlib": "^2.7.3 || ^3.0"}, "suggest": {"container-interop/container-interop": "^1.1.0, to use the lazy listeners feature", "zendframework/zend-stdlib": "^2.7.3 || ^3.0, to use the FilterChain feature"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev", "dev-develop": "3.3-dev"}}, "autoload": {"psr-4": {"Zend\\EventManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Trigger and listen to events within a PHP application", "homepage": "https://github.com/zendframework/zend-eventmanager", "keywords": ["event", "eventmanager", "events", "zf2"], "abandoned": "laminas/laminas-eventmanager", "time": "2018-04-25T15:33:34+00:00"}, {"name": "zircote/swagger-php", "version": "2.0.15", "source": {"type": "git", "url": "https://github.com/zircote/swagger-php.git", "reference": "5fd9439cfb76713925e23f206e9db4bf35784683"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zircote/swagger-php/zipball/5fd9439cfb76713925e23f206e9db4bf35784683", "reference": "5fd9439cfb76713925e23f206e9db4bf35784683", "shasum": ""}, "require": {"doctrine/annotations": "*", "php": ">=5.6", "symfony/finder": ">=2.2"}, "require-dev": {"phpunit/phpunit": ">=4.8.35 <=5.6", "squizlabs/php_codesniffer": ">=2.7", "zendframework/zend-form": "<2.8"}, "bin": ["bin/swagger"], "type": "library", "autoload": {"psr-4": {"Swagger\\": "src"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.zircote.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://bfanger.nl"}], "description": "Swagger-PHP - Generate interactive documentation for your RESTful API using phpdoc annotations", "homepage": "https://github.com/zircote/swagger-php/", "keywords": ["api", "json", "rest", "service discovery"], "time": "2020-01-25T14:15:57+00:00"}], "packages-dev": [{"name": "behat/gherkin", "version": "v4.6.2", "source": {"type": "git", "url": "https://github.com/Behat/Gherkin.git", "reference": "51ac4500c4dc30cbaaabcd2f25694299df666a31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Gherkin/zipball/51ac4500c4dc30cbaaabcd2f25694299df666a31", "reference": "51ac4500c4dc30cbaaabcd2f25694299df666a31", "shasum": ""}, "require": {"php": ">=5.3.1"}, "require-dev": {"phpunit/phpunit": "~4.5|~5", "symfony/phpunit-bridge": "~2.7|~3|~4", "symfony/yaml": "~2.3|~3|~4"}, "suggest": {"symfony/yaml": "If you want to parse features, represented in YAML files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-0": {"Behat\\Gherkin": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Gherkin DSL parser for PHP 5.3", "homepage": "http://behat.org/", "keywords": ["BDD", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ber", "DSL", "g<PERSON>kin", "parser"], "time": "2020-03-17T14:03:26+00:00"}, {"name": "codeception/codeception", "version": "4.1.4", "source": {"type": "git", "url": "https://github.com/Codeception/Codeception.git", "reference": "55d8d1d882fa0777e47de17b04c29b3c50fe29e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Codeception/zipball/55d8d1d882fa0777e47de17b04c29b3c50fe29e7", "reference": "55d8d1d882fa0777e47de17b04c29b3c50fe29e7", "shasum": ""}, "require": {"behat/gherkin": "^4.4.0", "codeception/lib-asserts": "^1.0", "codeception/phpunit-wrapper": ">6.0.15 <6.1.0 | ^6.6.1 | ^7.7.1 | ^8.1.1 | ^9.0", "codeception/stub": "^2.0 | ^3.0", "ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "guzzlehttp/psr7": "~1.4", "php": ">=5.6.0 <8.0", "symfony/console": ">=2.7 <6.0", "symfony/css-selector": ">=2.7 <6.0", "symfony/event-dispatcher": ">=2.7 <6.0", "symfony/finder": ">=2.7 <6.0", "symfony/yaml": ">=2.7 <6.0"}, "require-dev": {"codeception/module-asserts": "*@dev", "codeception/module-cli": "*@dev", "codeception/module-db": "*@dev", "codeception/module-filesystem": "*@dev", "codeception/module-phpbrowser": "*@dev", "codeception/specify": "~0.3", "codeception/util-universalframework": "*@dev", "monolog/monolog": "~1.8", "squizlabs/php_codesniffer": "~2.0", "symfony/process": ">=2.7 <6.0", "vlucas/phpdotenv": "^2.0 | ^3.0 | ^4.0"}, "suggest": {"codeception/specify": "BDD-style code blocks", "codeception/verify": "BDD-style assertions", "hoa/console": "For interactive console functionality", "stecman/symfony-console-completion": "For BASH autocompletion", "symfony/phpunit-bridge": "For phpunit-bridge support"}, "bin": ["codecept"], "type": "library", "extra": {"branch-alias": []}, "autoload": {"psr-4": {"Codeception\\": "src/Codeception", "Codeception\\Extension\\": "ext"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://codegyre.com"}], "description": "BDD-style testing framework", "homepage": "http://codeception.com/", "keywords": ["BDD", "TDD", "acceptance testing", "functional testing", "unit testing"], "time": "2020-03-23T17:07:20+00:00"}, {"name": "codeception/lib-asserts", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/Codeception/lib-asserts.git", "reference": "1fdc310bf857c11d3703ae02689a2e482c71c5ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/lib-asserts/zipball/1fdc310bf857c11d3703ae02689a2e482c71c5ab", "reference": "1fdc310bf857c11d3703ae02689a2e482c71c5ab", "shasum": ""}, "require": {"php": ">=7.2.0 <8.0", "phpunit/phpunit": "^8.4 | ^9.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://codegyre.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Assertion methods used by Codeception core and Asserts module", "homepage": "http://codeception.com/", "keywords": ["codeception"], "time": "2020-02-11T10:45:21+00:00"}, {"name": "codeception/lib-innerbrowser", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/Codeception/lib-innerbrowser.git", "reference": "2123542b1325cc349ac68868abe74638bcb32ab6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/lib-innerbrowser/zipball/2123542b1325cc349ac68868abe74638bcb32ab6", "reference": "2123542b1325cc349ac68868abe74638bcb32ab6", "shasum": ""}, "require": {"codeception/codeception": "*@dev", "php": ">=5.6.0 <8.0", "symfony/browser-kit": ">=2.7 <6.0", "symfony/dom-crawler": ">=2.7 <6.0"}, "conflict": {"codeception/codeception": "<4.0"}, "require-dev": {"codeception/util-universalframework": "dev-master"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://codegyre.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Parent library for all Codeception framework modules and PhpBrowser", "homepage": "http://codeception.com/", "keywords": ["codeception"], "time": "2020-02-20T14:46:50+00:00"}, {"name": "codeception/module-asserts", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/Codeception/module-asserts.git", "reference": "87c83ca3ccfbc0d79f5effb57e1f82eeaab0cb3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/module-asserts/zipball/87c83ca3ccfbc0d79f5effb57e1f82eeaab0cb3e", "reference": "87c83ca3ccfbc0d79f5effb57e1f82eeaab0cb3e", "shasum": ""}, "require": {"codeception/codeception": "*@dev", "codeception/lib-asserts": "^1.0.0", "php": ">=5.6.0 <8.0"}, "conflict": {"codeception/codeception": "<4.0"}, "require-dev": {"codeception/util-robohelpers": "dev-master"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Codeception module containing various assertions", "homepage": "http://codeception.com/", "keywords": ["assertions", "asserts", "codeception"], "time": "2019-11-13T17:32:27+00:00"}, {"name": "codeception/module-doctrine2", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/Codeception/module-doctrine2.git", "reference": "bbeb69dadc7ca5e724907e81d35bb392f27b4480"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/module-doctrine2/zipball/bbeb69dadc7ca5e724907e81d35bb392f27b4480", "reference": "bbeb69dadc7ca5e724907e81d35bb392f27b4480", "shasum": ""}, "require": {"codeception/codeception": "4.0.x-dev | ^4.0", "php": ">=5.6.0 <8.0"}, "require-dev": {"codeception/util-robohelpers": "dev-master", "doctrine/annotations": "^1", "doctrine/data-fixtures": "^1", "doctrine/orm": "^2", "ramsey/uuid-doctrine": "^1.5"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "Doctrine2 module for Codeception", "homepage": "http://codeception.com/", "keywords": ["codeception", "doctrine2"], "time": "2019-11-13T17:19:26+00:00"}, {"name": "codeception/module-phpbrowser", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/Codeception/module-phpbrowser.git", "reference": "fbf585c8562e4e4875f351f5392bcb2b1a633cbe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/module-phpbrowser/zipball/fbf585c8562e4e4875f351f5392bcb2b1a633cbe", "reference": "fbf585c8562e4e4875f351f5392bcb2b1a633cbe", "shasum": ""}, "require": {"codeception/codeception": "4.0.x-dev | ^4.0", "codeception/lib-innerbrowser": "^1.0", "guzzlehttp/guzzle": "^6.3.0", "php": ">=5.6.0 <8.0"}, "require-dev": {"codeception/module-rest": "dev-master | ^1.0", "codeception/util-robohelpers": "dev-master"}, "suggest": {"codeception/phpbuiltinserver": "Start and stop PHP built-in web server for your tests"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Codeception module for testing web application over HTTP", "homepage": "http://codeception.com/", "keywords": ["codeception", "functional-testing", "http"], "time": "2019-10-10T14:25:59+00:00"}, {"name": "codeception/module-symfony", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/Codeception/module-symfony.git", "reference": "1211886a16f2caa53ddb294c81f50410fbaceeb8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/module-symfony/zipball/1211886a16f2caa53ddb294c81f50410fbaceeb8", "reference": "1211886a16f2caa53ddb294c81f50410fbaceeb8", "shasum": ""}, "require": {"codeception/codeception": "4.0.x-dev | ^4.0", "codeception/lib-innerbrowser": "dev-master | ^1.0", "php": ">=5.6.0 <8.0"}, "require-dev": {"codeception/module-asserts": "dev-master | ^1.0", "codeception/module-doctrine2": "dev-master | ^1.0", "codeception/module-phpbrowser": "dev-master | ^1.0", "codeception/module-rest": "dev-master | ^1.0", "codeception/module-sequence": "dev-master | ^1.0", "codeception/util-robohelpers": "dev-master", "vlucas/phpdotenv": "^3.6"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Codeception module for Symfony framework", "homepage": "http://codeception.com/", "keywords": ["codeception", "symfony"], "time": "2019-10-11T11:49:53+00:00"}, {"name": "codeception/phpunit-wrapper", "version": "8.1.1", "source": {"type": "git", "url": "https://github.com/Codeception/phpunit-wrapper.git", "reference": "f1370a15e5fe60e7347b1c60642479b923a7ceef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/phpunit-wrapper/zipball/f1370a15e5fe60e7347b1c60642479b923a7ceef", "reference": "f1370a15e5fe60e7347b1c60642479b923a7ceef", "shasum": ""}, "require": {"php": ">=7.2", "phpunit/php-code-coverage": "^7.0", "phpunit/phpunit": "^8.0", "sebastian/comparator": "^3.0", "sebastian/diff": "^3.0"}, "require-dev": {"codeception/specify": "*", "vlucas/phpdotenv": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Codeception\\PHPUnit\\": "src\\"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHPUnit classes used by Codeception", "time": "2019-12-21T16:08:14+00:00"}, {"name": "codeception/stub", "version": "3.6.1", "source": {"type": "git", "url": "https://github.com/Codeception/Stub.git", "reference": "a3ba01414cbee76a1bced9f9b6b169cc8d203880"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Stub/zipball/a3ba01414cbee76a1bced9f9b6b169cc8d203880", "reference": "a3ba01414cbee76a1bced9f9b6b169cc8d203880", "shasum": ""}, "require": {"phpunit/phpunit": "^8.4 | ^9.0"}, "type": "library", "autoload": {"psr-4": {"Codeception\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Flexible Stub wrapper for PHPUnit's Mock Builder", "time": "2020-02-07T18:42:28+00:00"}, {"name": "doctrine/data-fixtures", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/doctrine/data-fixtures.git", "reference": "39e9777c9089351a468f780b01cffa3cb0a42907"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/data-fixtures/zipball/39e9777c9089351a468f780b01cffa3cb0a42907", "reference": "39e9777c9089351a468f780b01cffa3cb0a42907", "shasum": ""}, "require": {"doctrine/common": "^2.11", "doctrine/persistence": "^1.3.3", "php": "^7.2"}, "conflict": {"doctrine/phpcr-odm": "<1.3.0"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "doctrine/coding-standard": "^6.0", "doctrine/dbal": "^2.5.4", "doctrine/mongodb-odm": "^1.3.0", "doctrine/orm": "^2.7.0", "phpunit/phpunit": "^7.0"}, "suggest": {"alcaeus/mongo-php-adapter": "For using MongoDB ODM with PHP 7", "doctrine/mongodb-odm": "For loading MongoDB ODM fixtures", "doctrine/orm": "For loading ORM fixtures", "doctrine/phpcr-odm": "For loading PHPCR ODM fixtures"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\DataFixtures\\": "lib/Doctrine/Common/DataFixtures"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Data Fixtures for all Doctrine Object Managers", "homepage": "http://www.doctrine-project.org", "keywords": ["database"], "time": "2020-01-17T11:11:28+00:00"}, {"name": "doctrine/doctrine-fixtures-bundle", "version": "3.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineFixturesBundle.git", "reference": "8f07fcfdac7f3591f3c4bf13a50cbae05f65ed70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineFixturesBundle/zipball/8f07fcfdac7f3591f3c4bf13a50cbae05f65ed70", "reference": "8f07fcfdac7f3591f3c4bf13a50cbae05f65ed70", "shasum": ""}, "require": {"doctrine/data-fixtures": "^1.3", "doctrine/doctrine-bundle": "^1.11|^2.0", "doctrine/orm": "^2.6.0", "php": "^7.1", "symfony/config": "^3.4|^4.3|^5.0", "symfony/console": "^3.4|^4.3|^5.0", "symfony/dependency-injection": "^3.4|^4.3|^5.0", "symfony/doctrine-bridge": "^3.4|^4.1|^5.0", "symfony/http-kernel": "^3.4|^4.3|^5.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.4", "symfony/phpunit-bridge": "^4.1|^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\FixturesBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony DoctrineFixturesBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["Fixture", "persistence"], "time": "2019-11-13T15:46:58+00:00"}, {"name": "myclabs/deep-copy", "version": "1.9.5", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "b2c28789e80a97badd14145fda39b545d83ca3ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/b2c28789e80a97badd14145fda39b545d83ca3ef", "reference": "b2c28789e80a97badd14145fda39b545d83ca3ef", "shasum": ""}, "require": {"php": "^7.1"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "time": "2020-01-17T21:11:47+00:00"}, {"name": "nikic/php-parser", "version": "v4.3.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "9a9981c347c5c49d6dfe5cf826bb882b824080dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/9a9981c347c5c49d6dfe5cf826bb882b824080dc", "reference": "9a9981c347c5c49d6dfe5cf826bb882b824080dc", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "0.0.5", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2019-11-08T13:50:10+00:00"}, {"name": "phar-io/manifest", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "phar-io/version": "^2.0", "php": "^5.6 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "time": "2018-07-08T19:23:20+00:00"}, {"name": "phar-io/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/45a2ec53a73c70ce41d55cedef9063630abaf1b6", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "time": "2018-07-08T19:19:57+00:00"}, {"name": "phpspec/prophecy", "version": "v1.10.3", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "451c3cd1418cf640de218914901e51b064abb093"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/451c3cd1418cf640de218914901e51b064abb093", "reference": "451c3cd1418cf640de218914901e51b064abb093", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0|^5.0", "sebastian/comparator": "^1.2.3|^2.0|^3.0|^4.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0|^4.0"}, "require-dev": {"phpspec/phpspec": "^2.5 || ^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2020-03-05T15:02:03+00:00"}, {"name": "phpunit/php-code-coverage", "version": "7.0.10", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "f1884187926fbb755a9aaf0b3836ad3165b478bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/f1884187926fbb755a9aaf0b3836ad3165b478bf", "reference": "f1884187926fbb755a9aaf0b3836ad3165b478bf", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^7.2", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.1.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.2.2", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1.3"}, "require-dev": {"phpunit/phpunit": "^8.2.2"}, "suggest": {"ext-xdebug": "^2.7.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2019-11-20T13:55:58+00:00"}, {"name": "phpunit/php-file-iterator", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "050bedf145a257b1ff02746c31894800e5122946"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/050bedf145a257b1ff02746c31894800e5122946", "reference": "050bedf145a257b1ff02746c31894800e5122946", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2018-09-13T20:33:42+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "1038454804406b0b5f5f520358e78c1c2f71501e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/php-timer/zipball/1038454804406b0b5f5f520358e78c1c2f71501e", "reference": "1038454804406b0b5f5f520358e78c1c2f71501e", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2019-06-07T04:22:29+00:00"}, {"name": "phpunit/php-token-stream", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "995192df77f63a59e47f025390d2d1fdf8f425ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-token-stream/zipball/995192df77f63a59e47f025390d2d1fdf8f425ff", "reference": "995192df77f63a59e47f025390d2d1fdf8f425ff", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "abandoned": true, "time": "2019-09-17T06:23:10+00:00"}, {"name": "phpunit/phpunit", "version": "8.5.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "018b6ac3c8ab20916db85fa91bf6465acb64d1e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/phpunit/zipball/018b6ac3c8ab20916db85fa91bf6465acb64d1e0", "reference": "018b6ac3c8ab20916db85fa91bf6465acb64d1e0", "shasum": ""}, "require": {"doctrine/instantiator": "^1.2.0", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.9.1", "phar-io/manifest": "^1.0.3", "phar-io/version": "^2.0.1", "php": "^7.2", "phpspec/prophecy": "^1.8.1", "phpunit/php-code-coverage": "^7.0.7", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1.2", "sebastian/comparator": "^3.0.2", "sebastian/diff": "^3.0.2", "sebastian/environment": "^4.2.2", "sebastian/exporter": "^3.1.1", "sebastian/global-state": "^3.0.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^2.0.1", "sebastian/type": "^1.1.3", "sebastian/version": "^2.0.1"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*", "phpunit/php-invoker": "^2.0.0"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "8.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2020-01-08T08:49:49+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "time": "2017-03-04T06:30:41+00:00"}, {"name": "sebastian/comparator", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "shasum": ""}, "require": {"php": "^7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2018-07-12T15:12:46+00:00"}, {"name": "sebastian/diff", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "720fcc7e9b5cf384ea68d9d930d480907a0c1a29"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/720fcc7e9b5cf384ea68d9d930d480907a0c1a29", "reference": "720fcc7e9b5cf384ea68d9d930d480907a0c1a29", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "time": "2019-02-04T06:01:07+00:00"}, {"name": "sebastian/environment", "version": "4.2.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "464c90d7bdf5ad4e8a6aea15c091fec0603d4368"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/464c90d7bdf5ad4e8a6aea15c091fec0603d4368", "reference": "464c90d7bdf5ad4e8a6aea15c091fec0603d4368", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2019-11-20T08:46:58+00:00"}, {"name": "sebastian/exporter", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "68609e1261d215ea5b21b7987539cbfbe156ec3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/68609e1261d215ea5b21b7987539cbfbe156ec3e", "reference": "68609e1261d215ea5b21b7987539cbfbe156ec3e", "shasum": ""}, "require": {"php": "^7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2019-09-14T09:02:43+00:00"}, {"name": "sebastian/global-state", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "edf8a461cf1d4005f19fb0b6b8b95a9f7fa0adc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/edf8a461cf1d4005f19fb0b6b8b95a9f7fa0adc4", "reference": "edf8a461cf1d4005f19fb0b6b8b95a9f7fa0adc4", "shasum": ""}, "require": {"php": "^7.2", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^8.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2019-02-01T05:30:01+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/7cfd9e65d11ffb5af41198476395774d4c8a84c5", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5", "shasum": ""}, "require": {"php": "^7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "time": "2017-08-03T12:35:26+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "773f97c67f28de00d397be301821b06708fca0be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/773f97c67f28de00d397be301821b06708fca0be", "reference": "773f97c67f28de00d397be301821b06708fca0be", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "time": "2017-03-29T09:07:27+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2017-03-03T06:23:57+00:00"}, {"name": "sebastian/resource-operations", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "4d7a795d35b889bf80a0cc04e08d77cedfa917a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/4d7a795d35b889bf80a0cc04e08d77cedfa917a9", "reference": "4d7a795d35b889bf80a0cc04e08d77cedfa917a9", "shasum": ""}, "require": {"php": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2018-10-04T04:07:39+00:00"}, {"name": "sebastian/type", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "3aaaa15fa71d27650d62a948be022fe3b48541a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/3aaaa15fa71d27650d62a948be022fe3b48541a3", "reference": "3aaaa15fa71d27650d62a948be022fe3b48541a3", "shasum": ""}, "require": {"php": "^7.2"}, "require-dev": {"phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "time": "2019-07-02T08:10:15+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-10-03T07:35:21+00:00"}, {"name": "symfony/browser-kit", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "090ce406505149d6852a7c03b0346dec3b8cf612"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/090ce406505149d6852a7c03b0346dec3b8cf612", "reference": "090ce406505149d6852a7c03b0346dec3b8cf612", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/dom-crawler": "^3.4|^4.0|^5.0"}, "require-dev": {"symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/http-client": "^4.3|^5.0", "symfony/mime": "^4.3|^5.0", "symfony/process": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony BrowserKit Component", "homepage": "https://symfony.com", "time": "2020-02-23T10:00:59+00:00"}, {"name": "symfony/css-selector", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "d0a6dd288fa8848dcc3d1f58b94de6a7cc5d2d22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/d0a6dd288fa8848dcc3d1f58b94de6a7cc5d2d22", "reference": "d0a6dd288fa8848dcc3d1f58b94de6a7cc5d2d22", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony CssSelector Component", "homepage": "https://symfony.com", "time": "2020-02-04T09:01:01+00:00"}, {"name": "symfony/dom-crawler", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "11dcf08f12f29981bf770f097a5d64d65bce5929"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/11dcf08f12f29981bf770f097a5d64d65bce5929", "reference": "11dcf08f12f29981bf770f097a5d64d65bce5929", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"masterminds/html5": "<2.6"}, "require-dev": {"masterminds/html5": "^2.6", "symfony/css-selector": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DomCrawler Component", "homepage": "https://symfony.com", "time": "2020-02-29T10:05:28+00:00"}, {"name": "symfony/maker-bundle", "version": "v1.14.6", "source": {"type": "git", "url": "https://github.com/symfony/maker-bundle.git", "reference": "bc4df88792fbaaeb275167101dc714218475db5f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/maker-bundle/zipball/bc4df88792fbaaeb275167101dc714218475db5f", "reference": "bc4df88792fbaaeb275167101dc714218475db5f", "shasum": ""}, "require": {"doctrine/inflector": "^1.2", "nikic/php-parser": "^4.0", "php": "^7.0.8", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/filesystem": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/framework-bundle": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^3.4|^4.0|^5.0"}, "require-dev": {"doctrine/doctrine-bundle": "^1.8|^2.0", "doctrine/orm": "^2.3", "friendsofphp/php-cs-fixer": "^2.8", "friendsoftwig/twigcs": "^3.1.2", "symfony/http-client": "^4.3|^5.0", "symfony/phpunit-bridge": "^4.3|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/security-core": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MakerBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Maker helps you create empty commands, controllers, form classes, tests and more so you can forget about writing boilerplate code.", "homepage": "https://symfony.com/doc/current/bundles/SymfonyMakerBundle/index.html", "keywords": ["code generator", "generator", "scaffold", "scaffolding"], "time": "2020-03-04T13:57:29+00:00"}, {"name": "theseer/tokenizer", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "11336f6f84e16a720dae9d8e6ed5019efa85a0f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/11336f6f84e16a720dae9d8e6ed5019efa85a0f9", "reference": "11336f6f84e16a720dae9d8e6ed5019efa85a0f9", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "time": "2019-06-13T22:48:21+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"doctrine/couchdb": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^7.1.3", "ext-ctype": "*", "ext-iconv": "*"}, "platform-dev": [], "plugin-api-version": "1.1.0"}