# In all environments, the following files are loaded if they exist,
# the later taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices/configuration.html#infrastructure-related-configuration
###> symfony/framework-bundle ###
#APP_ENV=dev
APP_SECRET=5bb008fd9b61d3b4518c0fd7c8943f6b
#TRUSTED_PROXIES=127.0.0.1,*********
#TRUSTED_HOSTS='^localhost|example\.com$'
###< symfony/framework-bundle ###
###> doctrine/doctrine-bundle ###
# Format described at http://docs.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# For an SQLite database, use: "sqlite:///%kernel.project_dir%/var/data.db"
# Configure your db driver and server_version in config/packages/doctrine.yaml
#DATABASE_URL=pgsql://postgres:6nuk23@**************:5432/geoscan_cms

#DATABASE_URL=pgsql://postgres:6nuk23@***********:5432/geoscan_cms
#GEOSCAN_DATABASE_URL=pgsql://postgres:6nuk23@***********:5432/gs_main_v5

###< doctrine/doctrine-bundle ###
###> symfony/messenger ###
# MESSENGER_TRANSPORT_DSN=amqp://guest:guest@localhost:5672/%2f/messages
###< symfony/messenger ###
#GEOSCAN_API_BASE_URL=http://geoscan-laravel-api-nginx/
#TF_API_BASE_URL=http://tf-technofarm-api-nginx/

GEOSCAN_API_CLIENT_ID=1
GEOSCAN_API_CLIENT_SECRET=HeswUfret8esTuYe
#ANALYSIS_DIRECTORY=
#COUCH_DB_PASS=6nuk23

#GEOSCAN_APP_BASE_URL='http://appnew.geoscan.info'
###> symfony/mailer ###
#MAILER_DSN=smtp://***********:1025
MAIL_RECOMMENDATION_FROM='<EMAIL>'
MAIL_AGRONOMIST_TO='<EMAIL>'
MAIL_AGRONOMIST_ADDITIONAL_TO='<EMAIL>'
###< symfony/mailer ###

#CONTAINER_NAME=geoscan-cms-api
#EXTERNAL_PORT=8099
#KEY_DATA_VOLUME_NAME=geoscan-key-data

###> sentry/sentry-symfony ###
#SENTRY_CMS_DSN=
###< sentry/sentry-symfony ###

#susi main database data
#SUSI_MAIN_DB_HOST=
#SUSI_MAIN_DB_PORT=
#SUSI_MAIN_DB_USER=
#SUSI_MAIN_DB_PASS=

#gs main database data
# GS_MAIN_DB_HOST=
# GS_MAIN_DB_PORT=
# GS_MAIN_DB_NAME=
# GS_MAIN_DB_USER=
# GS_MAIN_DB_PASS=

#tf main database data
# TF_MAIN_DB_HOST=
# TF_MAIN_DB_PORT=
# TF_MAIN_DB_NAME=
# TF_MAIN_DB_USER=
# TF_MAIN_DB_PASS=

KEYCLOAK_BASE_URL=http://keycloak.geoscan.info:8081/
KEYCLOAK_ALG=RS256
KEYCLOAK_REALM=geotech
KEYCLOAK_CERTS_URI=${KEYCLOAK_BASE_URL}realms/${KEYCLOAK_REALM}/protocol/openid-connect/certs
KEYCLOAK_CLIENT_ID=symfony-idc
KEYCLOAK_CLIENT_SECRET=RV6kFqnkXj0Zuo0LDRp27FmvBPMvWKiV
KEYCLOAK_REDIRECT_URI=http://localhost:8089/connect/check-keycloak


KEYCLOAK_M2M_CLIENT_ID=contracts-backend-m2m
KEYCLOAK_M2M_CLIENT_SECRET=CtkA7JQa0kI0wQ6Lhc425xX3PWp853Po

REDIS_HOST="geoscan-cms-api-redis"
REDIS_PORT="6379"