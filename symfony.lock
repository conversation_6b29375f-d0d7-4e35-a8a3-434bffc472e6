{"beberlei/assert": {"version": "v3.3.1"}, "beberlei/doctrineextensions": {"version": "v1.2.6"}, "behat/gherkin": {"version": "v4.6.2"}, "behat/transliterator": {"version": "v1.3.0"}, "clue/stream-filter": {"version": "v1.5.0"}, "codeception/codeception": {"version": "2.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.3", "ref": "d2a29c0abee586aff913fd45fae1f59b669f4cbe"}, "files": ["codeception.yml", "tests/_data/.gitignore", "tests/_output/.gitignore", "tests/_support/AcceptanceTester.php", "tests/_support/FunctionalTester.php", "tests/_support/Helper/Acceptance.php", "tests/_support/Helper/Functional.php", "tests/_support/Helper/Unit.php", "tests/_support/UnitTester.php", "tests/_support/_generated/.gitignore", "tests/acceptance/.gitignore", "tests/functional/.gitignore", "tests/unit/.gitignore", "tests/acceptance.suite.yml", "tests/functional.suite.yml", "tests/unit.suite.yml"]}, "codeception/lib-asserts": {"version": "1.11.0"}, "codeception/lib-innerbrowser": {"version": "1.3.1"}, "codeception/module-asserts": {"version": "1.1.1"}, "codeception/module-doctrine2": {"version": "1.0.1"}, "codeception/module-phpbrowser": {"version": "1.0.0"}, "codeception/module-symfony": {"version": "1.0.0"}, "codeception/phpunit-wrapper": {"version": "8.1.1"}, "codeception/stub": {"version": "3.6.1"}, "cron/cron": {"version": "1.4.2"}, "cron/cron-bundle": {"version": "2.3.0"}, "doctrine/annotations": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/cache": {"version": "1.10.0"}, "doctrine/collections": {"version": "1.6.4"}, "doctrine/common": {"version": "2.12.0"}, "doctrine/couchdb": {"version": "1.0-dev"}, "doctrine/data-fixtures": {"version": "1.4.2"}, "doctrine/dbal": {"version": "v2.10.1"}, "doctrine/deprecations": {"version": "v0.5.3"}, "doctrine/doctrine-bundle": {"version": "1.12", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.12", "ref": "b11d5292f574a9cd092d506c899d05c79cf4d613"}, "files": ["config/packages/doctrine.yaml", "config/packages/prod/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.0", "ref": "fc52d86631a6dfd9fdf3381d0b7e3df2069e51b3"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "1.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.2", "ref": "c1431086fec31f17fbcfe6d6d7e92059458facc1"}, "files": ["config/packages/doctrine_migrations.yaml", "src/Migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.0"}, "doctrine/inflector": {"version": "1.3.1"}, "doctrine/instantiator": {"version": "1.3.0"}, "doctrine/lexer": {"version": "1.2.0"}, "doctrine/migrations": {"version": "2.2.1"}, "doctrine/orm": {"version": "v2.7.1"}, "doctrine/persistence": {"version": "1.3.6"}, "egulias/email-validator": {"version": "2.1.24"}, "exsyst/swagger": {"version": "v0.4.1"}, "firebase/php-jwt": {"version": "v5.1.0"}, "gedmo/doctrine-extensions": {"version": "v2.4.39"}, "guzzlehttp/guzzle": {"version": "6.5.2"}, "guzzlehttp/promises": {"version": "v1.3.1"}, "guzzlehttp/psr7": {"version": "1.6.1"}, "http-interop/http-factory-guzzle": {"version": "1.0.0"}, "jean85/pretty-package-versions": {"version": "1.6.0"}, "league/period": {"version": "4.9.0"}, "markbaker/complex": {"version": "1.4.7"}, "markbaker/matrix": {"version": "1.2.0"}, "martin-georgiev/postgresql-for-doctrine": {"version": "v1.5.2"}, "monolog/monolog": {"version": "1.25.3"}, "myclabs/deep-copy": {"version": "1.9.5"}, "nelmio/api-doc-bundle": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "3.0", "ref": "c8e0c38e1a280ab9e37587a8fa32b251d5bc1c94"}, "files": ["config/packages/nelmio_api_doc.yaml", "config/routes/nelmio_api_doc.yaml"]}, "nikic/php-parser": {"version": "v4.3.0"}, "ocramius/proxy-manager": {"version": "2.2.3"}, "oro/doctrine-extensions": {"version": "1.3.0"}, "pagerfanta/pagerfanta": {"version": "v2.7.2"}, "phar-io/manifest": {"version": "1.0.3"}, "phar-io/version": {"version": "2.0.1"}, "php": {"version": "7.4"}, "php-http/client-common": {"version": "2.3.0"}, "php-http/discovery": {"version": "1.13.0"}, "php-http/httplug": {"version": "2.2.0"}, "php-http/message": {"version": "1.11.0"}, "php-http/message-factory": {"version": "v1.0.2"}, "php-http/promise": {"version": "1.1.0"}, "phpdocumentor/reflection-common": {"version": "2.0.0"}, "phpdocumentor/reflection-docblock": {"version": "4.3.4"}, "phpdocumentor/type-resolver": {"version": "1.1.0"}, "phpoffice/phpspreadsheet": {"version": "1.11.0"}, "phpunit/php-code-coverage": {"version": "7.0.10"}, "phpunit/php-file-iterator": {"version": "2.0.2"}, "phpunit/php-text-template": {"version": "1.2.1"}, "phpunit/php-timer": {"version": "2.1.2"}, "phpunit/php-token-stream": {"version": "3.1.1"}, "phpunit/phpunit": {"version": "4.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.7", "ref": "00fdb38c318774cd39f475a753028a5e8d25d47c"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.0.0"}, "psr/http-client": {"version": "1.0.1"}, "psr/http-factory": {"version": "1.0.1"}, "psr/http-message": {"version": "1.0.1"}, "psr/log": {"version": "1.1.2"}, "psr/simple-cache": {"version": "1.0.1"}, "ralouphie/getallheaders": {"version": "3.0.3"}, "sebastian/code-unit-reverse-lookup": {"version": "1.0.1"}, "sebastian/comparator": {"version": "3.0.2"}, "sebastian/diff": {"version": "3.0.2"}, "sebastian/environment": {"version": "4.2.3"}, "sebastian/exporter": {"version": "3.1.2"}, "sebastian/global-state": {"version": "3.0.0"}, "sebastian/object-enumerator": {"version": "3.0.3"}, "sebastian/object-reflector": {"version": "1.1.1"}, "sebastian/recursion-context": {"version": "3.0.0"}, "sebastian/resource-operations": {"version": "2.0.1"}, "sebastian/type": {"version": "1.1.3"}, "sebastian/version": {"version": "2.0.1"}, "sensio/framework-extra-bundle": {"version": "5.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["config/packages/sensio_framework_extra.yaml"]}, "sentry/sdk": {"version": "3.1.0"}, "sentry/sentry": {"version": "3.1.0"}, "sentry/sentry-symfony": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "3.0", "ref": "9746f0823302d7980e5273ef7a69ef3f5ac80914"}}, "somnambulist/collection": {"version": "4.0.1"}, "somnambulist/cte-builder": {"version": "2.0.0"}, "stof/doctrine-extensions-bundle": {"version": "1.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.2", "ref": "6c1ceb662f8997085f739cd089bfbef67f245983"}, "files": ["config/packages/stof_doctrine_extensions.yaml"]}, "symfony/amqp-pack": {"version": "v1.0.12"}, "symfony/asset": {"version": "v4.4.5"}, "symfony/browser-kit": {"version": "v4.4.5"}, "symfony/cache": {"version": "v4.4.5"}, "symfony/cache-contracts": {"version": "v2.0.1"}, "symfony/config": {"version": "v4.4.5"}, "symfony/console": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "ea8c0eda34fda57e7d5cd8cbd889e2a387e3472c"}, "files": ["bin/console", "config/bootstrap.php"]}, "symfony/css-selector": {"version": "v4.4.5"}, "symfony/dependency-injection": {"version": "v4.4.5"}, "symfony/deprecation-contracts": {"version": "v2.4.0"}, "symfony/doctrine-bridge": {"version": "v4.4.5"}, "symfony/dom-crawler": {"version": "v4.4.5"}, "symfony/dotenv": {"version": "v4.4.5"}, "symfony/error-handler": {"version": "v4.4.5"}, "symfony/event-dispatcher": {"version": "v4.4.5"}, "symfony/event-dispatcher-contracts": {"version": "v1.1.7"}, "symfony/filesystem": {"version": "v4.4.5"}, "symfony/finder": {"version": "v4.4.5"}, "symfony/flex": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "c0eeb50665f0f77226616b6038a9b06c03752d8e"}, "files": [".env"]}, "symfony/form": {"version": "v4.4.5"}, "symfony/framework-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "23ecaccc551fe2f74baf613811ae529eb07762fa"}, "files": ["config/bootstrap.php", "config/packages/cache.yaml", "config/packages/framework.yaml", "config/packages/test/framework.yaml", "config/routes/dev/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v4.4.20"}, "symfony/http-client-contracts": {"version": "v2.3.1"}, "symfony/http-foundation": {"version": "v4.4.5"}, "symfony/http-kernel": {"version": "v4.4.5"}, "symfony/inflector": {"version": "v4.4.5"}, "symfony/mailer": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "15658c2a0176cda2e7dba66276a2030b52bd81b2"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "8a2675c061737658bed85102e9241c752620e575"}, "files": ["config/packages/messenger.yaml"]}, "symfony/mime": {"version": "v4.4.5"}, "symfony/monolog-bridge": {"version": "v4.4.5"}, "symfony/monolog-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "877bdb4223245783d00ed1f7429aa7ebc606d914"}, "files": ["config/packages/dev/monolog.yaml", "config/packages/prod/monolog.yaml", "config/packages/test/monolog.yaml"]}, "symfony/options-resolver": {"version": "v4.4.5"}, "symfony/polyfill-intl-icu": {"version": "v1.14.0"}, "symfony/polyfill-intl-idn": {"version": "v1.14.0"}, "symfony/polyfill-mbstring": {"version": "v1.14.0"}, "symfony/polyfill-php72": {"version": "v1.14.0"}, "symfony/polyfill-php73": {"version": "v1.14.0"}, "symfony/polyfill-php80": {"version": "v1.22.1"}, "symfony/process": {"version": "v4.4.5"}, "symfony/property-access": {"version": "v4.4.5"}, "symfony/property-info": {"version": "v4.4.5"}, "symfony/psr-http-message-bridge": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.1", "ref": "df8ec1e90aec60d301ec59a22093fc3febedb795"}, "files": ["config/packages/psr_http_message_bridge.yaml"]}, "symfony/routing": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.2", "ref": "683dcb08707ba8d41b7e34adb0344bfd68d248a7"}, "files": ["config/packages/prod/routing.yaml", "config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "7b4408dc203049666fe23fabed23cbadc6d8440f"}, "files": ["config/packages/security.yaml"]}, "symfony/security-core": {"version": "v4.4.5"}, "symfony/security-csrf": {"version": "v4.4.5"}, "symfony/security-guard": {"version": "v4.4.5"}, "symfony/security-http": {"version": "v4.4.5"}, "symfony/serializer": {"version": "v4.4.5"}, "symfony/serializer-pack": {"version": "v1.0.2"}, "symfony/service-contracts": {"version": "v2.0.1"}, "symfony/stopwatch": {"version": "v4.4.5"}, "symfony/translation": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "2ad9d2545bce8ca1a863e50e92141f0b9d87ffcd"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v2.0.1"}, "symfony/twig-bridge": {"version": "v4.4.5"}, "symfony/twig-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "15a41bbd66a1323d09824a189b485c126bbefa51"}, "files": ["config/packages/test/twig.yaml", "config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "d902da3e4952f18d3bf05aab29512eb61cabd869"}, "files": ["config/packages/test/validator.yaml", "config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v4.4.5"}, "symfony/var-exporter": {"version": "v4.4.5"}, "symfony/web-profiler-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "24bbc3d84ef2f427f82104f766014e799eefcc3e"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "symfony/workflow": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "3b2f8ca32a07fcb00f899649053943fa3d8bbfb6"}, "files": ["config/packages/workflow.yaml"]}, "symfony/yaml": {"version": "v4.4.5"}, "theseer/tokenizer": {"version": "1.1.3"}, "twig/twig": {"version": "v3.0.3"}, "webmozart/assert": {"version": "1.7.0"}, "yectep/phpspreadsheet-bundle": {"version": "v0.0.6"}, "zendframework/zend-code": {"version": "3.4.1"}, "zendframework/zend-eventmanager": {"version": "3.2.1"}, "zircote/swagger-php": {"version": "2.0.15"}}