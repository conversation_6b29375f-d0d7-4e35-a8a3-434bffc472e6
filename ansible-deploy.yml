---
- hosts: "servers"
  environment:
      DATABASE_URL: "{{ lookup('env', 'DATABASE_URL') }}"
      GEOSCAN_DATABASE_URL: "{{ lookup('env', 'GEOSCAN_DATABASE_URL') }}"
      GEOSCAN_API_BASE_URL: "{{ lookup('env', 'GEOSCAN_API_BASE_URL') }}"
      COUCH_DB_PASS: "{{ lookup('env', 'COUCH_DB_PASS') }}"
      GEOSCAN_APP_BASE_URL: "{{ lookup('env', 'GEOSCAN_APP_BASE_URL') }}"
      DOCKERHUB_REPO_NAME: "{{ lookup('env', 'DOCKERHUB_REPO_NAME') }}"
      CONTAINER_NAME: "{{ lookup('env', 'CONTAINER_NAME') }}"
      IMAGE_TAG_NAME: "{{ lookup('env', 'IMAGE_TAG_NAME') }}"
      APP_ENV: "{{ lookup('env', 'APP_ENV') }}"
      MAILER_DSN: "{{ lookup('env', '<PERSON><PERSON>ER_DSN') }}"
      SUSI_MAIN_DB_HOST: "{{ lookup('env', 'SUSI_MAIN_DB_HOST') }}"
      SUSI_MAIN_DB_PORT: "{{ lookup('env', 'SUSI_MAIN_DB_PORT') }}"
      SUSI_MAIN_DB_USER: "{{ lookup('env', 'SUSI_MAIN_DB_USER') }}"
      SUSI_MAIN_DB_PASS: "{{ lookup('env', 'SUSI_MAIN_DB_PASS') }}"
      ANALYSIS_DIRECTORY: "{{ lookup('env', 'ANALYSIS_DIRECTORY') }}"
      EXTERNAL_PORT: "{{ lookup('env', 'EXTERNAL_PORT') }}"
      SENTRY_CMS_DSN: "{{ lookup('env', 'SENTRY_CMS_DSN') }}"
      KEY_DATA_VOLUME_NAME: "{{ lookup('env', 'KEY_DATA_VOLUME_NAME') }}"
      KEYCLOAK_BASE_URL: "{{ lookup('env', 'KEYCLOAK_BASE_URL') }}"
      KEYCLOAK_ALG: "{{ lookup('env', 'KEYCLOAK_ALG') }}"
      KEYCLOAK_REALM: "{{ lookup('env', 'KEYCLOAK_REALM') }}"
      KEYCLOAK_CERTS_URI: "{{ lookup('env', 'KEYCLOAK_CERTS_URI') }}"
      TF_API_BASE_URL: "{{ lookup('env', 'TF_API_BASE_URL') }}"
      KEYCLOAK_ADMIN_CLIENT_ID: "{{ lookup('env', 'KEYCLOAK_ADMIN_CLIENT_ID') }}"
      KEYCLOAK_ADMIN_CLIENT_SECRET: "{{ lookup('env', 'KEYCLOAK_ADMIN_CLIENT_SECRET') }}"
      KEYCLOAK_CLIENT_ID: "{{ lookup('env', 'KEYCLOAK_CLIENT_ID') }}"
      KEYCLOAK_CLIENT_SECRET: "{{ lookup('env', 'KEYCLOAK_CLIENT_SECRET') }}"
      KEYCLOAK_M2M_CLIENT_ID: "{{ lookup('env', 'KEYCLOAK_M2M_CLIENT_ID') }}"
      KEYCLOAK_M2M_CLIENT_SECRET: "{{ lookup('env', 'KEYCLOAK_M2M_CLIENT_SECRET') }}"
      TF_MAIN_DB_HOST: "{{ lookup('env', 'TF_MAIN_DB_HOST') }}"
      TF_MAIN_DB_PORT: "{{ lookup('env', 'TF_MAIN_DB_PORT') }}"
      TF_MAIN_DB_NAME: "{{ lookup('env', 'TF_MAIN_DB_NAME') }}"
      TF_MAIN_DB_USER: "{{ lookup('env', 'TF_MAIN_DB_USER') }}"
      TF_MAIN_DB_PASS: "{{ lookup('env', 'TF_MAIN_DB_PASS') }}"
  tasks:
      - name: Log in
        community.docker.docker_login:
            username: "{{ lookup('env', 'DOCKERHUB_USER') }}"
            password: "{{ lookup('env', 'DOCKERHUB_PASSWORD') }}"

      - name: Docker pull
        community.docker.docker_image:
            name: technofarm/geoscan-cms-api:{{ lookup('env', 'IMAGE_TAG_NAME') }}
            source: pull
            force_source: yes

      - name: Copy docker-compose.prod.yml
        copy:
            src: docker-compose.prod.yml
            dest: ~/docker-compose.prod.yml
            mode: 0644

      - name: Copy env.example
        copy:
            src: .env.example
            dest: .env
            mode: 0644

      - name: Create docker volume analyses
        community.docker.docker_volume:
            name: "{{ lookup('env', 'CONTAINER_NAME') }}-analyses"

      - name: DOWN the containers
        community.docker.docker_compose:
            project_name: "{{ lookup('env', 'CONTAINER_NAME') }}"
            project_src: ~/
            state: absent
            files:
                - docker-compose.prod.yml

      - name: Clear app volume
        community.docker.docker_volume:
            name: "{{ lookup('env', 'CONTAINER_NAME') }}"
            state: absent

      - name: UP the containers
        community.docker.docker_compose:
            project_name: "{{ lookup('env', 'CONTAINER_NAME') }}"
            project_src: ~/
            recreate: always
            dependencies: no
            files:
                - docker-compose.prod.yml

      - name: Copy .env in "{{ lookup('env', 'CONTAINER_NAME') }}" container
        command: docker cp .env "{{ lookup('env', 'CONTAINER_NAME') }}":/var/www/html/app

      - name: Clear cache
        community.docker.docker_container_exec:
            container: "{{ lookup('env', 'CONTAINER_NAME') }}"
            command: /bin/sh -c "php bin/console cache:clear"

      - name: Image Prune
        community.docker.docker_prune:
            images: yes

      - name: Delete docker-compose.prod.yml
        ansible.builtin.file:
            path: ~/docker-compose.prod.yml
            state: absent

      - name: Delete .env file
        ansible.builtin.file:
            path: .env
            state: absent
