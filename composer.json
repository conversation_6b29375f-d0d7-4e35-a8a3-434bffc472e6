{"type": "project", "license": "proprietary", "require": {"php": "^7.4", "ext-amqp": "*", "ext-ctype": "*", "ext-iconv": "*", "beberlei/doctrineextensions": "^1.2", "cron/cron-bundle": "^2.2", "doctrine/annotations": "^1.6", "doctrine/couchdb": "@dev", "doctrine/doctrine-bundle": "^2.7", "doctrine/doctrine-migrations-bundle": "^3.1", "doctrine/orm": "^2.11", "firebase/php-jwt": "^6.3", "gedmo/doctrine-extensions": "^3.12", "guzzlehttp/guzzle": "^7.5", "guzzlehttp/psr7": "^2.4", "league/oauth2-client": "^2.6", "league/period": "^4.7", "martin-georgiev/postgresql-for-doctrine": "^1.5", "nelmio/api-doc-bundle": "^3.4", "oro/doctrine-extensions": "^1.2", "pagerfanta/pagerfanta": "^2", "phpdocumentor/reflection-docblock": "^5.3", "phpstan/phpdoc-parser": "^1.6", "sensio/framework-extra-bundle": "^6.2", "sentry/sentry-symfony": "^4.7", "somnambulist/cte-builder": "2.0.0", "stof/doctrine-extensions-bundle": "^1.8", "symfony/amqp-messenger": "5.4.*", "symfony/asset": "5.4.*", "symfony/cache": "5.4.*", "symfony/config": "5.4.*", "symfony/console": "5.4.*", "symfony/dependency-injection": "5.4.*", "symfony/dotenv": "5.4.*", "symfony/event-dispatcher": "5.4.*", "symfony/flex": "^1.1", "symfony/form": "5.4.*", "symfony/framework-bundle": "5.4.*", "symfony/http-kernel": "5.4.*", "symfony/mailer": "5.4.*", "symfony/messenger": "5.4.*", "symfony/monolog-bundle": "^3.6", "symfony/property-access": "^5.4.11", "symfony/property-info": "^5.4", "symfony/psr-http-message-bridge": "^2.1", "symfony/security-bundle": "5.4.*", "symfony/security-core": "5.4.*", "symfony/security-guard": "5.4.*", "symfony/security-http": "5.4.*", "symfony/serializer": "5.4.*", "symfony/translation": "5.4.*", "symfony/twig-bundle": "5.4.*", "symfony/uid": "5.4.*", "symfony/validator": "5.4.*", "symfony/workflow": "5.4.*", "symfony/yaml": "5.4.*", "yectep/phpspreadsheet-bundle": "^1.0.0"}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"symfony/flex": true, "php-http/discovery": true}}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "5.4.*"}}, "require-dev": {"codeception/codeception": "^4.1", "codeception/module-asserts": "^1.0.0", "codeception/module-doctrine2": "^1.0", "codeception/module-phpbrowser": "^1.0.0", "codeception/module-symfony": "^1.0", "doctrine/doctrine-fixtures-bundle": "^3.3", "symfony/maker-bundle": "^1.11", "symfony/stopwatch": "^5.4", "symfony/var-dumper": "4.4.*", "symfony/web-profiler-bundle": "5.4.*"}}