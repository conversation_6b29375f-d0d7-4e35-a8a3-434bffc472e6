-- public.abilities definition

-- Drop table

-- DROP TABLE public.abilities;

CREATE TABLE public.abilities (
	id serial4 NOT NULL,
	"name" varchar(255) NOT NULL,
	title varchar(255) NULL,
	entity_id int4 NULL,
	entity_type varchar(255) NULL,
	only_owned bool DEFAULT false NOT NULL,
	"options" json NULL,
	"scope" int4 NULL,
	created_at timestamp(0) NULL,
	updated_at timestamp(0) NULL,
	CONSTRAINT abilities_pkey PRIMARY KEY (id)
);
CREATE INDEX abilities_scope_index ON public.abilities USING btree (scope);


-- public.api_endpoints definition

-- Drop table

-- DROP TABLE public.api_endpoints;

CREATE TABLE public.api_endpoints (
	id serial4 NOT NULL,
	created_at timestamp(0) NULL,
	updated_at timestamp(0) NULL,
	"class" varchar(255) NOT NULL,
	"method" varchar(255) NOT NULL,
	description text NOT NULL,
	CONSTRAINT api_endpoints_pkey PRIMARY KEY (id)
);


-- public.config_params definition

-- Drop table

-- DROP TABLE public.config_params;

CREATE TABLE public.config_params (
	id serial4 NOT NULL,
	"name" varchar(255) NOT NULL,
	"domain" varchar(63) DEFAULT ''::character varying NOT NULL,
	CONSTRAINT config_params_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX config_params_name_uindex ON public.config_params USING btree (name);


-- public.countries definition

-- Drop table

-- DROP TABLE public.countries;

CREATE TABLE public.countries (
	id serial4 NOT NULL,
	"name" text NOT NULL,
	iso_alpha_2_code varchar(2) NOT NULL,
	iso_alpha_3_code varchar(3) NOT NULL,
	active bool DEFAULT false NULL,
	database_name varchar(255) NULL,
	CONSTRAINT countries_pkey PRIMARY KEY (id)
);


-- public.failed_jobs definition

-- Drop table

-- DROP TABLE public.failed_jobs;

CREATE TABLE public.failed_jobs (
	id bigserial NOT NULL,
	"uuid" varchar(255) NOT NULL,
	"connection" text NOT NULL,
	queue text NOT NULL,
	payload text NOT NULL,
	"exception" text NOT NULL,
	failed_at timestamp(0) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT failed_jobs_pkey PRIMARY KEY (id),
	CONSTRAINT failed_jobs_uuid_unique UNIQUE (uuid)
);


-- public.integrations_address definition

-- Drop table

-- DROP TABLE public.integrations_address;

CREATE TABLE public.integrations_address (
	id serial4 NOT NULL,
	"name" varchar(255) NOT NULL,
	url varchar(255) NOT NULL,
	created_at timestamp(0) NULL,
	updated_at timestamp(0) NULL,
	CONSTRAINT integrations_address_pkey PRIMARY KEY (id)
);


-- public.job_batches definition

-- Drop table

-- DROP TABLE public.job_batches;

CREATE TABLE public.job_batches (
	id varchar(255) NOT NULL,
	"name" varchar(255) NOT NULL,
	total_jobs int4 NOT NULL,
	pending_jobs int4 NOT NULL,
	failed_jobs int4 NOT NULL,
	failed_job_ids text NOT NULL,
	"options" text NULL,
	cancelled_at int4 NULL,
	created_at int4 NOT NULL,
	finished_at int4 NULL,
	CONSTRAINT job_batches_pkey PRIMARY KEY (id)
);


-- public.migrations definition

-- Drop table

-- DROP TABLE public.migrations;

CREATE TABLE public.migrations (
	migration varchar(255) NOT NULL,
	batch int4 NOT NULL
);


-- public.oauth_access_tokens definition

-- Drop table

-- DROP TABLE public.oauth_access_tokens;

CREATE TABLE public.oauth_access_tokens (
	id varchar(100) NOT NULL,
	user_id int4 NULL,
	client_id int4 NOT NULL,
	"name" varchar(255) NULL,
	scopes text NULL,
	revoked bool NOT NULL,
	created_at timestamp(0) NULL,
	updated_at timestamp(0) NULL,
	expires_at timestamp(0) NULL,
	CONSTRAINT oauth_access_tokens_pkey PRIMARY KEY (id)
);
CREATE INDEX oauth_access_tokens_user_id_index ON public.oauth_access_tokens USING btree (user_id);


-- public.oauth_auth_codes definition

-- Drop table

-- DROP TABLE public.oauth_auth_codes;

CREATE TABLE public.oauth_auth_codes (
	id varchar(100) NOT NULL,
	user_id int4 NOT NULL,
	client_id int4 NOT NULL,
	scopes text NULL,
	revoked bool NOT NULL,
	expires_at timestamp(0) NULL,
	CONSTRAINT oauth_auth_codes_pkey PRIMARY KEY (id)
);


-- public.oauth_clients definition

-- Drop table

-- DROP TABLE public.oauth_clients;

CREATE TABLE public.oauth_clients (
	id serial4 NOT NULL,
	user_id int4 NULL,
	"name" varchar(255) NOT NULL,
	secret varchar(100) NOT NULL,
	redirect text NOT NULL,
	personal_access_client bool NOT NULL,
	password_client bool NOT NULL,
	revoked bool NOT NULL,
	created_at timestamp(0) NULL,
	updated_at timestamp(0) NULL,
	CONSTRAINT oauth_clients_pkey PRIMARY KEY (id)
);
CREATE INDEX oauth_clients_user_id_index ON public.oauth_clients USING btree (user_id);


-- public.oauth_personal_access_clients definition

-- Drop table

-- DROP TABLE public.oauth_personal_access_clients;

CREATE TABLE public.oauth_personal_access_clients (
	id serial4 NOT NULL,
	client_id int4 NOT NULL,
	created_at timestamp(0) NULL,
	updated_at timestamp(0) NULL,
	CONSTRAINT oauth_personal_access_clients_pkey PRIMARY KEY (id)
);
CREATE INDEX oauth_personal_access_clients_client_id_index ON public.oauth_personal_access_clients USING btree (client_id);


-- public.oauth_refresh_tokens definition

-- Drop table

-- DROP TABLE public.oauth_refresh_tokens;

CREATE TABLE public.oauth_refresh_tokens (
	id varchar(100) NOT NULL,
	access_token_id varchar(100) NOT NULL,
	revoked bool NOT NULL,
	expires_at timestamp(0) NULL,
	CONSTRAINT oauth_refresh_tokens_pkey PRIMARY KEY (id)
);
CREATE INDEX oauth_refresh_tokens_access_token_id_index ON public.oauth_refresh_tokens USING btree (access_token_id);


-- public.roles definition

-- Drop table

-- DROP TABLE public.roles;

CREATE TABLE public.roles (
	id serial4 NOT NULL,
	"name" varchar(255) NOT NULL,
	title varchar(255) NULL,
	"level" int4 NULL,
	"scope" int4 NULL,
	created_at timestamp(0) NULL,
	updated_at timestamp(0) NULL,
	CONSTRAINT roles_name_unique UNIQUE (name, scope),
	CONSTRAINT roles_pkey PRIMARY KEY (id)
);
CREATE INDEX roles_scope_index ON public.roles USING btree (scope);


-- public.su_permission_role definition

-- Drop table

-- DROP TABLE public.su_permission_role;

CREATE TABLE public.su_permission_role (
	permission_id int4 NULL,
	role_id int4 NULL
);


-- public.su_permissions definition

-- Drop table

-- DROP TABLE public.su_permissions;

CREATE TABLE public.su_permissions (
	id serial4 NOT NULL,
	"name" varchar(255) NULL,
	display_name varchar(255) NULL,
	description varchar(255) NULL,
	created_at timestamp NULL,
	updated_at timestamp NULL
);


-- public.su_role_user definition

-- Drop table

-- DROP TABLE public.su_role_user;

CREATE TABLE public.su_role_user (
	user_id int4 NULL,
	role_id int4 NULL
);


-- public.su_roles definition

-- Drop table

-- DROP TABLE public.su_roles;

CREATE TABLE public.su_roles (
	id int4 DEFAULT nextval('su_roles_1_id_seq'::regclass) NOT NULL,
	"name" varchar(255) NULL,
	display_name varchar(255) NULL,
	description varchar(255) NULL,
	created_at timestamp NULL,
	updated_at timestamp NULL
);


-- public.api_endpoints_abilities definition

-- Drop table

-- DROP TABLE public.api_endpoints_abilities;

CREATE TABLE public.api_endpoints_abilities (
	api_endpoint_id int4 NOT NULL,
	ability_id int4 NOT NULL,
	CONSTRAINT api_endpoints_abilities_ability_id_foreign FOREIGN KEY (ability_id) REFERENCES public.abilities(id) ON DELETE CASCADE,
	CONSTRAINT api_endpoints_abilities_api_endpoint_id_foreign FOREIGN KEY (api_endpoint_id) REFERENCES public.api_endpoints(id) ON DELETE CASCADE
);


-- public.assigned_roles definition

-- Drop table

-- DROP TABLE public.assigned_roles;

CREATE TABLE public.assigned_roles (
	role_id int4 NOT NULL,
	entity_id int4 NOT NULL,
	entity_type varchar(255) NOT NULL,
	restricted_to_id int4 NULL,
	restricted_to_type varchar(255) NULL,
	"scope" int4 NULL,
	CONSTRAINT assigned_roles_role_id_foreign FOREIGN KEY (role_id) REFERENCES public.roles(id) ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE INDEX assigned_roles_entity_index ON public.assigned_roles USING btree (entity_id, entity_type, scope);
CREATE INDEX assigned_roles_role_id_index ON public.assigned_roles USING btree (role_id);
CREATE INDEX assigned_roles_scope_index ON public.assigned_roles USING btree (scope);


-- public.config_params_values definition

-- Drop table

-- DROP TABLE public.config_params_values;

CREATE TABLE public.config_params_values (
	id serial4 NOT NULL,
	config_param_id int4 NOT NULL,
	value varchar NOT NULL,
	country_id int4 NOT NULL,
	CONSTRAINT config_params_values_pkey PRIMARY KEY (id),
	CONSTRAINT config_params_values_config_params_id_fk FOREIGN KEY (config_param_id) REFERENCES public.config_params(id),
	CONSTRAINT config_params_values_countries_id_fk FOREIGN KEY (country_id) REFERENCES public.countries(id)
);


-- public.permissions definition

-- Drop table

-- DROP TABLE public.permissions;

CREATE TABLE public.permissions (
	ability_id int4 NOT NULL,
	entity_id int4 NOT NULL,
	entity_type varchar(255) NOT NULL,
	forbidden bool DEFAULT false NOT NULL,
	"scope" int4 NULL,
	CONSTRAINT permissions_ability_id_foreign FOREIGN KEY (ability_id) REFERENCES public.abilities(id) ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE INDEX permissions_ability_id_index ON public.permissions USING btree (ability_id);
CREATE INDEX permissions_entity_index ON public.permissions USING btree (entity_id, entity_type, scope);
CREATE INDEX permissions_scope_index ON public.permissions USING btree (scope);


-- public.service_providers definition

-- Drop table

-- DROP TABLE public.service_providers;

CREATE TABLE public.service_providers (
	id serial4 NOT NULL,
	"name" varchar(100) NOT NULL,
	slug varchar(20) NOT NULL,
	logo xml NULL,
	country_id int4 NOT NULL,
	company_info jsonb NULL,
	mail_config jsonb NULL,
	couchdb_config jsonb NULL,
	CONSTRAINT service_providers_pkey PRIMARY KEY (id),
	CONSTRAINT service_providers_country_id_foreign FOREIGN KEY (country_id) REFERENCES public.countries(id)
);


-- public.su_users definition

-- Drop table

-- DROP TABLE public.su_users;

CREATE TABLE public.su_users (
	id serial4 NOT NULL,
	username varchar(255) NULL,
	"password" varchar(255) NULL,
	"name" varchar(255) NULL,
	email varchar(255) NULL,
	old_id int4 NULL,
	old_group_id int4 NULL,
	country int4 NULL,
	hash varchar(255) NULL,
	is_superadmin bool NULL,
	active bool DEFAULT true NULL,
	group_id int4 NULL,
	parent_id int4 NULL,
	_lft int4 NULL,
	_rgt int4 NULL,
	service_provider_id int4 NULL,
	keycloak_uid uuid NULL,
	CONSTRAINT su_users_pm PRIMARY KEY (id),
	CONSTRAINT "FK_service_providers_id" FOREIGN KEY (service_provider_id) REFERENCES public.service_providers(id) ON DELETE CASCADE,
	CONSTRAINT country_fk FOREIGN KEY (country) REFERENCES public.countries(id),
	CONSTRAINT su_users_su_users_id_fk FOREIGN KEY (parent_id) REFERENCES public.su_users(id)
);
CREATE UNIQUE INDEX su_users_username_country_unique ON public.su_users USING btree (username, country);


-- public.request_logs definition

-- Drop table

-- DROP TABLE public.request_logs;

CREATE TABLE public.request_logs (
	id serial4 NOT NULL,
	created_at timestamp(0) NULL,
	updated_at timestamp(0) NULL,
	url text NOT NULL,
	"method" varchar(255) NOT NULL,
	ip varchar(255) NOT NULL,
	status int4 NOT NULL,
	request text NOT NULL,
	duration float8 NOT NULL,
	user_id int4 NULL,
	CONSTRAINT request_logs_pkey PRIMARY KEY (id),
	CONSTRAINT request_logs_user_id_foreign FOREIGN KEY (user_id) REFERENCES public.su_users(id) ON DELETE CASCADE
);
CREATE INDEX request_logs_user_id_idx ON public.request_logs USING btree (user_id);