<?php

namespace App\Form\Farm;

use App\Model\Farm\BankInfo;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class BankInfoType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('name', TextType::class)
            ->add('iban', TextType::class)
            ->add('bic', TextType::class)
            ->add('bank_branch', TextType::class)
            ->add('bank_branch_address', TextType::class);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefault('data_class', BankInfo::class);
    }
}
