<?php

namespace App\Form\Farm;

use App\Model\Farm\PostPaymentFields;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class PostPaymentFieldsType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('sender-city', TextType::class, ['property_path' => 'senderCity'])
            ->add('sender-region', TextType::class, ['property_path' => 'senderRegion'])
            ->add('sender-street', TextType::class, ['property_path' => 'senderStreet'])
            ->add('sender-building', TextType::class, ['property_path' => 'senderBuilding'])
            ->add('sender-entrance', TextType::class, ['property_path' => 'senderEntrance'])
            ->add('sender-floor', TextType::class, ['property_path' => 'senderFloor'])
            ->add('sender-appartment', TextType::class, ['property_path' => 'senderAppartment'])
            ->add('sender-post-code', TextType::class, ['property_path' => 'senderPostCode']);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefault('data_class', PostPaymentFields::class);
    }
}
