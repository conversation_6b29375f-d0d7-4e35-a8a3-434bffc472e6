<?php

namespace App\Form\Farm;

use App\Model\Farm\Farm;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FarmType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('uuid', TextType::class, ['required' => true])
            ->add('organizationId', IntegerType::class, ['required' => true])
            ->add('id', IntegerType::class)
            ->add('name', TextType::class)
            ->add('isVisible', CheckboxType::class)
            ->add('isAttached', CheckboxType::class)
            ->add('address', TextType::class)
            ->add('company', TextType::class)
            ->add('company_ekatte', TextType::class)
            ->add('bulstat', TextType::class)
            ->add('company_address', TextType::class)
            ->add('mol', TextType::class)
            ->add('mol_egn', TextType::class)
            ->add('farming_mol_phone', TextType::class)
            ->add('iban_arr', CollectionType::class, [
                'entry_type' => BankInfoType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'allow_extra_fields' => true,
            ])
            ->add('post_payment_fields', PostPaymentFieldsType::class, ['allow_extra_fields' => true])
            ->add('number_of_contracts', IntegerType::class);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefault('data_class', Farm::class);
    }
}
