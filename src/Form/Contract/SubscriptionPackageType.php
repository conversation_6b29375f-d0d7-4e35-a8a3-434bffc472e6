<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Form\Contract;

use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Package;
use App\Entity\ServiceProvider;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class SubscriptionPackageType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('period', IntegerType::class)
            ->add('startDate', DateType::class, ['widget' => 'single_text', 'format' => 'yyyy-MM-dd HH:mm:ss', 'html5' => false])
            ->add('endDate', DateType::class, ['widget' => 'single_text', 'format' => 'yyyy-MM-dd HH:mm:ss', 'html5' => false])
            ->add('package', EntityType::class, [
                'class' => Package::class,
                'choice_value' => 'slug',
            ])
            ->add('amount', NumberType::class)
            ->add('subscriptionPackageFields', CollectionType::class, [
                'entry_type' => SubscriptionPackageFieldType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
            ])
            ->add('serviceProvider', EntityType::class, [
                'class' => ServiceProvider::class,
                'choice_value' => 'id',
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefault('data_class', SubscriptionPackage::class);
    }
}
