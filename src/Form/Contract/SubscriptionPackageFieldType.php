<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Form\Contract;

use App\Entity\Contract\SubscriptionPackageField;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class SubscriptionPackageFieldType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('plotUuid', TextType::class)
            ->add('area', NumberType::class)
            ->add('orderUuid', TextType::class)
            ->add('farmId', NumberType::class);
    }

    public function configureOptions(OptionsResol<PERSON> $resolver)
    {
        $resolver->setDefaults([
            'data_class' => SubscriptionPackageField::class,
            'allow_extra_fields' => true,
        ]);
    }
}
