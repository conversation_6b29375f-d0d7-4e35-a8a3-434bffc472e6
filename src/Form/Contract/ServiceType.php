<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Form\Contract;

use App\Entity\Contract\Service;
use App\Form\ContractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ServiceType extends ContractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        parent::buildForm($builder, $options);
        $builder
            ->add('prices', CollectionType::class, [
                'entry_type' => PriceType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
            ])
            ->add('servicePackages', CollectionType::class, [
                'entry_type' => ServicePackageType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefault('data_class', Service::class);
    }
}
