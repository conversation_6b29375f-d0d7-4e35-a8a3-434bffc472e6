<?php

namespace App\Form\GeoSCAN;

use App\Model\GeoSCAN\UserPermissions;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UserPermissionsType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('organizationId', IntegerType::class)
            ->add('organizationIdentityNumber', TextType::class)
            ->add('userPermissions', CollectionType::class, [
                'entry_type' => PermissionType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefault('data_class', UserPermissions::class);
        $resolver->setDefault('allow_extra_fields', true);
    }
}
