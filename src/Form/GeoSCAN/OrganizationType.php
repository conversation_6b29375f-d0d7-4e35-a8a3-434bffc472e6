<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Form\GeoSCAN;

use App\Form\Farm\FarmType;
use App\Form\GeoSCAN\Organization\AddressType;
use App\Form\GeoSCAN\Organization\ContactPersonType;
use App\Model\GeoSCAN\Organization;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class OrganizationType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('id', IntegerType::class)
            ->add('name', TextType::class)
            ->add('identityNumber', TextType::class)
            ->add('vatNumber', TextType::class)
            ->add('addresses', CollectionType::class, [
                'entry_type' => AddressType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
            ])
            ->add('contactPersons', CollectionType::class, [
                'entry_type' => ContactPersonType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
            ]);

        // Insert users and farms, when create Organizations. When create User or Farms, that will be excluded.
        if (!$options['exclude_users_and_farms']) {
            $builder
                ->add('usersAssigned', CollectionType::class, [
                    'entry_type' => UserType::class,
                    'allow_add' => true,
                    'allow_delete' => true,
                    'by_reference' => false,
                ])
                ->add('farms', CollectionType::class, [
                    'entry_type' => FarmType::class,
                    'allow_add' => true,
                    'allow_delete' => true,
                    'by_reference' => false,
                ]);
        }
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefault('data_class', Organization::class);
        $resolver->setDefault('exclude_users_and_farms', true);
        $resolver->setDefault('allow_extra_fields', true);
    }
}
