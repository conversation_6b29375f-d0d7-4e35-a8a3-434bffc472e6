<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Form\GeoSCAN;

use App\Form\Farm\FarmType;
use App\Model\GeoSCAN\User;
use App\Validator\Constraints\RoleConstraint;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UserType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('id', IntegerType::class)
            ->add('groupId', IntegerType::class)
            ->add('oldId', IntegerType::class)
            ->add('username', TextType::class)
            ->add('password', PasswordType::class)
            ->add('passwordReType', PasswordType::class)
            ->add('name', TextType::class)
            ->add('email', TextType::class)
            ->add('phone', TextType::class)
            ->add('active', CheckboxType::class)
            ->add('profileImage', TextType::class)
            ->add('role', RoleType::class, [
                'required' => true,
                'constraints' => [
                    new RoleConstraint(),
                ],
            ])
            ->add('farms', CollectionType::class, [
                'entry_type' => FarmType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
            ])
            ->add('organizations', CollectionType::class, [
                'entry_type' => OrganizationType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
            ])
            ->add('abilities', CollectionType::class, [
                'entry_type' => AbilityType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
            ])
            ->add('permissions', CollectionType::class, [
                'entry_type' => UserPermissionsType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefault('data_class', User::class);
        $resolver->setDefault('allow_extra_fields', true);
    }
}
