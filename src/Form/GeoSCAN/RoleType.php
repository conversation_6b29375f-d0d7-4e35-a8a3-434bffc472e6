<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>.nonchev
 * Date: 7/30/2019
 * Time: 10:23 AM.
 */

namespace App\Form\GeoSCAN;

use App\Model\GeoSCAN\Role;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class RoleType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('name', TextType::class)
            ->add('title', TextType::class);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefault('data_class', Role::class);
    }
}
