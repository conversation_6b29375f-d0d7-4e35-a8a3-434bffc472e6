<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Form;

use App\Entity\Contract;
use App\Entity\Currency;
use App\Entity\ServiceProvider;
use App\Form\Contract\ResponsibleUserType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResol<PERSON>;

class ContractType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->add('contractDate', DateType::class, ['format' => DateType::HTML5_FORMAT, 'widget' => 'single_text'])
            ->add('startDate', DateType::class, ['widget' => 'single_text', 'format' => 'yyyy-MM-dd HH:mm:ss', 'html5' => false])
            ->add('endDate', DateType::class, ['widget' => 'single_text', 'format' => 'yyyy-MM-dd HH:mm:ss', 'html5' => false])
            ->add('currency', EntityType::class, [
                'class' => Currency::class,
                'choice_value' => 'slug',
            ])
            ->add('parent', EntityType::class, [
                'class' => Contract::class,
                'choice_value' => 'id',
            ])
            ->add('serviceProvider', EntityType::class, [
                'class' => ServiceProvider::class,
                'choice_value' => 'id',
            ])
            ->add('status', TextType::class)
            ->add('area', NumberType::class)
            ->add('customerIdentification', TextType::class)
            ->add('organizationId', IntegerType::class)
            ->add('responsibleUsers', CollectionType::class, [
                'entry_type' => ResponsibleUserType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefault('data_class', Contract::class);
    }
}
