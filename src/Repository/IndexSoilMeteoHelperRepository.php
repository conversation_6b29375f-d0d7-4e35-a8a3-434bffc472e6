<?php

namespace App\Repository;

use App\Entity\Helper\IndexSoilMeteoHelper;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|IndexSoilMeteoHelper find($id, $lockMode = null, $lockVersion = null)
 * @method null|IndexSoilMeteoHelper findOneBy(array $criteria, array $orderBy = null)
 * @method IndexSoilMeteoHelper[] findAll()
 * @method IndexSoilMeteoHelper[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class IndexSoilMeteoHelperRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, IndexSoilMeteoHelper::class);
    }

    public function getContractsData()
    {
        $query = '
        with package as (
            select distinct
                organization_id,
                to_date(split_part(period, \' - \', 1), \'YYYY-MM-DD\') as start_date,
                to_date(split_part(period, \' - \', 2), \'YYYY-MM-DD\') as end_date,
                min(contract_date) as contract_date,
                round(max(area)::numeric, 2) as area,
                json_agg(elements)::jsonb as orders_plots,
                to_char(to_date(split_part(period, \' - \', 1), \'YYYY-MM-DD\'), \'MM-DD\') as start_date_md,
                to_char(to_date(split_part(period, \' - \', 2), \'YYYY-MM-DD\'), \'MM-DD\') as end_date_md,
                age(
                    to_date(split_part(period, \' - \', 2), \'YYYY-MM-DD\') + 1, --including end date
                    to_date(split_part(period, \' - \', 1),  \'YYYY-MM-DD\')
                ) as duration,
                helper_type
            from 
                index_soil_meteo_helper ismh,
                jsonb_array_elements(orders_plots::jsonb) elements
            where 
                (for_delete = false or for_delete is null)
            group by 
                organization_id,
                customer_identification,
                helper_type,
                duration,
                start_date_md,
                end_date_md,
                start_date,
                end_date
        )
        select
            ismh.organization_id as "organizationId",
            ismh.customer_identification as "customerIdentification",
            to_char(min(package.contract_date), \'YYYY-MM-DD\') as "contractDate",
            min(package.start_date) as "startDate",
            max(package.end_date) as "endDate",
            max(package.area) as area,
            case
                when extract(year from package.duration) >= 1 and package.start_date_md = \'10-01\' and package.end_date_md = \'09-30\' then \'farming_year\'
                when extract(year from package.duration) >= 1 then \'year\'
                when extract(month from package.duration) >= 1 then \'month\'
                when extract(day from package.duration) >= 1 then \'day\'
            end as "durationType",
            json_agg(
                distinct json_build_object(
                    \'type\', ismh.helper_type,
                    \'startDate\', package.start_date,
			        \'endDate\', package.end_date,
                    \'area\', package.area,
                    \'ordersPlots\', package.orders_plots
                )::jsonb
            )::jsonb as packages
        from 
            index_soil_meteo_helper ismh, package
        where 
            package.organization_id = ismh.organization_id
            and package.helper_type = ismh.helper_type
            and (for_delete = false or for_delete is null)
        group by 
            ismh.organization_id,
            ismh.customer_identification,
            "durationType",
            package.start_date_md,
            package.end_date_md
        order by
            ismh.organization_id
       ';

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    // /**
    //  * @return IndexSoilMeteoHelper[] Returns an array of IndexSoilMeteoHelper objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('i.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?IndexSoilMeteoHelper
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
