<?php

namespace App\Repository\Log\Recommendation;

use App\Entity\Log\Recommendation\RecommendationElementsResultsLog;
use App\Entity\Recommendation\Recommendation;
use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Query\QueryBuilder;
use Gedmo\Loggable\Entity\Repository\LogEntryRepository;
use Somnambulist\CTEBuilder\Expression;
use Somnambulist\CTEBuilder\ExpressionBuilder;

/**
 * @extends LogEntryRepository
 *
 * @method null|RecommendationElementsResultsLog find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationElementsResultsLog findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationElementsResultsLog[] findAll()
 * @method RecommendationElementsResultsLog[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationElementsResultsLogRepository extends LogEntryRepository
{
    /**
     * Get recommendation elements results history log.
     *
     * @param array $orderBy Associative array where keys are the columns
     *                       and the values specify the order direction
     */
    public function getHistory(Recommendation $recommendation, array $orderBy = ['logged_at' => 'DESC'])
    {
        $conn = $this->getEntityManager()->getConnection();
        $eb = new ExpressionBuilder($conn);

        /** @var QueryBuilder  */
        $recommendationResultsHistoryQb = $eb->createQuery()
            ->select(
                'rerl.logged_at',
                'rerl.object_id AS result_id',
                'NULLIF(rerl."data", \'N;\') AS current_value',
                'LAG(rerl."data", 1) OVER (PARTITION BY rerl.object_id ORDER BY rerl."version") AS previous_value',
                'rerl.username'
            )
            ->from('loggable.recommendation_elements_results_log', 'rerl')
            ->where('rerl.recommendation_id = :recommendationId');

        $recommendationResultsHistoryCTE = new Expression('recommendation_results_history', $recommendationResultsHistoryQb);
        $recommendationResultsHistoryAggQb = $eb->with($recommendationResultsHistoryCTE)
            ->select(
                'TO_CHAR(logged_at, \'yyyy-MM-dd HH:MI:ss\') AS "updatedAt"',
                'JSONB_OBJECT_AGG(
                    result_id,
                    current_value
                ) AS current',
                'JSONB_OBJECT_AGG(
                    result_id,
                    previous_value
                ) AS previous',
                'username as "editorName"'
            )
            ->from('recommendation_results_history')
            ->groupBy([
                'logged_at',
                'username',
            ]);

        foreach ($orderBy as $column => $direction) {
            $recommendationResultsHistoryAggQb->addGroupBy($column);
            $recommendationResultsHistoryAggQb->addOrderBy($column, $direction);
        }

        $recommendationResultsHistoryStmt = $conn->executeQuery(
            $recommendationResultsHistoryAggQb->getSQL(),
            ['recommendationId' => $recommendation->getId()],
            ['recommendationId' => ParameterType::INTEGER]
        );

        $queryResults = $recommendationResultsHistoryStmt->fetchAllAssociative();
        $results = [];

        foreach ($queryResults as $result) {
            $allResultElements = [];
            $updatedAt = $result['updatedAt'];
            $editorName = $result['editorName'];

            if (!$updatedAt || !$editorName) {
                continue;
            }

            $current = json_decode($result['current'], true) ?? [];
            $result['current'] = [];
            foreach ($current as $curr) {
                // Map current resutls
                $unserialized = unserialize($curr);
                $resultElement = $unserialized['resultElement'] ?? null;
                $resultValue = $unserialized['value'] ?? null;

                if (!$resultElement && !$resultValue) {
                    continue;
                }

                $result['current'][$resultElement] = ['key' => $resultElement, 'value' => $resultValue];
                $allResultElements[] = $resultElement;
            }

            $previous = json_decode($result['previous'], true) ?? [];
            $result['previous'] = [];
            foreach ($previous as $prev) {
                // Map previous resutls
                $unserialized = unserialize($prev);
                $resultElement = $unserialized['resultElement'] ?? null;
                $resultValue = $unserialized['value'] ?? null;

                if (!$resultElement && !$resultValue) {
                    continue;
                }

                $result['previous'][$resultElement] = ['key' => $resultElement, 'value' => $resultValue];
                $allResultElements[] = $resultElement;
            }

            $uniqueResultElements = array_unique($allResultElements);
            foreach ($uniqueResultElements as $resultElement) {
                // Add missing result elements from current to previous and vice versa.
                // This is needed when an element is deleted to set its value to null in 'current'
                if (!isset($result['current'][$resultElement])) {
                    $result['current'][$resultElement] = ['key' => $resultElement, 'value' => null];
                }

                if (!isset($result['previous'][$resultElement])) {
                    $result['previous'][$resultElement] = ['key' => $resultElement, 'value' => null];
                }
            }

            $result['current'] = array_values($result['current']);
            $result['previous'] = array_values($result['previous']);

            $resultKey = "{$updatedAt} {$editorName}";
            $results[$resultKey] = $result;
        }

        return $results;
    }

    public function add(RecommendationElementsResultsLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(RecommendationElementsResultsLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
