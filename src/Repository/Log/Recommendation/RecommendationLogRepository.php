<?php

namespace App\Repository\Log\Recommendation;

use App\Entity\AnalysisRecommendationConfig\RecommendationModelsConfig;
use App\Entity\Log\Recommendation\RecommendationLog;
use App\Entity\Package\SamplingType;
use App\Entity\Recommendation\Recommendation;
use App\Repository\AnalysisRecommendationConfig\RecommendationModelsConfigRepository;
use App\Repository\Package\SamplingTypeRepository;
use DateTime;
use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Query\QueryBuilder;
use Gedmo\Loggable\Entity\Repository\LogEntryRepository;

/**
 * @extends LogEntryRepository
 *
 * @method null|RecommendationLog find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationLog findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationLog[] findAll()
 * @method RecommendationLog[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationLogRepository extends LogEntryRepository
{
    /**
     * Get recommendation history log.
     *
     * @param array $orderBy Associative array where keys are the columns
     *                       and the values specify the order direction
     */
    public function getHistory(Recommendation $recommendation, array $orderBy = ['logged_at' => 'DESC'])
    {
        $em = $this->getEntityManager();
        $conn = $em->getConnection();

        /** @var QueryBuilder */
        $recommendationHistoryQb = $conn->createQueryBuilder()
            ->select(
                "TO_CHAR(rl.logged_at, 'yyyy-MM-dd HH:MI:SS') AS \"updatedAt\"",
                'rl."data" AS "current"',
                'LAG(rl."data", 1) OVER (PARTITION BY rl.object_id ORDER BY rl."version") AS "previous"',
                'rl.username AS "editorName"'
            )
            ->from('loggable.recommendations_log', 'rl')
            ->where('rl.object_id = :recommendationId');

        foreach ($orderBy as $column => $direction) {
            $recommendationHistoryQb->addOrderBy($column, $direction);
        }

        $recommendationHistoryStmt = $conn->executeQuery(
            $recommendationHistoryQb->getSQL(),
            ['recommendationId' => $recommendation->getId()],
            ['recommendationId' => ParameterType::INTEGER]
        );

        $queryResults = $recommendationHistoryStmt->fetchAllAssociative();
        $results = [];
        $missingPrevResults = [];

        foreach ($queryResults as $result) {
            $updatedAt = $result['updatedAt'];
            $editorName = $result['editorName'];
            $resultKey = "{$updatedAt} {$editorName}";

            if (!$updatedAt || !$editorName) {
                continue;
            }

            $current = false !== unserialize($result['current']) ? unserialize($result['current']) : [];
            $currentKeys = array_keys($current);
            $result['current'] = [];

            $previous = false !== unserialize($result['previous']) ? unserialize($result['previous']) : [];
            $previousKeys = array_keys($previous);
            $result['previous'] = [];

            foreach ($current as $column => $value) {
                if ($value instanceof DateTime) {
                    $value = $value->format('Y-m-d H:i:s');
                }

                if ('model' === $column) {
                    /** @var RecommendationModelsConfigRepository */
                    $recommendationModelCnfigRepo = $em->getRepository(RecommendationModelsConfig::class);
                    [$model] = $recommendationModelCnfigRepo->findBy($value);
                    $value = $model->getName();
                }

                if ('samplingTypeIds' === $column) {
                    /** @var SamplingTypeRepository */
                    $samplingTypeRepo = $em->getRepository(SamplingType::class);
                    $samplingTypes = $samplingTypeRepo->findBy(['id' => $value]);
                    $value = array_map(function ($samplingType) {
                        return $samplingType->getType();
                    }, $samplingTypes);
                    $column = 'samplingTypes';
                }

                if (!in_array($column, $previousKeys)) {
                    $missingPrevResults[] = [
                        'resultKey' => $resultKey,
                        'updatedAt' => $updatedAt,
                        'column' => $column,
                    ];
                }

                $result['current'][] = ['key' => $column, 'value' => $value];
            }

            $currentKeys = array_keys($current);
            foreach ($previous as $column => $value) {
                if (!in_array($column, $currentKeys)) {
                    continue;
                }

                if ($value instanceof DateTime) {
                    $value = $value->format('Y-m-d H:i:s');
                }

                if ('model' === $column) {
                    /** @var RecommendationModelsConfigRepository */
                    $recommendationModelCnfigRepo = $em->getRepository(RecommendationModelsConfig::class);
                    [$model] = $recommendationModelCnfigRepo->findBy($value);
                    $value = $model->getName();
                }

                if ('samplingTypeIds' === $column) {
                    /** @var SamplingTypeRepository */
                    $samplingTypeRepo = $em->getRepository(SamplingType::class);
                    $samplingTypes = $samplingTypeRepo->findBy(['id' => $value]);
                    $value = array_map(function ($samplingType) {
                        return $samplingType->getType();
                    }, $samplingTypes);
                    $column = 'samplingTypes';
                }

                $result['previous'][] = ['key' => $column, 'value' => $value];
            }

            $results[$resultKey] = $result;
        }

        // Add missing previous values for which there are current values
        foreach ($results as $result) {
            $resultColumns = array_column($result['current'], 'key');

            foreach ($missingPrevResults as $missingResult) {
                if (
                    new DateTime($result['updatedAt']) >= new DateTime($missingResult['updatedAt'])
                    || !in_array($missingResult['column'], $resultColumns)
                ) {
                    continue;
                }

                $missingChange = array_filter($result['current'], function ($currResult) use ($missingResult) {
                    return $currResult['key'] === $missingResult['column'];
                });

                if ($missingChange && count($missingChange) > 0) {
                    $results[$missingResult['resultKey']]['previous'][] = reset($missingChange);
                }
            }
        }

        return $results;
    }

    public function add(RecommendationLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(RecommendationLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
