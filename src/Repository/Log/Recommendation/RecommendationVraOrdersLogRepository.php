<?php

namespace App\Repository\Log\Recommendation;

use App\Entity\Log\Recommendation\RecommendationVraOrdersLog;
use App\Entity\Recommendation\Recommendation;
use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Query\QueryBuilder;
use Gedmo\Loggable\Entity\Repository\LogEntryRepository;
use Somnambulist\CTEBuilder\Expression;
use Somnambulist\CTEBuilder\ExpressionBuilder;

/**
 * @extends LogEntryRepository
 *
 * @method null|RecommendationVraOrdersLog find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationVraOrdersLog findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationVraOrdersLog[] findAll()
 * @method RecommendationVraOrdersLog[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationVraOrdersLogRepository extends LogEntryRepository
{
    /**
     * Get recommendation vra orders history log.
     *
     * @param array $orderBy Associative array where keys are the columns
     *                       and the values specify the order direction
     */
    public function getHistory(Recommendation $recommendation, array $orderBy = ['logged_at' => 'DESC'])
    {
        $conn = $this->getEntityManager()->getConnection();
        $eb = new ExpressionBuilder($conn);

        /** @var QueryBuilder  */
        $recommendationVraHistoryQb = $eb->createQuery()
            ->select(
                'rvol.logged_at',
                'NULLIF(rvol."data", \'N;\') AS current_value',
                'LAG(rvol."data", 1) OVER (PARTITION BY rvol.object_id ORDER BY rvol."version") AS previous_value',
                'rvol.username'
            )
            ->from('loggable.recommendations_vra_orders_log', 'rvol')
            ->where('rvol.recommendation_id = :recommendationId');

        $recommendationVraHistoryCTE = new Expression('recommendation_vra_history', $recommendationVraHistoryQb);
        $recommendationVraHistoryAggQb = $eb->with($recommendationVraHistoryCTE)
            ->select(
                'TO_CHAR(logged_at, \'yyyy-MM-dd HH:MI:ss\') AS "updatedAt"',
                'JSONB_AGG(
                    current_value
                ) AS current',
                'JSONB_AGG(
                    previous_value
                ) AS previous',
                'username as "editorName"'
            )
            ->from('recommendation_vra_history')
            ->groupBy([
                'logged_at',
                'username',
            ]);

        foreach ($orderBy as $column => $direction) {
            $recommendationVraHistoryAggQb->addGroupBy($column);
            $recommendationVraHistoryAggQb->addOrderBy($column, $direction);
        }

        $recommendationVraHistoryStmt = $conn->executeQuery(
            $recommendationVraHistoryAggQb->getSQL(),
            ['recommendationId' => $recommendation->getId()],
            ['recommendationId' => ParameterType::INTEGER]
        );

        $queryResults = $recommendationVraHistoryStmt->fetchAllAssociative();
        $results = [];

        foreach ($queryResults as $result) {
            $updatedAt = $result['updatedAt'];
            $editorName = $result['editorName'];

            if (!$updatedAt || !$editorName) {
                continue;
            }

            $current = json_decode($result['current'], true) ?? [];
            $result['current'] = ['key' => 'vra_orders', 'value' => []];
            foreach ($current as $curr) {
                $unserialized = unserialize($curr);
                if (!isset($unserialized['vraOrderId'], $unserialized['vraOrderType'])) {
                    continue;
                }

                unset($unserialized['recommendation']);
                $result['current']['value'][] = $unserialized;
            }
            $result['current'] = [$result['current']];

            $previous = json_decode($result['previous'], true) ?? [];
            $result['previous'] = ['key' => 'vra_orders', 'value' => []];
            foreach ($previous as $prev) {
                $unserialized = unserialize($prev);
                if (!isset($unserialized['vraOrderId'], $unserialized['vraOrderType'])) {
                    continue;
                }

                unset($unserialized['recommendation']);
                $result['previous']['value'][] = $unserialized;
            }
            $result['previous'] = [$result['previous']];

            $resultKey = "{$updatedAt} {$editorName}";
            $results[$resultKey] = $result;
        }

        return $results;
    }

    public function add(RecommendationVraOrdersLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(RecommendationVraOrdersLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
