<?php

namespace App\Repository\Log\Recommendation;

use App\Entity\Log\Recommendation\RecommendationElementsSuscesLog;
use App\Entity\Recommendation\Recommendation;
use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Query\QueryBuilder;
use Gedmo\Loggable\Entity\Repository\LogEntryRepository;
use Somnambulist\CTEBuilder\Expression;
use Somnambulist\CTEBuilder\ExpressionBuilder;

/**
 * @extends LogEntryRepository
 *
 * @method null|RecommendationElementsSuscesLog find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationElementsSuscesLog findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationElementsSuscesLog[] findAll()
 * @method RecommendationElementsSuscesLog[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationElementsSuscesLogRepository extends LogEntryRepository
{
    /**
     * Get recommendation elements susces history log.
     *
     * @param array $orderBy Associative array where keys are the columns
     *                       and the values specify the order direction
     */
    public function getHistory(Recommendation $recommendation, array $orderBy = ['logged_at' => 'DESC'])
    {
        $conn = $this->getEntityManager()->getConnection();
        $eb = new ExpressionBuilder($conn);

        /** @var QueryBuilder  */
        $recommendationSuscesHistoryQb = $eb->createQuery()
            ->select(
                'resl.logged_at',
                'resl.object_id AS susces_id',
                'NULLIF(resl."data", \'N;\') AS current_value',
                'LAG(resl."data", 1) OVER (PARTITION BY resl.object_id ORDER BY resl."version") AS previous_value',
                'resl.username'
            )
            ->from('loggable.recommendation_elements_susces_log', 'resl')
            ->where('resl.recommendation_id = :recommendationId');

        $recommendationSuscesHistoryCTE = new Expression('recommendation_susces_history', $recommendationSuscesHistoryQb);
        $recommendationSuscesHistoryAggQb = $eb->with($recommendationSuscesHistoryCTE)
            ->select(
                'TO_CHAR(logged_at, \'yyyy-MM-dd HH:MI:ss\') AS "updatedAt"',
                'JSONB_OBJECT_AGG(
                    susces_id,
                    current_value
                ) AS current',
                'JSONB_OBJECT_AGG(
                    susces_id,
                    previous_value
                ) AS previous',
                'username as "editorName"'
            )
            ->from('recommendation_susces_history')
            ->groupBy([
                'logged_at',
                'username',
            ]);

        foreach ($orderBy as $column => $direction) {
            $recommendationSuscesHistoryAggQb->addGroupBy($column);
            $recommendationSuscesHistoryAggQb->addOrderBy($column, $direction);
        }

        $recommendationSuscesHistoryStmt = $conn->executeQuery(
            $recommendationSuscesHistoryAggQb->getSQL(),
            ['recommendationId' => $recommendation->getId()],
            ['recommendationId' => ParameterType::INTEGER]
        );

        $queryResults = $recommendationSuscesHistoryStmt->fetchAllAssociative();
        $results = [];

        foreach ($queryResults as $result) {
            $allElements = [];
            $updatedAt = $result['updatedAt'];
            $editorName = $result['editorName'];

            if (!$updatedAt || !$editorName) {
                continue;
            }

            $current = json_decode($result['current'], true) ?? [];
            $result['current'] = [];
            foreach ($current as $curr) {
                // Map current resutls
                $unserialized = unserialize($curr);
                $element = $unserialized['element'] ?? null;
                $value = $unserialized['value'] ?? null;

                if (!$element && !$value) {
                    continue;
                }

                $result['current'][$element] = ['key' => $element, 'value' => $value];
                $allElements[] = $element;
            }

            $previous = json_decode($result['previous'], true) ?? [];
            $result['previous'] = [];
            foreach ($previous as $prev) {
                // Map previous resutls
                $unserialized = unserialize($prev);
                $element = $unserialized['element'] ?? null;
                $value = $unserialized['value'] ?? null;

                if (!$element && !$value) {
                    continue;
                }

                $result['previous'][$element] = ['key' => $element, 'value' => $value];
                $allElements[] = $element;
            }

            $uniqueElements = array_unique($allElements);
            foreach ($uniqueElements as $element) {
                // Add the missing elements from 'current' to 'previous' and vice versa.
                // This is needed when a record is deleted to set its value to null in 'current'
                if (!isset($result['current'][$element])) {
                    $result['current'][$element] = ['key' => $element, 'value' => null];
                }

                if (!isset($result['previous'][$element])) {
                    $result['previous'][$element] = ['key' => $element, 'value' => null];
                }
            }

            $result['current'] = array_values($result['current']);
            $result['previous'] = array_values($result['previous']);

            $resultKey = "{$updatedAt} {$editorName}";
            $results[$resultKey] = $result;
        }

        return $results;
    }

    public function add(RecommendationElementsSuscesLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(RecommendationElementsSuscesLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
