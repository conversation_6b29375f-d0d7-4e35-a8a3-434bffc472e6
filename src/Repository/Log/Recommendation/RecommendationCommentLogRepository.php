<?php

namespace App\Repository\Log\Recommendation;

use App\Entity\Log\Recommendation\RecommendationCommentLog;
use App\Entity\Recommendation\Recommendation;
use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Query\QueryBuilder;
use Gedmo\Loggable\Entity\Repository\LogEntryRepository;
use Somnambulist\CTEBuilder\Expression;
use Somnambulist\CTEBuilder\ExpressionBuilder;

/**
 * @extends LogEntryRepository
 *
 * @method null|RecommendationCommentLog find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationCommentLog findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationCommentLog[] findAll()
 * @method RecommendationCommentLog[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationCommentLogRepository extends LogEntryRepository
{
    /**
     * Get recommendation comments history log.
     *
     * @param array $orderBy Associative array where keys are the columns
     *                       and the values specify the order direction
     */
    public function getHistory(Recommendation $recommendation, array $orderBy = ['logged_at' => 'DESC'])
    {
        $conn = $this->getEntityManager()->getConnection();
        $eb = new ExpressionBuilder($conn);

        /** @var QueryBuilder  */
        $recommendationCommentHistoryQb = $eb->createQuery()
            ->select(
                'rcl.logged_at',
                'rcl.object_id AS comment_id',
                'NULLIF(rcl."data", \'N;\') AS current_value',
                'LAG(rcl."data", 1) OVER (PARTITION BY rcl.object_id ORDER BY rcl."version") AS previous_value',
                'rcl.username'
            )
            ->from('loggable.recommendation_comments_log', 'rcl')
            ->where('rcl.recommendation_id = :recommendationId');

        $recommendationCommentHistoryCTE = new Expression('recommendation_comments_history', $recommendationCommentHistoryQb);
        $recommendationCommentHistoryAggQb = $eb->with($recommendationCommentHistoryCTE)
            ->select(
                'TO_CHAR(logged_at, \'yyyy-MM-dd HH:MI:ss\') AS "updatedAt"',
                'JSONB_AGG(
                    current_value
                ) AS current',
                'JSONB_AGG(
                    previous_value
                ) AS previous',
                'username as "editorName"'
            )
            ->from('recommendation_comments_history')
            ->groupBy([
                'logged_at',
                'username',
            ]);

        foreach ($orderBy as $column => $direction) {
            $recommendationCommentHistoryAggQb->addGroupBy($column);
            $recommendationCommentHistoryAggQb->addOrderBy($column, $direction);
        }

        $recommendationCommentHistoryStmt = $conn->executeQuery(
            $recommendationCommentHistoryAggQb->getSQL(),
            ['recommendationId' => $recommendation->getId()],
            ['recommendationId' => ParameterType::INTEGER]
        );

        $queryResults = $recommendationCommentHistoryStmt->fetchAllAssociative();
        $results = [];

        foreach ($queryResults as $result) {
            $updatedAt = $result['updatedAt'];
            $editorName = $result['editorName'];

            if (!$updatedAt || !$editorName) {
                continue;
            }

            $current = json_decode($result['current'], true) ?? [];
            $result['current'] = ['key' => 'comments', 'value' => []];
            foreach ($current as $curr) {
                $unserialized = unserialize($curr);
                if (!isset($unserialized['value'])) {
                    continue;
                }

                $result['current']['value'][] = $unserialized['value'];
            }
            $result['current'] = [$result['current']];

            $previous = json_decode($result['previous'], true) ?? [];
            $result['previous'] = ['key' => 'comments', 'value' => []];
            foreach ($previous as $prev) {
                $unserialized = unserialize($prev);
                if (!isset($unserialized['value'])) {
                    continue;
                }

                $result['previous']['value'][] = $unserialized['value'];
            }
            $result['previous'] = [$result['previous']];

            $resultKey = "{$updatedAt} {$editorName}";
            $results[$resultKey] = $result;
        }

        return $results;
    }

    public function add(RecommendationCommentLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(RecommendationCommentLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
