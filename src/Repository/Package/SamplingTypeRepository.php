<?php

namespace App\Repository\Package;

use App\Entity\Package\SamplingType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Exception;

/**
 * @method null|SamplingType find($id, $lockMode = null, $lockVersion = null)
 * @method null|SamplingType findOneBy(array $criteria, array $orderBy = null)
 * @method SamplingType[] findAll()
 * @method SamplingType[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SamplingTypeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SamplingType::class);
    }

    public function validateAllSamplingTypesExist(array $samplingTypeIds = [])
    {
        $existingSamplingTypes = $this->findAll();
        $existingSamplingTypeIds = array_map(function ($st) {
            return $st->getId();
        }, $existingSamplingTypes);

        $nonExistingSamplingTypes = array_diff($samplingTypeIds, $existingSamplingTypeIds);

        if (count($nonExistingSamplingTypes) > 0) {
            $nonExistingSamplingTypesStr = implode(', ', $nonExistingSamplingTypes);

            throw new Exception("The sampling types with ids: {$nonExistingSamplingTypesStr} do not exist!");
        }
    }

    public function getSamplingTypes(array $filter): array
    {
        if (0 === count($filter)) {
            return $this->findAll();
        }

        $params = [];
        $qb = $this->getEntityManager()->getConnection()->createQueryBuilder();

        $qb->select(
            'st.id',
            'st.type'
        )->from('sampling_type', 'st')
            ->join('st', 'package_sampling_type', 'pst', 'pst.sampling_type_id  = st.id')
            ->join('pst', 'package', 'p', 'pst.package_id  = p.id')
            ->join('p', 'subscription_package', 'sp', 'sp.package_id = p.id')
            ->join('sp', 'subscription_package_field', 'spf', 'spf.subscription_package_id  = sp.id');

        if (isset($filter['plot_uuid'])) {
            $qb->andWhere('spf.plot_uuid = :plotUuid');
            $params['plotUuid'] = $filter['plot_uuid'];
        }

        if (isset($filter['from_date'], $filter['to_date'])) {
            $qb->andWhere(':from_date <= date(sp.start_date) OR :to_date >= date(sp.start_date)');
            $params['from_date'] = $filter['from_date'];
            $params['to_date'] = $filter['to_date'];
        }

        if (isset($filter['package_id'])) {
            $qb->andWhere('sp.id = :package_id');
            $params['package_id'] = $filter['package_id'];
        }

        $qb->setParameters(
            $params
        )
            ->groupBy('st.id');

        return $qb->execute()->fetchAll();
    }

    // /**
    //  * @return SamplingType[] Returns an array of SamplingType objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('s.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?SamplingType
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
