<?php

namespace App\Repository\Recommendation;

use App\Entity\Contract\SubscriptionPackageField;
use App\Entity\Recommendation\Recommendation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\ParameterType;
use Doctrine\ORM\Query\Expr;
use Doctrine\Persistence\ManagerRegistry;
use Somnambulist\CTEBuilder\Expression;
use Somnambulist\CTEBuilder\ExpressionBuilder;

/**
 * @method null|Recommendation find($id, $lockMode = null, $lockVersion = null)
 * @method null|Recommendation findOneBy(array $criteria, array $orderBy = null)
 * @method Recommendation[] findAll()
 * @method Recommendation[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Recommendation::class);
    }

    public function getLabElementGroups(int $recommendationId)
    {
        $qb = $this->createQueryBuilder('r');

        $qb->join('App\Entity\Analysis\LabElementGroup', 'leg', Expr\Join::WITH, $qb->expr()->andX(
            $qb->expr()->eq('leg.plotUuid', 'r.plotUuid'),
            $qb->expr()->eq('leg.packageType', 'r.packageType'),
            $qb->expr()->eq('leg.packageId', 'r.package')
        ))
            ->where('r.id = :recommendationId')->setParameter('recommendationId', $recommendationId)
            ->select('leg');

        return $qb->getQuery()->getResult();
    }

    public function getRecommendationContractQb(int $recommendationId = null)
    {
        $qb = $this->createQueryBuilder('r')
            ->leftJoin('App\Entity\Contract\SubscriptionPackageField', 'supf', Expr\Join::WITH, '(supf.plotUuid = r.plotUuid)')
            ->leftJoin('App\Entity\Contract\SubscriptionPackage', 'sup', Expr\Join::WITH, '(supf.subscriptionPackage = sup.id AND r.packageType = \'subscription\' AND r.package = sup.id)')
            ->leftJoin('App\Entity\Contract\Subscription', 'suc', Expr\Join::WITH, '(sup.contract = suc.id)')
            ->leftJoin('App\Entity\Contract\ServicePackageField', 'sepf', Expr\Join::WITH, '(sepf.plotUuid = r.plotUuid)')
            ->leftJoin('App\Entity\Contract\ServicePackage', 'secp', Expr\Join::WITH, '(sepf.servicePackage = secp.id AND r.packageType = \'service\' AND r.package = secp.id)')
            ->leftJoin('App\Entity\Contract\Service', 'sec', Expr\Join::WITH, '(secp.contract = sec.id)')
            ->join('App\Entity\Contract', 'c', Expr\Join::WITH, '(c.id = COALESCE(suc.id, sec.id))');

        if (isset($recommendationId)) {
            $qb->where('r.id = :recommendationId')->setParameter('recommendationId', $recommendationId);
        }

        return $qb;
    }

    public function getRecommendationDate(int $recommendationId)
    {
        $qb = $this->getRecommendationContractQb($recommendationId);
        $qb->select(
            'COALESCE(sup.startDate, c.startDate) as date'
        )->distinct();

        $recommendationDate = $qb->getQuery()->getSingleResult();

        return $recommendationDate['date'];
    }

    /**
     * List recommendations data by concrete plot and farming year.
     *
     * @throws Exception
     *
     * @return array|false|mixed
     */
    public function findRecommendationsByPlot(string $plotUuid, string $fromDate, string $toDate, string $status)
    {
        $qb = $this->getEntityManager()->getConnection()->createQueryBuilder();

        $qb->select(
            'r.id',
            'r.crop_id',
            'r.status',
            'r.target_yield as yield',
            'r.humus',
            'r.valid_from',
            'to_char(r.created_at , \'YYYY-mm-dd\') as created_at',
            'jsonb_agg(
                jsonb_build_object(
                    \'id\', st.id,
                    \'type\', st.type
                ))::jsonb as sampling_types'
        )->from('recommendations', 'r')
            ->join('r', 'subscription_package', 'sp', 'r.package_id = sp.id')
            ->join('r', 'sampling_type', 'st', 'st.id = ANY(r.sampling_type_ids)')
            ->where('r.status = :status')
            ->andWhere('r.plot_uuid = :plotUuid')
            ->andWhere(':from_date <= date(sp.start_date) OR :to_date >= date(sp.start_date)')
            ->setParameters([
                'status' => $status,
                'plotUuid' => $plotUuid,
                'from_date' => $fromDate,
                'to_date' => $toDate,
            ])
            ->groupBy('r.id', 'r.crop_id', 'r.status', 'r.target_yield', 'r.humus', 'r.created_at');

        $result = $qb->execute()->fetchAll();

        return array_map(function ($data) {
            $data['sampling_types'] = json_decode($data['sampling_types'], true);

            return $data;
        }, $result);
    }

    /**
     * @throws \Doctrine\DBAL\DBALException
     *
     * @return array|false|mixed
     */
    public function findRecommendations(int $serviceProviderId, array $filters, bool $pagination = true, int $limit = 1, int $offset = 0) // TODO:: This is agronomic tasks
    {
        $statuses = isset($filters['status']) ? json_decode($filters['status'], true) : [];
        $showTasksWithoutRecommendation = false;

        if (in_array(Recommendation::NO_RECOMMENDATION, $statuses)) {
            $showTasksWithoutRecommendation = true;
        }

        $flt = $this->recommendationRawSqlFilters($serviceProviderId, $filters);
        $result = $this->getRecommendationDataByFilter($flt, $limit, $offset, $pagination, $showTasksWithoutRecommendation);

        return [
            'recommendations' => (array)json_decode($result['agronomic_tasks'], true),
            'total' => (int)$result['total'],
        ];
    }

    public function getSubscriptionPackageField(Recommendation $recommendation): ?SubscriptionPackageField
    {
        if ('subscription' !== $recommendation->getPackageType()) {
            return null;
        }

        $subscriptionPackageFieldRepo = $this->getEntityManager()->getRepository(SubscriptionPackageField::class);

        return $subscriptionPackageFieldRepo->findOneBy([
            'subscriptionPackage' => $recommendation->getPackage(),
            'plotUuid' => $recommendation->getPlotUuid(),
        ]);
    }

    private function getRecommendationDataByFilter(array $conditions, int $limit = 1, int $offset = 0, bool $pagination = true, bool $showTasksWithoutRecommendation = false)
    {
        $conn = $this->getEntityManager()->getConnection();
        $eb = new ExpressionBuilder($conn);

        $recommendationsWithSamplingTypes = $eb->createQuery()
            ->select("
                    r.id,
                    r.package_id,
                    r.package_type,
                    r.plot_uuid,
                    r.valid_from,
                    jsonb_build_object(
                                    'id', rmc.id,
                                    'name', rmc.name,
                                    'calcModel', rmc.calc_model_id
                                ) as model,
                    r.crop_id,
                    r.status,
                    r.target_yield as yield,
                    r.humus,
                    jsonb_agg(jsonb_build_object('id', st.id, 'type', st.\"type\")) as sampling_types
            ")
            ->from('recommendations', 'r')
            ->leftJoin('r', 'recommendation_models_config', 'rmc', ' r.model_id = rmc.id')
            ->join('r', 'sampling_type', 'st', 'st.id = any(r.sampling_type_ids)')
            ->join('r', 'subscription_package', 'sp', 'sp.id = r.package_id')
            ->join('sp', 'contract', 'c', ' c.id = sp.contract_id')
            ->andWhere('c.service_provider_id = :service_provider')
            ->orderBy('r.id', 'ASC');

        if (isset($conditions['params']['customer_identification'])) {
            $recommendationsWithSamplingTypes->andWhere('c.customer_identification IN (:customer_identification)');
        }

        if (isset($conditions['params']['organization_id'])) {
            $recommendationsWithSamplingTypes->andWhere('c.organization_id = :organization_id');
        }

        if (isset($conditions['params']['contract_id'])) {
            $recommendationsWithSamplingTypes->andWhere('c.id = :contract_id');
        }

        if (isset($conditions['params']['start_date'])) {
            $recommendationsWithSamplingTypes->andWhere('to_char(sp.start_date, \'YYYY-mm-dd\')::date >= (:start_date)::date');
        }

        if (isset($conditions['params']['end_date'])) {
            $recommendationsWithSamplingTypes->andWhere('to_char(sp.end_date, \'YYYY-mm-dd\')::date <= (:end_date)::date');
        }

        if (isset($conditions['params']['plot_uuids']) && count($conditions['params']['plot_uuids'])) {
            $recommendationsWithSamplingTypes->andWhere('r.plot_uuid IN (:plot_uuids)');
        }

        if (isset($conditions['params']['crop_id'])) {
            $recommendationsWithSamplingTypes->andWhere('r.crop_id = :crop_id');
        }

        if (isset($conditions['params']['recommendation_id'])) {
            $recommendationsWithSamplingTypes->andWhere('r.id = :recommendation_id');
        }

        $recommendationsWithSamplingTypes->groupBy('r.id, rmc.id');

        $agronomicTasks = $eb->createQuery()
            ->select("
                    json_build_object(
                        'subsriptionPackageFieldId', spf.id,
                        'plotUuid', spf.plot_uuid,
                        'orderUuid', spf.order_uuid,
                        'contract', json_build_object(
                            'number', c.\"number\",
                            'contractDate', c.contract_date,
                            'customerIdentification', c.customer_identification,
                            'organizationId', c.organization_id
                        ),
                        'organizationId', c.organization_id,
                        'package', jsonb_build_object(
                            'id', sp.id,
                            'slug', p.slug,
                            'slugShort', p.slug_short,
                            'style', p.style,
                            'isSampling', p.is_sampling,
                            'isVRA', p.is_vra,
                            'startDate', sp.start_date,
                            'endDate', sp.end_date
                        ),
                        'results', COALESCE(jsonb_agg(DISTINCT
                            jsonb_build_object(
                                'id', leg.id,
                                'state', CASE
                                        WHEN leg.state IN ('For recommendation', 'Delivered') THEN 'For recommendation'
                                        ELSE 'Other'
                                    END,
                                'element', lage.\"element\"
                            )
                        ), '[]'::jsonb),
                        'dueDate', calculate_recommendation_due_date(max(pgp.state_updated_at)::date, 20),
                        'recommendations', coalesce(jsonb_agg(distinct
                            jsonb_build_object(
                                'id', rwst.id,
                                'model', rwst.model,
                                'crop_id', rwst.crop_id,
                                'status', rwst.status,
                                'yield', rwst.yield,
                                'humus', rwst.humus,
                                'valid_from', rwst.valid_from,
                                'sampling_types', rwst.sampling_types
                            )
                        ) filter (where rwst.id notnull), '[]'::jsonb)
                    ) AS agronomic_task_json
            ")
            ->from('contract', 'c')
            ->join('c', 'subscription_package', 'sp', 'sp.contract_id = c.id')
            ->join('sp', 'package', 'p', 'p.id = sp.package_id')
            ->join('sp', 'subscription_package_field', 'spf', 'spf.subscription_package_id = sp.id')
            ->join('spf', 'lab_element_group', 'leg', 'leg.package_id = sp.id AND leg.plot_uuid = spf.plot_uuid')
            ->join('leg', 'lab_analysis_package_group', 'lapg', 'lapg.id = leg.lab_analysis_package_group_id')
            ->join('lapg', 'lab_analysis_group_element', 'lage', 'lage.lab_analysis_group_id = lapg.lab_analysis_group_id')
            ->leftJoin('leg', 'recommendations_with_sampling_types', 'rwst', ' rwst.package_id = leg.package_id
                                                                                    and rwst.package_type = leg.package_type
                                                                                    and rwst.plot_uuid = leg.plot_uuid
                                                                                    and p.is_sampling = true')
            ->join('leg', 'package_grid_points', 'pgp', 'pgp.package_id = sp.id and pgp.plot_uuid = spf.plot_uuid and pgp.state = \'ReceivedInLab\'')
            ->andWhere('c.service_provider_id = :service_provider')
            ->andWhere('spf.field_state in (\'For recommendation\', \'Delivered\')');

        if (isset($conditions['params']['customer_identification'])) {
            $agronomicTasks->andWhere('c.customer_identification IN (:customer_identification)');
        }

        if (isset($conditions['params']['organization_id'])) {
            $agronomicTasks->andWhere('c.organization_id = :organization_id');
        }

        if (isset($conditions['params']['contract_id'])) {
            $agronomicTasks->andWhere('c.id = :contract_id');
        }

        if (isset($conditions['params']['plot_uuids']) && count($conditions['params']['plot_uuids'])) {
            $agronomicTasks->andWhere('spf.plot_uuid IN (:plot_uuids)');
        }

        if (isset($conditions['params']['order_uuid']) && count($conditions['params']['order_uuid'])) {
            $agronomicTasks->andWhere('spf.order_uuid IN (:order_uuid)');
        }

        if (isset($conditions['params']['start_date'])) {
            $agronomicTasks->andWhere('to_char(sp.start_date, \'YYYY-mm-dd\')::date >= (:start_date)::date');
        }

        if (isset($conditions['params']['end_date'])) {
            $agronomicTasks->andWhere('to_char(sp.end_date, \'YYYY-mm-dd\')::date <= (:end_date)::date');
        }

        if (isset($conditions['params']['subscription_package_field_id'])) {
            $agronomicTasks->andWhere('spf.id = :subscription_package_field_id');
        }

        if (isset($conditions['params']['status']) && !$showTasksWithoutRecommendation) {
            $agronomicTasks->andWhere('rwst.status IN (:status)');
        } elseif (isset($conditions['params']['status']) && $showTasksWithoutRecommendation) {
            $agronomicTasks->andWhere('rwst.status IN (:status) OR rwst.package_id IS NULL');
        } elseif ($showTasksWithoutRecommendation) {
            $agronomicTasks->andWhere('rwst.package_id IS NULL');
        }

        $agronomicTasks->groupBy('c.id, spf.id, sp.id, p.id')
            ->orderBy('c.id', 'ASC')
            ->addOrderBy('spf.plot_uuid', 'ASC')
            ->addOrderBy('spf.order_uuid', 'ASC');

        $total = $eb->createQuery()
            ->select('count(COALESCE(rt.agronomic_task_json, \'[]\'::json)) as cnt')
            ->from('agronomic_tasks', 'rt');

        $agronomicTasksResults = $eb->createQuery()
            ->select('COALESCE(rt.agronomic_task_json, \'[]\'::json) AS agronomic_tasks')
            ->from('agronomic_tasks', 'rt')
            ->setMaxResults($limit)
            ->setFirstResult($offset);

        $recommendationsWithSamplingTypesCTE = new Expression('recommendations_with_sampling_types', $recommendationsWithSamplingTypes);
        $agronomicTasksCTE = new Expression('agronomic_tasks', $agronomicTasks);
        $totalCTE = new Expression('total', $total);
        $agronomicTasksResultsCTE = new Expression('agronomic_tasks_results', $agronomicTasksResults);

        $query = $eb->with($recommendationsWithSamplingTypesCTE)
            ->with($agronomicTasksCTE)
            ->with($totalCTE)
            ->with($agronomicTasksResultsCTE)
            ->select(
                'json_agg(atr.agronomic_tasks) AS agronomic_tasks,
                    max(total.cnt) AS total'
            )
            ->from('agronomic_tasks_results atr, total');

        $stmt = $conn->executeQuery($query->getSQL(), $conditions['params'], $conditions['paramTypes']);

        return $stmt->fetchAssociative();
    }

    private function recommendationRawSqlFilters(int $serviceProviderId, array $filters = []): array
    {
        $params = [];
        $paramTypes = [];

        $params['service_provider'] = $serviceProviderId;
        $paramTypes['service_provider'] = ParameterType::INTEGER;

        if (isset($filters['customer_identification'])) {
            $params['customer_identification'] = json_decode($filters['customer_identification'], true);
            $paramTypes['customer_identification'] = Connection::PARAM_STR_ARRAY;
        }

        if (isset($filters['organization_id'])) {
            $params['organization_id'] = (int)$filters['organization_id'];
            $paramTypes['organization_id'] = ParameterType::INTEGER;
        }

        if (isset($filters['contract_id'])) {
            $params['contract_id'] = (int)$filters['contract_id'];
            $paramTypes['contract_id'] = ParameterType::INTEGER;
        }
        if (isset($filters['plot_uuids']) && count(json_decode($filters['plot_uuids']))) {
            $params['plot_uuids'] = json_decode($filters['plot_uuids'], true);
            $paramTypes['plot_uuids'] = Connection::PARAM_STR_ARRAY;
        }
        if (isset($filters['order_uuid']) && count(json_decode($filters['order_uuid']))) {
            $params['order_uuid'] = json_decode($filters['order_uuid'], true);
            $paramTypes['order_uuid'] = Connection::PARAM_STR_ARRAY;
        }

        if (isset($filters['start_date'])) {
            $params['start_date'] = $filters['start_date'];
            $paramTypes['start_date'] = ParameterType::STRING;
        }

        if (isset($filters['end_date'])) {
            $params['end_date'] = $filters['end_date'];
            $paramTypes['end_date'] = ParameterType::STRING;
        }

        if (isset($filters['status'])) {
            $statuses = (array)json_decode($filters['status'], true);
            $statuses = array_diff($statuses, [Recommendation::NO_RECOMMENDATION]);
            if (count($statuses)) {
                $params['status'] = $statuses;
                $paramTypes['status'] = Connection::PARAM_STR_ARRAY;
            }
        }

        if (isset($filters['crop_id'])) {
            $params['crop_id'] = (int)$filters['crop_id'];
            $paramTypes['crop_id'] = ParameterType::INTEGER;
        }

        if (isset($filters['recommendation_id'])) {
            $params['recommendation_id'] = (int)$filters['recommendation_id'];
            $paramTypes['recommendation_id'] = ParameterType::INTEGER;
        }

        if (isset($filters['subscription_package_field_id'])) {
            $params['subscription_package_field_id'] = (int)$filters['subscription_package_field_id'];
            $paramTypes['subscription_package_field_id'] = ParameterType::INTEGER;
        }

        return [
            'params' => $params,
            'paramTypes' => $paramTypes,
        ];
    }

    // /**
    //  * @return Recommendation[] Returns an array of Recommendation objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('r.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?Recommendation
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
