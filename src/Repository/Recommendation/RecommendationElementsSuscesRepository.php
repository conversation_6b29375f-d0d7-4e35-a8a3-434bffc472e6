<?php

namespace App\Repository\Recommendation;

use App\Entity\Recommendation\RecommendationElementsSusces;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|RecommendationElementsSusces find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationElementsSusces findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationElementsSusces[] findAll()
 * @method RecommendationElementsSusces[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationElementsSuscesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RecommendationElementsSusces::class);
    }

    // /**
    //  * @return RecommendationElementsSusces[] Returns an array of RecommendationElementsSusces objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('r.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?RecommendationElementsSusces
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
