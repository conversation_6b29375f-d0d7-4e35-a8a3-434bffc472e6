<?php

namespace App\Repository\Recommendation;

use App\Entity\Recommendation\RecommendationVraOrders;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|RecommendationVraOrders find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationVraOrders findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationVraOrders[] findAll()
 * @method RecommendationVraOrders[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationVraOrdersRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RecommendationVraOrders::class);
    }

    /**
     * @return int|mixed|string
     */
    public function getVraOrdersByRecommendationId(int $recommendationId)
    {
        $conn = $this->getEntityManager()->getConnection();
        $qb = $conn->createQueryBuilder();

        $qb->select(
            'vra_order_type',
            'JSONB_AGG(vra_order_id) as vra_order_ids'
        )
            ->from('recommendations_vra_orders', 'rvo')
            ->where('recommendation_id = :recommendationId')
            ->setParameter('recommendationId', $recommendationId)
            ->groupBy('vra_order_type');

        return array_map('json_decode', $qb->execute()->fetchAllKeyValue());
    }
}
