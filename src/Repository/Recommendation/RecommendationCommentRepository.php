<?php

namespace App\Repository\Recommendation;

use App\Entity\Recommendation\RecommendationComment;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|RecommendationComment find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationComment findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationComment[] findAll()
 * @method RecommendationComment[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationCommentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RecommendationComment::class);
    }

    // /**
    //  * @return RecommendationComment[] Returns an array of RecommendationComment objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('r.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?RecommendationComments
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
