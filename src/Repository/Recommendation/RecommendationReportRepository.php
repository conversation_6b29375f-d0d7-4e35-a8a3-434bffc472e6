<?php

namespace App\Repository\Recommendation;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;

class RecommendationReportRepository extends RecommendationRepository
{
    /**
     * @param ?int $limit
     * @param ?int $offset
     *
     * @throws \Doctrine\DBAL\DBALException
     */
    public function getRecommendationReport(int $serviceProviderId, array $filter, string $groupBy, array $orderBy, ?int $limit, ?int $offset): array
    {
        $connection = $this->getEntityManager()->getConnection();

        $groupByConfig = $this->getGroupByMapped($groupBy);
        $groupColumn = $groupByConfig['groupColumn'];
        $groupSelector = $groupByConfig['groupSelector'];

        $flt = $this->recommendationReportRawSqlFilters($serviceProviderId, $filter);
        $sql = $this->recommendationReportRawSqlCommand(array_column($flt, 'condition'), $groupColumn, $groupSelector, $orderBy, $limit, $offset);
        $grandTotal = $this->recommendationReportGrandTotalRawSql(array_column($flt, 'condition'));

        $dataValues = array_column($flt, 'value');
        $dataTypes = array_column($flt, 'type');
        $params = array_merge($dataValues, $dataValues, $dataValues); // This is needed because sqlFilters is used in 3 places in sql.
        $types = array_merge($dataTypes, $dataTypes, $dataTypes);    // For that is needed to set values and type x3 in executeQuery

        $statement = $connection->executeQuery(
            $sql,
            $params,
            $types
        );

        $statementGrandTotal = $connection->executeQuery(
            $grandTotal,
            $dataValues,
            $dataTypes
        );

        $result = $statement->fetchAll();
        $resultGrandTotal = $statementGrandTotal->fetch();

        $rows = array_map(function ($data) {
            return json_decode($data['recommendations_report'], true);
        }, $result);

        return [
            'rows' => $rows,
            'footer' => $rows ? json_decode($resultGrandTotal['grand_totals_object'], true) : [],
            'total' => $result[0]['total'] ?? 0,
        ];
    }

    private function recommendationReportRawSqlCommand(array $conditions, string $groupBy, string $groupSelector, array $orderBy, ?int $limit, ?int $offset): string
    {
        $limitCondition = '';
        $whereCondition = '';
        $orderByCondition = '';

        $mainFields = $this->getMainFields($groupBy);

        if (count($conditions)) {
            $whereCondition = 'WHERE ' . implode(' AND ', $conditions) . '';
        }
        if ($limit && $offset) {
            $limitCondition = 'LIMIT ' . $limit . ' OFFSET ' . $offset . '';
        }
        if ($orderBy && count($orderBy)) {
            $orderByCondition = 'order by';
            $i = 1;
            foreach ($orderBy as $name => $type) {
                $orderByCondition .= " \"data\"->'" . $name . "' " . $type;
                if ($i !== count($orderBy)) {
                    $orderByCondition .= ',';
                }
                $i++;
            }
        }

        return '
            with result_and_susces_data as (
                select
                    r.id as recommendation_id,
                    r.' . $groupSelector . ",
                    jsonb_object_agg(rer.result_element, (rer.value::numeric(10, 2) * spf.area::numeric(10, 2))::numeric(10, 2)) ||
                    jsonb_object_agg(res.\"element\" , case when res.value then '*' else '' end) as \"data\",
                    string_agg(distinct st.type, ',') as soil_layer_cm
                from
                    recommendations r
                join recommendation_elements_results rer on
                    rer.recommendation_id = r.id
                join recommendation_elements_susces res on
                    res.recommendation_id = r.id
                join sampling_type st on
                    st.id = any(r.sampling_type_ids)
                join subscription_package sp on
                    sp.id = r.package_id
                join subscription_package_field spf on
                    spf.subscription_package_id  = sp.id and spf.plot_uuid = r.plot_uuid
                join contract c on
                    c.id = sp.contract_id
                " . $whereCondition . '
                group by r.id, ' . $groupBy . '
                ),
            total_data as (
                select
                    r.' . $groupSelector . ',
                    rer.result_element, 
                    sum((rer.value::numeric(10, 2) * spf.area::numeric(10, 2))::numeric(10, 2))
                from
                    recommendations r
                join recommendation_elements_results rer on
                    rer.recommendation_id = r.id
                join subscription_package sp on
                    sp.id = r.package_id
                join subscription_package_field spf on
                    spf.subscription_package_id  = sp.id and spf.plot_uuid = r.plot_uuid
                join contract c on
                    c.id = sp.contract_id
                ' . $whereCondition . "
                and rer.result_element in('Add_N_fall','Add_P_total','Add_K_total','Add_N_total')
                group by " . $groupBy . ', rer.result_element
                ),
            group_total_data as (
                select 
                    td.' . $groupSelector . ',
                    jsonb_object_agg(
                                td.result_element, td."sum"
                            ) as total
                           from total_data td group by ' . $groupSelector . "
                ),
            report_main as (
                select
                    jsonb_build_object(
                    'key', ROW_NUMBER () OVER ( ORDER BY r." . $groupSelector . ')::text,
                    ' . $mainFields . "
                    'Add_N_fall',  (gtd.total::json->>'Add_N_fall')::numeric(10,2),
                    'Add_N_total', (gtd.total::json->>'Add_N_total')::numeric(10,2),
                    'Add_K_total', (gtd.total::json->>'Add_K_total')::numeric(10,2),
                    'Add_P_total', (gtd.total::json->>'Add_P_total')::numeric(10,2),
                    'children', jsonb_agg(
                                    JSONB_BUILD_OBJECT(
                                        'key', concat(sp.id, '-', r.id),
                                        'farm_year', concat(to_char(sp.start_date, 'YYYY'), '/', to_char(sp.end_date, 'YYYY')),
                                        'recommendation_id', r.id,
                                        'plot_name', r.plot_name,
                                        'plot_uuid', r.plot_uuid,
                                        'crop_id', r.crop_id,
                                        'yield', r.target_yield,
                                        'soil_layer_cm', rasd.soil_layer_cm
                                    ) || rasd.\"data\" " . $orderByCondition . '
                                )
                    ) as recommendations_report
                from
                    recommendations r
                join subscription_package sp on
                    sp.id = r.package_id
                join subscription_package_field spf on
                    spf.subscription_package_id  = sp.id and spf.plot_uuid = r.plot_uuid
                join contract c on
                    c.id = sp.contract_id
                join result_and_susces_data rasd on
                    rasd.recommendation_id = r.id
                join group_total_data gtd on
                    gtd.' . $groupSelector . ' = r.' . $groupSelector . '
                ' . $whereCondition . '
                group by ' . $groupBy . ', gtd.total
            ),
            rows_count as (
                select count(recommendations_report) as total
                from report_main
            )
            select rm.recommendations_report ,rc.total 
            from report_main as rm
            cross join rows_count as rc
                ' . $limitCondition . '
        ';
    }

    /**
     * @param string $groupBy
     * @param string $groupSelector
     * @param array $orderBy
     * @param int $limit
     * @param int $offset
     * @param bool $pagination
     */
    private function recommendationReportGrandTotalRawSql(array $conditions): string
    {
        $whereCondition = '';

        if (count($conditions)) {
            $whereCondition = 'WHERE ' . implode(' AND ', $conditions) . '';
        }

        return '
                with grand_totals as (
                    select
                        rer.result_element,
                        sum((rer.value)::numeric(10, 2) * (spf.area)::numeric(10, 2))::numeric(10,2)
                    from
                        recommendations r
                    join recommendation_elements_results rer on
                        rer.recommendation_id = r.id
                    join subscription_package sp on
                        sp.id = r.package_id
                    join subscription_package_field spf on
                        spf.subscription_package_id  = sp.id and spf.plot_uuid = r.plot_uuid
                    join contract c on
                        c.id = sp.contract_id
                    ' . $whereCondition . "
                    and rer.result_element in('Add_N_fall', 'Add_P_total', 'Add_K_total', 'Add_N_total')
                    group by rer.result_element
                )
                select jsonb_object_agg(result_element, \"sum\") as grand_totals_object
                from grand_totals
        ";
    }

    private function recommendationReportRawSqlFilters(int $serviceProviderId, array $filter = []): array
    {
        $flt = [];

        array_push(
            $flt,
            [
                'condition' => 'c.service_provider_id = ?',
                'value' => $serviceProviderId,
                'type' => ParameterType::INTEGER,
            ],
            [
                'condition' => ' r.status = ?',
                'value' => 'Delivered',
                'type' => ParameterType::STRING,
            ]
        );

        if (isset($filter['customer_identification'])) {
            $flt[] = [
                'condition' => 'c.customer_identification IN (?)',
                'value' => json_decode($filter['customer_identification']),
                'type' => Connection::PARAM_STR_ARRAY,
            ];
        }

        if (isset($filter['plot_uuids']) && count(json_decode($filter['plot_uuids']))) {
            $flt[] = [
                'condition' => 'r.plot_uuid IN (?)',
                'value' => json_decode($filter['plot_uuids']),
                'type' => Connection::PARAM_STR_ARRAY,
            ];
        }

        if (isset($filter['start_date'])) {
            $flt[] = [
                'condition' => "to_char(sp.start_date, 'YYYY-mm-dd')::date >= to_timestamp(?)::date",
                'value' => $filter['start_date'],
                'type' => ParameterType::STRING,
            ];
        }

        if (isset($filter['end_date'])) {
            $flt[] = [
                'condition' => "to_char(sp.end_date, 'YYYY-mm-dd')::date <= to_timestamp(?)::date",
                'value' => $filter['end_date'],
                'type' => ParameterType::STRING,
            ];
        }

        if (isset($filter['crop_ids']) && count(json_decode($filter['crop_ids']))) {
            $flt[] = [
                'condition' => 'r.crop_id in (?)',
                'value' => json_decode($filter['crop_ids']),
                'type' => Connection::PARAM_INT_ARRAY,
            ];
        }

        if (isset($filter['sampling_type_ids']) && count(json_decode($filter['sampling_type_ids']))) {
            $flt[] = [
                'condition' => 'r.sampling_type_ids @> ARRAY[?]::INT[]',
                'value' => json_decode($filter['sampling_type_ids']),
                'type' => Connection::PARAM_INT_ARRAY,
            ];
        }

        if (isset($filter['farm_ids']) && count(json_decode($filter['farm_ids']))) {
            $flt[] = [
                'condition' => 'spf.farm_id IN (?)',
                'value' => json_decode($filter['farm_ids']),
                'type' => Connection::PARAM_INT_ARRAY,
            ];
        }

        return $flt;
    }

    private function getGroupByMapped(string $GroupByFromUI): array
    {
        $plotNameByConfig = ['groupColumn' => 'r.plot_name', 'groupSelector' => 'plot_name', 'jsonKey' => 'plot_name'];
        $cropByConfig = ['groupColumn' => 'r.crop_id', 'groupSelector' => 'crop_id', 'jsonKey' => 'crop_id'];

        $groupByConfig = [
            'plot' => $plotNameByConfig,
            'crop' => $cropByConfig,
        ];

        return $groupByConfig[$GroupByFromUI] ?? $plotNameByConfig;
    }

    private function getMainFields(string $groupBy): string
    {
        $mainFieldsTemplate = "
            'plot_name', '',
            'crop_name', '',";

        if ('r.plot_name' === $groupBy) {
            $mainFields = str_replace("'plot_name', ''", "'plot_name', r.plot_name", $mainFieldsTemplate);
        }
        if ('r.crop_id' === $groupBy) {
            $mainFields = str_replace("'crop_id', ''", "'crop_id', r.crop_id", $mainFieldsTemplate);
        }

        return $mainFields;
    }
}
