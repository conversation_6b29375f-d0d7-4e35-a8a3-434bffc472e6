<?php

namespace App\Repository\Recommendation;

use App\Entity\Recommendation\RecommendationElementsResults;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|RecommendationElementsResults find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationElementsResults findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationElementsResults[] findAll()
 * @method RecommendationElementsResults[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationElementsResultsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RecommendationElementsResults::class);
    }

    // /**
    //  * @return RecommendationElementsResults[] Returns an array of RecommendationElementsResults objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('r.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?RecommendationElementsResults
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
