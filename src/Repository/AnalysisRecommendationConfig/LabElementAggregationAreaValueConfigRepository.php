<?php

namespace App\Repository\AnalysisRecommendationConfig;

use App\Entity\AnalysisRecommendationConfig\LabElementAggregationAreaValueConfig;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|LabElementAggregationAreaValueConfig find($id, $lockMode = null, $lockVersion = null)
 * @method null|LabElementAggregationAreaValueConfig findOneBy(array $criteria, array $orderBy = null)
 * @method LabElementAggregationAreaValueConfig[] findAll()
 * @method LabElementAggregationAreaValueConfig[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LabElementAggregationAreaValueConfigRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LabElementAggregationAreaValueConfig::class);
    }

    // /**
    //  * @return LabElementAggregationAreaValueConfig[] Returns an array of LabElementAggregationAreaValueConfig objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('l.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?LabElementAggregationAreaValueConfig
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
