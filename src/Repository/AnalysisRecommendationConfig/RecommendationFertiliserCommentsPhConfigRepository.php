<?php

namespace App\Repository\AnalysisRecommendationConfig;

use App\Entity\AnalysisRecommendationConfig\RecommendationFertiliserCommentsPhConfig;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|RecommendationFertiliserCommentsPhConfig find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationFertiliserCommentsPhConfig findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationFertiliserCommentsPhConfig[] findAll()
 * @method RecommendationFertiliserCommentsPhConfig[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationFertiliserCommentsPhConfigRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RecommendationFertiliserCommentsPhConfig::class);
    }

    // /**
    //  * @return RecommendationFertiliserCommentsPhConfig[] Returns an array of RecommendationFertiliserCommentsPhConfig objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('r.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?RecommendationFertiliserCommentsPhConfig
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
