<?php

namespace App\Repository\AnalysisRecommendationConfig;

use App\Entity\AnalysisRecommendationConfig\LabInterpretationClassesConfig;
use App\Repository\BaseRepository;
use Doctrine\Persistence\ManagerRegistry;
use PDO;

/**
 * @method null|LabInterpretationClassesConfig find($id, $lockMode = null, $lockVersion = null)
 * @method null|LabInterpretationClassesConfig findOneBy(array $criteria, array $orderBy = null)
 * @method LabInterpretationClassesConfig[] findAll()
 * @method LabInterpretationClassesConfig[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LabInterpretationClassesConfigRepository extends BaseRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LabInterpretationClassesConfig::class);
    }

    /**
     * @throws \Doctrine\DBAL\DBALException
     */
    public function soilAnalyzesData(int $serviceProviderId, array $filters = [])
    {
        $executeArr[] = $serviceProviderId;
        $executeArr[] = $serviceProviderId;
        $conn = $this->getEntityManager()->getConnection();

        $sql = '
                with chart_data as(
                select
                    lage."element", JSONB_AGG(jsonb_build_object(\'from\', lower(leic."range") , \'to\', upper(leic."range"), \'label\', licc.description, \'color\', leic.color, \'element_visual_order\',evo.visual_order)) as data
                from
                    lab_element_interpretations_config leic
                join lab_interpretation_classes_config licc on
                    licc.id = leic.class_id
                join lab_analysis_group_element lage on
                    lage.id = leic.element_id
                join lab_analysis_group_element_visual_order evo 
                    ON evo.lab_analysis_group_element_id = lage.id and evo.service_provider_id = ?
                join meta_elements_groups as meg on meg."element" = lage."element"
                join meta_groups as mg on mg.id = meg.group_id
                where lage.has_soil_map = true
                and leic.service_provider_id = ?';

        if (isset($filters['meta_groups'])) {
            $metaGroups = json_decode($filters['meta_groups'], true);
            $qMarksCustIdent = str_repeat('?,', count($metaGroups) - 1) . '?';
            $sql .= 'and mg.name in (' . $qMarksCustIdent . ')';
            $executeArr = array_merge($executeArr, $metaGroups);
        }

        if (isset($filters['elementNames'])) {
            $elementNames = json_decode($filters['elementNames'], true);
            $qMarksCustIdent = str_repeat('?,', count($elementNames) - 1) . '?';
            $sql .= 'and lage."element" in (' . $qMarksCustIdent . ')';
            $executeArr = array_merge($executeArr, $elementNames);
        }

        $sql .= '
                group by
                    lage."element",
                    evo.visual_order
                order by 
                    evo.visual_order)
                    select json_object_agg(element, data) as data from chart_data
        ';

        $stmt = $conn->prepare($sql);
        $stmt->execute($executeArr);
        $results = $stmt->fetch(PDO::FETCH_COLUMN);

        return json_decode($results, true);
    }
}
