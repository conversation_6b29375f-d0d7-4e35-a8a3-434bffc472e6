<?php

namespace App\Repository\AnalysisRecommendationConfig;

use App\Entity\AnalysisRecommendationConfig\RecommendationModelsConfig;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|RecommendationModelsConfig find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationModelsConfig findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationModelsConfig[] findAll()
 * @method RecommendationModelsConfig[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationModelsConfigRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RecommendationModelsConfig::class);
    }

    /**
     * Get all models by service provider.
     */
    public function getByServiceProvider(int $serviceProviderId): array
    {
        return $this->createQueryBuilder('rmc')
            ->where('rmc.serviceProvider = :serviceProviderId')
            ->setParameter('serviceProviderId', $serviceProviderId)
            ->getQuery()
            ->getArrayResult();
    }

    // /**
    //  * @return RecommendationModelsConfig[] Returns an array of RecommendationModelsConfig objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('r.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?RecommendationModelsConfig
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
