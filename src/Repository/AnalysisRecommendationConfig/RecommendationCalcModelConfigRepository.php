<?php

namespace App\Repository\AnalysisRecommendationConfig;

use App\Entity\AnalysisRecommendationConfig\RecommendationCalcModelConfig;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|RecommendationCalcModelConfig find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationCalcModelConfig findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationCalcModelConfig[] findAll()
 * @method RecommendationCalcModelConfig[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationCalcModelConfigRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RecommendationCalcModelConfig::class);
    }

    // /**
    //  * @return RecommendationCalcModelConfig[] Returns an array of RecommendationCalcModelConfig objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('r.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?RecommendationCalcModelConfig
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
