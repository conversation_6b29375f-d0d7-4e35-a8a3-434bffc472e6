<?php

namespace App\Repository\AnalysisRecommendationConfig;

use App\Entity\AnalysisRecommendationConfig\LabAggregatedElementInterpetationsConfig;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|LabAggregatedElementInterpetationsConfig find($id, $lockMode = null, $lockVersion = null)
 * @method null|LabAggregatedElementInterpetationsConfig findOneBy(array $criteria, array $orderBy = null)
 * @method LabAggregatedElementInterpetationsConfig[] findAll()
 * @method LabAggregatedElementInterpetationsConfig[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LabAggregatedElementInterpetationsConfigRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LabAggregatedElementInterpetationsConfig::class);
    }

    // /**
    //  * @return LabAggregatedElementInterpetationsConfig[] Returns an array of LabAggregatedElementInterpetationsConfig objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('l.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?LabAggregatedElementInterpetationsConfig
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
