<?php

namespace App\Repository\AnalysisRecommendationConfig;

use App\Entity\AnalysisRecommendationConfig\RecommendationLeafFertiliserCommentsConfig;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|RecommendationLeafFertiliserCommentsConfig find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationLeafFertiliserCommentsConfig findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationLeafFertiliserCommentsConfig[] findAll()
 * @method RecommendationLeafFertiliserCommentsConfig[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationLeafFertiliserCommentsConfigRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RecommendationLeafFertiliserCommentsConfig::class);
    }

    // /**
    //  * @return RecommendationLeafFertiliserCommentsConfig[] Returns an array of RecommendationLeafFertiliserCommentsConfig objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('r.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?RecommendationLeafFertiliserCommentsConfig
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
