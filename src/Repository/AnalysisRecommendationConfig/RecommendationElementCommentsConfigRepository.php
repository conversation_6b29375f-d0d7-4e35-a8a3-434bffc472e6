<?php

namespace App\Repository\AnalysisRecommendationConfig;

use App\Entity\AnalysisRecommendationConfig\RecommendationElementCommentsConfig;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|RecommendationElementCommentsConfig find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationElementCommentsConfig findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationElementCommentsConfig[] findAll()
 * @method RecommendationElementCommentsConfig[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationElementCommentsConfigRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RecommendationElementCommentsConfig::class);
    }

    // /**
    //  * @return RecommendationElementCommentsConfig[] Returns an array of RecommendationElementCommentsConfig objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('r.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?RecommendationElementCommentsConfig
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
