<?php

namespace App\Repository\AnalysisRecommendationConfig;

use App\Entity\AnalysisRecommendationConfig\LabElementInterpretationsConfig;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\ParameterType;
use Doctrine\Persistence\ManagerRegistry;
use Somnambulist\CTEBuilder\Expression;
use Somnambulist\CTEBuilder\ExpressionBuilder;

/**
 * @method null|LabElementInterpretationsConfig find($id, $lockMode = null, $lockVersion = null)
 * @method null|LabElementInterpretationsConfig findOneBy(array $criteria, array $orderBy = null)
 * @method LabElementInterpretationsConfig[] findAll()
 * @method LabElementInterpretationsConfig[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LabElementInterpretationsConfigRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LabElementInterpretationsConfig::class);
    }

    // /**
    //  * @return LabElementInterpretationsConfig[] Returns an array of LabElementInterpretationsConfig objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('l.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
        public function findOneBySomeField($value): ?LabElementInterpretationsConfig
        {
            return $this->createQueryBuilder('l')
                ->andWhere('l.exampleField = :val')
                ->setParameter('val', $value)
                ->getQuery()
                ->getOneOrNullResult()
            ;
        }
        */

    public function getElementInterpretationClasses(int $serviceProviderId)
    {
        $conn = $this->getEntityManager()->getConnection();
        $qb = $conn->createQueryBuilder();
        $eb = new ExpressionBuilder($conn);

        $elementClassesQb = $eb->createQuery()
            ->select(
                'lage."element"',
                'evo.visual_order',
                'MAX(lage.lab_analysis_group_id) AS lab_analysis_group_id',
                'JSON_AGG( 
                    JSON_BUILD_OBJECT(
                        \'class_id\', licc.id,
                        \'slug\', licc.slug,
                        \'description\', licc.description,
                        \'color\', leic.color,
                        \'range\', jsonb_build_object(
                            \'from\', lower(leic."range"),
                            \'to\', upper(leic."range")
                        )
                    ) ORDER BY LOWER(leic."range") NULLS FIRST
                ) AS classes'
            )
            ->from('lab_analysis_group_element', 'lage')
            ->join('lage', 'lab_analysis_group_element_visual_order', 'evo', 'evo.lab_analysis_group_element_id = lage.id and evo.service_provider_id = :service_provider_id')
            ->join('lage', 'lab_element_interpretations_config', 'leic', 'leic.element_id = lage.id')
            ->join('leic', 'lab_interpretation_classes_config', 'licc', 'licc.id = leic.class_id')
            ->where('leic.service_provider_id = :service_provider_id')
            ->andWhere('licc.slug NOTNULL')
            ->setParameter('service_provider_id', $serviceProviderId, ParameterType::INTEGER)
            ->groupBy('lage."element", evo.visual_order')
            ->orderBy('evo.visual_order');

        $elementClassesCTE = new Expression('element_classes', $elementClassesQb);

        $query = $eb->with($elementClassesCTE)
            ->select('json_object_agg(
                ec."element",
                ec.classes
                ORDER BY ec.visual_order::integer ASC, ec.lab_analysis_group_id ASC
            )')
            ->from('element_classes', 'ec');

        $result = $query->execute()->fetchOne();

        return json_decode($result, true);
    }
}
