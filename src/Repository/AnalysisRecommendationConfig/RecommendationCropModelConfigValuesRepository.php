<?php

namespace App\Repository\AnalysisRecommendationConfig;

use App\Entity\AnalysisRecommendationConfig\RecommendationCropModelConfigValues;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|RecommendationCropModelConfigValues find($id, $lockMode = null, $lockVersion = null)
 * @method null|RecommendationCropModelConfigValues findOneBy(array $criteria, array $orderBy = null)
 * @method RecommendationCropModelConfigValues[] findAll()
 * @method RecommendationCropModelConfigValues[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RecommendationCropModelConfigValuesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RecommendationCropModelConfigValues::class);
    }

    // /**
    //  * @return RecommendationCropModelConfigValues[] Returns an array of RecommendationCropModelConfigValues objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('r.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?RecommendationCropModelConfigValues
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
