<?php

namespace App\Repository\Navigation;

use App\Entity\Navigation\PackageMainNavigation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<PackageMainNavigation>
 *
 * @method null|PackageMainNavigation find($id, $lockMode = null, $lockVersion = null)
 * @method null|PackageMainNavigation findOneBy(array $criteria, array $orderBy = null)
 * @method PackageMainNavigation[] findAll()
 * @method PackageMainNavigation[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PackageMainNavigationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PackageMainNavigation::class);
    }

    public function add(PackageMainNavigation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(PackageMainNavigation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    //    /**
    //     * @return PackageMainNavigation[] Returns an array of PackageMainNavigation objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('p.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?PackageMainNavigation
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
