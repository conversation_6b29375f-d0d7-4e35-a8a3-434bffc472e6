<?php

namespace App\Repository\Navigation;

use App\Entity\Navigation\MainNavigation;
use App\Repository\BaseRepository;
use Doctrine\DBAL\ParameterType;
use Doctrine\Persistence\ManagerRegistry;
use Somnambulist\CTEBuilder\Expression;
use Somnambulist\CTEBuilder\ExpressionBuilder;

/**
 * @extends BaseRepository
 *
 * @method null|MainNavigation find($id, $lockMode = null, $lockVersion = null)
 * @method null|MainNavigation findOneBy(array $criteria, array $orderBy = null)
 * @method MainNavigation[] findAll()
 * @method MainNavigation[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MainNavigationRepository extends BaseRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MainNavigation::class);
    }

    public function add(MainNavigation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(MainNavigation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getByOrganization(int $organizationId, string $lang, int $serviceProviderId, string $instance): array
    {
        $conn = $this->getEntityManager()->getConnection();
        $eb = new ExpressionBuilder($conn);

        $params = [
            'organizationId' => $organizationId,
            'serviceProviderId' => $serviceProviderId,
            'instance' => $instance,
        ];
        $paramTypes = [
            'organizationId' => ParameterType::INTEGER,
            'serviceProviderId' => ParameterType::INTEGER,
            'instance' => ParameterType::STRING,
        ];

        $organizationContractPackagesStatusQb = $eb->createQuery()
            ->select(
                'sp.package_id',
                'BOOL_OR(
                    CASE WHEN 
                        sp.status = \'Active\'::package_statuses_enum 
                        AND (now()::date BETWEEN c.contract_date AND sp.end_date)
                    THEN
                        TRUE
                    ELSE
                        FALSE
                    END
                ) AS is_active'
            )
            ->from('contract', 'c')
            ->join('c', 'subscription_package', 'sp', 'sp.contract_id = c.id')
            ->where('c.organization_id = :organizationId')
            ->andWhere('c.service_provider_id = :serviceProviderId')
            ->andWhere('now()::date BETWEEN c.contract_date AND sp.end_date')
            ->groupBy('sp.package_id');

        $mainNavigationPackagesStatusQb = $eb->createQuery()
            ->select(
                'DISTINCT ON (main_navigation_id) main_navigation_id',
                'ocps.package_id',
                'ocps.is_active',
                'pmn.visual_order',
                'pmn.target'
            )
            ->from('organization_contract_packakges_status', 'ocps')
            ->join('ocps', 'package_main_navigation', 'pmn', 'pmn.package_id = ocps.package_id')
            ->orderBy('main_navigation_id', 'ASC')
            ->addOrderBy('ocps.is_active', 'DESC'); // True values first

        $organizationContractPackagesStatusCTE = new Expression('organization_contract_packakges_status', $organizationContractPackagesStatusQb);
        $mainNavigationPackagesStatusCTE = new Expression('main_navigation_packages_status', $mainNavigationPackagesStatusQb);

        $query = $eb->with($organizationContractPackagesStatusCTE)
            ->with($mainNavigationPackagesStatusCTE)
            ->select(
                "JSONB_AGG(
                    JSONB_BUILD_OBJECT(
                        'id', mn.id,
                        'url', mn.url,
                        'icon', mn.icon,
                        'path', mn.path,
                        'label', mn.label_{$lang},
                        'level', nlevel(path),
                        'children', COALESCE(get_package_main_navigation_tree('{$lang}', mnps.package_id, mn.path, :instance), '[]'::JSONB),
                        'no_data_url', mn.no_data_url,
                        'visual_order', mnps.visual_order,
                        'is_active', mnps.is_active,
                        'target', mnps.target
                    )
                    ORDER BY mnps.visual_order ASC
                )"
            )
            ->from('main_navigation', 'mn')
            ->join('mn', 'main_navigation_packages_status', 'mnps', 'mnps.main_navigation_id = mn.id')
            ->where('nlevel(mn.path) = 1')
            ->andWhere('mn.instance = :instance');

        $stmt = $conn->executeQuery($query->getSQL(), $params, $paramTypes);

        $result = $stmt->fetchOne();

        return json_decode($result, true) ?? [];
    }
}
