<?php

namespace App\Repository\Analysis;

use App\Entity\Analysis\LabElementsResultsRaw;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\Expr;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|LabElementsResultsRaw find($id, $lockMode = null, $lockVersion = null)
 * @method null|LabElementsResultsRaw findOneBy(array $criteria, array $orderBy = null)
 * @method LabElementsResultsRaw[] findAll()
 * @method LabElementsResultsRaw[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LabElementsResultsRawRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LabElementsResultsRaw::class);
    }

    public function getCalculatedValuesQb()
    {
        $qb = $this->createQueryBuilder('LabElementsResultsRaw');

        $qb->select('LabElementsResults as labElementsResults', 'LabElementsResultsRaw.id as LabElementsResultsRawId');
        $qb->addSelect('CASE WHEN LabElementsCalculations.operation=\'*\' THEN LabElementsResultsRaw.value * LabElementsCalculations.coefficient ELSE LabElementsResultsRaw.value / LabElementsCalculations.coefficient END AS calc_value');
        $qb->join('App\Entity\Analysis\PackageGridPoints', 'PackageGridPoints', Expr\Join::WITH, 'PackageGridPoints.labNumber = LabElementsResultsRaw.labNumber');
        $qb->join(
            'App\Entity\Analysis\LabElementsResults',
            'LabElementsResults',
            Expr\Join::WITH,
            'LabElementsResults.packageGridPoints = PackageGridPoints.id AND LabElementsResultsRaw.element = LabElementsResults.element'
        );
        $qb->join(
            'App\Entity\Analysis\LabElementsCalculations',
            'LabElementsCalculations',
            Expr\Join::WITH,
            'LabElementsCalculations.element = LabElementsResults.element'
        );

        return $qb;
    }
}
