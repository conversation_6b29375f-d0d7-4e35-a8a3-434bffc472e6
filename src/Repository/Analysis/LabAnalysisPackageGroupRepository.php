<?php

namespace App\Repository\Analysis;

use App\Entity\Analysis\LabAnalysisPackageGroup;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|LabAnalysisPackageGroup find($id, $lockMode = null, $lockVersion = null)
 * @method null|LabAnalysisPackageGroup findOneBy(array $criteria, array $orderBy = null)
 * @method LabAnalysisPackageGroup[] findAll()
 * @method LabAnalysisPackageGroup[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LabAnalysisPackageGroupRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LabAnalysisPackageGroup::class);
    }

    // /**
    //  * @return LabAnalysisPackageGroup[] Returns an array of LabAnalysisPackageGroup objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('l.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?LabAnalysisPackageGroup
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
