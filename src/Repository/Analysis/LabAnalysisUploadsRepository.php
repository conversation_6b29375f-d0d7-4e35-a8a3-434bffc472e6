<?php

namespace App\Repository\Analysis;

use App\Entity\Analysis\LabAnalysisUploads;
use App\Entity\ServiceProvider;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Configuration;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|LabAnalysisUploads find($id, $lockMode = null, $lockVersion = null)
 * @method null|LabAnalysisUploads findOneBy(array $criteria, array $orderBy = null)
 * @method LabAnalysisUploads[] findAll()
 * @method LabAnalysisUploads[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LabAnalysisUploadsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LabAnalysisUploads::class);
    }

    public function getByServiceProvider(ServiceProvider $serviceProvider, array $sort = null)
    {
        $config = new Configuration();
        $config->addCustomDatetimeFunction('date', 'Oro\ORM\Query\AST\Functions\SimpleFunction');
        $config->addCustomStringFunction('regexp_replace', 'DoctrineExtensions\Query\Postgresql\RegexpReplace');
        $qb = $this->createQueryBuilder('l');

        $qb->select('l.id', 'l.userId', 'date(l.date) as date', 'regexp_replace(l.filePath, :regexp_str, \'\2\') as fileName', 'l.state', 'l.records')
            ->where('l.serviceProvider = :serviceProvider')
            ->setParameters([
                'regexp_str' => '(.+\/)(.+)(\..*)', // get only filename (without path and extension)
                'serviceProvider' => $serviceProvider,
            ]);

        if (!empty($sort)) {
            foreach ($sort as $value) {
                $orderDirection = 'ASC';
                $orderField = $value;
                if ('-' === substr($value, 0, 1)) {
                    $orderDirection = 'DESC';
                    $orderField = substr($value, 1, strlen($value));
                }

                $qb->addOrderBy('l.' . $orderField, $orderDirection);
            }
        }

        return $qb->getQuery()->getResult();
    }

    // /**
    //  * @return LabAnalysisUploads[] Returns an array of LabAnalysisUploads objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('l.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?LabAnalysisUploads
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
