<?php

namespace App\Repository\Analysis;

use App\Entity\Analysis\LabElementGroup;
use App\Entity\Analysis\LabElementsResults;
use App\Entity\Analysis\PackageGridPoints;
use App\Entity\Contract;
use App\Entity\Contract\ServicePackage;
use App\Entity\Contract\ServicePackageField;
use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Contract\SubscriptionPackageField;
use App\Repository\BaseRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Query\QueryBuilder as DBALQueryBuilder;
use Doctrine\ORM\Query;
use Doctrine\ORM\Query\Expr;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Somnambulist\CTEBuilder\Expression;
use Somnambulist\CTEBuilder\ExpressionBuilder;
use Symfony\Component\Security\Core\Security;

/**
 * @method null|PackageGridPoints find($id, $lockMode = null, $lockVersion = null)
 * @method null|PackageGridPoints findOneBy(array $criteria, array $orderBy = null)
 * @method PackageGridPoints[] findAll()
 * @method PackageGridPoints[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PackageGridPointsRepository extends BaseRepository
{
    private Security $security;

    public function __construct(ManagerRegistry $registry, Security $security)
    {
        parent::__construct($registry, PackageGridPoints::class);
        $this->security = $security;
    }

    public function getBarcodesBy($packageId, $state)
    {
        $qb = $this->createQueryBuilder('PackageGridPoints');

        $qb->select('PackageGridPoints.barcode');
        $qb->where('PackageGridPoints.barcode IS NOT NULL');
        $qb->andWhere("PackageGridPoints.barcode != ''");
        $qb->andWhere('PackageGridPoints.state = :state')->setParameter('state', $state);
        $qb->andWhere('PackageGridPoints.packageId = :packageId')->setParameter('packageId', $packageId);

        $query = $qb->getQuery();

        return $query->getArrayResult();
    }

    public function getElementGroupData($packageId, $packageType, $plotUuids, $isFullPackage = true)
    {
        $qb = $this->createQueryBuilder('pgp');

        $qb->select(
            'DISTINCT pgp.plotUuid',
            'pgp.packageId as packageId',
            'pgp.packageType',
            'LabAnalysisPackageGroup.id as labAnalysisPackageGroupId',
        )
            ->join(
                'service' == $packageType ? 'App\Entity\Contract\ServicePackage' : 'App\Entity\Contract\SubscriptionPackage',
                'Package',
                Expr\Join::WITH,
                'Package.id = pgp.packageId'
            )
            ->join('App\Entity\Package', 'PackageType', Expr\Join::WITH, 'PackageType.id = Package.package')
            ->join(
                'App\Entity\Analysis\LabAnalysisPackageGroup',
                'LabAnalysisPackageGroup',
                Expr\Join::WITH,
                '
                    LabAnalysisPackageGroup.package = Package.package
                    AND LabAnalysisPackageGroup.packageType = pgp.packageType
                    AND LabAnalysisPackageGroup.samplingType = pgp.samplingType
                '
            )
            ->andWhere('pgp.packageId = :packageId')->setParameter('packageId', $packageId)
            ->andWhere('pgp.packageType = :packageType')->setParameter('packageType', $packageType)
            ->andWhere('pgp.plotUuid IN (:plotUuids)')->setParameter('plotUuids', $plotUuids);

        if ('service' !== $packageType && $isFullPackage) {
            $qb->andWhere('PackageType.isFullSampling = true');
        }

        if ('service' !== $packageType && !$isFullPackage) {
            $qb->andWhere('pgp.state = :state')->setParameter('state', PackageGridPoints::STATE_FOR_SAMPLING);
        }

        $query = $qb->getQuery();

        return $query->getArrayResult();
    }

    public function getElementsResultsData($packageId, $packageType, $plotUuids, $isFullPackage = true)
    {
        $qb = $this->createQueryBuilder('pgp');
        $qb->select(
            'LabAnalysisGroupElement.element',
            'LabElementGroup.id as lab_element_group_id',
            'pgp.id as package_grid_points_id',
        )
            ->join(
                'service' == $packageType ? 'App\Entity\Contract\ServicePackage' : 'App\Entity\Contract\SubscriptionPackage',
                'Package',
                Expr\Join::WITH,
                'Package.id = pgp.packageId'
            )
            ->join('App\Entity\Package', 'PackageType', Expr\Join::WITH, 'PackageType.id = Package.package')
            ->join(
                'App\Entity\Analysis\LabElementGroup',
                'LabElementGroup',
                Expr\Join::WITH,
                '
                    LabElementGroup.packageId = Package.id
                    AND LabElementGroup.packageType = pgp.packageType
                    AND LabElementGroup.plotUuid = pgp.plotUuid
                '
            )
            ->join(
                'App\Entity\Analysis\LabAnalysisPackageGroup',
                'LabAnalysisPackageGroup',
                Expr\Join::WITH,
                '
                    LabAnalysisPackageGroup.id = LabElementGroup.labAnalysisPackageGroup
                    AND LabAnalysisPackageGroup.samplingType = pgp.samplingType
                '
            )
            ->join(
                'App\Entity\Analysis\LabAnalysisGroupElement',
                'LabAnalysisGroupElement',
                Expr\Join::WITH,
                'LabAnalysisGroupElement.labAnalysisGroup = LabAnalysisPackageGroup.labAnalysisGroup'
            )
            ->join(
                'App\Entity\Analysis\LabAnalysisGroupElementVisualOrder',
                'LabAnalysisGroupElementVisualOrder',
                Expr\Join::WITH,
                'LabAnalysisGroupElementVisualOrder.labAnalysisGroupElement = LabAnalysisGroupElement.id
                AND LabAnalysisGroupElementVisualOrder.serviceProviderId = PackageType.serviceProviderId'
            )
            ->andWhere('pgp.packageId = :packageId')->setParameter('packageId', $packageId)
            ->andWhere('pgp.packageType = :packageType')->setParameter('packageType', $packageType)
            ->andWhere('pgp.plotUuid IN (:plotUuids)')->setParameter('plotUuids', $plotUuids)
            ->orderBy('pgp.id', 'DESC');

        if ('service' !== $packageType && $isFullPackage) {
            $qb->andWhere('PackageType.isFullSampling = true');
        }

        if ('service' !== $packageType && !$isFullPackage) {
            $qb->andWhere('pgp.state = :state')->setParameter('state', PackageGridPoints::STATE_FOR_SAMPLING);
        }

        $query = $qb->getQuery();

        return $query->getArrayResult();
    }

    /**
     * @return Query
     */
    public function findByPlotsQuery(array $arrPlotUuids)
    {
        $qb = $this->createQueryBuilder('PackageGridPoints');

        $qb->select('PackageGridPoints');
        $qb->andWhere('PackageGridPoints.plotUuid IN (:plotUuids)');
        $qb->setParameter('plotUuids', $arrPlotUuids);

        return $qb->getQuery();
    }

    /**
     * @param ?string $barcodeSearch
     *
     * @return array
     */
    public function getBarcodes(string $customerIdentification, ?string $barcodeSearch = null)
    {
        $qb = $this->createQueryBuilder('pgp');
        $qb->select('pgp.barcode')
            ->leftJoin(SubscriptionPackage::class, 'SubscriptionPackage', Expr\Join::WITH, 'SubscriptionPackage.id = pgp.packageId')
            ->join('SubscriptionPackage.contract', 'contract')
            ->where('pgp.barcode IS NOT NULL')
            ->andWhere('contract.customerIdentification = :customerIdentification')->setParameter('customerIdentification', $customerIdentification);

        if ($barcodeSearch) {
            $qb->andWhere($qb->expr()->like('pgp.barcode', ':word'))->setParameter('word', '%' . $barcodeSearch . '%');
        }

        $query = $qb->getQuery();
        $results = $query->getArrayResult();

        return array_column($results, 'barcode');
    }

    /**
     * @param ?string $labNumberSearch
     *
     * @return array
     */
    public function getLabNumbers(string $customerIdentification, ?string $labNumberSearch = null)
    {
        $qb = $this->createQueryBuilder('pgp');
        $qb->select('pgp.labNumber')
            ->leftJoin(SubscriptionPackage::class, 'SubscriptionPackage', Expr\Join::WITH, 'SubscriptionPackage.id = pgp.packageId')
            ->join('SubscriptionPackage.contract', 'contract')
            ->where('pgp.labNumber IS NOT NULL')
            ->andWhere('contract.customerIdentification = :customerIdentification')->setParameter('customerIdentification', $customerIdentification);

        if ($labNumberSearch) {
            $qb->andWhere($qb->expr()->like('pgp.labNumber', ':word'))->setParameter('word', '%' . $labNumberSearch . '%');
        }

        $query = $qb->getQuery();
        $results = $query->getArrayResult();

        return array_column($results, 'labNumber');
    }

    public function getPackageGridPintsByBarcodes(array $barcodes): QueryBuilder
    {
        $qb = $this->createQueryBuilder('pgp');
        $qb->andWhere('pgp.barcode IN (:barcode)')->setParameter('barcode', $barcodes);

        return $qb;
    }

    public function removeByPackageFields(array $fieldIds, $type = Contract::TYPE_SUBSCRIPTION)
    {
        $qb = $this->createQueryBuilder('PackageGridPoints');

        $qb->select('PackageGridPoints.id');

        if (Contract::TYPE_SERVICE === $type) {
            $qb->join(ServicePackage::class, 'Package', Expr\Join::WITH, 'Package.id = PackageGridPoints.packageId');
            $qb->join(ServicePackageField::class, 'PackageField', Expr\Join::WITH, '(PackageField.servicePackage = Package.id AND PackageGridPoints.plotUuid = PackageField.plotUuid)');
        } else {
            $qb->join(SubscriptionPackage::class, 'Package', Expr\Join::WITH, 'Package.id = PackageGridPoints.packageId');
            $qb->join(SubscriptionPackageField::class, 'PackageField', Expr\Join::WITH, '(PackageField.subscriptionPackage = Package.id AND PackageGridPoints.plotUuid = PackageField.plotUuid)');
        }

        $qb->where('PackageField.id IN (:fieldIds)')->setParameter('fieldIds', $fieldIds);

        $query = $qb->getQuery();
        $results = $query->getArrayResult();
        $packageGridPointIds = array_column($results, 'id');

        return $this->createQueryBuilder('PackageGridPoints')
            ->where('PackageGridPoints.id in (:ids)')
            ->setParameter('ids', $packageGridPointIds)
            ->delete()
            ->getQuery()
            ->execute();
    }

    public function cellsForSamplingWithElements(array $filter)
    {
        $env = getenv();
        $susiMainDBName = $this->security->getUser()->getServiceProvider()->getCountry()->getDatabaseName();
        $susiMainDBConnStr = 'host=' . $env['SUSI_MAIN_DB_HOST']
            . ' port=' . $env['SUSI_MAIN_DB_PORT']
            . ' dbname=' . $susiMainDBName
            . ' user=' . $env['SUSI_MAIN_DB_USER']
            . ' password=' . $env['SUSI_MAIN_DB_PASS'];

        $params = [
            'fullSamplingPackageStates' => [LabElementsResults::STATE_ANALYSED, LabElementsResults::STATE_APPROVED],
            'notFullSamplingPackageStates' => [LabElementsResults::STATE_ANALYSED, LabElementsResults::STATE_FOR_ANALYSIS, LabElementsResults::STATE_APPROVED],
            'serviceProviderId' => $this->security->getUser()->getServiceProvider()->getId(),
            'contractId' => $filter['contract_id'], // required
            'subscriptionPackageId' => $filter['subscription_package_id'],
            'plotUuids' => json_decode($filter['plot_uuids'], true),
            'susiMainDbConn' => $susiMainDBConnStr,
        ];
        $paramTypes = [
            'fullSamplingPackageStates' => Connection::PARAM_STR_ARRAY,
            'notFullSamplingPackageStates' => Connection::PARAM_STR_ARRAY,
            'serviceProviderId' => ParameterType::INTEGER,
            'contractId' => ParameterType::INTEGER,
            'subscriptionPackageId' => ParameterType::STRING,
            'plotUuids' => Connection::PARAM_STR_ARRAY,
            'susiMainDbConn' => ParameterType::STRING,
        ];

        $conn = $this->getEntityManager()->getConnection();
        $eb = new ExpressionBuilder($conn);
        /**
         * @var DBALQueryBuilder $contractPackagesWithPlotsQb
         *                       Query for getting packages and plots for all periods of the contract
         */
        $contractPackagesWithPlotsQb = $eb->createQuery()
            ->select(
                'c.id AS contract_id',
                'sp.id AS package_id',
                'sp.period',
                'concat(to_char(sp.start_date, \'mm/YYYY\'), \'-\', to_char(sp.end_date, \'mm/YYYY\')) AS period_date',
                'spf.id AS subscription_package_field_id',
                'spf.plot_uuid',
                'spf.order_uuid',
                'spf.parent_id',
                'p.is_full_sampling'
            )
            ->from('contract', 'c')
            ->join('c', 'subscription_contracts', 'sc', 'sc.id = c.id')
            ->join('c', 'subscription_package', 'sp', 'sp.contract_id = c.id')
            ->join('sp', 'package', 'p', 'p.id = sp.package_id AND p.is_sampling = TRUE')
            ->join('sp', 'subscription_package_field', 'spf', 'spf.subscription_package_id = sp.id')
            ->where('c.service_provider_id = :serviceProviderId')
            ->andWhere('c.id = :contractId');

        $contractPackagesWithPlotsQb->groupBy(
            'c.id',
            'sp.id',
            'spf.id',
            'spf.parent_id',
            'p.is_full_sampling'
        );

        /**
         * @var \Doctrine\DBAL\Query\QueryBuilder $ordersPlotsUuidsQb
         *                                        Query for getting all plot_uuids and order_uuids for all periods of the contract
         */
        $ordersPlotsUuidsQb = $eb->createQuery()
            ->select(
                'ARRAY_AGG(cpwp.plot_uuid) AS plot_uuids',
                'ARRAY_AGG(cpwp.order_uuid) AS order_uuids'
            )
            ->from('contract_packages_with_plots', 'cpwp');

        /**
         * @var \Doctrine\DBAL\Query\QueryBuilder $cellsGeomQb
         *                                        Query for getting cell geojson geometry for all package grid points of the contract
         */
        $cellsGeomQb = $eb->createQuery()
            ->select(
                'point_uuid',
                'geom_json'
            )
            ->from('orders_plots_uuids', 'opu')
            ->leftJoin(
                'opu',
                'dblink(
                    :susiMainDbConn,
                    FORMAT(
                        $$
                            SELECT
                                ssp.uuid AS point_uuid,
                                ST_AsGeoJSON(ST_Transform(ssg.geom, 3857))::JSONB AS geom_json
                            FROM
                                su_satellite_orders_plots_rel sopr
                            JOIN su_satellite_soil_points AS ssp
                                ON ssp.sopr_id = sopr.id
                            JOIN su_satellite_soil_grid AS ssg
                                ON ssg.sopr_id = ssp.sopr_id
                                AND ssg.sample_id = ssp.sample_id
                            WHERE
                                sopr.plot_uuid = ANY(%L)
                                AND sopr.order_uuid = ANY(%L)
                        $$,
                        plot_uuids, order_uuids
                    )
                )',
                'plot_cells_geom(point_uuid UUID, geom_json JSONB)',
                'true'
            );

        /**
         * @var DBALQueryBuilder $currentPeriodDataQb
         *                       Query for getting package, plots and cells data (package grid points)
         *                       for the current period of the contract (:subscriptionPackageId)
         */
        $statePending = PackageGridPoints::STATE_PENDING;
        $stateNotSampling = PackageGridPoints::STATE_NOT_SAMPLED;
        $currentPeriodDataQb = $eb->createQuery()
            ->select(
                'cpwp.subscription_package_field_id',
                'cpwp.parent_id',
                'pgp.sample_id as cell_number',
                "pgp.state not in('{$statePending}'::point_states_enum, '{$stateNotSampling}'::point_states_enum) AS checked",
                'pgp.lab_number',
                'pgp.state',
                'cpwp.package_id',
                'cpwp.period_date',
                'cpwp.period',
                'cpwp.plot_uuid',
                'st.id AS sampling_type_id',
                'st.type AS soil_layer_cm',
                'cpwp.is_full_sampling',
                'CASE WHEN cg.geom_json NOTNULL
                    THEN JSONB_BUILD_OBJECT(
                            \'type\', \'Feature\',
                            \'geometry\', cg.geom_json
                        )
                    ELSE NULL
                END AS cell_geom_json'
            )
            ->from('contract_packages_with_plots', 'cpwp')
            ->join(
                'cpwp',
                'package_grid_points',
                'pgp',
                '
                    pgp.package_id = cpwp.package_id
                    AND pgp.package_type = \'subscription\'
                    AND pgp.plot_uuid = cpwp.plot_uuid
                '
            )
            ->join('pgp', 'sampling_type', 'st', 'st.id = pgp.sampling_type_id')
            ->leftJoin('pgp', 'cells_geom', 'cg', 'cg.point_uuid = pgp.point_uuid::UUID')
            ->where('cpwp.package_id = :subscriptionPackageId')
            ->andWhere('cpwp.plot_uuid in (:plotUuids)')
            ->groupBy(
                'cpwp.contract_id',
                'cpwp.package_id',
                'cpwp.period',
                'cpwp.period_date',
                'cpwp.subscription_package_field_id',
                'cpwp.plot_uuid',
                'cpwp.parent_id',
                'cpwp.is_full_sampling',
                'pgp.sample_id',
                'pgp.lab_number',
                'st.id',
                'pgp.state',
                'cg.geom_json'
            )
            ->orderBy('cpwp.subscription_package_field_id', 'ASC')
            ->addOrderBy('pgp.sample_id', 'ASC')
            ->addOrderBy('st.type', 'ASC');

        /**
         * @var DBALQueryBuilder $fullSamplingPackageResultsQb
         *                       Query for getting elements results for the full sampling package of the contract
         */
        $fullSamplingPackageResultsQb = $eb->createQuery()
            ->select(
                'cpwp.contract_id',
                'cpwp.package_id',
                'cpwp.period',
                'cpwp.period_date',
                'cpwp.subscription_package_field_id',
                'cpwp.plot_uuid',
                'pgp.sample_id AS cell_number',
                'pgp.lab_number',
                'pgp.state',
                'st.type AS soil_layer_cm',
                'JSONB_BUILD_OBJECT(
                    \'id\', ler.id,
                    \'name\', ler.element,
                    \'state\', ler.state,
                    \'uploaded_date\', to_char(lau.date, \'YYYY-MM-DD\'), 
                    \'uploaded_by\', lau.user_id, 
                    \'file\', SPLIT_PART(lau.file_path, \'public\', 2), 
                    \'groups\', JSONB_AGG(DISTINCT meg.group_id), 
                    \'value\', ROUND(ler.value::numeric, 2),
                    \'visual_order\', lagevo.visual_order,
                    \'class\', json_build_object(
                                \'id\', licc.id,
                                \'color\', leic.color,
                                \'description\', licc.description,
                                \'slug\', licc.slug
                        )
                ) AS element',
                'cpd.plot_uuid AS current_period_plot_uuid',
                'cpd.cell_geom_json'
            )
            ->from('current_period_data', 'cpd')
            ->join(
                'cpd',
                'contract_packages_with_plots',
                'cpwp',
                '
                    cpwp.subscription_package_field_id = cpd.parent_id
                    OR (
                        cpwp.is_full_sampling = TRUE
                        AND cpwp.subscription_package_field_id = cpd.subscription_package_field_id
                        AND cpd.parent_id ISNULL
                    ) -- if the current period package is same as the full sampling package period
                '
            )
            ->join(
                'cpwp',
                'package_grid_points',
                'pgp',
                '
                    pgp.package_type = \'subscription\'
                    AND pgp.package_id = cpwp.package_id
                    AND pgp.plot_uuid = cpwp.plot_uuid
                    AND pgp.sample_id = cpd.cell_number
                    AND pgp.sampling_type_id = cpd.sampling_type_id
                '
            )
            ->join('pgp', 'sampling_type', 'st', 'st.id = pgp.sampling_type_id')
            ->join('pgp', 'lab_elements_results', 'ler', 'ler.package_grid_points_id = pgp.id')
            ->join('ler', 'lab_analysis_group_element', 'lage', 'lage.element = ler.element')
            ->join(
                'lage',
                'lab_analysis_group_element_visual_order',
                'lagevo',
                'lagevo.lab_analysis_group_element_id = lage.id AND lagevo.service_provider_id = :serviceProviderId'
            )
            ->leftJoin('ler', 'lab_elements_results_raw', 'lerr', 'lerr.id = ler.lab_elements_results_raw_id')
            ->leftJoin('lerr', 'lab_analysis_uploads', 'lau', 'lau.id = lerr.lab_analisys_uploads_id')
            ->leftJoin(
                'lage',
                'lab_element_interpretations_config',
                'leic',
                '
                    leic.service_provider_id = :serviceProviderId
                    AND leic.element_id = lage.id
                    AND ler.value::NUMERIC <@ leic."range"
                '
            )
            ->leftJoin('leic', 'lab_interpretation_classes_config', 'licc', 'licc.id = leic.class_id')
            ->leftJoin('lage', 'meta_elements_groups', 'meg', 'meg.element = lage.element')
            ->where('ler.state::text IN (:fullSamplingPackageStates)')
            ->groupBy(
                'ler.id',
                'lau.id',
                'lagevo.visual_order',
                'cpwp.contract_id',
                'cpwp.package_id',
                'cpwp.period',
                'cpwp.period_date',
                'cpwp.subscription_package_field_id',
                'cpwp.plot_uuid',
                'pgp.sample_id',
                'pgp.lab_number',
                'pgp.state',
                'cpd.cell_geom_json',
                'st.type',
                'cpd.plot_uuid',
                'leic.color',
                'licc.id',
                'licc.description',
                'licc.slug'
            )
            ->orderBy('cpwp.subscription_package_field_id', 'ASC')
            ->addOrderBy('pgp.sample_id', 'ASC')
            ->addOrderBy('st.type', 'ASC');

        /**
         * @var DBALQueryBuilder $fullSamplingPackageResultsAggQb
         *                       Query for getting aggregated elements results for the full sampling package of the contract
         */
        $fullSamplingPackageResultsAggQb = $eb->createQuery()
            ->select(
                'contract_id',
                'package_id',
                'period',
                'period_date',
                'subscription_package_field_id',
                'plot_uuid',
                'cell_number',
                'lab_number',
                'state',
                'soil_layer_cm',
                'current_period_plot_uuid',
                'cell_geom_json',
                'JSONB_AGG(element ORDER BY (element->>\'visual_order\')::int) AS elements'
            )
            ->from('full_sampling_package_results', 'fspr')
            ->groupBy(
                'contract_id',
                'package_id',
                'period',
                'period_date',
                'subscription_package_field_id',
                'plot_uuid',
                'cell_number',
                'lab_number',
                'soil_layer_cm',
                'current_period_plot_uuid',
                'state',
                'cell_geom_json'
            );

        /**
         * @var DBALQueryBuilder $fullSamplingPackageElementsQb
         *                       Query for getting all elements from the full sampling package results
         */
        $fullSamplingPackageElementsQb = $eb->createQuery()
            ->select(
                'DISTINCT JSONB_BUILD_OBJECT(
			        \'name\', fspr.element->>\'name\',
			        \'groups\', fspr."element"->\'groups\',
			        \'visual_order\', (fspr.element->>\'visual_order\')::int
		        ) AS element_data'
            )
            ->from('full_sampling_package_results', 'fspr');

        /**
         * @var DBALQueryBuilder $notFullSamplingPackageResultsQb
         *                       Query for getting elements results for the not full sampling packages of the contract
         */
        $notFullSamplingPackageResultsQb = $eb->createQuery()
            ->select(
                'cpwp.contract_id',
                'cpwp.package_id',
                'cpwp.period',
                'cpwp.period_date',
                'cpwp.subscription_package_field_id',
                'cpwp.plot_uuid',
                'pgp.sample_id AS cell_number',
                'pgp.state',
                'pgp.lab_number',
                'st.type AS soil_layer_cm',
                'JSONB_BUILD_OBJECT(
                    \'id\', ler.id,
                    \'name\', ler.element,
                    \'state\', ler.state,
                    \'uploaded_date\', to_char(lau.date, \'YYYY-MM-DD\'), 
                    \'uploaded_by\', lau.user_id, 
                    \'file\', SPLIT_PART(lau.file_path, \'public\', 2), 
                    \'groups\', JSONB_AGG(DISTINCT meg.group_id), 
                    \'value\', ROUND(ler.value::numeric, 2),
                    \'visual_order\', lagevo.visual_order,
                    \'class\', json_build_object(
                                \'id\', licc.id,
                                \'color\', leic.color,
                                \'description\', licc.description,
                                \'slug\', licc.slug
                        )
                ) AS element',
                'CASE WHEN pgp.state = \'ReceivedInLab\' 
                    THEN TRUE 
                    ELSE FALSE 
                END AS checked',
                'cpd.plot_uuid AS current_period_plot_uuid',
                'cpd.cell_geom_json'
            )
            ->from('current_period_data', 'cpd')
            ->join(
                'cpd',
                'contract_packages_with_plots',
                'cpwp',
                '
                    cpd.parent_id = cpwp.parent_id
                    AND cpd.period > cpwp.period
                    AND cpwp.is_full_sampling = FALSE
                '
            )
            ->join(
                'cpwp',
                'package_grid_points',
                'pgp',
                '
                    pgp.package_type = \'subscription\'
                    AND pgp.package_id = cpwp.package_id
                    AND pgp.plot_uuid = cpwp.plot_uuid
                    AND pgp.sample_id = cpd.cell_number
                    AND pgp.sampling_type_id = cpd.sampling_type_id
                '
            )
            ->join('pgp', 'sampling_type', 'st', 'st.id = pgp.sampling_type_id')
            ->join('pgp', 'lab_elements_results', 'ler', 'ler.package_grid_points_id = pgp.id')
            ->join('ler', 'lab_analysis_group_element', 'lage', 'lage.element = ler.element')
            ->join(
                'lage',
                'lab_analysis_group_element_visual_order',
                'lagevo',
                'lagevo.lab_analysis_group_element_id = lage.id AND lagevo.service_provider_id = :serviceProviderId'
            )
            ->leftJoin('ler', 'lab_elements_results_raw', 'lerr', 'lerr.id = ler.lab_elements_results_raw_id')
            ->leftJoin('lerr', 'lab_analysis_uploads', 'lau', 'lau.id = lerr.lab_analisys_uploads_id')
            ->leftJoin(
                'lage',
                'lab_element_interpretations_config',
                'leic',
                '
                    leic.service_provider_id = :serviceProviderId
                    AND leic.element_id = lage.id
                    AND ler.value::NUMERIC <@ leic."range"
                '
            )
            ->leftJoin('leic', 'lab_interpretation_classes_config', 'licc', 'licc.id = leic.class_id')
            ->leftJoin('lage', 'meta_elements_groups', 'meg', 'meg.element = lage.element')
            ->where('ler.state::text IN (:notFullSamplingPackageStates)')
            ->groupBy(
                'ler.id',
                'lau.id',
                'lagevo.visual_order',
                'cpwp.contract_id',
                'cpwp.package_id',
                'cpwp.period',
                'cpwp.period_date',
                'cpwp.subscription_package_field_id',
                'cpwp.plot_uuid',
                'cpd.cell_geom_json',
                'pgp.sample_id',
                'pgp.lab_number',
                'st.type',
                'cpd.plot_uuid',
                'pgp.state',
                'leic.color',
                'licc.id',
                'licc.description',
                'licc.slug'
            )
            ->orderBy('cpwp.subscription_package_field_id', 'ASC')
            ->addOrderBy('pgp.sample_id', 'ASC')
            ->addOrderBy('st.type', 'ASC');

        /**
         * @var DBALQueryBuilder $notFullSamplingPackageResultsAggQb
         *                       Query for getting aggregated elements results for the not full sampling packages of the contract
         */
        $notFullSamplingPackageResultsAggQb = $eb->createQuery()
            ->select(
                'CONCAT(plot_uuid, \'/\',package_id, \'/\',cell_number, \'/\',soil_layer_cm) AS key',
                'cell_number',
                'checked',
                'contract_id',
                'current_period_plot_uuid',
                'JSONB_AGG(element ORDER BY ("element"->>\'visual_order\')::int) AS elements',
                'lab_number',
                'state',
                'package_id',
                'period',
                'period_date',
                'plot_uuid',
                'soil_layer_cm',
                'cell_geom_json'
            )
            ->from('not_full_sampling_package_results', 'nfspr')
            ->groupBy(
                'current_period_plot_uuid',
                'cell_number',
                'soil_layer_cm',
                'checked',
                'contract_id',
                'lab_number',
                'package_id',
                'period',
                'period_date',
                'plot_uuid',
                'soil_layer_cm',
                'state',
                'cell_geom_json'
            );

        /**
         * @var DBALQueryBuilder $rowQb
         *                       Query for getting parent row with children and current period data
         */
        $rowQb = $eb->createQuery()
            ->select(
                'CONCAT(fspra.plot_uuid, \'/\', fspra.package_id, \'/\', fspra.cell_number, \'/\', fspra.soil_layer_cm) AS key',
                'fspra.plot_uuid',
                'fspra.package_id',
                'fspra.cell_number',
                'fspra.soil_layer_cm',
                'fspra.period',
                'fspra.period_date',
                'fspra.lab_number',
                'fspra.state',
                'fspra.elements AS elements',
                'fspra.cell_geom_json',
                'CASE WHEN nfspra.plot_uuid NOTNULL
                    THEN TRUE
                    ELSE FALSE 
                END AS checked',
                'CASE WHEN nfspra.plot_uuid NOTNULL
                    THEN JSONB_AGG(DISTINCT nfspra)
                    ELSE NULL
                END AS children',
                'JSONB_BUILD_OBJECT( 
                    \'cell_number\', cpd.cell_number,
                    \'checked\', cpd.checked,
                    \'lab_number\', cpd.lab_number,
                    \'package_id\', cpd.package_id,
                    \'period_date\', cpd.period_date,
                    \'period_id\', cpd.period,
                    \'plot_uuid\', cpd.plot_uuid,
                    \'soil_layer_cm\', cpd.soil_layer_cm,
                    \'state\', cpd.state
                ) AS current_period_data'
            )
            ->from('current_period_data', 'cpd')
            ->join(
                'cpd',
                'full_sampling_package_results_agg',
                'fspra',
                '
                    fspra.current_period_plot_uuid = cpd.plot_uuid
                    AND fspra.cell_number = cpd.cell_number
                    AND fspra.soil_layer_cm = cpd.soil_layer_cm
                '
            )
            ->leftJoin(
                'cpd',
                'not_full_sampling_package_results_agg',
                'nfspra',
                '
                    nfspra.current_period_plot_uuid = cpd.plot_uuid
                    AND nfspra.cell_number = cpd.cell_number
                    AND nfspra.soil_layer_cm = cpd.soil_layer_cm
                '
            )
            ->groupBy(
                'fspra.plot_uuid',
                'fspra.package_id',
                'fspra.cell_number',
                'fspra.soil_layer_cm',
                'fspra.period',
                'fspra.period_date',
                'fspra.lab_number',
                'fspra.elements',
                'fspra.state',
                'fspra.cell_geom_json',
                'nfspra.plot_uuid',
                'cpd.cell_number',
                'cpd.checked',
                'cpd.lab_number',
                'cpd.package_id',
                'cpd.period_date',
                'cpd.period',
                'cpd.plot_uuid',
                'cpd.soil_layer_cm',
                'cpd.state'
            )
            ->orderBy('cpd.plot_uuid', 'ASC')
            ->addOrderBy('cpd.cell_number::int', 'ASC')
            ->addOrderBy('cpd.soil_layer_cm', 'ASC');

        /**
         * @var DBALQueryBuilder $columnsQb
         *                       Query for getting the columns from elements of the full sampling package
         */
        $columnsQb = $eb->createQuery()
            ->select(
                'JSONB_AGG(element_data ORDER BY (element_data->>\'visual_order\')::int ASC) AS data'
            )
            ->from('full_sampling_package_elements', 'fspe');

        $contractPackagesWithPlotsCTE = new Expression('contract_packages_with_plots', $contractPackagesWithPlotsQb);
        $ordersPlotsUuidsCTE = new Expression('orders_plots_uuids', $ordersPlotsUuidsQb);
        $cellsGeomCTE = new Expression('cells_geom', $cellsGeomQb);
        $currentPeriodDataCTE = new Expression('current_period_data', $currentPeriodDataQb);
        $fullSamplingPackageResultsCTE = new Expression('full_sampling_package_results', $fullSamplingPackageResultsQb);
        $fullSamplingPackageResultsAggCTE = new Expression('full_sampling_package_results_agg', $fullSamplingPackageResultsAggQb);
        $fullSamplingPackageElementsCTE = new Expression('full_sampling_package_elements', $fullSamplingPackageElementsQb);
        $notFullSamplingPackageResultsCTE = new Expression('not_full_sampling_package_results', $notFullSamplingPackageResultsQb);
        $notFullSamplingPackageResultsAggCTE = new Expression('not_full_sampling_package_results_agg', $notFullSamplingPackageResultsAggQb);

        $rowCTE = new Expression('row', $rowQb);
        $columnsCTE = new Expression('columns', $columnsQb);

        $query = $eb->with($contractPackagesWithPlotsCTE)
            ->with($ordersPlotsUuidsCTE)
            ->with($cellsGeomCTE)
            ->with($currentPeriodDataCTE)
            ->with($fullSamplingPackageResultsCTE)
            ->with($fullSamplingPackageResultsAggCTE)
            ->with($fullSamplingPackageElementsCTE)
            ->with($notFullSamplingPackageResultsCTE)
            ->with($notFullSamplingPackageResultsAggCTE)
            ->with($rowCTE)
            ->with($columnsCTE)
            ->select(
                'JSON_BUILD_OBJECT(
                    \'columns\', COALESCE(columns.data, \'[]\'::jsonb),
                    \'rows\', COALESCE(JSONB_AGG(row), \'[]\'::jsonb)
                )'
            )
            ->from('row, columns')
            ->groupBy('columns.data');

        $stmt = $conn->executeQuery($query->getSQL(), $params, $paramTypes);
        $result = $stmt->fetchOne();

        return json_decode($result, true);
    }

    public function getPackageGridPointIdsByLabElementGroup(LabElementGroup $labElementGroup): array
    {
        $conn = $this->getEntityManager()->getConnection();
        $qb = $conn->createQueryBuilder();

        $params = [
            'labElementGroupId' => $labElementGroup->getId(),
        ];

        $paramTypes = [
            'labElementGroupId' => ParameterType::INTEGER,
        ];

        $qb->select(
            'pgp.id',
        )
            ->from('package_grid_points', 'pgp')
            ->join('pgp', 'lab_elements_results', 'ler', 'ler.package_grid_points_id = pgp.id')
            ->join('ler', 'lab_element_group', 'leg', 'leg.id = ler.lab_element_group_id')
            ->where(
                'leg.id = :labElementGroupId'
            )
            ->distinct();

        $stmt = $conn->executeQuery($qb->getSQL(), $params, $paramTypes);

        return $stmt->fetchFirstColumn();
    }
}
