<?php

namespace App\Repository\Analysis;

use App\Entity\Analysis\LabAnalysisGroup;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|LabAnalysisGroup find($id, $lockMode = null, $lockVersion = null)
 * @method null|LabAnalysisGroup findOneBy(array $criteria, array $orderBy = null)
 * @method LabAnalysisGroup[] findAll()
 * @method LabAnalysisGroup[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LabAnalysisGroupRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LabAnalysisGroup::class);
    }

    // /**
    //  * @return LabAnalysisGroup[] Returns an array of LabAnalysisGroup objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('l.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?LabAnalysisGroup
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
