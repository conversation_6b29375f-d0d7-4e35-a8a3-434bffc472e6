<?php

namespace App\Repository\Analysis;

use App\Entity\Analysis\LabAnalysisGroupElement;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\Security;

/**
 * @method null|LabAnalysisGroupElement find($id, $lockMode = null, $lockVersion = null)
 * @method null|LabAnalysisGroupElement findOneBy(array $criteria, array $orderBy = null)
 * @method LabAnalysisGroupElement[] findAll()
 * @method LabAnalysisGroupElement[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LabAnalysisGroupElementRepository extends ServiceEntityRepository
{
    private Security $security;

    public function __construct(ManagerRegistry $registry, Security $security)
    {
        parent::__construct($registry, LabAnalysisGroupElement::class);
        $this->security = $security;
    }

    /**
     * @return array
     */
    public function elementsForSoilMap(array $filters = [])
    {
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();
        $connection = $this->getEntityManager()->getConnection();

        $params = [
            'serviceProviderId' => $serviceProviderId,
        ];

        $paramTypes = [
            'serviceProviderId' => ParameterType::INTEGER,
        ];

        $qb = new QueryBuilder($connection);

        $qb->select(
            'lage.element',
            'lage.color',
            'lagevo.visual_order'
        )
            ->from('lab_analysis_group_element', 'lage')
            ->join('lage', 'lab_analysis_group_element_visual_order', 'lagevo', 'lagevo.lab_analysis_group_element_id = lage.id')
            ->where('lage.has_soil_map = TRUE')
            ->andWhere('lagevo.service_provider_id = :serviceProviderId');

        if (isset($filters['meta_groups'])) {
            $qb->join('lage', 'meta_elements_groups', 'meg', 'meg.element = lage.element');
            $qb->join('meg', 'meta_groups', 'mg', 'mg.id = meg.group_id AND mg.name IN (:metaGroups)');

            $params['metaGroups'] = json_decode($filters['meta_groups'], true);
            $paramTypes['metaGroups'] = Connection::PARAM_STR_ARRAY;
        }

        $stmt = $connection->executeQuery($qb->getSQL(), $params, $paramTypes);

        return $stmt->fetchAllAssociative();
    }

    /**
     * @throws DBALException
     */
    public function getElementsUnit()
    {
        $conn = $this->getEntityManager()->getConnection();

        $qb = $conn->createQueryBuilder()
            ->select(
                'JSON_OBJECT_AGG(lage."element", unit)'
            )
            ->from('lab_analysis_group_element', 'lage')
            ->join(
                'lage',
                'lab_analysis_group_element_visual_order',
                'lagevo',
                'lagevo.lab_analysis_group_element_id = lage.id'
            )
            ->where('lagevo.service_provider_id = :serviceProviderId');

        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();
        $stmt = $conn->executeQuery(
            $qb->getSQL(),
            ['serviceProviderId' => $serviceProviderId],
            ['serviceProviderId' => ParameterType::INTEGER]
        );
        $results = $stmt->fetchOne();

        return json_decode($results, true);
    }

    public function elementExists(string $element): bool
    {
        $conn = $this->getEntityManager()->getConnection();

        $qb = new QueryBuilder($conn);
        $qb->select('(:element)::elements_enum')
            ->setParameters(
                ['element' => $element],
                ['element' => ParameterType::STRING]
            );

        try {
            $qb->execute();
        } catch (DBALException $e) {
            return false;
        }

        return true;
    }
}
