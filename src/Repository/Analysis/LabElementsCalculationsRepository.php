<?php

namespace App\Repository\Analysis;

use App\Entity\Analysis\LabElementsCalculations;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|LabElementsCalculations find($id, $lockMode = null, $lockVersion = null)
 * @method null|LabElementsCalculations findOneBy(array $criteria, array $orderBy = null)
 * @method LabElementsCalculations[] findAll()
 * @method LabElementsCalculations[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LabElementsCalculationsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LabElementsCalculations::class);
    }

    public function getCalculationAndCoefficientByElement()
    {
        $connection = $this->getEntityManager()->getConnection();

        $query = 'SELECT JSON_OBJECT_AGG(
            element, JSON_BUILD_OBJECT(
                \'coefficient\', coefficient,
                \'operation\', operation
            )
        )
        FROM lab_elements_calculations';

        $statement = $connection->prepare($query);
        $statement->execute();
        $result = $statement->fetchColumn(0);

        return json_decode($result, true);
    }

    // /**
    //  * @return LabElementsCalculations[] Returns an array of LabElementsCalculations objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('l.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?LabElementsCalculations
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
