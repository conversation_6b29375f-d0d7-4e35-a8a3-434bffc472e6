<?php

namespace App\Repository\Analysis;

use App\Entity\Analysis\MetaGroups;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|MetaGroups find($id, $lockMode = null, $lockVersion = null)
 * @method null|MetaGroups findOneBy(array $criteria, array $orderBy = null)
 * @method MetaGroups[] findAll()
 * @method MetaGroups[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MetaGroupsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MetaGroups::class);
    }

    // /**
    //  * @return MetaGroups[] Returns an array of MetaGroups objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('m.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?MetaGroups
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
