<?php

namespace App\Repository\Analysis;

use App\Entity\Analysis\LabElementsResults;
use App\Repository\BaseRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\ORM\Configuration;
use Doctrine\ORM\Query;
use Doctrine\ORM\Query\Expr;
use Doctrine\Persistence\ManagerRegistry;
use Somnambulist\CTEBuilder\Expression;
use Somnambulist\CTEBuilder\ExpressionBuilder;
use Symfony\Component\Routing\Exception\InvalidParameterException;
use Symfony\Component\Security\Core\Security;

/**
 * @method null|LabElementsResults find($id, $lockMode = null, $lockVersion = null)
 * @method null|LabElementsResults findOneBy(array $criteria, array $orderBy = null)
 * @method LabElementsResults[] findAll()
 * @method LabElementsResults[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LabElementsResultsRepository extends BaseRepository
{
    private Security $security;
    private ManagerRegistry $managerRegistry;

    public function __construct(ManagerRegistry $registry, Security $security)
    {
        parent::__construct($registry, LabElementsResults::class);
        $this->managerRegistry = $registry;
        $this->security = $security;
    }

    /**
     * @return Query
     */
    public function findByIdsQuery(array $arrIds)
    {
        $qb = $this->createQueryBuilder('LabElementsResults');

        $qb->select('LabElementsResults');
        $qb->andWhere('LabElementsResults.packageGridPoints IN (:arrIds)');
        $qb->setParameter('arrIds', $arrIds);

        return $qb->getQuery();
    }

    public function getRecommendationElementsResults($labElementGroupIds)
    {
        $connection = $this->getEntityManager()->getConnection();
        $query = $this->getRecommendationElementsResultsQuery($labElementGroupIds);

        $selectStmt = '
            SELECT row
            FROM rows ORDER BY row->>\'lab_number\' ASC ';

        $query .= $selectStmt;
        $statement = $connection->prepare($query);
        $statement->execute();

        return $statement->fetchAll();
    }

    public function getElementsResults($filters, $withPagination, $page = 0, $limit = 0)
    {
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();

        $connection = $this->getEntityManager()->getConnection();
        $eb = new ExpressionBuilder($connection);

        $params = [
            'serviceProviderId' => $serviceProviderId,
        ];
        $paramTypes = [
            'serviceProviderId' => ParameterType::INTEGER,
        ];

        $filteredPointsQb = $eb->createQuery()
            ->select(
                'COUNT(*) OVER () AS total_count',
                'pgp.id',
                'pgp.plot_uuid',
                'pgp.point_uuid',
                'pgp.lab_number',
                'pgp.package_id',
                'pgp.sample_id',
                'pgp.sampling_type_id',
                'ARRAY_AGG(ler.id) AS ler_ids',
            )
            ->from('package_grid_points', 'pgp')
            ->join(
                'pgp',
                'subscription_package',
                'sp',
                'sp.id = pgp.package_id AND sp.service_provider_id = :serviceProviderId'
            )
            ->join(
                'pgp',
                'lab_elements_results',
                'ler',
                'ler.package_grid_points_id = pgp.id AND ler.state IN (\'For Analysis\', \'Analysed\', \'Approved\', \'For reanalysis\')'
            );

        foreach ($filters as $key => $value) {
            switch ($key) {
                case 'lab_number':
                    $filteredPointsQb->andWhere($filteredPointsQb->expr()->like('LOWER(pgp.lab_number)', ':labNumber'));
                    $params['labNumber'] = strtolower("%{$value}%");
                    $paramTypes['labNumber'] = ParameterType::STRING;

                    break;
                case 'plot_uuid':
                    $filteredPointsQb->andWhere($filteredPointsQb->expr()->eq('pgp.plot_uuid', ':plotUuid'));
                    $params['plotUuid'] = $value;
                    $paramTypes['plotUuid'] = ParameterType::STRING;

                    break;
                case 'package_id':
                    $filteredPointsQb->andWhere($filteredPointsQb->expr()->eq('pgp.package_id', ':packageId'));
                    $params['packageId'] = $value;
                    $paramTypes['packageId'] = ParameterType::INTEGER;

                    break;
                case 'elements_states':
                    $elementsStatesJson = json_encode($value);
                    $filteredPointsQb->having('JSONB_OBJECT_AGG(ler."element", ler.state) @> :elementsStates::jsonb');
                    $params['elementsStates'] = $elementsStatesJson;
                    $paramTypes['elementsStates'] = ParameterType::STRING;

                    break;
            }
        }

        $filteredPointsQb->groupBy('pgp.id');
        $filteredPointsQb->addOrderBy('pgp.plot_uuid', 'ASC');
        $filteredPointsQb->addOrderBy('pgp.sampling_type_id', 'ASC');

        if ($withPagination) {
            $offset = ($page - 1) * $limit;
            $filteredPointsQb->setMaxResults($limit);
            $filteredPointsQb->setFirstResult($offset);
        }

        $elementsQb = $eb->createQuery()
            ->select(
                'fp.total_count',
                'fp.package_id',
                'fp.plot_uuid',
                'fp.point_uuid',
                'fp.sample_id AS cell_number',
                'fp.lab_number AS lab_number',
                'st.type AS soil_layer_cm',
                'JSONB_BUILD_OBJECT(
                    \'id\', ler.id,
                    \'name\', ler.element,
                    \'state\', ler.state,
                    \'uploaded_date\', TO_CHAR(lau.date, \'YYYY-MM-DD\'),
                    \'uploaded_by\', lau.user_id,
                    \'file\', SPLIT_PART(lau.file_path, \'public\', 2),
                    \'groups\', JSONB_AGG(DISTINCT meg.group_id),
                    \'value\', ROUND(ler.value::numeric, 2),
                    \'color\', leic.color
                ) AS element'
            )
            ->from('filtered_points', 'fp')
            ->join(
                'fp',
                'lab_elements_results',
                'ler',
                'ler.id = ANY(fp.ler_ids) AND ler.package_grid_points_id = fp.id'
            )
            ->join('ler', 'lab_analysis_group_element', 'lage', 'lage.element = ler.element')
            ->join(
                'lage',
                'lab_analysis_group_element_visual_order',
                'lagevo',
                'lagevo.lab_analysis_group_element_id = lage.id AND lagevo.service_provider_id = :serviceProviderId'
            )
            ->join('ler', 'meta_elements_groups', 'meg', 'meg.element = ler.element')
            ->join('ler', 'lab_elements_results_raw', 'lerr', 'ler.lab_elements_results_raw_id = lerr.id')
            ->leftJoin('lerr', 'lab_analysis_uploads', 'lau', 'lerr.lab_analisys_uploads_id = lau.id')
            ->leftJoin('fp', 'sampling_type', 'st', 'st.id = fp.sampling_type_id')
            ->leftJoin(
                'lage',
                'lab_element_interpretations_config',
                'leic',
                'leic.element_id = lage.id AND ler.value::numeric <@ leic.range'
            )
            ->groupBy([
                'fp.total_count',
                'fp.id',
                'fp.package_id',
                'fp.plot_uuid',
                'fp.point_uuid',
                'fp.sample_id',
                'fp.lab_number',
                'st.type',
                'ler.id',
                'lau.date',
                'lau.user_id',
                'lau.file_path',
                'leic.color',
            ]);

        $rowsQb = $eb->createQuery()
            ->select(
                'JSONB_BUILD_OBJECT(
                    \'plot_uuid\', elements.plot_uuid,
                    \'point_uuid\', elements.point_uuid,
                    \'cell_number\', elements.cell_number,
                    \'soil_layer_cm\', elements.soil_layer_cm,
                    \'package_id\', elements.package_id,
                    \'lab_number\', elements.lab_number,
                    \'elements\', jsonb_agg(elements.element ORDER BY lower(element->>\'name\') ASC)
                 ) AS row',
                'elements.total_count'
            )
            ->from('elements')
            ->groupBy([
                'elements.total_count',
                'elements.lab_number',
                'elements.plot_uuid',
                'elements.point_uuid',
                'elements.package_id',
                'elements.cell_number',
                'elements.soil_layer_cm',
            ])
            ->addOrderBy('elements.plot_uuid', 'ASC')
            ->addOrderBy('(elements.cell_number)::int', 'ASC')
            ->addOrderBy('elements.soil_layer_cm', 'ASC');

        $filteredPointsCTE = new Expression('filtered_points', $filteredPointsQb);
        $elementsCTE = new Expression('elements', $elementsQb);
        $rowsCTE = new Expression('rows', $rowsQb);

        $query = $eb->with($filteredPointsCTE)
            ->with($elementsCTE)
            ->with($rowsCTE)
            ->select(
                'row',
                'total_count'
            )
            ->from('rows');

        $stmt = $connection->executeQuery($query->getSQL(), $params, $paramTypes);

        return $stmt->fetchAllAssociative();
    }

    public function getElementsResultsForApprove(array $filter): array
    {
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();
        $em = $this->getEntityManager();
        $conn = $em->getConnection();
        $eb = new ExpressionBuilder($conn);

        $env = getenv();

        $susiMainDBName = $this->security->getUser()->getServiceProvider()->getCountry()->getDatabaseName();
        $susiMainDBConnStr = 'host=' . $env['SUSI_MAIN_DB_HOST']
            . ' port=' . $env['SUSI_MAIN_DB_PORT']
            . ' dbname=' . $susiMainDBName
            . ' user=' . $env['SUSI_MAIN_DB_USER']
            . ' password=' . $env['SUSI_MAIN_DB_PASS'];

        $params = [
            'serviceProviderId' => $serviceProviderId,
            'contractId' => $filter['contract_id'],
            'subscriptionPackageId' => $filter['subscription_package_id'],
            'susiMainDbConn' => $susiMainDBConnStr,
            'elementResultStates' => [
                LabElementsResults::STATE_FOR_ANALYSIS,
                LabElementsResults::STATE_ANALYSED,
                LabElementsResults::STATE_APPROVED,
                LabElementsResults::STATE_FOR_REANALYSIS,
            ],
        ];

        $paramTypes = [
            'serviceProviderId' => ParameterType::INTEGER,
            'contractId' => ParameterType::INTEGER,
            'subscriptionPackageId' => ParameterType::INTEGER,
            'susiMainDbConn' => ParameterType::STRING,
            'elementResultStates' => Connection::PARAM_STR_ARRAY,
        ];

        /**
         * @var QueryBuilder $contractPackagesWithPlotsQb
         *                   Query for getting packages and plots for all periods of the contract
         */
        $contractPackagesWithPlotsQb = $eb->createQuery()
            ->select(
                'c.id AS contract_id',
                'sp.id AS package_id',
                'sp.period',
                'CONCAT(TO_CHAR(sp.start_date, \'mm/YYYY\'), \'-\', TO_CHAR(sp.end_date, \'mm/YYYY\')) AS period_date',
                'spf.id AS subscription_package_field_id',
                'spf.plot_uuid',
                'spf.order_uuid',
                'spf.parent_id',
                'p.is_full_sampling',
                'CASE WHEN sp.id = :subscriptionPackageId
                    THEN TRUE
                    ELSE FALSE
                END AS is_current_period'
            )
            ->from('contract', 'c')
            ->join('c', 'subscription_contracts', 'sc', 'sc.id = c.id')
            ->join('c', 'subscription_package', 'sp', 'sp.contract_id = c.id')
            ->join('sp', 'package', 'p', 'p.id = sp.package_id AND p.is_sampling = TRUE')
            ->join('sp', 'subscription_package_field', 'spf', 'spf.subscription_package_id = sp.id')
            ->where('c.id = :contractId');

        if (isset($filter['plot_uuids'])) {
            $params['plotUuids'] = json_decode($filter['plot_uuids']);
            $paramTypes['plotUuids'] = Connection::PARAM_STR_ARRAY;
            $contractPackagesWithPlotsQb->andWhere('spf.plot_uuid IN (:plotUuids)');
        }

        /**
         * @var QueryBuilder $ordersPlotsUuidsQb
         *                   Query for getting all plot_uuids and order_uuids for all periods of the contract
         */
        $ordersPlotsUuidsQb = $eb->createQuery()
            ->select(
                'ARRAY_AGG(cpwp.plot_uuid) AS plot_uuids',
                'ARRAY_AGG(cpwp.order_uuid) AS order_uuids'
            )
            ->from('contract_packages_with_plots', 'cpwp');

        /**
         * @var QueryBuilder $cellsGeomQb
         *                   Query for getting cell geojson geometry for all package grid points of the contract
         */
        $cellsGeomQb = $eb->createQuery()
            ->select(
                'point_uuid',
                'geom_json'
            )
            ->from('orders_plots_uuids', 'opu')
            ->leftJoin(
                'opu',
                'dblink(
                    :susiMainDbConn,
                    FORMAT(
                        $$
                            SELECT
                                ssp.uuid AS point_uuid,
                                ST_AsGeoJSON(ST_Transform(ssg.geom, 3857))::JSONB AS geom_json
                            FROM
                                su_satellite_orders_plots_rel sopr
                            JOIN su_satellite_soil_points AS ssp
                                ON ssp.sopr_id = sopr.id
                            JOIN su_satellite_soil_grid AS ssg
                                ON ssg.sopr_id = ssp.sopr_id
                                AND ssg.sample_id = ssp.sample_id
                            WHERE
                                sopr.plot_uuid = ANY(%L)
                                AND sopr.order_uuid = ANY(%L)
                        $$,
                        plot_uuids, order_uuids
                    )
                )',
                'plot_cells_geom(point_uuid UUID, geom_json JSONB)',
                'true'
            );

        /**
         * @var QueryBuilder $currentPeriodDataQb
         *                   Query for getting package, plots and cells data (package grid points)
         *                   for the current period of the contract (:subscriptionPackageId)
         */
        $currentPeriodDataQb = $eb->createQuery()
            ->select(
                'cpwp.contract_id',
                'cpwp.subscription_package_field_id',
                'cpwp.parent_id',
                'pgp.sample_id AS cell_number',
                'pgp.lab_number',
                'cpwp.package_id',
                'cpwp.period_date',
                'cpwp.period',
                'cpwp.plot_uuid',
                'cpwp.is_full_sampling',
                'st.id AS sampling_type_id',
                'st.type AS soil_layer_cm',
                'pgp.id AS package_grid_points_id',
                'pgp.point_uuid',
                'CASE WHEN cg.geom_json NOTNULL
                    THEN JSONB_BUILD_OBJECT(
                            \'type\', \'Feature\',
                            \'geometry\', cg.geom_json
                        )
                    ELSE NULL
                END AS cell_geom_json'
            )
            ->from('contract_packages_with_plots', 'cpwp')
            ->join(
                'cpwp',
                'package_grid_points',
                'pgp',
                '
                    pgp.package_id = cpwp.package_id
                    AND pgp.package_type = \'subscription\'
                    AND pgp.plot_uuid = cpwp.plot_uuid
                '
            )
            ->join('pgp', 'sampling_type', 'st', 'st.id = pgp.sampling_type_id')
            ->leftJoin('pgp', 'cells_geom', 'cg', 'cg.point_uuid = pgp.point_uuid::UUID')
            ->where('cpwp.is_current_period = TRUE')
            ->orderBy('cpwp.subscription_package_field_id', 'ASC')
            ->addOrderBy('pgp.sample_id', 'ASC')
            ->addOrderBy('st.type', 'ASC');

        /**
         * @var QueryBuilder $otherPeriodsDataQb
         *                   Query for getting package, plots and cells data (package grid points)
         *                   for the all other (different than the current) periods of the contract
         */
        $otherPeriodsDataQb = $eb->createQuery()
            ->select(
                'opd.contract_id',
                'opd.package_id',
                'opd.subscription_package_field_id',
                'opd.parent_id',
                'opd.period_date',
                'opd.period',
                'opd.plot_uuid',
                'cpd.plot_uuid AS current_period_plot_uuid',
                'opd.is_full_sampling',
                'pgp.id AS package_grid_points_id',
                'pgp.sample_id AS cell_number',
                'pgp.lab_number',
                'pgp.point_uuid',
                'st.id AS sampling_type_id',
                'st.type AS soil_layer_cm',
                'CASE WHEN cg.geom_json NOTNULL
                    THEN JSONB_BUILD_OBJECT(
                            \'type\', \'Feature\',
                            \'geometry\', cg.geom_json
                        )
                    ELSE NULL
                END AS cell_geom_json'
            )
            ->from('current_period_data', 'cpd')
            ->join(
                'cpd',
                'contract_packages_with_plots',
                'opd',
                '
                    opd.is_current_period = false
                    AND (
                        ( -- if the current period package is full sampling then match plots from other periods by parent_id 
                            cpd.is_full_sampling = TRUE
                            AND opd.parent_id = cpd.subscription_package_field_id
                        ) OR (
                            -- if the current period package is not full sampling 
                            cpd.is_full_sampling = FALSE
                            AND (
                                ( -- match the plots from full sampling package
                                    opd.is_full_sampling = TRUE 
                                    AND opd.parent_id ISNULL
                                    AND opd.subscription_package_field_id = cpd.parent_id
                                )
                                OR ( -- match the plots from not full sampling packages
                                    opd.is_full_sampling = FALSE 
                                    AND opd.parent_id = cpd.parent_id
                                )
                            )
                        )
                    )
                '
            )
            ->join(
                'opd',
                'package_grid_points',
                'pgp',
                '
                    pgp.package_id = opd.package_id
                    AND pgp.package_type = \'subscription\'
                    AND pgp.plot_uuid = opd.plot_uuid
                    AND pgp.sample_id = cpd.cell_number
                    AND pgp.sampling_type_id = cpd.sampling_type_id
                '
            )
            ->join('pgp', 'sampling_type', 'st', 'st.id = pgp.sampling_type_id')
            ->leftJoin('pgp', 'cells_geom', 'cg', 'cg.point_uuid = pgp.point_uuid::UUID')
            ->orderBy('opd.subscription_package_field_id', 'ASC')
            ->addOrderBy('pgp.sample_id', 'ASC')
            ->addOrderBy('st.type', 'ASC');

        /**
         * @var QueryBuilder $currentPeriodResultsQb
         *                   Query for getting elements results for the current period of the contract (:subscriptionPackageId)
         */
        $currentPeriodResultsQb = $eb->createQuery()
            ->select(
                'cpd.contract_id',
                'cpd.package_id',
                'cpd.period',
                'cpd.period_date',
                'cpd.subscription_package_field_id',
                'cpd.plot_uuid',
                'cpd.cell_number',
                'cpd.lab_number',
                'cpd.soil_layer_cm',
                'JSONB_BUILD_OBJECT(
                    \'id\', ler.id,
                    \'name\', ler.element,
                    \'state\', ler.state,
                    \'uploaded_date\', to_char(lau.date, \'YYYY-MM-DD\'), 
                    \'uploaded_by\', lau.user_id, 
                    \'file\', SPLIT_PART(lau.file_path, \'public\', 2), 
                    \'groups\', JSONB_AGG(DISTINCT meg.group_id), 
                    \'value\', ROUND(ler.value::NUMERIC, 2),
                    \'visual_order\', lagevo.visual_order,
                    \'class\', json_build_object(
                                \'id\', licc.id,
                                \'color\', leic.color,
                                \'description\', licc.description,
                                \'slug\', licc.slug
                        )
                ) as element',
                'cpd.cell_geom_json'
            )
            ->from('current_period_data', 'cpd')
            ->join('cpd', 'lab_elements_results', 'ler', 'ler.package_grid_points_id = cpd.package_grid_points_id')
            ->join('ler', 'lab_analysis_group_element', 'lage', 'lage.element = ler.element')
            ->join(
                'lage',
                'lab_analysis_group_element_visual_order',
                'lagevo',
                '
                    lagevo.lab_analysis_group_element_id = lage.id
                    AND lagevo.service_provider_id = :serviceProviderId
                '
            )
            ->leftJoin('ler', 'lab_elements_results_raw', 'lerr', 'lerr.id = ler.lab_elements_results_raw_id')
            ->leftJoin('lerr', 'lab_analysis_uploads', 'lau', 'lau.id = lerr.lab_analisys_uploads_id')
            ->leftJoin(
                'lage',
                'lab_element_interpretations_config',
                'leic',
                '
                    leic.service_provider_id = :serviceProviderId
                    AND leic.element_id = lage.id
                    AND ler.value::NUMERIC <@ leic.range
                '
            )
            ->leftJoin('leic', 'lab_interpretation_classes_config', 'licc', 'licc.id = leic.class_id')
            ->leftJoin('lage', 'meta_elements_groups', 'meg', 'meg.element = lage.element')
            ->where('ler.state IN (:elementResultStates)')
            ->groupBy(
                'ler.id',
                'lau.id',
                'lagevo.visual_order',
                'leic.color',
                'cpd.contract_id',
                'cpd.package_id',
                'cpd.period',
                'cpd.period_date',
                'cpd.subscription_package_field_id',
                'cpd.plot_uuid',
                'cpd.lab_number',
                'cpd.cell_number',
                'cpd.plot_uuid',
                'cpd.soil_layer_cm',
                'cpd.cell_geom_json',
                'licc.id',
                'licc.description',
                'licc.slug'
            )
            ->orderBy('cpd.subscription_package_field_id', 'ASC')
            ->addOrderBy('cpd.cell_number', 'ASC')
            ->addOrderBy('cpd.soil_layer_cm', 'ASC');

        /**
         * @var QueryBuilder $currentPeriodResultsAggQb
         *                   Query for getting aggregated elements results for the current period of the contract (:subscriptionPackageId)
         */
        $currentPeriodResultsAggQb = $eb->createQuery()
            ->select(
                'contract_id',
                'package_id',
                'period',
                'period_date',
                'subscription_package_field_id',
                'plot_uuid',
                'cell_number',
                'lab_number',
                'soil_layer_cm',
                'JSONB_AGG(element ORDER BY (element->>\'visual_order\')::INT) AS elements',
                'CASE WHEN cell_geom_json NOTNULL
                    THEN (
                            cell_geom_json::JSONB || 
                            JSONB_BUILD_OBJECT(\'properties\', JSONB_OBJECT_AGG(element->>\'name\', element->>\'color\'))
                        )::JSONB 
                    ELSE NULL
                END AS cell_geom_json'
            )
            ->from('current_period_results', 'cpd')
            ->groupBy(
                'contract_id',
                'package_id',
                'period',
                'period_date',
                'subscription_package_field_id',
                'plot_uuid',
                'cell_number',
                'lab_number',
                'soil_layer_cm',
                'cell_geom_json'
            );

        /**
         * @var QueryBuilder $currentPeriodElementsQb
         *                   Query for getting all elements from the current period of the contract (:subscriptionPackageId)
         */
        $currentPeriodElementsQb = $eb->createQuery()
            ->select(
                'DISTINCT JSONB_BUILD_OBJECT(
                    \'name\', element->>\'name\',
                    \'groups\', element->\'groups\',
                    \'visual_order\', (element->>\'visual_order\')::int
                ) AS element_data'
            )
            ->from('current_period_results', 'cpr');

        /**
         * @var QueryBuilder $otherPeriodsResultsQb
         *                   Query for getting elements results for the other periods (different than the current) of the contract
         */
        $otherPeriodsResultsQb = $eb->createQuery()
            ->select(
                'opd.contract_id',
                'opd.package_id',
                'opd.period',
                'opd.period_date',
                'opd.subscription_package_field_id',
                'opd.plot_uuid',
                'opd.current_period_plot_uuid',
                'opd.cell_number',
                'opd.lab_number',
                'opd.soil_layer_cm',
                'JSONB_BUILD_OBJECT(
                    \'id\', ler.id,
                    \'name\', ler.element,
                    \'state\', ler.state,
                    \'uploaded_date\', TO_CHAR(lau.date, \'YYYY-MM-DD\'), 
                    \'uploaded_by\', lau.user_id, 
                    \'file\', SPLIT_PART(lau.file_path, \'public\', 2), 
                    \'groups\', JSONB_AGG(DISTINCT meg.group_id), 
                    \'value\', ROUND(ler.value::numeric, 2),
                    \'visual_order\', lagevo.visual_order,
                    \'class\', json_build_object(
                                \'id\', licc.id,
                                \'color\', leic.color,
                                \'description\', licc.description,
                                \'slug\', licc.slug
                        )
                ) as element',
                'opd.cell_geom_json'
            )
            ->from('other_periods_data', 'opd')
            ->join('opd', 'lab_elements_results', 'ler', 'ler.package_grid_points_id = opd.package_grid_points_id')
            ->join('ler', 'lab_analysis_group_element', 'lage', 'lage.element = ler.element')
            ->join(
                'lage',
                'lab_analysis_group_element_visual_order',
                'lagevo',
                '
                    lagevo.lab_analysis_group_element_id = lage.id
                    AND lagevo.service_provider_id = :serviceProviderId
                '
            )
            ->leftJoin('ler', 'lab_elements_results_raw', 'lerr', 'lerr.id = ler.lab_elements_results_raw_id')
            ->leftJoin('lerr', 'lab_analysis_uploads', 'lau', 'lau.id = lerr.lab_analisys_uploads_id')
            ->leftJoin(
                'lage',
                'lab_element_interpretations_config',
                'leic',
                '
                    leic.service_provider_id = :serviceProviderId
                    AND leic.element_id = lage.id
                    AND ler.value::NUMERIC <@ leic."range"
                '
            )
            ->leftJoin('leic', 'lab_interpretation_classes_config', 'licc', 'licc.id = leic.class_id')
            ->leftJoin('lage', 'meta_elements_groups', 'meg', 'meg.element = lage.element')
            ->where('ler.state IN (:elementResultStates)')
            ->groupBy(
                'ler.id',
                'lau.id',
                'lagevo.visual_order',
                'leic.color',
                'opd.contract_id',
                'opd.package_id',
                'opd.period',
                'opd.period_date',
                'opd.subscription_package_field_id',
                'opd.plot_uuid',
                'opd.current_period_plot_uuid',
                'opd.lab_number',
                'opd.cell_number',
                'opd.plot_uuid',
                'opd.soil_layer_cm',
                'opd.cell_geom_json',
                'licc.id',
                'licc.description',
                'licc.slug'
            )
            ->orderBy('opd.subscription_package_field_id', 'ASC')
            ->addOrderBy('opd.cell_number', 'ASC')
            ->addOrderBy('opd.soil_layer_cm', 'ASC');

        /**
         * @var QueryBuilder $otherPeriodsResultsAggQb
         *                   Query for getting aggregated elements results for the other periods (different than the current) of the contract
         */
        $otherPeriodsResultsAggQb = $eb->createQuery()
            ->select(
                'CONCAT(plot_uuid, \'/\', package_id, \'/\', cell_number, \'/\', soil_layer_cm) AS key',
                'cell_number',
                'contract_id',
                'current_period_plot_uuid',
                'lab_number',
                'package_id',
                'period',
                'period_date',
                'plot_uuid',
                'soil_layer_cm',
                'JSONB_AGG(element ORDER BY (element->>\'visual_order\')::int) AS elements',
                'CASE WHEN cell_geom_json NOTNULL
                    THEN (
                            cell_geom_json::JSONB || 
                            JSONB_BUILD_OBJECT(\'properties\', JSONB_OBJECT_AGG(element->>\'name\', element->>\'color\'))
                        )::JSONB 
                    ELSE NULL
                END AS cell_geom_json'
            )
            ->from('other_periods_results', 'opr')
            ->groupBy(
                'current_period_plot_uuid',
                'cell_number',
                'soil_layer_cm',
                'contract_id',
                'lab_number',
                'package_id',
                'period',
                'period_date',
                'plot_uuid',
                'soil_layer_cm',
                'cell_geom_json'
            );

        /**
         * @var QueryBuilder $rowQb
         *                   Query for getting parent row (the row for the current period) with children (the rows for all other periods)
         */
        $rowQb = $eb->createQuery()
            ->select(
                'CONCAT(cpra.plot_uuid, \'/\', cpra.package_id, \'/\', cpra.cell_number, \'/\', cpra.soil_layer_cm) AS key',
                'cpra.plot_uuid',
                'cpra.package_id',
                'cpra.cell_number',
                'cpra.soil_layer_cm',
                'cpra.period',
                'cpra.period_date',
                'cpra.lab_number',
                'cpra.elements',
                'cpra.cell_geom_json',
                'CASE WHEN opra.plot_uuid NOTNULL
                    THEN JSONB_AGG(opra ORDER BY opra.period DESC)
                    ELSE NULL
                END AS children'
            )
            ->from('current_period_results_agg', 'cpra')
            ->leftJoin(
                'cpra',
                'other_periods_results_agg',
                'opra',
                '
                    opra.current_period_plot_uuid = cpra.plot_uuid
                    AND opra.cell_number = cpra.cell_number
                    AND opra.soil_layer_cm = cpra.soil_layer_cm
                '
            )
            ->groupBy(
                'cpra.plot_uuid',
                'cpra.package_id',
                'cpra.cell_number',
                'cpra.soil_layer_cm',
                'cpra.period',
                'cpra.period_date',
                'cpra.lab_number',
                'cpra.elements',
                'opra.plot_uuid',
                'cpra.cell_geom_json'
            )
            ->orderBy('cpra.plot_uuid', 'ASC')
            ->addOrderBy('cpra.cell_number', 'ASC')
            ->addOrderBy('cpra.soil_layer_cm', 'ASC');

        /**
         * @var QueryBuilder $columnsQb
         *                   Query for getting the columns from elements of the current period package
         */
        $columnsQb = $eb->createQuery()
            ->select(
                'JSONB_AGG(element_data ORDER BY (element_data->>\'visual_order\')::INT ASC) AS data'
            )
            ->from('current_period_elements', 'cpe');

        $contractPackagesWithPlotsCTE = new Expression('contract_packages_with_plots', $contractPackagesWithPlotsQb);
        $ordersPlotsUuidsCTE = new Expression('orders_plots_uuids', $ordersPlotsUuidsQb);
        $cellsGeomCTE = new Expression('cells_geom', $cellsGeomQb);
        $currentPeriodDataCTE = new Expression('current_period_data', $currentPeriodDataQb);
        $otherPeriodsDataCTE = new Expression('other_periods_data', $otherPeriodsDataQb);
        $currentPeriodResultsCTE = new Expression('current_period_results', $currentPeriodResultsQb);
        $currentPeriodResultsAggCTE = new Expression('current_period_results_agg', $currentPeriodResultsAggQb);
        $currentPeriodElementsCTE = new Expression('current_period_elements', $currentPeriodElementsQb);
        $otherPeriodsResultsCTE = new Expression('other_periods_results', $otherPeriodsResultsQb);
        $otherPeriodsResultsAggCTE = new Expression('other_periods_results_agg', $otherPeriodsResultsAggQb);
        $rowCTE = new Expression('row', $rowQb);
        $columnsCTE = new Expression('columns', $columnsQb);

        $query = $eb->with($contractPackagesWithPlotsCTE)
            ->with($ordersPlotsUuidsCTE)
            ->with($cellsGeomCTE)
            ->with($currentPeriodDataCTE)
            ->with($otherPeriodsDataCTE)
            ->with($currentPeriodResultsCTE)
            ->with($currentPeriodResultsAggCTE)
            ->with($currentPeriodElementsCTE)
            ->with($otherPeriodsResultsCTE)
            ->with($otherPeriodsResultsAggCTE)
            ->with($rowCTE)
            ->with($columnsCTE)
            ->select(
                'columns.data AS columns',
                'JSONB_AGG(row) AS rows'
            )
            ->from('columns, row')
            ->groupBy(
                'columns.data'
            );

        $stmt = $conn->executeQuery($query->getSQL(), $params, $paramTypes);
        $result = $stmt->fetchAssociative();

        return [
            'columns' => json_decode($result['columns'] ?? '[]', true),
            'rows' => json_decode($result['rows'] ?? '[]', true),
        ];
    }

    public function getElementsResultsForCSV($filters)
    {
        $connection = $this->getEntityManager()->getConnection();
        $result = $this->getElementsResultsQuery($filters);
        $query = $result['query'];
        $elementsArrayFilterStr = $result['elementsArrayFilterStr'];

        $query = '
                SELECT * from crosstab($$' . $query;

        $visibleColumns = $filters['visible_columns'];

        $selectStmt = '
                SELECT 
                    row->>\'lab_number\',
                    jsonb_array_elements(row->\'elements\')->>\'name\' as element,
                    (jsonb_array_elements(row->\'elements\')->>\'value\')::float as value
                FROM rows
                ' . $elementsArrayFilterStr . '
                ORDER BY row->>\'lab_number\' ASC
                LIMIT 10000
                $$, $$ VALUES (\'' . implode("'), ('", $visibleColumns) . '\') $$)
                as ct("Lab ID" varchar(255), "' . implode('" float, "', $visibleColumns) . '" float);';

        $query .= $selectStmt;
        $statement = $connection->prepare($query);
        $statement->execute();

        return $statement->fetchAll();
    }

    public function getPlotsCountByState(array $filter)
    {
        if (!$filter['element_group_state']) {
            throw new InvalidParameterException('Lab element group state filter is missing', 400);
        }

        $qb = $this->createQueryBuilder('LabElementsResults');

        $qb->select('LabElementGroup.plotUuid', 'LabElementGroup.packageId', 'count(LabElementsResults.id) as count');
        $qb->join('App\Entity\Analysis\LabElementGroup', 'LabElementGroup', Expr\Join::WITH, 'LabElementGroup.id = LabElementsResults.labElementGroup');
        $qb->andWhere('LabElementGroup.state = (:state)');
        $qb->setParameter('state', $filter['element_group_state']);

        if (isset($filter['element_state_diff'])) {
            $qb->andWhere('LabElementsResults.state <> (:element_state_diff)');
            $qb->setParameter('element_state_diff', $filter['element_state_diff']);
        }

        if (isset($filter['plot_uuids']) && count(json_decode($filter['plot_uuids']))) {
            $qb->andWhere('LabElementGroup.plotUuid in(:plotUuids)');
            $qb->setParameter('plotUuids', json_decode($filter['plot_uuids']));
        }

        if (isset($filter['package_id'])) {
            $qb->andWhere('LabElementGroup.packageId in(:package_id)');
            $qb->setParameter('package_id', $filter['package_id']);
        }

        $qb->groupBy('LabElementGroup.plotUuid', 'LabElementGroup.packageId');

        return $qb->getQuery();
    }

    public function getResultsByCustomerIdentificationQb(array $customerIdentifications, $serviceProviderId, $state)
    {
        $qb = $this->createQueryBuilder('LabElementsResults');

        $qb->join('App\Entity\Analysis\LabElementGroup', 'LabElementGroup', Expr\Join::WITH, 'LabElementGroup.id = LabElementsResults.labElementGroup');
        $qb->join('App\Entity\Contract\SubscriptionPackage', 'SubscriptionPackage', Expr\Join::WITH, 'SubscriptionPackage.id = LabElementGroup.packageId');
        $qb->join('App\Entity\Contract', 'Contract', Expr\Join::WITH, 'Contract.id = SubscriptionPackage.contract');
        $qb->andWhere('LabElementGroup.state = (:state)')->setParameter('state', $state);
        $qb->andWhere('Contract.customerIdentification IN (:customerIdentifications)')->setParameter('customerIdentifications', $customerIdentifications);
        $qb->andWhere('Contract.serviceProvider = (:serviceProviderId)')->setParameter('serviceProviderId', $serviceProviderId);

        return $qb;
    }

    /**
     * @return \Doctrine\ORM\QueryBuilder
     */
    public function getAnalysesReportQB(array $filter): QueryBuilder
    {
        $config = new Configuration();
        $config->addCustomNumericFunction('ROUND', 'Oro\ORM\Query\AST\Functions\Numeric\Round');
        $config->addCustomStringFunction('CAST', 'Oro\ORM\Query\AST\Functions\Cast');

        $qb = $this->getEntityManager()->getConnection()->createQueryBuilder();

        $qb->from('contract', 'c')
            ->join('c', 'subscription_package', 'sp', 'sp.contract_id = c.id')
            ->join('sp', 'subscription_package_field', 'spf', 'spf.subscription_package_id = sp.id')
            ->join('sp', 'package', 'p', 'p.id = sp.package_id')
            ->join('spf', 'lab_element_group', 'leg', 'leg.package_id = sp.id AND leg.plot_uuid = spf.plot_uuid')
            ->join('leg', 'package_grid_points', 'pgp', 'pgp.package_id = leg.package_id AND pgp.plot_uuid = leg.plot_uuid')
            ->join('pgp', 'sampling_type', 'st', 'st.id = pgp.sampling_type_id')
            ->join('leg', 'lab_elements_results', 'ler', 'ler.lab_element_group_id = leg.id AND ler.package_grid_points_id = pgp.id')
            ->where('pgp.state = \'ReceivedInLab\'')
            ->andWhere('ler.state = \'Approved\'');

        if (isset($filter['subscription_packages_id'])) {
            $packages = json_decode($filter['subscription_packages_id'], true);
            $qb->andWhere('sp.id IN (' . implode(',', $packages) . ')');
        }

        if (isset($filter['plot_uuids'])) {
            $plotUuids = json_decode($filter['plot_uuids'], true);
            $qb->andWhere('pgp.plot_uuid IN (\'' . implode("','", $plotUuids) . '\')');
        }

        if (isset($filter['farm_ids'])) {
            $farmIds = json_decode($filter['farm_ids'], true);
            $qb->andWhere('spf.farm_id IN (\'' . implode("','", $farmIds) . '\')');
        }

        if (isset($filter['barcode'])) {
            $qb->andWhere('pgp.barcode = :barcode')->setParameter('barcode', $filter['barcode']);
        }

        if (isset($filter['lab_number'])) {
            $qb->andWhere('pgp.lab_number = :lab_number')->setParameter('lab_number', $filter['lab_number']);
        }

        if (isset($filter['sampling_type_ids'])) {
            $samplingTypeIds = json_decode($filter['sampling_type_ids']);
            $qb->andWhere('st.id IN (' . implode(',', $samplingTypeIds) . ')');
        }

        return $qb;
    }

    protected function getRecommendationElementsResultsQuery($labElementGroupIds)
    {
        $elementsQuery = '
            SELECT 
                pgp.plot_uuid,
                pgp.sample_id AS cell_number,
                pgp.package_id,
                pgp.lab_number AS lab_number,
                to_char(pgp.state_updated_at, \'YYYY-MM-DD\') AS result_date,
                lau.date as uploaded_date,
                json_build_object(
                    \'id\', ler.id,
                    \'name\', ler.element,
                    \'state\', ler.state,
                    \'uploaded_date\', to_char(lau.date, \'YYYY-MM-DD\'),
                    \'uploaded_by\', lau.user_id,
                    \'file\', split_part(lau.file_path, \'public\', 2),
                    \'groups\', array_agg(distinct meg.group_id),
                    \'value\', ler.value,
                    \'previous_values\', array_remove(array_agg(distinct prev_values.value), NULL)
                )::jsonb AS element,
                json_build_object(
                    \'name\', ler.element,
                    \'state\', ler.state
                )::jsonb AS element_state
            FROM lab_elements_results ler
            LEFT JOIN package_grid_points pgp ON pgp.id = ler.package_grid_points_id
            LEFT JOIN lab_elements_results_raw lerr ON ler.lab_elements_results_raw_id = lerr.id
            LEFT JOIN (
                SELECT 
                    json_build_object(
                        \'value\', CASE lec.operation 
                            WHEN \'*\' THEN lerr.value * lec.coefficient
                            WHEN \'/\' THEN lerr.value / lec.coefficient
                            WHEN \'+\' THEN lerr.value + lec.coefficient
                            WHEN \'-\' THEN lerr.value - lec.coefficient
                        END,
                        \'uploaded_date\', to_char(lau.date, \'YYYY-MM-DD\'),
                        \'uploaded_by\', lau.user_id,
                        \'file\', lau.file_path
                    )::jsonb AS value,
                    lerr.element,
                    lerr.lab_number,
                    lerr.id
                FROM 
                    lab_elements_results_raw lerr
                LEFT JOIN lab_analysis_uploads lau ON lerr.lab_analisys_uploads_id = lau.id
                LEFT JOIN lab_elements_calculations lec ON lec.element = lerr.element
            ) prev_values ON (
                ler.element = prev_values.element
                AND pgp.lab_number = prev_values.lab_number
                AND prev_values.id < ler.lab_elements_results_raw_id
            )
            LEFT JOIN lab_analysis_uploads lau ON lerr.lab_analisys_uploads_id = lau.id
            INNER JOIN meta_elements_groups meg ON meg.element = ler.element
            WHERE ler.lab_element_group_id IN (' . implode(',', $labElementGroupIds) . ')  AND ler.state IN (\'Approved\')
            GROUP BY 
                ler.element,
                ler.state,
                lau.date,
                lau.user_id,
                lau.file_path,
                pgp.id,
                ler.id
        ';

        $rowsQuery = '
            SELECT json_build_object(
                    \'plot_uuid\', elements.plot_uuid,
                    \'cell_number\', elements.cell_number,
                    \'package_id\', elements.package_id,
                    \'lab_number\', elements.lab_number,
                    \'result_date\', elements.result_date,
                    \'uploaded_date\', to_char(max(elements.uploaded_date), \'YYYY-MM-DD\'),
                    \'elements\', array_agg(elements.element ORDER BY lower(element->>\'name\') ASC)
                )::jsonb AS row,
                array_agg(elements.element_state) AS est
                FROM elements 
                GROUP BY 
                    elements.lab_number,
                    elements.plot_uuid,
                    elements.package_id,
		            elements.cell_number,
                    elements.result_date
        ';

        return 'WITH elements AS (' . $elementsQuery . '), rows AS (' . $rowsQuery . ')';
    }
}
