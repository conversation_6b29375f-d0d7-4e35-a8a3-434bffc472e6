<?php

namespace App\Repository\Analysis;

use App\Entity\Analysis\LabAnalysisGroupElementVisualOrder;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|LabAnalysisGroupElementVisualOrder find($id, $lockMode = null, $lockVersion = null)
 * @method null|LabAnalysisGroupElementVisualOrder findOneBy(array $criteria, array $orderBy = null)
 * @method LabAnalysisGroupElementVisualOrder[] findAll()
 * @method LabAnalysisGroupElementVisualOrder[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LabAnalysisGroupElementVisualOrderRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LabAnalysisGroupElementVisualOrder::class);
    }

    // /**
    //  * @return LabAnalysisGroupElementVisualOrder[] Returns an array of LabAnalysisGroupElementVisualOrder objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('l.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?LabAnalysisGroupElementVisualOrder
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
