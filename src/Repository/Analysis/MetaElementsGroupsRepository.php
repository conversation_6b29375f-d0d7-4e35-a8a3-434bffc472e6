<?php

namespace App\Repository\Analysis;

use App\Entity\Analysis\MetaElementsGroups;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\ParameterType;
use Doctrine\Persistence\ManagerRegistry;
use Somnambulist\CTEBuilder\Expression;
use Somnambulist\CTEBuilder\ExpressionBuilder;
use Symfony\Component\Security\Core\Security;

/**
 * @method null|MetaElementsGroups find($id, $lockMode = null, $lockVersion = null)
 * @method null|MetaElementsGroups findOneBy(array $criteria, array $orderBy = null)
 * @method MetaElementsGroups[] findAll()
 * @method MetaElementsGroups[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MetaElementsGroupsRepository extends ServiceEntityRepository
{
    private Security $security;

    public function __construct(ManagerRegistry $registry, Security $security)
    {
        parent::__construct($registry, MetaElementsGroups::class);
        $this->security = $security;
    }

    // /**
    //  * @return MetaElementsGroups[] Returns an array of MetaElementsGroups objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('m.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?MetaElementsGroups
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */

    public function getSortedElementsWithGroups($sortType = 'ASC')
    {
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();

        $connection = $this->getEntityManager()->getConnection();
        $eb = new ExpressionBuilder($connection);

        $params = [
            'serviceProviderId' => $serviceProviderId,
        ];
        $paramTypes = [
            'serviceProviderId' => ParameterType::INTEGER,
        ];

        $elementsOrderByServiceProviderQb = $eb->createQuery()
            ->select(
                'lage.element',
                'lagevo.visual_order'
            )
            ->from('lab_analysis_group_element', 'lage')
            ->leftJoin(
                'lage',
                'lab_analysis_group_element_visual_order',
                'lagevo',
                'lagevo.lab_analysis_group_element_id = lage.id'
            )
            ->where('lagevo.service_provider_id = :serviceProviderId');

        $elementsOrderByServiceProviderCTE = new Expression('elements_order_by_service_provider', $elementsOrderByServiceProviderQb);

        $query = $eb->with($elementsOrderByServiceProviderCTE)
            ->select(
                'meg.element as name',
                'JSONB_AGG(DISTINCT meg.group_id) as groups'
            )
            ->from('meta_elements_groups', 'meg')
            ->leftJoin('meg', 'elements_order_by_service_provider', 'eobsp', 'eobsp.element = meg.element')
            ->groupBy('meg.element, eobsp.visual_order')
            ->orderBy('eobsp.visual_order', $sortType);

        $stmt = $connection->executeQuery($query, $params, $paramTypes);
        $result = $stmt->fetchAllAssociative();

        return array_map(function ($data) {
            if (isset($data['groups'])) {
                $data['groups'] = json_decode($data['groups'], true);
            }

            return $data;
        }, $result);
    }

    public function getAllElements()
    {
        $qb = $this->createQueryBuilder('m');
        $qb->select('m.element')->distinct();
        $query = $qb->getQuery();

        return array_column($query->getScalarResult(), 'element');
    }
}
