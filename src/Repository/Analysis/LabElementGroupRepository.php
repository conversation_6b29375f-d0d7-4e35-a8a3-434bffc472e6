<?php

namespace App\Repository\Analysis;

use App\Entity\Analysis\LabElementGroup;
use App\Entity\Analysis\PackageGridPoints;
use App\Entity\Contract;
use App\Entity\Contract\ServicePackage;
use App\Entity\Contract\ServicePackageField;
use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Contract\SubscriptionPackageField;
use App\Repository\BaseRepository;
use Doctrine\DBAL\ParameterType;
use Doctrine\ORM\Query\Expr;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|LabElementGroup find($id, $lockMode = null, $lockVersion = null)
 * @method null|LabElementGroup findOneBy(array $criteria, array $orderBy = null)
 * @method LabElementGroup[] findAll()
 * @method LabElementGroup[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LabElementGroupRepository extends BaseRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LabElementGroup::class);
    }

    /**
     * @return Query
     */
    public function findByRelationDataQuery(array $arrRelationData)
    {
        $qb = $this->createQueryBuilder('LabElementGroup');

        $qb->select('LabElementGroup');
        $qb->andWhere('LabElementGroup.plotUuid IN (:plotUuids)');
        $qb->andWhere('LabElementGroup.packageId IN (:packageIds)');
        $qb->andWhere('LabElementGroup.packageType IN (:packageTypes)');
        $qb->setParameter('plotUuids', $arrRelationData['plotUuids']);
        $qb->setParameter('packageIds', $arrRelationData['packageIds']);
        $qb->setParameter('packageTypes', $arrRelationData['packageTypes']);

        return $qb->getQuery();
    }

    public function getLabElementGroupIdsByPackageGridPoint(PackageGridPoints $pgp): array
    {
        $conn = $this->getEntityManager()->getConnection();
        $qb = $conn->createQueryBuilder();

        $params = [
            'packageGridPointId' => $pgp->getId(),
        ];

        $paramTypes = [
            'packageGridPointId' => ParameterType::INTEGER,
        ];

        $qb->select(
            'leg.id'
        )
            ->from('lab_element_group', 'leg')
            ->join('leg', 'lab_elements_results', 'ler', 'ler.lab_element_group_id = leg.id')
            ->join('ler', 'package_grid_points', 'pgp', 'pgp.id = ler.package_grid_points_id')
            ->where(
                'pgp.id = :packageGridPointId'
            )
            ->distinct();

        $stmt = $conn->executeQuery($qb->getSQL(), $params, $paramTypes);

        return $stmt->fetchFirstColumn();
    }

    /**
     * @throws \Doctrine\DBAL\DBALException
     *
     * @return array
     */
    public function getSoilMapData(int $serviceProviderId, string $plotUuid, int $labElementGroupId = null)
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "WITH element_ranges AS (
                SELECT
                    row_number () over (partition by element_id order by \"range\") \"position\",
                    lage.\"element\",
                    lage.unit,
                    CASE WHEN lower_inc(leic.\"range\") THEN lower(leic.\"range\") ELSE lower(leic.\"range\") + 0.01 END AS range_min,
                    CASE WHEN upper_inc(leic.\"range\") THEN upper(leic.\"range\") ELSE upper(leic.\"range\") - 0.01 END AS range_max,
                    leic.\"range\",
                    leic.color
                FROM
                    lab_element_interpretations_config leic
                JOIN lab_analysis_group_element lage
                    ON lage.id = leic.element_id
                WHERE 
                    leic.service_provider_id = :serviceProviderId
                    AND lage.has_soil_map = TRUE
            ),
            element_interpretations as (
                SELECT
                    er.\"element\",
                    er.unit,
                    jsonb_agg(
                        jsonb_build_object(
                            'position', er.\"position\",
                            'color', er.color,
                            'min', er.range_min,
                            'max', er.range_max,
                            'label', CASE 
                                WHEN er.range_max NOTNULL THEN coalesce(er.range_min, 0) || '|' || coalesce(er.range_min, 0) || ' - ' || er.range_max
                                ELSE er.range_min || '|>= ' || er.range_min
                            END
                        ) order by er.\"position\"
                    )AS ranges
                FROM
                    element_ranges AS er
                GROUP BY 
                    er.\"element\",
                    er.\"unit\"
            ),
            package_grid_points_cte as (
                SELECT
				    id,
				    sample_id,
				    MAX(state_updated_at) OVER (PARTITION BY plot_uuid, package_id) AS last_state_update,
				    package_id,
				    package_type,
				    grid_type,
				    barcode,
				    lab_number,
				    state,
				    state_updated_at,
				    point_uuid,
				    plot_uuid,
                    sampling_type_id
				FROM
				    package_grid_points
				WHERE plot_uuid = :plotUuid
            )
            SELECT
                jsonb_agg(distinct
                    jsonb_build_object(
                        'pointUuid', pgp.point_uuid,
                        'element', ler.\"element\",
                        'value', ROUND (ler.value::NUMERIC, 3),
                        'date', pgp.\"last_state_update\",
                        'plotUuid', leg.plot_uuid,
                        'orderUuid', COALESCE (supf.order_uuid, sepf.order_uuid),
                        'samplingTypeId', st.id
                    )
                ) AS points,
                jsonb_object_agg(DISTINCT
                    ei.\"element\", jsonb_build_object(
                        'unit', ei.unit,
                        'ranges', ei.ranges
                    ) 
                ) AS element_interpretations
            FROM
                lab_element_group leg 
            JOIN lab_elements_results ler
                ON  ler.lab_element_group_id = leg.id
            JOIN package_grid_points_cte pgp
                ON pgp.id = ler.package_grid_points_id
            LEFT JOIN service_package_field sepf
                ON sepf.service_package_id = pgp.package_id
                AND pgp.package_type = 'service'
                AND sepf.plot_uuid = pgp.plot_uuid
            LEFT JOIN subscription_package_field supf
                ON supf.subscription_package_id = pgp.package_id
                AND pgp.package_type = 'subscription'
                AND supf.plot_uuid = pgp.plot_uuid
            LEFT JOIN service_contract_packages sep
                ON sep.id = sepf.service_package_id
            LEFT JOIN subscription_package sup
                ON sup.id = supf.subscription_package_id
            JOIN package p
                ON p.id = sup.package_id
                OR p.id = sep.package_id
            JOIN package_sampling_type pst
                ON pst.package_id = p.id
            JOIN sampling_type st
                ON st.id = pst.sampling_type_id
                AND st.id = pgp.sampling_type_id
            JOIN lab_analysis_group_element lage
                ON ler.\"element\" = lage.\"element\"
            JOIN lab_analysis_group_element_visual_order lagevo
                ON lagevo.lab_analysis_group_element_id = lage.id
		        AND lagevo.service_provider_id = :serviceProviderId
            JOIN element_interpretations ei
                ON ei.\"element\" = lage.\"element\"
            WHERE
                leg.plot_uuid = :plotUuid
        ";

        $executeArr['plotUuid'] = $plotUuid;
        $executeArr['serviceProviderId'] = $serviceProviderId;

        if (isset($labElementGroupId)) {
            $sql .= ' AND leg.id = :labElementGroupId';
            $executeArr['labElementGroupId'] = $labElementGroupId;
        }

        $stmt = $conn->prepare($sql);
        $stmt->execute($executeArr);
        $results = $stmt->fetch();

        return [
            'points' => isset($results['points']) ? json_decode($results['points'], true) : [],
            'element_interpretations' => isset($results['element_interpretations']) ? json_decode($results['element_interpretations'], true) : [],
        ];
    }

    public function getResultsByCustomerIdentificationQbGroup(array $customerIdentifications, $serviceProviderId, $state)
    {
        $qb = $this->createQueryBuilder('LabElementGroup');

        $qb->select(
            $qb->expr()->countDistinct('LabElementGroup.plotUuid')
        )
            ->join('App\Entity\Contract\SubscriptionPackage', 'SubscriptionPackage', Expr\Join::WITH, 'SubscriptionPackage.id = LabElementGroup.packageId')
            ->join('App\Entity\Contract', 'Contract', Expr\Join::WITH, 'Contract.id = SubscriptionPackage.contract')
            ->andWhere('LabElementGroup.state = (:state)')->setParameter('state', $state)
            ->andWhere('Contract.customerIdentification IN (:customerIdentifications)')->setParameter('customerIdentifications', $customerIdentifications)
            ->andWhere('Contract.serviceProvider = (:serviceProviderId)')->setParameter('serviceProviderId', $serviceProviderId);

        return $qb;
    }

    public function removeByPackageFields(array $fieldIds, $type = Contract::TYPE_SUBSCRIPTION)
    {
        $qb = $this->createQueryBuilder('LabElementGroup');

        $qb->select('LabElementGroup.id');

        if (Contract::TYPE_SERVICE === $type) {
            $qb->join(ServicePackage::class, 'Package', Expr\Join::WITH, 'Package.id = LabElementGroup.packageId');
            $qb->join(ServicePackageField::class, 'PackageField', Expr\Join::WITH, '(PackageField.servicePackage = Package.id AND LabElementGroup.plotUuid = PackageField.plotUuid)');
        } else {
            $qb->join(SubscriptionPackage::class, 'Package', Expr\Join::WITH, 'Package.id = LabElementGroup.packageId');
            $qb->join(SubscriptionPackageField::class, 'PackageField', Expr\Join::WITH, '(PackageField.subscriptionPackage = Package.id AND LabElementGroup.plotUuid = PackageField.plotUuid)');
        }

        $qb->where('PackageField.id IN (:fieldIds)')->setParameter('fieldIds', $fieldIds);

        $query = $qb->getQuery();
        $results = $query->getArrayResult();
        $labElementGroupIds = array_column($results, 'id');

        return $this->createQueryBuilder('LabElementGroup')
            ->where('LabElementGroup.id in (:ids)')
            ->setParameter('ids', $labElementGroupIds)
            ->delete()
            ->getQuery()
            ->execute();
    }
}
