<?php

namespace App\Repository;

use App\Entity\Contract;
use App\Entity\Contract\SubscriptionPackageField;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Doctrine\ORM\Configuration;
use Doctrine\ORM\Query\Expr;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Exception;
use Somnambulist\CTEBuilder\Expression;
use Somnambulist\CTEBuilder\ExpressionBuilder;

/**
 * @method null|Contract find($id, $lockMode = null, $lockVersion = null)
 * @method null|Contract findOneBy(array $criteria, array $orderBy = null)
 * @method Contract[] findAll()
 * @method Contract[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ContractRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Contract::class);
    }

    public function getFilteredAndSortedQb($type, array $filters = null, array $sort = null): QueryBuilder
    {
        $qb = $this->createQueryBuilder('contract');

        if (!empty($type)) {
            $qb->where('contract instance of :type')->setParameter('type', $type);
        }
        if (!empty($sort)) {
            foreach ($sort as $value) {
                $orderDirection = 'ASC';
                $orderField = $value;
                if ('-' === substr($value, 0, 1)) {
                    $orderDirection = 'DESC';
                    $orderField = substr($value, 1, strlen($value));
                }

                $qb->addOrderBy('contract.' . $orderField, $orderDirection);
            }
        }
        if (!empty($filters)) {
            foreach ($filters as $key => $filter) {
                $type = null;
                switch ($filter['type']) {
                    case 'eq':
                        $type = 'type' == $key ? ' INSTANCE OF ' : ' = ';

                        break;
                    case 'noteq':
                        $type = 'type' == $key ? ' NOT INSTANCE OF ' : ' != ';
                        // no break
                    case 'in':
                        $type = ' IN ';

                        break;
                }

                $value = json_decode($filter['x'], true);
                if (!$value) {
                    $value = $filter['x'];
                }

                if ('packages' == $key) {
                    $qb->leftJoin('App\Entity\Contract\SubscriptionPackage', 'SubscriptionPackage', Expr\Join::WITH, 'SubscriptionPackage.contract = contract.id');
                    $qb->leftJoin('App\Entity\Contract\ServicePackage', 'ServicePackage', Expr\Join::WITH, 'ServicePackage.contract = contract.id');
                    $qb->andWhere('SubscriptionPackage.package IN (:packageIds) OR ServicePackage.package IN (:packageIds)')->setParameter('packageIds', $value);

                    continue;
                }

                if ('orderUuids' == $key) {
                    $qb->leftJoin('App\Entity\Contract\SubscriptionPackage', 'SubscriptionPackage', Expr\Join::WITH, 'SubscriptionPackage.contract = contract.id');
                    $qb->leftJoin('App\Entity\Contract\SubscriptionPackageField', 'SubscriptionPackageField', Expr\Join::WITH, 'SubscriptionPackageField.subscriptionPackage = SubscriptionPackage.id');
                    $qb->andWhere('SubscriptionPackageField.orderUuid IN (:orderUuids)')->setParameter('orderUuids', $value);

                    continue;
                }

                if ('includeResponsibleServiceUser' == $key) {
                    $qb->leftJoin('App\Entity\Contract\ResponsibleUser', 'ResponsibleUser', Expr\Join::WITH, 'ResponsibleUser.contract = contract.id');
                    $qb->andWhere('ResponsibleUser.role ' . $type . ' (:role)')
                        ->setParameter('role', 'SERVICE');

                    continue;
                }

                if ('serviceUser' == $key) {
                    $qb->leftJoin('App\Entity\Contract\ResponsibleUser', 'ResponsibleUser', Expr\Join::WITH, 'ResponsibleUser.contract = contract.id');
                    $qb->andWhere('ResponsibleUser.username ' . $type . ' (:username) AND ResponsibleUser.role ' . $type . ' (:role)')->setParameter('username', $value)->setParameter('role', 'SERVICE');

                    continue;
                }

                if ('signedPeriod' == $key) {
                    $startDate = $value[0] ?? '';
                    $endDate = $value[1] ?? '';
                    if ($startDate && $endDate) {
                        $qb->andWhere('contract.contractDate >= :startDate AND contract.contractDate <= :endDate')->setParameter('startDate', $startDate)->setParameter('endDate', $endDate);

                        continue;
                    }

                    continue;
                }

                if ('startPeriod' == $key) {
                    $startDate = $value[0] ?? '';
                    $endDate = $value[1] ?? '';
                    if ($startDate && $endDate) {
                        $qb->andWhere('contract.startDate >= :startDate AND contract.startDate <= :endDate')->setParameter('startDate', $startDate)->setParameter('endDate', $endDate);

                        continue;
                    }

                    continue;
                }

                if ('endPeriod' == $key) {
                    $startDate = $value[0] ?? '';
                    $endDate = $value[1] ?? '';
                    if ($startDate && $endDate) {
                        $qb->andWhere('contract.endDate >= :startDate AND contract.endDate <= :endDate')->setParameter('startDate', $startDate)->setParameter('endDate', $endDate);

                        continue;
                    }

                    continue;
                }

                $qb->andWhere('contract' . ('type' != $key ? '.' . $key : '') . $type . '(:value_' . $key . ')')->setParameter('value_' . $key, $value);
            }
        }

        return $qb;
    }

    /**
     * @throws \Doctrine\DBAL\DBALException
     *
     * @return array
     */
    public function listValidContractsWithCalculatedAreaByRules(array $data, bool $groupByPackagePeriod = false)
    {
        $conn = $this->getEntityManager()->getConnection();
        $eb = new ExpressionBuilder($conn);
        $params = [];
        $paramTypes = [];

        $plotsStateQb = $eb->createQuery()
            ->select('
            contract.id as contract_id, 
            subs_pack.id as package_id, 
            jsonb_build_object(
                \'state\', subs_pack_field.field_state,
                \'numberOfPlotsWithThisState\', count(subs_pack_field.field_state)
            ) as plots_state 
            ')
            ->from('contract')
            ->leftJoin('contract', 'subscription_package', 'subs_pack', 'contract.id = subs_pack.contract_id')
            ->leftJoin('subs_pack', 'package', 'package_subs', 'subs_pack.package_id = package_subs.id')
            ->leftJoin('subs_pack', 'subscription_package_field', 'subs_pack_field', 'subs_pack.id = subs_pack_field.subscription_package_id')
            ->leftJoin('subs_pack', 'package', 'package', 'package.id = subs_pack.package_id');

        if (isset($data['filter']['fields_state_for_reference'])) {
            $plotsStateQb->andWhere($plotsStateQb->expr()->in('subs_pack_field.field_state', ':fields_state_for_reference'));
            $params['fields_state_for_reference'] = json_decode($data['filter']['fields_state_for_reference']);
            $paramTypes['fields_state_for_reference'] = Connection::PARAM_STR_ARRAY;
        }

        $subscriptionContractsQb = $eb->createQuery()
            ->select(
                'c.id as contract_id',
                'sp.id as subscription_package_id',
                'SUM(DISTINCT spf.area) as sub_fields_area',
                'sp.period',
                'JSONB_AGG(DISTINCT spf.order_uuid) as order_uuids'
            )
            ->from('contract', 'c')
            ->join('c', 'subscription_contracts', 'sc', 'c.id = sc.id')
            ->leftJoin('c', 'subscription_package', 'sp', 'c.id = sp.contract_id')
            ->leftJoin('sp', 'subscription_package_field', 'spf', 'sp.id = spf.subscription_package_id')
            ->groupBy(
                'c.id',
                'sc.id',
                'sp.id'
            )
            ->distinct();

        $subscriptionPeriodsOrdersQb = $eb->createQuery()
            ->select(
                'contract_id',
                'JSONB_BUILD_OBJECT(
                    \'period\', period,
                    \'order_uuids\', COALESCE(JSONB_AGG(DISTINCT order_uuid) FILTER (WHERE order_uuid NOTNULL), \'[]\'::JSONB)
                ) AS period_orders'
            )
            ->from('
                subs_left_join,
                JSONB_ARRAY_ELEMENTS_TEXT(subs_left_join.order_uuids) as order_uuid
            ')
            ->groupBy(
                'contract_id',
                'period'
            )
            ->distinct();

        $serviceContractsQb = $eb->createQuery()
            ->select(
                'sc.id AS contract_id',
                'scp.id AS service_package_id',
                'SUM(DISTINCT spf.area) AS serv_field_area'
            )
            ->from('contract', 'c')
            ->join('c', 'service_contracts', 'sc', 'c.id = sc.id')
            ->leftJoin('c', 'service_contract_packages', 'scp', 'c.id = scp.contract_id')
            ->leftJoin('scp', 'service_package_field', 'spf', 'scp.id = spf.service_package_id')
            ->groupBy(
                'c.id',
                'sc.id',
                'scp.id'
            )
            ->distinct();

        $resultQb = $eb->createQuery()
            ->select(
                'contract.id',
                'contract.status',
                'contract.contract_date',
                'TO_CHAR(contract.start_date, :contract_date_format) AS start_date',
                'TO_CHAR(contract.end_date, :contract_date_format) AS end_date',
                'TO_CHAR(contract.created_at, :contract_date_format) AS created_at',
                'contract.number',
                'contract.customer_identification',
                'contract.type',
                'contract.service_provider_id',
                'ROUND(contract.area, 2) AS "contractArea"',
                'JSONB_AGG(DISTINCT subs_periods_orders.period_orders) AS periods_orders',
                'sampler.user_id AS sampler_id',
                'COALESCE(
                        ROUND(SUM(DISTINCT serv_left_join.serv_field_area), 2),
                        ROUND(SUM(DISTINCT subs_left_join.sub_fields_area), 2),
                        0
                ) AS "usedArea"',
                'COALESCE(
                    ROUND((SUM(DISTINCT serv_left_join.serv_field_area) / NULLIF(contract.area, 0)) * 100),
                    ROUND((SUM(DISTINCT subs_left_join.sub_fields_area) / NULLIF(contract.area, 0)) * 100),
                    0
                ) as "usedAreaPercentage"
                '
            )
            ->from('contract')
            ->leftJoin('contract', 'service_contracts', 'service_c', 'contract.id = service_c.id')
            ->leftJoin('contract', 'subscription_contracts', 'subscription_c', 'contract.id = subscription_c.id')
            ->leftJoin('subscription_c', 'subscription_package', 'subs_pack', 'subscription_c.id = subs_pack.contract_id')
            ->leftJoin('subs_pack', 'package', 'package_subs', 'subs_pack.package_id = package_subs.id')
            ->leftJoin('service_c', 'service_contract_packages', 'serv_pack', 'service_c.id = serv_pack.contract_id')
            ->leftJoin('serv_pack', 'package', 'package_serv', 'serv_pack.package_id = package_serv.id')
            ->leftJoin('subs_pack', 'subscription_package_field', 'subs_pack_field', 'subs_pack.id = subs_pack_field.subscription_package_id')
            ->leftJoin('serv_pack', 'service_package_field', 'serv_pack_field', 'serv_pack.id = serv_pack_field.service_package_id')
            ->leftJoin('subscription_c', 'subs_left_join', 'subs_left_join', 'subs_pack.id = subs_left_join.subscription_package_id')
            ->leftJoin('subscription_c', 'subs_periods_orders', 'subs_periods_orders', 'subscription_c.id = subs_periods_orders.contract_id')
            ->leftJoin('serv_pack', 'serv_left_join', 'serv_left_join', 'serv_pack.id = serv_left_join.service_package_id')
            ->leftJoin('contract', 'responsible_user', 'sampler', 'contract.id = sampler.contract_id AND sampler.role = \'SAMPLER\'')
            ->where('contract.end_date >= now()');

        /* The contract_date_format param is used, because the pdo takes :MI and :SS (from YYYY-MM-DD HH24:MI:SS)
         *  as placeholders and expects parameters for them if placed directly in the query.
         */
        $params['contract_date_format'] = 'YYYY-MM-DD HH24:MI:SS';
        $paramTypes['contract_date_format'] = ParameterType::STRING;

        // Add packages data
        if (isset($data['filter']['has_packages']) && true === (bool)$data['filter']['has_packages']) {
            // Add packages to the subs_left_join CTE
            $subscriptionContractsQb->leftJoin('sp', 'package', 'p', 'sp.package_id = p.id')
                ->leftJoin('p', 'package_sampling_type', 'pst', 'p.id = pst.package_id')
                ->leftJoin('pst', 'sampling_type', 'st', 'pst.sampling_type_id = st.id');

            $subscriptionContractsQb->addGroupBy('p.id');
            $subscriptionContractsQb->addSelect(
                'JSONB_BUILD_OBJECT(
                    \'id\', sp.id,
                    \'slug\', p.slug,
                    \'slugShort\', p.slug_short,
                    \'style\', p.style,
                    \'periodNumber\', sp.period,
                    \'period\', CONCAT(TO_CHAR(sp.start_date, \'YYYY\'), \'/\', TO_CHAR(sp.end_date, \'YYYY\')),
                    \'stateValue\', sp.state,
                    \'packageStartDate\', DATE(sp.start_date),
                    \'packageEndDate\', DATE(sp.end_date),
                    \'allFieldsAreaSum\', COALESCE(ROUND(SUM(DISTINCT spf.area), 2), 0), 
                    \'usedAreaPercentage\', COALESCE(ROUND(((SUM(DISTINCT spf.area)/c.area) * 100), 2), 0), 
                    \'contractId\', c.id,
                    \'startDate\', DATE(c.start_date),
                    \'endDate\', DATE(c.end_date),
                    \'isSampling\', p.is_sampling,
                    \'isFullSampling\', p.is_full_sampling,
                    \'samplingTypes\', COALESCE(
                        JSONB_AGG(distinct
                            JSONB_BUILD_OBJECT(
                                \'id\', st.id,
                                \'type\', st.type
                            ) 
                        ) FILTER (WHERE st.id NOTNULL),
                    \'[]\'::JSONB),
                    \'plotsState\', jsonb_agg(distinct plots_state.plots_state)
                ) AS package_data
            '
            );

            // Add plots state to the subs_left_join CTE
            $subscriptionContractsQb->leftJoin('c', 'plots_state', 'plots_state', 'plots_state.contract_id = c.id and plots_state.package_id = sp.id');

            // Add packages to the serv_left_join CTE
            $serviceContractsQb->leftJoin('scp', 'package', 'p', 'scp.package_id = p.id')
                ->leftJoin('p', 'package_sampling_type', 'pst', 'p.id = pst.package_id')
                ->leftJoin('pst', 'sampling_type', 'st', 'pst.sampling_type_id = st.id');

            $serviceContractsQb->addGroupBy('p.id');
            $serviceContractsQb->addSelect(
                'JSONB_BUILD_OBJECT(
                    \'id\', scp.id,
                    \'slug\', p.slug,
                    \'style\', p.style,
                    \'slugShort\', p.slug_short,
                    \'stateValue\', scp.state,
                    \'allFieldsAreaSum\', COALESCE(ROUND(SUM(DISTINCT spf.area), 2), 0),
                    \'usedAreaPercentage\', COALESCE(ROUND(((SUM(DISTINCT spf.area)/c.area) * 100), 2), 0),
                    \'startDate\', DATE(c.start_date),
                    \'endDate\', DATE(c.end_date),
                    \'period\', CONCAT(TO_CHAR(c.start_date, \'dd-mm-yyyy\'), \' - \' , TO_CHAR(c.end_date, \'dd-mm-yyyy\')),
                    \'contractId\', c.id,
                    \'isSampling\', p.is_sampling,
                    \'isFullSampling\', p.is_full_sampling,
                    \'samplingTypes\', COALESCE(
                        JSONB_AGG(distinct
                            JSONB_BUILD_OBJECT(
                                \'id\', st.id,
                                \'type\', st.type
                            ) 
                        ) FILTER (WHERE st.id NOTNULL),
                    \'[]\'::JSONB)
                ) AS package_data
            '
            );

            // Add packages to the result CTE
            $resultQb->addSelect('JSONB_AGG(DISTINCT COALESCE(subs_left_join.package_data, serv_left_join.package_data, \'[]\'::JSONB)) AS packages');
        }

        if (isset($data['filter']['period_numbers'])) {
            // Only for  subs_left_join CTE
            $subscriptionContractsQb->where($subscriptionContractsQb->expr()->in('sp.period', ':period_numbers'));
            $resultQb->where($resultQb->expr()->in('subs_pack.period', ':period_numbers'));
            $params['period_numbers'] = json_decode($data['filter']['period_numbers']);
            $paramTypes['period_numbers'] = Connection::PARAM_INT_ARRAY;
        }

        if (isset($data['filter']['contracts_id'])) {
            $resultQb->andWhere($resultQb->expr()->in('contract.id', ':contracts_id'));
            $params['contracts_id'] = json_decode($data['filter']['contracts_id']);
            $paramTypes['contracts_id'] = Connection::PARAM_INT_ARRAY;
        }

        if (isset($data['filter']['status'])) {
            $resultQb->andWhere($resultQb->expr()->in('contract.status', ':status'));
            $params['status'] = json_decode($data['filter']['status']);
            $paramTypes['status'] = Connection::PARAM_STR_ARRAY;
        }

        if (isset($data['filter']['contain_fields'])) {
            $resultQb->andWhere($resultQb->expr()->or(
                'package_subs.contain_fields = :contain_fields',
                'package_serv.contain_fields = :contain_fields'
            ));
            $plotsStateQb->andWhere('package_subs.contain_fields = :contain_fields');
            $params['contain_fields'] = $data['filter']['contain_fields'];
            $paramTypes['contain_fields'] = ParameterType::BOOLEAN;
        }

        if (isset($data['filter']['is_sampling'])) {
            $resultQb->andWhere($resultQb->expr()->or(
                'package_subs.is_sampling = :is_sampling',
                'package_serv.is_sampling = :is_sampling'
            ));
            $plotsStateQb->andWhere('package.is_sampling = :is_sampling');
            $params['is_sampling'] = $data['filter']['is_sampling'];
            $paramTypes['is_sampling'] = ParameterType::BOOLEAN;
        }

        if (isset($data['filter']['customer_identification'])) {
            $serviceContractsQb->where($serviceContractsQb->expr()->in('c.customer_identification', ':customer_identification'));
            $subscriptionContractsQb->where($subscriptionContractsQb->expr()->in('c.customer_identification', ':customer_identification'));
            $resultQb->andWhere($resultQb->expr()->in('contract.customer_identification', ':customer_identification'));
            $plotsStateQb->andWhere($plotsStateQb->expr()->in('contract.customer_identification', ':customer_identification'));
            $params['customer_identification'] = json_decode($data['filter']['customer_identification']);
            $paramTypes['customer_identification'] = Connection::PARAM_STR_ARRAY;
        }

        if (isset($data['filter']['packages_state'])) {
            $resultQb->andWhere($resultQb->expr()->or(
                $resultQb->expr()->in('subs_pack.state', ':packages_state'),
                $resultQb->expr()->in('serv_pack.state', ':packages_state')
            ));
            $plotsStateQb->andWhere($plotsStateQb->expr()->in('subs_pack.state', ':packages_state'));
            $params['packages_state'] = json_decode($data['filter']['packages_state']);
            $paramTypes['packages_state'] = Connection::PARAM_STR_ARRAY;
        }

        if (isset($data['filter']['plot_uuid'])) {
            $resultQb->andWhere($resultQb->expr()->or(
                $resultQb->expr()->eq('serv_pack_field.plot_uuid', ':plot_uuid'),
                $resultQb->expr()->eq('subs_pack_field.plot_uuid', ':plot_uuid')
            ));
            $params['plot_uuid'] = $data['filter']['plot_uuid'];
            $paramTypes['plot_uuid'] = ParameterType::STRING;
        }

        if (isset($data['filter']['fields_state'])) {
            $resultQb->andWhere($resultQb->expr()->in('subs_pack_field.field_state', ':fields_state'));
            $params['fields_state'] = json_decode($data['filter']['fields_state']);
            $paramTypes['fields_state'] = Connection::PARAM_STR_ARRAY;
        }

        if (isset($data['filter']['start_date'])) {
            $resultQb->andWhere('to_char(subs_pack.start_date, \'YYYY-mm-dd\')::date >= (:start_date)::date');
            $params['start_date'] = $data['filter']['start_date'];
            $paramTypes['start_date'] = ParameterType::STRING;
        }

        if (isset($data['filter']['end_date'])) {
            $resultQb->andWhere('to_char(subs_pack.end_date, \'YYYY-mm-dd\')::date <= (:end_date)::date');
            $params['end_date'] = $data['filter']['end_date'];
            $paramTypes['end_date'] = ParameterType::STRING;
        }

        $plotsStateQb->groupBy('contract.id, subs_pack.id, subs_pack_field.field_state');
        $resultQb->groupBy([
            'contract.id',
            'contract.status',
            'contract.contract_date',
            'contract.start_date',
            'contract.end_date',
            'contract.created_at',
            'contract.number',
            '"contractArea"',
            'contract.customer_identification',
            'contract.type',
            'contract.service_provider_id',
            'sampler.user_id',
        ]);

        // Group by package period
        if (
            isset($data['filter']['has_packages'])
            && true === (bool)$data['filter']['has_packages']
            && $groupByPackagePeriod
        ) {
            $resultQb->addGroupBy('subs_left_join."period"');
        }

        if (isset($data['filter']['packages'])) {
            $resultQb->andHaving($resultQb->expr()->or(
                'ARRAY_AGG(DISTINCT subs_pack.package_id) @> ARRAY[:packages]::INTEGER[]',
                'ARRAY_AGG(DISTINCT serv_pack.package_id) @> ARRAY[:packages]::INTEGER[]'
            ));

            $params['packages'] = json_decode($data['filter']['packages']);
            $paramTypes['packages'] = Connection::PARAM_INT_ARRAY;
        }

        $itemsQb = $eb->createQuery()
            ->select('*')
            ->from('result');

        if (isset($data['limit'], $data['offset'])) {
            $itemsQb->setMaxResults($data['limit'])->setFirstResult($data['offset']);
        }

        $totalQb = $eb->createQuery()
            ->select('COUNT(result)')
            ->from('result');

        $plotsStateCTE = new Expression('plots_state', $plotsStateQb);
        $subsLeftJoinCTE = new Expression('subs_left_join', $subscriptionContractsQb);
        $subsPeriodsOrdersCTE = new Expression('subs_periods_orders', $subscriptionPeriodsOrdersQb);
        $servLeftJoinCTE = new Expression('serv_left_join', $serviceContractsQb);
        $resultCTE = new Expression('result', $resultQb);
        $itemsCTE = new Expression('items', $itemsQb);
        $totalCTE = new Expression('total', $totalQb);

        $query = (isset($data['filter']['has_packages']) && true === (bool)$data['filter']['has_packages'])
            ? $eb->with($plotsStateCTE)
                ->with($subsLeftJoinCTE)
            : $eb->with($subsLeftJoinCTE);

        $query->with($subsPeriodsOrdersCTE)
            ->with($servLeftJoinCTE)
            ->with($resultCTE)
            ->with($itemsCTE)
            ->with($totalCTE)
            ->select(
                'JSONB_AGG(items) AS items',
                'total.count AS total'
            )
            ->from('items, total')
            ->groupBy('total.count');

        $stmt = $conn->executeQuery($query->getSQL(), $params, $paramTypes);
        $result = $stmt->fetchAssociative();

        return [
            'items' => json_decode($result['items'] ?? '[]', true),
            'total' => $result['total'] ?? 0,
        ];
    }

    /**
     * @throws \Doctrine\DBAL\DBALException
     *
     * @return mixed[]
     */
    public function countAllAmountsForContractsByFilter(array $data)
    {
        $executeArr = [
            'customer_identification' => '{}',
            'contract_id' => '{}',
            'has_station' => null,
            'is_sampling' => null,
            'contain_fields' => null,
        ];

        $conn = $this->getEntityManager()->getConnection();
        $sql = '
            select
                coalesce(sum(contracts_amount.serv_amount),0) + coalesce(sum(contracts_amount.sub_amount),0) as "Amount"
            from
            (
                select
                    coalesce(round(sum(distinct serv_left_join.serv_amount), 2), 0) as "serv_amount", 
                    coalesce(round(sum(distinct subs_left_join.sub_amount), 2), 0) as "sub_amount"
                from
                    contract
                left join service_contracts service_c on
                    contract.id = service_c.id
                left join subscription_contracts subscription_c on
                    contract.id = subscription_c.id
                left join subscription_package subs_pack on
                    (subs_pack.contract_id = contract.id)
                left join package as package_subs on
                    (subs_pack.package_id = package_subs.id)
                left join service_contract_packages serv_pack on
                    (serv_pack.contract_id = contract.id)
                left join package as package_serv on
                    (serv_pack.package_id = package_serv.id)
                left join(
                    select
                        case when :has_station::int notnull then
                            min(sp.amount)
                        else
                            sum(sp.amount)
                        end as sub_amount,
                        sub_c.id as subsLeftJoinContratId
                    from
                        subscription_package sp
                    left join subscription_contracts on
                        (sp.contract_id = subscription_contracts.id)
                    left join package on
                        (sp.package_id = package.id)
                    left join contract sub_c on
                        (subscription_contracts.id = sub_c.id)
                    where
                        (array_length(:customer_identification::varchar[] , 1) isnull or sub_c.customer_identification::varchar = any(:customer_identification::varchar[]))
                        and (array_length(:contract_id::int[] , 1) isnull or sub_c.id::int = any(:contract_id::int[]))
                        and (:has_station::int isnull or package.has_station::int = :has_station)
                        and (:is_sampling::int isnull or package.is_sampling::int = :is_sampling)
                        and (:contain_fields::int isnull or package.contain_fields::int = :contain_fields)
                    group by
                        sub_c.id ) as subs_left_join on
                    (contract.id = subs_left_join.subsLeftJoinContratId)
                left join(
                    select
                        case when :has_station::int notnull then
                            min(service_contract_packages.amount)
                        else
                            sum(service_contract_packages.amount)
                        end as serv_amount,
                        serv_c.id as servLeftJoinContractId
                    from
                        service_contract_packages
                    left join service_contracts on
                        (service_contract_packages.contract_id = service_contracts.id)
                    left join package on
                        (service_contract_packages.package_id = package.id)
                    left join contract as serv_c on
                        (service_contracts.id = serv_c.id)
                    where
                        (array_length(:customer_identification::varchar[], 1) isnull or serv_c.customer_identification::varchar = any(:customer_identification::varchar[]))
                        and (array_length(:contract_id::int[], 1) isnull or serv_c.id::int = any(:contract_id::int[]))
                        and (:has_station::int isnull or package.has_station::int = :has_station) 
                        and (:is_sampling::int isnull or package.is_sampling::int = :is_sampling)
                        and (:contain_fields::int isnull or package.contain_fields::int = :contain_fields)
                    group by
                        serv_c.id) as serv_left_join on
                    (contract.id = serv_left_join.servLeftJoinContractId)
                where
                    contract.end_date >= now()
                    and (array_length(:customer_identification::varchar[], 1) isnull or contract.customer_identification::varchar = any(:customer_identification::varchar[]))
                    and (array_length(:contract_id::int[], 1) isnull or contract.id::int = any(:contract_id::int[]))
                    and ((:has_station::int isnull or package_subs.has_station::int = :has_station) or (:has_station::int isnull or package_serv.has_station::int = :has_station))
                    and ((:is_sampling::int isnull or package_subs.is_sampling::int = :is_sampling) or (:is_sampling::int isnull or package_serv.is_sampling::int = :is_sampling))
                    and ((:contain_fields::int isnull or package_subs.contain_fields::int = :contain_fields) or (:contain_fields::int isnull or package_serv.contain_fields::int = :contain_fields))
                group by
                    contract.id
            ) as contracts_amount
            ';

        if (isset($data['filter']['customer_identification'])) {
            $executeArr['customer_identification'] = '{' . implode(',', json_decode($data['filter']['customer_identification'], true)) . '}';
        }

        if (isset($data['filter']['contract_id'])) {
            $executeArr['contract_id'] = '{' . implode(',', json_decode($data['filter']['contract_id'], true)) . '}';
        }

        if (isset($data['filter']['has_station'])) {
            $executeArr['has_station'] = (int)$data['filter']['has_station'];
        }
        if (isset($data['filter']['is_sampling'])) {
            $executeArr['is_sampling'] = (int)$data['filter']['is_sampling'];
        }
        if (isset($data['filter']['contain_fields'])) {
            $executeArr['contain_fields'] = (int)$data['filter']['contain_fields'];
        }

        $stmt = $conn->prepare($sql);
        $stmt->execute($executeArr);

        return $stmt->fetch();
    }

    public function getStationsAmountByCustomerIdentifications($customerIdentifications, $withPagination, $limit, $offset, $count = false)
    {
        $config = new Configuration();
        $config->addCustomNumericFunction('ROUND', 'Oro\ORM\Query\AST\Functions\Numeric\Round');
        $config->addCustomStringFunction('CAST', 'Oro\ORM\Query\AST\Functions\Cast');

        $qb = $this->getStationContractsQb($customerIdentifications);
        $qb->select(
            'contract.id as contractId',
            'date_format(contract.startDate, \'dd-mm-yyyy\') as contractStartDate',
            'date_format(contract.endDate, \'dd-mm-yyyy\') as contractEndDate',
            'CAST(ROUND(min(coalesce(sup.amount, 0) + coalesce(sep.amount ,0)), 0) as int) as allStations',
            'contract.customerIdentification as customerIdentification'
        );
        $qb->groupBy('contract.id');

        if ($count) {
            $query = $qb->getQuery();

            return count($query->getResult());
        }

        if ($withPagination) {
            $qb->setMaxResults($limit);
            $qb->setFirstResult($offset);
        }

        $query = $qb->getQuery();

        return $query->getResult();
    }

    public function getStationContractsByCustomerIdentifications($customerIdentifications)
    {
        $qb = $this->getStationContractsQb($customerIdentifications);
        $qb->select('contract');
        $query = $qb->getQuery();

        return $query->getResult();
    }

    public function getStationContractsQb($customerIdentifications)
    {
        $qb = $this->createQueryBuilder('contract');
        $qb->leftJoin('App\Entity\Contract\SubscriptionPackage', 'sup', Expr\Join::WITH, 'sup.contract = contract');
        $qb->leftJoin('App\Entity\Contract\ServicePackage', 'sep', Expr\Join::WITH, 'sep.contract = contract');
        $qb->join('App\Entity\Package', 'p', Expr\Join::WITH, '(p.id = sep.package or p.id = sup.package)');
        $qb->where('p.hasStation = true')
            ->andWhere('p.containFields = false')
            ->andWhere('contract.endDate >= CURRENT_TIMESTAMP()');

        if ($customerIdentifications) {
            $qb->andWhere('contract.customerIdentification IN (:customerIds)')
                ->setParameter('customerIds', $customerIdentifications);
        }

        return $qb;
    }

    public function getPackagesBySlugQb($slug, $customerIdentifications, $status = null, $packageType = null)
    {
        $qb = $this->createQueryBuilder('contract');
        if (!$packageType) {
            $qb->leftJoin('App\Entity\Contract\SubscriptionPackage', 'sup', Expr\Join::WITH, 'sup.contract = contract');
            $qb->leftJoin('App\Entity\Contract\ServicePackage', 'sep', Expr\Join::WITH, 'sep.contract = contract');
            $qb->join('App\Entity\Package', 'p', Expr\Join::WITH, '(p.id = sup.package or p.id = sep.package)');
        }
        if ($packageType) {
            $qb->leftJoin('App\Entity\Contract\\' . $packageType, 'sup', Expr\Join::WITH, 'sup.contract = contract');
            $qb->join('App\Entity\Package', 'p', Expr\Join::WITH, '(p.id = sup.package)');
        }

        $qb->where('p.slug = :slug')->setParameter('slug', $slug);
        $qb->andWhere('contract.endDate >= CURRENT_TIMESTAMP()');

        if ($status) {
            $qb->andWhere('coalesce(sep.status, sup.status) = :status')->setParameter('status', $status);
        }

        if ($customerIdentifications) {
            $qb->andWhere('contract.customerIdentification IN (:customerIds)')->setParameter('customerIds', $customerIdentifications);
        }

        return $qb;
    }

    /**
     * @return array
     */
    public function findOverlapContracts(Contract $contract, int $serviceProviderId, string $status, bool $hasStation)
    {
        $results = $this->createQueryBuilder('c')
            ->select([' distinct c.id'])
            ->leftJoin('App\Entity\Contract\SubscriptionPackage', 'sp', Expr\Join::WITH, 'sp.contract = c.id')
            ->leftJoin('App\Entity\Package', 'p', Expr\Join::WITH, 'p.id = sp.package')
            ->andWhere('c.serviceProvider = :serviceProviderId')->setParameter('serviceProviderId', $serviceProviderId)
            ->andWhere('c.status = :status')->setParameter('status', $status)
            ->andWhere('p.hasStation = :hasStation')->setParameter('hasStation', $hasStation)
            ->andWhere('c.id != :id')->setParameter('id', $contract->getId())
            ->andWhere('c.startDate >= :startDate')->setParameter('startDate', $contract->getStartDate())
            ->andWhere('c.endDate <= :endDate')->setParameter('endDate', $contract->getEndDate())
            ->getQuery()->getResult();

        return array_column($results, 'id');
    }

    public function getContractFields(Contract $contract, string $contractType, int $packageId)
    {
        if (!in_array($contractType, Contract::TYPES)) {
            throw new Exception('Invalid contract type!');
        }

        $conn = $this->getEntityManager()->getConnection();
        $eb = new ExpressionBuilder($conn);
        $params = [
            'contractId' => $contract->getId(),
            'fieldStates' => [
                SubscriptionPackageField::STATE_GRIDDED,
                SubscriptionPackageField::STATE_CELLS_SELECTED,
                SubscriptionPackageField::STATE_FOR_SAMPLING,
                SubscriptionPackageField::STATE_SAMPLING,
            ],
            'packageId' => $packageId,
        ];
        $paramTypes = [
            'contractId' => ParameterType::INTEGER,
            'fieldStates' => Connection::PARAM_STR_ARRAY,
            'packageIds' => ParameterType::INTEGER,
        ];

        $fieldsQb = $eb->createQuery()
            ->select(
                'spf.plot_uuid AS "plotUuid"',
                'spf.order_uuid AS "orderUuid"',
                'CASE WHEN (p.is_sampling = TRUE AND spf.field_state in (:fieldStates)) OR p.is_sampling = FALSE
                    THEN TRUE
                    ELSE FALSE
                END AS "canRemove"'
            )
            ->from('contract', 'c');

        if (Contract::TYPE_SUBSCRIPTION === $contractType) {
            $fieldsQb->join('c', 'subscription_contracts', 'sc', 'sc.id = c.id')
                ->join('sc', 'subscription_package', 'sp', 'sp.contract_id = sc.id')
                ->join('sp', 'subscription_package_field', 'spf', 'spf.subscription_package_id = sp.id')
                ->addSelect('spf.field_state AS "fieldState"');
        }

        if (Contract::TYPE_SERVICE === $contractType) {
            $fieldsQb->join('c', 'service_contracts', 'sc', 'sc.id = c.id')
                ->join('sc', 'service_contract_packages', 'sp', 'sp.contract_id = sc.id')
                ->join('sp', 'service_package_field', 'spf', 'spf.service_package_id = sp.id')
                ->addSelect('NULL AS "fieldState"');
        }

        $fieldsQb->join('sp', 'package', 'p', 'p.id = sp.package_id')
            ->where('c.id = :contractId')
            ->andWhere('sp.id = :packageId');

        $fieldsCTE = new Expression('fields', $fieldsQb);

        $query = $eb->with($fieldsCTE)
            ->select(
                'fields."plotUuid"',
                'fields."orderUuid"',
                'fields."fieldState"',
                'bool_and(fields."canRemove") AS "canRemove"'
            )
            ->from('fields')
            ->where('fields."plotUuid" NOTNULL')
            ->groupBy(
                '"plotUuid"',
                '"orderUuid"',
                '"fieldState"'
            )
            ->orderBy('"canRemove"', 'desc');

        $stmt = $conn->executeQuery($query->getSQL(), $params, $paramTypes);

        return $stmt->fetchAllAssociative();
    }
}
