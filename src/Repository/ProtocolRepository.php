<?php

namespace App\Repository;

use App\Entity\Protocol;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|Protocol find($id, $lockMode = null, $lockVersion = null)
 * @method null|Protocol findOneBy(array $criteria, array $orderBy = null)
 * @method Protocol[] findAll()
 * @method Protocol[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ProtocolRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Protocol::class);
    }

    public function getProtocolsData(string $customerIdentification, int $id = null)
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT protocol.id AS \"protocolId\", date(protocol.date) AS \"protocolDate\", count(distinct coalesce(supf.plot_uuid, sepf.plot_uuid)) AS \"numberOfPlots\",
            round(sum(DISTINCT coalesce(supf.area, sepf.area)), 3) AS \"totalArea\", ppf.package_field_type AS \"packageFieldType\", contract.id AS \"contractId\",
            contract.area AS \"contractArea\", contract.start_date AS \"contractStartDate\", contract.end_date AS \"contractEndDate\", ru.username AS \"responsibleUser\",
            json_agg(
                DISTINCT coalesce(supf.plot_uuid, sepf.plot_uuid)
            ) AS \"plotUuids\",
            json_agg(
                DISTINCT coalesce(supf.order_uuid, sepf.order_uuid)
            ) AS \"orderUuids\"
            FROM protocol
            JOIN protocol_package_field AS ppf ON ppf.protocol_id = protocol.id
            LEFT JOIN subscription_package_field supf ON supf.id = ppf.package_field_id
            LEFT JOIN service_package_field sepf ON sepf.id = ppf.package_field_id
            LEFT JOIN subscription_package AS sup ON sup.id = supf.subscription_package_id
            LEFT JOIN service_contract_packages AS scp ON scp.id = sepf.service_package_id
            JOIN contract ON contract.id = sup.contract_id OR contract.id = scp.contract_id
            JOIN responsible_user AS ru ON ru.contract_id = contract.id
            WHERE contract.customer_identification = '{$customerIdentification}'
            AND ru.role = 'SERVICE'
        ";

        if ($id) {
            $sql .= "
                AND protocol.id = {$id}
            ";
        }

        $sql .= '
            GROUP BY protocol.id, ppf.package_field_type, contract.id, ru.username;
        ';

        $stmt = $conn->prepare($sql);
        $stmt->execute();

        $protocolData = $stmt->fetchAll();
        $result = array_map(function ($data) {
            if (isset($data['plotUuids'])) {
                $data['plotUuids'] = json_decode($data['plotUuids'], true);
            }

            if (isset($data['orderUuids'])) {
                $data['orderUuids'] = json_decode($data['orderUuids'], true);
            }

            return $data;
        }, $protocolData);

        return $id ? $result[0] : $result;
    }

    // /**
    //  * @return Protocol[] Returns an array of Protocol objects
    //  */
    /*
    public function findByExampleField($value)
    {
    return $this->createQueryBuilder('p')
    ->andWhere('p.exampleField = :val')
    ->setParameter('val', $value)
    ->orderBy('p.id', 'ASC')
    ->setMaxResults(10)
    ->getQuery()
    ->getResult()
    ;
    }
     */

    /*
public function findOneBySomeField($value): ?Protocol
{
return $this->createQueryBuilder('p')
->andWhere('p.exampleField = :val')
->setParameter('val', $value)
->getQuery()
->getOneOrNullResult()
;
}
 */
}
