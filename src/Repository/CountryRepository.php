<?php

namespace App\Repository;

use App\EntityGeoscan\Country;
use Doctrine\ORM\EntityRepository;

/**
 * @method null|Country find($id, $lockMode = null, $lockVersion = null)
 * @method null|Country findOneBy(array $criteria, array $orderBy = null)
 * @method Country[] findAll()
 * @method Country[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CountryRepository extends EntityRepository
{
    // /**
    //  * @return Country[] Returns an array of Country objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('c.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?Country
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
