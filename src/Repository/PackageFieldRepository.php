<?php

namespace App\Repository;

use App\Entity\PackageField;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|PackageField find($id, $lockMode = null, $lockVersion = null)
 * @method null|PackageField findOneBy(array $criteria, array $orderBy = null)
 * @method PackageField[] findAll()
 * @method PackageField[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PackageFieldRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PackageField::class);
    }

    // /**
    //  * @return PackageField[] Returns an array of PackageField objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('p.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?PackageField
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
