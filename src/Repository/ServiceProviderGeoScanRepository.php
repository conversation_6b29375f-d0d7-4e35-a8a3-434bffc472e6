<?php

namespace App\Repository;

use App\EntityGeoscan\ServiceProviderGeoScan;
use Doctrine\ORM\EntityRepository;

/**
 * @method null|ServiceProviderGeoScan find($id, $lockMode = null, $lockVersion = null)
 * @method null|ServiceProviderGeoScan findOneBy(array $criteria, array $orderBy = null)
 * @method ServiceProviderGeoScan[] findAll()
 * @method ServiceProviderGeoScan[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ServiceProviderGeoScanRepository extends EntityRepository
{
    // /**
    //  * @return ServiceProviderGeoScan[] Returns an array of ServiceProviderGeoScan objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('s.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?ServiceProviderGeoScan
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
