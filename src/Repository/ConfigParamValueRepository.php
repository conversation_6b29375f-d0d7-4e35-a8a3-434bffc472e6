<?php

namespace App\Repository;

use App\EntityGeoscan\ConfigParamValue;
use Doctrine\ORM\EntityRepository;

/**
 * @method null|ConfigParamValue find($id, $lockMode = null, $lockVersion = null)
 * @method null|ConfigParamValue findOneBy(array $criteria, array $orderBy = null)
 * @method ConfigParamValue[] findAll()
 * @method ConfigParamValue[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ConfigParamValueRepository extends EntityRepository
{
    // /**
    //  * @return ConfigParamValue[] Returns an array of ConfigParamValue objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('c.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?ConfigParamValue
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
