<?php

namespace App\Repository;

use App\Entity\ProtocolPackageField;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|ProtocolPackageField find($id, $lockMode = null, $lockVersion = null)
 * @method null|ProtocolPackageField findOneBy(array $criteria, array $orderBy = null)
 * @method ProtocolPackageField[] findAll()
 * @method ProtocolPackageField[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ProtocolPackageFieldRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ProtocolPackageField::class);
    }

    // /**
    //  * @return ProtocolPackageField[] Returns an array of ProtocolPackageField objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('p.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?ProtocolPackageField
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
