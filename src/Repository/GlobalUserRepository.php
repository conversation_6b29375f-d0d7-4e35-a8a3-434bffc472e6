<?php

namespace App\Repository;

use App\EntityGeoscan\GlobalUser;
use Doctrine\ORM\EntityRepository;

/**
 * @method null|GlobalUser find($id, $lockMode = null, $lockVersion = null)
 * @method null|GlobalUser findOneBy(array $criteria, array $orderBy = null)
 * @method GlobalUser[] findAll()
 * @method GlobalUser[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class GlobalUserRepository extends EntityRepository
{
    public function findOneByUsername($username): ?GlobalUser
    {
        return $this->createQueryBuilder('u')
            ->where('LOWER(u.username) = LOWER(:username)')
            ->setParameter('username', $username)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function search(string $value, int $serviceProviderId)
    {
        $qb = $this->createQueryBuilder('u');

        $qb->select('u.old_id as id, u.username, u.name, u.email')
            ->distinct()
            ->innerJoin('u.serviceProvider', 'sp')
            ->where('sp.id = :serviceProviderId')
            ->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->like('LOWER(u.username)', ':value'),
                    $qb->expr()->like('LOWER(u.name)', ':value'),
                    $qb->expr()->like('LOWER(u.email)', ':value')
                )
            )
            ->setParameter('value', '%' . trim(strtolower($value)) . '%')
            ->setParameter('serviceProviderId', $serviceProviderId)
            ->orderBy('u.username', 'ASC');

        return $qb->getQuery()->getResult();
    }
}
