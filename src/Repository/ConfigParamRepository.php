<?php

namespace App\Repository;

use App\EntityGeoscan\ConfigParam;
use Doctrine\ORM\EntityRepository;

/**
 * @method null|ConfigParam find($id, $lockMode = null, $lockVersion = null)
 * @method null|ConfigParam findOneBy(array $criteria, array $orderBy = null)
 * @method ConfigParam[] findAll()
 * @method ConfigParam[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ConfigParamRepository extends EntityRepository
{
    // /**
    //  * @return ConfigParam[] Returns an array of ConfigParam objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('c.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?ConfigParam
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
