<?php

namespace App\Repository\Contract;

use App\Entity\Contract;
use App\Entity\Contract\ServicePackageField;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Doctrine\Persistence\ManagerRegistry;
use Somnambulist\CTEBuilder\Expression;
use Somnambulist\CTEBuilder\ExpressionBuilder;

/**
 * @method null|ServicePackageField find($id, $lockMode = null, $lockVersion = null)
 * @method null|ServicePackageField findOneBy(array $criteria, array $orderBy = null)
 * @method ServicePackageField[] findAll()
 * @method ServicePackageField[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ServicePackageFieldRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ServicePackageField::class);
    }

    // /**
    //  * @return ServicePackageField[] Returns an array of ServicePackageField objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('s.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?ServicePackageField
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */

    public function getServicePackageFieldQueryBuilder(string $contractId, ?array $filter = null)
    {
        $qb = $this->createQueryBuilder('servicePackageField');

        $qb->leftJoin('servicePackageField.servicePackage', 'servicePackage')
            ->leftJoin('servicePackage.contract', 'contract')
            ->leftJoin('servicePackage.package', 'package')
            ->where('contract.id = :contractId')->setParameter('contractId', $contractId);

        if (isset($filter['contain_fields'])) {
            $qb->andWhere('package.containFields = true');
        }

        if (isset($filter['is_sampling'])) {
            $qb->andWhere('package.isSampling = true');
        }

        if (isset($filter['is_full_sampling'])) {
            $qb->andWhere('package.isFullSampling = :isFullSampling')->setParameter('isFullSampling', $filter['is_full_sampling']);
        }

        if (isset($filter['packages'])) {
            $qb->andWhere('servicePackage.id IN(:packages)')->setParameter('packages', json_decode($filter['packages'], true));
        }

        if (isset($filter['packages_state'])) {
            $qb->andWhere('servicePackage.state IN(:servPackState)')->setParameter('servPackState', json_decode($filter['packages_state'], true));
        }

        return $qb;
    }

    public function getFieldsToRemove(Contract $contract, array $plotUuids, array $orderUuids)
    {
        $conn = $this->getEntityManager()->getConnection();
        $eb = new ExpressionBuilder($conn);
        $params = [];
        $paramTypes = [];

        $fieldsQb = $eb->createQuery()
            ->select(
                'spf.id',
                'sp.id AS "servicePackageId"',
                'CASE WHEN
                    (
                        p.is_sampling = TRUE 
                        AND spf.field_state in (:field_states)
                    ) OR p.is_sampling = FALSE
                THEN 
                    TRUE
                ELSE
                    FALSE
                END AS "canRemove"'
            )
            ->from('contract', 'c')
            ->join('c', 'service_contracts', 'sc', 'sc.id = c.id')
            ->join('sc', 'service_contract_packages', 'sp', 'sp.contract_id = sc.id')
            ->join('sp', 'service_package_field', 'spf', 'spf.service_package_id = sp.id')
            ->join('sp', 'package', 'p', 'p.id = sp.package_id');

        $params['field_states'] = [
            ServicePackageField::STATE_GRIDDED,
            ServicePackageField::STATE_CELLS_SELECTED,
            ServicePackageField::STATE_FOR_SAMPLING,
            ServicePackageField::STATE_SAMPLING,
        ];
        $paramTypes['field_states'] = Connection::PARAM_STR_ARRAY;

        $fieldsQb->where('c.id = :contract_id');
        $params['contract_id'] = $contract->getId();
        $paramTypes['contract_id'] = ParameterType::INTEGER;

        $fieldsQb->andWhere('spf.order_uuid in (:order_uuids)');
        $params['order_uuids'] = $orderUuids;
        $paramTypes['order_uuids'] = Connection::PARAM_STR_ARRAY;

        $fieldsQb->andWhere('spf.plot_uuid in (:plot_uuids)');
        $params['plot_uuids'] = $plotUuids;
        $paramTypes['plot_uuids'] = Connection::PARAM_STR_ARRAY;

        $fieldsCTE = new Expression('fields', $fieldsQb);

        $query = $eb->with($fieldsCTE)
            ->select(
                'jsonb_agg(fields.id) AS ids',
                'jsonb_agg(distinct fields."servicePackageId") AS "packageIds"',
                'bool_and(fields."canRemove") AS "canRemove"'
            )
            ->from('fields');

        $stmt = $conn->executeQuery($query->getSQL(), $params, $paramTypes);

        $fieldsToRemove = $stmt->fetchAssociative();
        $fieldsToRemove['ids'] = json_decode($fieldsToRemove['ids'], true);
        $fieldsToRemove['packageIds'] = json_decode($fieldsToRemove['packageIds'], true);

        return $fieldsToRemove;
    }

    public function removeByIds(array $ids)
    {
        return $this->createQueryBuilder('spf')
            ->where('spf.id in (:ids)')
            ->setParameter('ids', $ids)
            ->delete()
            ->getQuery()
            ->execute();
    }
}
