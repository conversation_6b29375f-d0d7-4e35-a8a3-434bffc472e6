<?php

namespace App\Repository\Contract;

use App\Entity\Contract\DurationType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|DurationType find($id, $lockMode = null, $lockVersion = null)
 * @method null|DurationType findOneBy(array $criteria, array $orderBy = null)
 * @method DurationType[] findAll()
 * @method DurationType[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DurationTypeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DurationType::class);
    }

    // /**
    //  * @return DurationType[] Returns an array of DurationType objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('d')
            ->andWhere('d.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('d.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?DurationType
    {
        return $this->createQueryBuilder('d')
            ->andWhere('d.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
