<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Repository\Contract;

use App\Entity\Contract\Service;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|Service find($id, $lockMode = null, $lockVersion = null)
 * @method null|Service findOneBy(array $criteria, array $orderBy = null)
 * @method Service[] findAll()
 * @method Service[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ServiceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Service::class);
    }

    public function getFilteredAndSortedQb(array $filters = null, array $sort = null): QueryB<PERSON>er
    {
        return parent::createQueryBuilder('service');
    }
}
