<?php

namespace App\Repository\Contract;

use App\Entity\Analysis\PackageGridPoints;
use App\Entity\Contract\SubscriptionPackage;
use App\Repository\BaseRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Doctrine\ORM\Configuration;
use Doctrine\ORM\Query\Expr;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|SubscriptionPackage find($id, $lockMode = null, $lockVersion = null)
 * @method null|SubscriptionPackage findOneBy(array $criteria, array $orderBy = null)
 * @method SubscriptionPackage[] findAll()
 * @method SubscriptionPackage[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SubscriptionPackageRepository extends BaseRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SubscriptionPackage::class);
    }

    // /**
    //  * @return SubscriptionPackage[] Returns an array of SubscriptionPackage objects
    //  */
    /*
    public function findByExampleField($value)
    {
    return $this->createQueryBuilder('s')
    ->andWhere('s.exampleField = :val')
    ->setParameter('val', $value)
    ->orderBy('s.id', 'ASC')
    ->setMaxResults(10)
    ->getQuery()
    ->getResult()
    ;
    }
     */

    /*
    public function findOneBySomeField($value): ?SubscriptionPackage
    {
    return $this->createQueryBuilder('s')
    ->andWhere('s.exampleField = :val')
    ->setParameter('val', $value)
    ->getQuery()
    ->getOneOrNullResult()
    ;
    }
     */

    public function findByOrganization($customerIdentification)
    {
        $config = new Configuration();
        $config->addCustomNumericFunction('ROUND', 'Oro\ORM\Query\AST\Functions\Numeric\Round');

        $qb = $this->createQueryBuilder('subscriptionPackage');

        $qb->select(
            'subscriptionPackage.id as packageId',
            '\'subscription\' as packageType',
            'concat(to_char(subscriptionPackage.startDate, \'YYYY\'), \'/\', to_char(subscriptionPackage.endDate, \'YYYY\')) as period',
            'DATE(subscriptionPackage.startDate) as startDate',
            'DATE(subscriptionPackage.endDate) as endDate',
            'contract.number as contractNumber',
            'contract.id as contractId',
            'ROUND(contract.area, 2) as contractArea',
            'package.slug',
            'ROUND(sum(fields.area), 2) as allFieldsAreaSum',
            'ROUND((sum(fields.area)/contract.area) * 100) as usedAreaPercentage'
        )
            ->join('subscriptionPackage.contract', 'contract')
            ->join('subscriptionPackage.package', 'package')
            ->join('contract.durationType', 'durationType')
            ->join('subscriptionPackage.subscriptionPackageFields', 'fields')
            ->where('contract.customerIdentification = :customerIdentification')
            ->andWhere('subscriptionPackage.status = \'Active\'')
            ->andWhere('package.containFields = true')
            ->groupBy('packageId', 'contractNumber', 'contract.area', 'contract.startDate', 'contract.endDate', 'package.slug', 'contract.id')
            ->setParameter('customerIdentification', $customerIdentification);

        return $qb->getQuery()->getResult();
    }

    /**
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getPackagesCustom(array $filter = []): array
    {
        $params = [];
        $paramTypes = [];
        $config = new Configuration();
        $config->addCustomNumericFunction('ROUND', 'Oro\ORM\Query\AST\Functions\Numeric\Round');

        $conn = $this->getEntityManager()->getConnection();
        $subQueryBuilder = $conn->createQueryBuilder();
        $subQueryBuilder->select(
            'subscriptionPackage.id',
            'DATE(subscriptionPackage.start_date) as "packageStartDate"',
            'DATE(subscriptionPackage.end_date) as "packageEndDate"',
            'contract.id as "contractId"'
        )
            ->from('subscription_package', 'subscriptionPackage')
            ->leftJoin('subscriptionPackage', 'contract', 'contract', 'subscriptionPackage.contract_id = contract.id')
            ->leftJoin('subscriptionPackage', 'package', 'package', 'subscriptionPackage.package_id = package.id')
            ->andWhere('package.is_full_sampling = true');

        $queryBuilder = $conn->createQueryBuilder();
        $queryBuilder->select(
            'subscriptionPackage.id',
            'package.slug',
            'package.slug_short as "slugShort"',
            'package.style',
            'subscriptionPackage.period as "periodNumber"',
            'concat(to_char(subscriptionPackage.start_date, \'YYYY\'), \'/\', to_char(subscriptionPackage.end_date, \'YYYY\')) as period',
            'subscriptionPackage.state as "stateValue"',
            'DATE(subscriptionPackage.start_date) as "packageStartDate"',
            'DATE(subscriptionPackage.end_date) as "packageEndDate"',
            'COALESCE(ROUND(sum(fields.area), 2), 0) as "allFieldsAreaSum"',
            'COALESCE(ROUND(((sum(fields.area)/NULLIF(contract.area, 0)) * 100), 2), 0) as "usedAreaPercentage"',
            'contract.id as "contractId"',
            'DATE(contract.start_date) as "startDate"',
            'DATE(contract.end_date) as "endDate"',
            'package.is_sampling as "isSampling"',
            'package.is_full_sampling as "isFullSampling"',
            'COALESCE(
                JSONB_AGG(
                    distinct JSONB_BUILD_OBJECT(
                        \'id\', samplingType.id,
                        \'type\', samplingType.type
                    )
                )  filter (where samplingType.id notnull),
                \'[]\'::JSONB
            ) as "samplingTypes"',
            'get_farming_year(fullSamplingSubscriptionPackage."packageStartDate"::date) as "fullSamplingFarmYear"',
            'contract.organization_id'
        )
            ->from('subscription_package', 'subscriptionPackage')
            ->leftJoin('subscriptionPackage', 'contract', 'contract', 'subscriptionPackage.contract_id = contract.id')
            ->leftJoin('subscriptionPackage', 'package', 'package', 'subscriptionPackage.package_id = package.id')
            ->leftJoin('package', 'package_sampling_type', 'packageSamplingType', 'package.id = packageSamplingType.package_id')
            ->leftJoin('packageSamplingType', 'sampling_type', 'samplingType', 'samplingType.id = packageSamplingType.sampling_type_id')
            ->leftJoin('subscriptionPackage', 'subscription_package_field', 'fields', 'subscriptionPackage.id = fields.subscription_package_id')
            ->leftJoin('contract', sprintf('(%s)', $subQueryBuilder->getSQL()), 'fullSamplingSubscriptionPackage', 'fullSamplingSubscriptionPackage."contractId" = contract.id');

        if (isset($filter['contract_id'])) {
            $subQueryBuilder->andWhere('contract.id IN(:contracts)');
            $queryBuilder->andWhere('contract.id IN(:contracts)');
            $params['contracts'] = json_decode($filter['contract_id'], true);
            $paramTypes['contracts'] = Connection::PARAM_INT_ARRAY;
        }

        if (isset($filter['organization_id'])) {
            $subQueryBuilder->andWhere('contract.organization_id =:organization_id');
            $queryBuilder->andWhere('contract.organization_id =:organization_id');
            $params['organization_id'] = $filter['organization_id'];
            $paramTypes['contracts'] = Connection::PARAM_INT_ARRAY;
        }

        if (isset($filter['customer_identification'])) {
            $subQueryBuilder->andWhere('contract.customer_identification IN (:customerIdentification )');
            $queryBuilder->andWhere('contract.customer_identification IN (:customerIdentification )');
            $customerIdentification = json_decode($filter['customer_identification'], true);
            $params['customerIdentification'] = $customerIdentification;
            $paramTypes['customerIdentification'] = is_array($customerIdentification) ? Connection::PARAM_STR_ARRAY : ParameterType::STRING;
        }

        if (isset($filter['packages'])) {
            $queryBuilder->andWhere('package.id IN (:packagesId)');
            $params['packagesId'] = json_decode($filter['packages'], true);
            $paramTypes['packagesId'] = Connection::PARAM_INT_ARRAY;
        }

        if (isset($filter['subscription_packages_id'])) {
            $queryBuilder->andWhere('subscriptionPackage.id IN (:subPackagesId)');
            $params['subPackagesId'] = json_decode($filter['subscription_packages_id'], true);
            $paramTypes['subPackagesId'] = Connection::PARAM_INT_ARRAY;
        }

        if (isset($filter['package_slug'])) {
            $queryBuilder->andWhere('package.slug IN (:packageSlug)');
            $params['packageSlug'] = json_decode($filter['package_slug'], true);
            $paramTypes['packageSlug'] = Connection::PARAM_STR_ARRAY;
        }

        if (isset($filter['package_slug_short'])) {
            $queryBuilder->andWhere('package.slug_short IN (:packageSlugShort)');
            $params['packageSlugShort'] = json_decode($filter['package_slug_short'], true);
            $paramTypes['packageSlugShort'] = Connection::PARAM_STR_ARRAY;
        }

        if (isset($filter['contain_fields'])) {
            $queryBuilder->andWhere('package.contain_fields = true');
        }

        if (isset($filter['is_sampling'])) {
            $queryBuilder->andWhere('package.is_sampling = true');
        }

        if (isset($filter['has_station'])) {
            $queryBuilder->andWhere('package.has_station = true');
        }

        if (isset($filter['packages_status'])) {
            $queryBuilder->andWhere('subscriptionPackage.status IN(:packageStatus)');
            $params['packageStatus'] = json_decode($filter['packages_status'], true);

            $paramTypes['packageStatus'] = Connection::PARAM_STR_ARRAY;
        }

        if (isset($filter['packages_state'])) {
            $queryBuilder->andWhere('subscriptionPackage.state IN(:packageStates)');
            $params['packageStates'] = json_decode($filter['packages_state'], true);
            $paramTypes['packageStates'] = Connection::PARAM_STR_ARRAY;
        }

        if (isset($filter['fields_state'])) {
            $queryBuilder->andWhere('fields.field_state IN(:fieldStates)');
            $params['fieldStates'] = json_decode($filter['fields_state'], true);
            $paramTypes['fieldStates'] = Connection::PARAM_STR_ARRAY;
        }

        if (isset($filter['plot_uuid'])) {
            $queryBuilder->andWhere('fields.plot_uuid = (:plotUuid)');
            $params['plotUuid'] = $filter['plot_uuid'];
            $paramTypes['plotUuid'] = ParameterType::STRING;
        }

        if (isset($filter['period_number'])) {
            $queryBuilder->andWhere('subscriptionPackage.period = :period');
            $params['period'] = $filter['period_number'];
            $paramTypes['period'] = ParameterType::INTEGER;
        }

        if (isset($filter['start_date'])) {
            $startDate = is_numeric($filter['start_date']) ? date('Y-m-d', $filter['start_date']) : $filter['start_date'];
            $queryBuilder->andWhere('date(subscriptionPackage.start_date) >= :start_date');
            $params['start_date'] = $startDate;
            $paramTypes['start_date'] = ParameterType::STRING;
        }

        if (isset($filter['end_date'])) {
            $endDate = is_numeric($filter['end_date']) ? date('Y-m-d', $filter['end_date']) : $filter['end_date'];
            $queryBuilder->andWhere('date(subscriptionPackage.end_date) <= :end_date');
            $params['end_date'] = $endDate;
            $paramTypes['end_date'] = ParameterType::STRING;
        }

        $queryBuilder->groupBy(
            'subscriptionPackage.id',
            'package.slug',
            'package.slug_short',
            'package.style',
            'subscriptionPackage.state',
            'contract.area',
            'subscriptionPackage.period',
            'package.id',
            'contract.id',
            'fullSamplingSubscriptionPackage."packageStartDate"'
        );

        $stmt = $conn->executeQuery($queryBuilder->getSQL(), $params, $paramTypes);

        return $stmt->fetchAllAssociative();
    }

    /**
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function countAllPackagesByIdentNumber(array $customerIdentifications)
    {
        $qb = $this->createQueryBuilder('subscriptionPackage');
        $qb->select('count(subscriptionPackage.id) as countPackages')
            ->leftJoin('subscriptionPackage.contract', 'contract')
            ->leftJoin('subscriptionPackage.package', 'package')
            ->where('contract.customerIdentification IN(:customerIdentifications)')->setParameter('customerIdentifications', $customerIdentifications)
            ->andWhere('package.containFields = true')
            ->andWhere('contract.endDate >= :data')->setParameter('data', 'now()');

        return $qb->getQuery()->getSingleResult();
    }

    public function countAvailablePackagesByIdentNumber(array $customerIdentifications, $packageStates)
    {
        $config = new Configuration();
        $config->addCustomNumericFunction('ROUND', 'Oro\ORM\Query\AST\Functions\Numeric\Round');

        $qb = $this->createQueryBuilder('subscriptionPackage');
        $qb->select('subscriptionPackage.id')
            ->leftJoin('subscriptionPackage.contract', 'contract')
            ->leftJoin('subscriptionPackage.package', 'package')
            ->leftJoin('subscriptionPackage.subscriptionPackageFields', 'fields')
            ->where('contract.customerIdentification IN(:customerIdentifications)')->setParameter('customerIdentifications', $customerIdentifications)
            ->andWhere('package.containFields = true')
            ->andWhere('contract.endDate >= :data')->setParameter('data', 'now()');
        $qb->andWhere('subscriptionPackage.state IN(:packageStates)')->setParameter('packageStates', $packageStates);
        $qb->groupBy('subscriptionPackage.id', 'contract.area');
        $qb->having('contract.area > COALESCE(ROUND(sum(fields.area), 2), 0)');

        return $qb->getQuery()->getResult();
    }

    public function getAllPackageFieldsUuidsByPackageId($packageId)
    {
        $qb = $this->createQueryBuilder('subscriptionPackage');

        $qb->join('subscriptionPackage.subscriptionPackageFields', 'packageField')
            ->where('subscriptionPackage.id = :packageId')->setParameter('packageId', $packageId)
            ->select('packageField.plotUuid', 'packageField.orderUuid')
            ->distinct();

        return $qb->getQuery()->getResult();
    }

    /**
     * @throws \Doctrine\ORM\NonUniqueResultException
     *
     * @return null|int|mixed|string
     */
    public function packageForSamplesContent(string $plotUuid, string $orderUuid)
    {
        $qb = $this->createQueryBuilder('sp');

        return $qb->select(
            'distinct sp.id',
            'p.slug',
            'p.slugShort',
            'p.isSampling',
            'p.isFullSampling',
            'sp.period',
            'sp.state as stateValue',
            'sp.startDate',
            'sp.endDate',
            'spf.id as subscriptionPackageFieldId'
        )
            ->join('sp.package', 'p')
            ->join('sp.subscriptionPackageFields', 'spf')
            ->join(PackageGridPoints::class, 'pgp', Expr\Join::WITH, 'sp.id = pgp.packageId')
            ->where('pgp.plotUuid = :plotUuid')->setParameter('plotUuid', $plotUuid)
            ->andWhere('spf.plotUuid = :plotUuid')->setParameter('plotUuid', $plotUuid)
            ->andWhere('spf.orderUuid = :orderUuid')->setParameter('orderUuid', $orderUuid)
            ->andWhere('pgp.state = \'ReceivedInLab\'')
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Undocumented function.
     *
     * @param [type] $filter
     */
    public function getActiveSubscriptions(array $filter = [])
    {
        $qb = $this->createQueryBuilder('subscriptionPackage');

        $qb->select(
            'contract.organizationId',
            'contract.customerIdentification',
            'package.slug',
            'package.integration',
            'package.attributes',
            'subscriptionPackage.startDate',
            'subscriptionPackage.endDate',
            'subscriptionPackage.amount'
        )
            ->join('subscriptionPackage.contract', 'contract')
            ->join('subscriptionPackage.package', 'package')
            ->join('contract.durationType', 'durationType')
            ->where('subscriptionPackage.status = \'Active\'');

        if (isset($filter['integration'])) {
            $qb->andWhere('package.integration = :integration');
            $parameters['integration'] = $filter['integration'];
        }

        if (isset($filter['identityNumbers']) && count($filter['identityNumbers']) > 0) {
            $qb->andWhere('contract.customerIdentification IN (:identityNumbers )');
            $parameters['identityNumbers'] = $filter['identityNumbers'];
        }

        if (isset($filter['organizationId'])) {
            $qb->andWhere('contract.organizationId = :organizationId');
            $parameters['organizationId'] = $filter['organizationId'];
        }

        if (isset($filter['packageId'])) {
            $qb->andWhere('package.id = :packageId');
            $parameters['packageId'] = $filter['packageId'];
        }

        if (isset($filter['endDate'])) {
            $qb->andWhere('subscriptionPackage.endDate > :endDate');
            $parameters['endDate'] = $filter['endDate'];
        }

        if (isset($filter['startDate'])) {
            $qb->andWhere('subscriptionPackage.startDate >= :startDate');
            $parameters['startDate'] = $filter['startDate'];
        }

        if (isset($filter['startDateBefore'])) {
            $qb->andWhere('subscriptionPackage.startDate <= :startDateBefore');
            $parameters['startDateBefore'] = $filter['startDateBefore'];
        }

        if (isset($filter['packageSlug'])) {
            $qb->andWhere('package.slug = :packageSlug');
            $parameters['packageSlug'] = $filter['packageSlug'];
        }

        $qb
            ->groupBy(
                'contract.organizationId',
                'contract.customerIdentification',
                'package.slug',
                'package.integration',
                'package.attributes',
                'subscriptionPackage.endDate',
                'subscriptionPackage.startDate',
                'subscriptionPackage.amount'
            )
            ->setParameters(
                $parameters
            );

        return $qb->getQuery()->getResult();
    }
}
