<?php

namespace App\Repository\Contract;

use App\Entity\Contract\ResponsibleUser;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\Expr;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|ResponsibleUser find($id, $lockMode = null, $lockVersion = null)
 * @method null|ResponsibleUser findOneBy(array $criteria, array $orderBy = null)
 * @method ResponsibleUser[] findAll()
 * @method ResponsibleUser[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ResponsibleUserRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ResponsibleUser::class);
    }

    // /**
    //  * @return ResponsibleUser[] Returns an array of ResponsibleUser objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('r.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?ResponsibleUser
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */

    /**
     * Returns the responsible users for a giver order uuid.
     *
     * @return ResponsibleUser[]
     */
    public function findByOrderUuid(string $orderUuid)
    {
        $qb = $this->createQueryBuilder('ru');

        return $qb
            ->innerJoin('App\Entity\Contract\SubscriptionPackage', 'sp', Expr\Join::WITH, 'sp.contract = ru.contract')
            ->innerJoin('App\Entity\Contract\SubscriptionPackageField', 'spf', Expr\Join::WITH, 'spf.subscriptionPackage = sp.id')
            ->andWhere('spf.orderUuid = :order_uuid')
            ->groupBy('ru.id')
            ->setParameter('order_uuid', $orderUuid)
            ->getQuery()
            ->getResult();
    }
}
