<?php

namespace App\Repository\Contract;

use App\Entity\Contract;
use App\Entity\Contract\SubscriptionPackageField;
use App\Repository\BaseRepository;
use DateTime;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Doctrine\Persistence\ManagerRegistry;
use Somnambulist\CTEBuilder\Expression;
use Somnambulist\CTEBuilder\ExpressionBuilder;

/**
 * @method null|SubscriptionPackageField find($id, $lockMode = null, $lockVersion = null)
 * @method null|SubscriptionPackageField findOneBy(array $criteria, array $orderBy = null)
 * @method SubscriptionPackageField[] findAll()
 * @method SubscriptionPackageField[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SubscriptionPackageFieldRepository extends BaseRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SubscriptionPackageField::class);
    }

    public function getSubscriptionPackageFieldQueryBuilder(string $contractId, ?array $filter = null)
    {
        $qb = $this->createQueryBuilder('subscriptionPackageField');

        $qb->leftJoin('subscriptionPackageField.subscriptionPackage', 'subscriptionPackage')
            ->leftJoin('subscriptionPackage.contract', 'contract')
            ->leftJoin('subscriptionPackage.package', 'package')
            ->leftJoin('contract.durationType', 'durationType')
            ->where('contract.id = :contractId')->setParameter('contractId', $contractId);

        if (isset($filter['is_sampling'])) {
            $qb->andWhere('package.isSampling = true');
        }

        if (isset($filter['is_full_sampling'])) {
            $qb->andWhere('package.isFullSampling = :isFullSampling')->setParameter('isFullSampling', $filter['is_full_sampling']);
        }

        if (isset($filter['contain_fields'])) {
            $qb->andWhere('package.containFields = true');
        }

        if (isset($filter['packages'])) {
            $qb->andWhere('subscriptionPackage.id IN(:packages)')->setParameter('packages', json_decode($filter['packages'], true));
        }

        if (isset($filter['packages_state'])) {
            $qb->andWhere('subscriptionPackage.state IN(:subsPackState)')->setParameter('subsPackState', json_decode($filter['packages_state'], true));
        }

        if (isset($filter['fields_state'])) {
            $qb->andWhere('subscriptionPackageField.fieldState IN(:subsPackFieldState)')->setParameter('subsPackFieldState', json_decode($filter['fields_state'], true));
        }

        if (isset($filter['period_number'])) {
            $qb->andWhere('subscriptionPackage.period = :period')->setParameter('period', $filter['period_number']);
        }

        return $qb;
    }

    public function countFieldsByStates(array $customerIdentifications, ?array $filter)
    {
        $qb = $this->createQueryBuilder('subscriptionPackageField');
        $qb->select('subscriptionPackageField.fieldState as state', 'count(subscriptionPackageField.plotUuid) as numberOfPlotsWithThisState')
            ->leftJoin('subscriptionPackageField.subscriptionPackage', 'subscriptionPackage')
            ->leftJoin('subscriptionPackage.package', 'package')
            ->leftJoin('subscriptionPackage.contract', 'contract')
            ->where('subscriptionPackage.endDate >= :now')->setParameter('now', new DateTime())
            ->andWhere('contract.customerIdentification IN(:customerIdentifications)')->setParameter('customerIdentifications', $customerIdentifications)
            ->andWhere('subscriptionPackage.state = :state')->setParameter('state', Contract\SubscriptionPackage::STATUS_IN_PROGRESS);

        if (isset($filter['fieldsState']) && count($filter['fieldsState']) > 0) {
            $qb->andWhere('subscriptionPackageField.fieldState IN(:fieldsState)')->setParameter('fieldsState', $filter['fieldsState']);
        }

        if (isset($filter['is_sampling'])) {
            $qb->andWhere('package.isSampling = :isSampling')->setParameter('isSampling', $filter['is_sampling']);
        }

        $qb->groupBy('subscriptionPackageField.fieldState');

        return $qb->getQuery()->getResult();
    }

    public function getSamplingPackagesPlotsByOrderUuids(int $serviceProviderId, array $orderUuids)
    {
        $connection = $this->getEntityManager()->getConnection();

        $query = "
            WITH p_packages AS (
                SELECT
                    sp.id as subscription_package_id 
                    , json_agg(distinct
                        jsonb_build_object(
                            'id', sp.id,
                            'slug', p.slug,
                            'slugShort', p.slug_short,
                            'style', p.style,
                            'isSampling', p.is_sampling,
                            'isVRA', p.is_vra
                        )
                    ) packages	
                FROM
                    subscription_package sp 
                LEFT JOIN subscription_package_field spf ON spf.subscription_package_id = sp.id 
                LEFT JOIN package p ON sp.package_id = p.id
                WHERE
                    p.is_sampling = true 
                group by sp.id  
            ),
            p_json AS (
                select json_build_object(
                'id', sp.id,
                'recommendation_id', r.id,
                'order_uuid', spf.order_uuid, 
                'plot_uuid', spf.plot_uuid,
                'contract', json_build_object(
                    'number', c.\"number\",
                    'contractDate', c.contract_date,
                    'customerIdentification', c.customer_identification,
                    'organizationId', c.organization_id
                ),	
                'packages', COALESCE (pp.packages, '[]'::json)
                ) result_json 
                from subscription_package sp 
                LEFT JOIN subscription_package_field spf ON spf.subscription_package_id = sp.id 
                left join p_packages pp  ON pp.subscription_package_id = sp.id 
                left join recommendations r  ON r.plot_uuid = spf.plot_uuid 
                LEFT JOIN contract c ON c.id = sp.contract_id
                where c.service_provider_id = ? AND spf.order_uuid  IN (?)
            )
            SELECT
                json_agg(pj.result_json) results
            FROM p_json pj
        ";

        $statement = $connection->executeQuery(
            $query,
            [
                $serviceProviderId,
                $orderUuids,
            ],
            [
                ParameterType::INTEGER,
                Connection::PARAM_STR_ARRAY,
            ]
        );
        $result = $statement->fetch();

        return (array)json_decode($result['results'], true);
    }

    public function getSamplingPackagesPlotsByFilters(int $serviceProviderId, array $filter)
    {
        $flt = [];
        array_push(
            $flt,
            [
                'condition' => 'sp.service_provider_id = ?',
                'value' => $serviceProviderId,
                'type' => ParameterType::INTEGER,
            ]
        );
        if (isset($filter['is_sampling']) && filter_var($filter['is_sampling'], FILTER_VALIDATE_BOOLEAN)) {
            array_push(
                $flt,
                [
                    'condition' => 'p.is_sampling = ?',
                    'value' => true,
                    'type' => ParameterType::BOOLEAN,
                ]
            );
        }
        if (isset($filter['is_vra']) && filter_var($filter['is_vra'], FILTER_VALIDATE_BOOLEAN)) {
            array_push(
                $flt,
                [
                    'condition' => 'p.is_vra = ?',
                    'value' => true,
                    'type' => ParameterType::BOOLEAN,
                ]
            );
        }
        array_push(
            $flt,
            [
                'condition' => 'spf.order_uuid NOTNULL',
            ]
        );
        $connection = $this->getEntityManager()->getConnection();

        $query = '
                SELECT
                    spf.order_uuid 
                FROM
                    subscription_package sp 
                LEFT JOIN subscription_package_field spf ON spf.subscription_package_id = sp.id 
                LEFT JOIN package p ON sp.package_id = p.id
                WHERE
                    ' . implode(' AND ', array_column($flt, 'condition')) . ' 
                   GROUP BY spf.order_uuid 
        ';

        $statement = $connection->executeQuery(
            $query,
            array_column($flt, 'value'),
            array_column($flt, 'type')
        );
        $results = $statement->fetchAll();

        return array_column($results, 'order_uuid');
    }

    /**
     * @param bool $countSql
     *
     * @throws \Doctrine\DBAL\DBALException
     *
     * @return array|int
     */
    public function getFieldsABOverview(int $serviceProviderId, array $filers, int $limit = 1, int $offset = 0, bool $pagination = true, $countSql = false)
    {
        $executeArr = [
            'service_provider_id' => (int)$serviceProviderId,
            'customer_identification' => '{}',
            'package_id' => '{}',
            'is_sampling' => null,
            'start_date' => null,
            'end_date' => null,
        ];

        $fieldStateClause = $this->prepareAbOverviewPlaceholders($filers, 'field_state', $executeArr);
        $plotUuidClause = $this->prepareAbOverviewPlaceholders($filers, 'plot_uuid', $executeArr);
        $labNumberClause = $this->prepareAbOverviewPlaceholders($filers, 'lab_number', $executeArr);
        $barcodeClause = $this->prepareAbOverviewPlaceholders($filers, 'barcode', $executeArr);

        if (isset($filers['customer_identification'])) {
            $executeArr['customer_identification'] = '{' . implode(',', $filers['customer_identification']) . '}';
        }

        if (isset($filers['is_sampling'])) {
            $executeArr['is_sampling'] = (int)$filers['is_sampling'];
        }

        if (isset($filers['start_date'])) {
            $executeArr['start_date'] = $filers['start_date'];
        }

        if (isset($filers['end_date'])) {
            $executeArr['end_date'] = $filers['end_date'];
        }

        if (isset($filers['packages'])) {
            $executeArr['package_id'] = '{' . implode(',', $filers['packages']) . '}';
        }

        $sql = '
            with plot_with_elements as (select
                sp.contract_id,
                sp.id as package_id,
                spf.plot_uuid,
                jsonb_agg(jsonb_build_object(
                \'status\', leg.state, 
                \'name\', lag."name" )) as group_elements 
            from subscription_package as sp
            join contract c on c.id = sp.contract_id
            join package on package.id = sp.package_id 
            join subscription_package_field spf on spf.subscription_package_id = sp.id 
            join lab_element_group as leg on leg.package_id = sp.id and spf.plot_uuid = leg.plot_uuid
            join lab_analysis_package_group lapg on lapg.id = leg.lab_analysis_package_group_id
            join lab_analysis_group lag on lag.id = lapg.lab_analysis_group_id
                where
                    c.service_provider_id::int = :service_provider_id::int
                and to_char(sp.start_date, \'YYYY-mm-dd\')::date >= :start_date::date and to_char(sp.start_date, \'YYYY-mm-dd\')::date <= :end_date::date
                and c.customer_identification::varchar = any(:customer_identification)
                and (:is_sampling::int isnull or package.is_sampling::int = :is_sampling)
                and (' . $this->buildAbOverviewCondition('spf.field_state', $fieldStateClause) . ')
                and (' . $this->buildAbOverviewCondition('spf.plot_uuid', $plotUuidClause) . ')
                and (array_length(:package_id::int[], 1) isnull or package.id::int = any(:package_id::int[]))
            group by sp.contract_id, sp.id , spf.plot_uuid
            ),
            grid_ponits_with_elements as (
            select
                sp.contract_id,
                sp.id as package_id,
                pgp.id,
                pgp.plot_uuid,
                pgp.sample_id,
                pgp.state as status,
                pgp.barcode,
                pgp.lab_number, 
                coalesce(jsonb_agg(jsonb_build_object(\'status\', les.state, \'name\', les."element")) filter (where les.state notnull), \'[]\'::jsonb) as elements
            from
                package_grid_points as pgp
            left join lab_elements_results les on pgp.id = les.package_grid_points_id 
            join subscription_package sp on sp.id = pgp.package_id
            join contract c on c.id = sp.contract_id
            join package on package.id = sp.package_id
            where
                c.service_provider_id::int = :service_provider_id::int
                and to_char(sp.start_date, \'YYYY-mm-dd\')::date >= :start_date::date and to_char(sp.start_date, \'YYYY-mm-dd\')::date <= :end_date::date
                and c.customer_identification::varchar = any(:customer_identification)
                and (' . $this->buildAbOverviewCondition('pgp.plot_uuid', $plotUuidClause) . ')
                and (' . $this->buildAbOverviewCondition('pgp.barcode', $barcodeClause) . ')
                and (' . $this->buildAbOverviewCondition('pgp.lab_number', $labNumberClause) . ')
                and (array_length(:package_id::int[], 1) isnull or package.id::int = any(:package_id::int[]))
            group by sp.contract_id, sp.id, pgp.id
            order by pgp.plot_uuid, pgp.sample_id
                )
            select
                sp.contract_id,
                spf.plot_uuid as uuid,
                spf.field_state as state,
                json_build_object(
                        \'id\', sp.id,
                        \'status\', sp.status,
                        \'state\', sp.state,
                        \'slug\', package.slug,
                        \'slugShort\', package.slug_short,
                        \'style\', package.style,
                        \'isSampling\', package.is_sampling,
                        \'startDate\', sp.start_date::date ,
                        \'endDate\', sp.end_date::date
                ) as package,
                pwe.group_elements,
                json_agg(gpwe) as grids_with_elements
            from
                subscription_package sp
            join contract c on c.id = sp.contract_id
            join package on (sp.package_id = package.id)
            join subscription_package_field as spf on (spf.subscription_package_id = sp.id)
            join plot_with_elements as pwe on pwe.plot_uuid = spf.plot_uuid and pwe.contract_id = sp.contract_id and pwe.package_id = sp.id
            join grid_ponits_with_elements as gpwe on gpwe.plot_uuid = spf.plot_uuid and gpwe.contract_id = sp.contract_id and gpwe.package_id = sp.id
                where
                    c.service_provider_id::int = :service_provider_id::int
                and to_char(sp.start_date, \'YYYY-mm-dd\')::date >= :start_date::date and to_char(sp.start_date, \'YYYY-mm-dd\')::date <= :end_date::date
                and c.customer_identification::varchar = any(:customer_identification)
                and (:is_sampling::int isnull or package.is_sampling::int = :is_sampling)
                and (' . $this->buildAbOverviewCondition('spf.field_state', $fieldStateClause) . ')
                and (' . $this->buildAbOverviewCondition('spf.plot_uuid', $plotUuidClause) . ')
                and (array_length(:package_id::int[], 1) isnull or package.id::int = any(:package_id::int[]))
            group by 
                sp.contract_id,
                spf.plot_uuid,
                spf.field_state,
                pwe.group_elements,
                spf.plot_uuid,
                sp.id,
                package.slug,
                package.slug_short,
                package.style,
                package.is_sampling,
                sp.start_date,
                sp.end_date
            order by 
                sp.contract_id desc,
                sp.id
        ';

        if ($pagination && !$countSql) {
            $sql .= 'LIMIT ' . $limit . ' OFFSET ' . $offset . '';
        }

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);
        $stmt->execute($executeArr);

        if ($countSql) {
            return $stmt->rowCount();
        }

        $result = $stmt->fetchAll();

        $result = array_map(function ($raw) {
            $raw['package'] = json_decode($raw['package'], true);
            $raw['group_elements'] = json_decode($raw['group_elements'], true);
            $raw['grids_with_elements'] = json_decode($raw['grids_with_elements'], true);

            return $raw;
        }, $result);

        return (array)json_decode(json_encode($result), true);
    }

    public function getFieldsToRemove(Contract $contract, array $plotUuids, array $orderUuids)
    {
        $conn = $this->getEntityManager()->getConnection();
        $eb = new ExpressionBuilder($conn);
        $params = [];
        $paramTypes = [];

        $fieldsQb = $eb->createQuery()
            ->select(
                'spf.id',
                'sp.id AS "subscriptionPackageId"',
                'CASE WHEN
                    (
                        p.is_sampling = TRUE 
                        AND spf.field_state in (:field_states)
                    ) OR p.is_sampling = FALSE
                THEN 
                    TRUE
                ELSE
                     FALSE
                END AS "canRemove"',
            )
            ->from('contract', 'c')
            ->join('c', 'subscription_contracts', 'sc', 'sc.id = c.id')
            ->join('sc', 'subscription_package', 'sp', 'sp.contract_id = sc.id')
            ->join('sp', 'subscription_package_field', 'spf', 'spf.subscription_package_id = sp.id')
            ->join('sp', 'package', 'p', 'p.id = sp.package_id');

        $params['field_states'] = [
            SubscriptionPackageField::STATE_GRIDDED,
            SubscriptionPackageField::STATE_CELLS_SELECTED,
            SubscriptionPackageField::STATE_FOR_SAMPLING,
            SubscriptionPackageField::STATE_SAMPLING,
        ];
        $paramTypes['field_states'] = Connection::PARAM_STR_ARRAY;

        $fieldsQb->where('c.id = :contract_id');
        $params['contract_id'] = $contract->getId();
        $paramTypes['contract_id'] = ParameterType::INTEGER;

        $fieldsQb->andWhere('spf.order_uuid in (:order_uuids)');
        $params['order_uuids'] = $orderUuids;
        $paramTypes['order_uuids'] = Connection::PARAM_STR_ARRAY;

        $fieldsQb->andWhere('spf.plot_uuid in (:plot_uuids)');
        $params['plot_uuids'] = $plotUuids;
        $paramTypes['plot_uuids'] = Connection::PARAM_STR_ARRAY;

        $fieldsCTE = new Expression('fields', $fieldsQb);

        $query = $eb->with($fieldsCTE)
            ->select(
                'jsonb_agg(fields.id) AS ids',
                'jsonb_agg(distinct fields."subscriptionPackageId") AS "packageIds"',
                'bool_and(fields."canRemove") AS "canRemove"'
            )
            ->from('fields');

        $stmt = $conn->executeQuery($query->getSQL(), $params, $paramTypes);

        $fieldsToRemove = $stmt->fetchAssociative();
        $fieldsToRemove['ids'] = json_decode($fieldsToRemove['ids'], true);
        $fieldsToRemove['packageIds'] = json_decode($fieldsToRemove['packageIds'], true);

        return $fieldsToRemove;
    }

    public function removeByIds(array $ids)
    {
        return $this->createQueryBuilder('spf')
            ->where('spf.id in (:ids)')
            ->setParameter('ids', $ids)
            ->delete()
            ->getQuery()
            ->execute();
    }

    public function getPackageFieldsByUuids(array $plotUuids, array $orderUuids): array
    {
        $qb = $this->createQueryBuilder('subscriptionPackageField')
            ->join('subscriptionPackageField.subscriptionPackage', 'subscriptionPackage')
            ->where('subscriptionPackageField.plotUuid IN (:plotUuids)')->setParameter('plotUuids', $plotUuids)
            ->andWhere('subscriptionPackageField.orderUuid IN (:orderUuids)')->setParameter('orderUuids', $orderUuids)
            ->select(
                'jsonb_agg(subscriptionPackage.id) as packageIds',
                'jsonb_agg(subscriptionPackageField.plotUuid) as plotUuids'
            );

        return array_map('json_decode', $qb->getQuery()->getSingleResult());
    }

    private function buildAbOverviewCondition(string $field, ?string $placeholders, string $type = 'varchar'): string
    {
        return empty($placeholders)
            ? 'TRUE'
            : "{$field}::{$type} IN (" . $placeholders . ')';
    }

    private function prepareAbOverviewPlaceholders(array $filters, string $key, &$executeArr): ?string
    {
        if (empty($filters[$key])) {
            return null;
        }

        $placeholders = [];
        foreach ($filters[$key] as $index => $value) {
            $placeholder = ":{$key}_{$index}";
            $placeholders[] = $placeholder;
            $executeArr["{$key}_{$index}"] = $value;
        }

        return implode(',', $placeholders);
    }
}
