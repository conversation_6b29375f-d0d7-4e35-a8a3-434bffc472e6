<?php

namespace App\Repository\Contract;

use App\Entity\Contract\ServicePackage;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Doctrine\ORM\Configuration;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|ServicePackage find($id, $lockMode = null, $lockVersion = null)
 * @method null|ServicePackage findOneBy(array $criteria, array $orderBy = null)
 * @method ServicePackage[] findAll()
 * @method ServicePackage[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ServicePackageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ServicePackage::class);
    }

    // /**
    //  * @return ServicePackage[] Returns an array of ServicePackage objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('s.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?ServicePackage
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */

    public function findByOrganization($customerIdentification)
    {
        $config = new Configuration();
        $config->addCustomNumericFunction('ROUND', 'Oro\ORM\Query\AST\Functions\Numeric\Round');

        $qb = $this->createQueryBuilder('servicePackage');

        $qb->select(
            'servicePackage.id as packageId',
            '\'service\' as packageType',
            'contract.number as contractNumber',
            'contract.id as contractId',
            'package.slug',
            'ROUND(contract.area, 2) as contractArea',
            'ROUND(sum(fields.area), 2) as allFieldsAreaSum',
            'ROUND((sum(fields.area)/contract.area) * 100) as usedAreaPercentage'
        )
            ->join('servicePackage.contract', 'contract')
            ->join('servicePackage.package', 'package')
            ->join('servicePackage.servicePackageFields', 'fields')
            ->where('contract.customerIdentification = :customerIdentification')
            ->andWhere('package.containFields = true')
            ->groupBy('packageId', 'packageType', 'contract.number', 'package.slug', 'contract.area', 'contract.id')
            ->setParameter('customerIdentification', $customerIdentification);

        return $qb->getQuery()->getResult();
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    public function getPackagesCustom(array $filter = []): array
    {
        $params = [];
        $paramTypes = [];
        $config = new Configuration();
        $config->addCustomNumericFunction('ROUND', 'Oro\ORM\Query\AST\Functions\Numeric\Round');

        $conn = $this->getEntityManager()->getConnection();
        $queryBuilder = $conn->createQueryBuilder();

        $queryBuilder->select(
            'servicePackage.id',
            'package.slug',
            'package.slug_short as "slugShort"',
            'package.style',
            'servicePackage.state as "stateValue"',
            'COALESCE(ROUND(sum(fields.area), 2), 0) as "allFieldsAreaSum"',
            'COALESCE(ROUND((sum(fields.area)/NULLIF(contract.area, 0)) * 100), 0) as "usedAreaPercentage"',
            'DATE(contract.start_date) as "startDate"',
            'DATE(contract.end_date) as "endDate"',
            'concat(to_char(contract.start_date, \'dd-mm-yyyy\'), \' - \' , to_char(contract.end_date, \'dd-mm-yyyy\')) as period',
            'contract.id as "contractId"',
            'COALESCE(
                JSONB_AGG(
                    distinct JSONB_BUILD_OBJECT(
                        \'id\', samplingType.id,
                        \'type\', samplingType.type
                    )
                )  filter (where samplingType.id notnull),
                \'[]\'::JSONB
            ) as "samplingTypes"',
        )
            ->from('service_contract_packages', 'servicePackage')
            ->leftJoin('servicePackage', 'contract', 'contract', 'servicePackage.contract_id = contract.id')
            ->leftJoin('servicePackage', 'package', 'package', 'servicePackage.package_id = package.id')
            ->leftJoin('package', 'package_sampling_type', 'packageSamplingType', 'package.id = packageSamplingType.package_id')
            ->leftJoin('packageSamplingType', 'sampling_type', 'samplingType', 'samplingType.id = packageSamplingType.sampling_type_id')
            ->leftJoin('servicePackage', 'subscription_package_field', 'fields', 'servicePackage.id = fields.subscription_package_id');

        if (isset($filter['contract_id'])) {
            $queryBuilder->andWhere('contract.id IN(:contracts)');
            $params['contracts'] = json_decode($filter['contract_id'], true);
            $paramTypes['contracts'] = Connection::PARAM_INT_ARRAY;
        }

        if (isset($filter['customer_identification'])) {
            $customerIdentification = json_decode($filter['customer_identification'], true);
            $queryBuilder->andWhere('contract.customer_identification IN (:customerIdentification )');
            $params['customerIdentification'] = $customerIdentification;
            $paramTypes['customerIdentification'] = is_array($customerIdentification) ? Connection::PARAM_STR_ARRAY : ParameterType::STRING;
        }

        if (isset($filter['packages'])) {
            $queryBuilder->andWhere('package.id IN (:packagesId)');
            $params['packagesId'] = json_decode($filter['packages'], true);
            $paramTypes['packagesId'] = Connection::PARAM_INT_ARRAY;
        }

        if (isset($filter['service_packages_id'])) {
            $queryBuilder->andWhere('servicePackage.id IN (:servicePackagesId)');
            $params['servicePackagesId'] = json_decode($filter['service_packages_id'], true);
            $paramTypes['servicePackagesId'] = Connection::PARAM_INT_ARRAY;
        }

        if (isset($filter['package_slug'])) {
            $queryBuilder->andWhere('package.slug IN(:packageSlug)');
            $params['packageSlug'] = json_decode($filter['package_slug'], true);
            $paramTypes['packageSlug'] = Connection::PARAM_STR_ARRAY;
        }

        if (isset($filter['package_slug_short'])) {
            $queryBuilder->andWhere('package.slug_short IN (:packageSlugShort)');
            $params['packageSlugShort'] = json_decode($filter['package_slug_short'], true);
            $paramTypes['packageSlugShort'] = Connection::PARAM_STR_ARRAY;
        }

        if (isset($filter['contain_fields'])) {
            $queryBuilder->andWhere('package.contain_fields = true');
        }

        if (isset($filter['is_sampling'])) {
            $queryBuilder->andWhere('package.is_sampling = true');
        }

        if (isset($filter['has_station'])) {
            $queryBuilder->andWhere('package.has_station = true');
        }

        if (isset($filter['plot_uuid'])) {
            $queryBuilder->andWhere('fields.plot_uuid = (:plotUuid)');
            $params['plotUuid'] = $filter['plot_uuid'];
            $paramTypes['plotUuid'] = ParameterType::STRING;
        }

        if (isset($filter['packages_status'])) {
            $queryBuilder->andWhere('servicePackage.status IN(:packageStatus)');
            $params['packageStatus'] = json_decode($filter['packages_status'], true);
            $paramTypes['packageStatus'] = Connection::PARAM_STR_ARRAY;
        }

        if (isset($filter['packages_state'])) {
            $queryBuilder->andWhere('servicePackage.state IN(:packageStates)');
            $params['packageStates'] = json_decode($filter['packages_state'], true);
            $paramTypes['packageStates'] = Connection::PARAM_STR_ARRAY;
        }

        $queryBuilder->groupBy(
            'servicePackage.id',
            'package.slug',
            'package.slug_short',
            'package.style',
            'contract.area',
            'contract.start_date',
            'contract.end_date',
            'package.id',
            'contract.id',
        );

        $stmt = $conn->executeQuery($queryBuilder->getSQL(), $params, $paramTypes);

        return $stmt->fetchAllAssociative();
    }

    /**
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function countPackagesByIdentNumber(array $customerIdentifications)
    {
        $qb = $this->createQueryBuilder('servicePackage');
        $qb->select('count(servicePackage.id)  as countPackages')
            ->leftJoin('servicePackage.contract', 'contract')
            ->leftJoin('servicePackage.package', 'package')
            ->where('contract.customerIdentification IN(:customerIdentifications)')->setParameter('customerIdentifications', $customerIdentifications)
            ->andWhere('package.containFields = true')
            ->andWhere('contract.endDate >= :data')->setParameter('data', 'now()');

        return $qb->getQuery()->getSingleResult();
    }

    /**
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function countAvailablePackagesByIdentNumber(array $customerIdentifications, $packageStates)
    {
        $config = new Configuration();
        $config->addCustomNumericFunction('ROUND', 'Oro\ORM\Query\AST\Functions\Numeric\Round');

        $qb = $this->createQueryBuilder('servicePackage');
        $qb->select('servicePackage.id')
            ->leftJoin('servicePackage.contract', 'contract')
            ->leftJoin('servicePackage.package', 'package')
            ->leftJoin('servicePackage.servicePackageFields', 'fields')
            ->where('contract.customerIdentification IN(:customerIdentifications)')->setParameter('customerIdentifications', $customerIdentifications)
            ->andWhere('package.containFields = true')
            ->andWhere('contract.endDate >= :data')->setParameter('data', 'now()');
        $qb->andWhere('servicePackage.state IN(:packageStates)')->setParameter('packageStates', $packageStates);
        $qb->groupBy('servicePackage.id', 'contract.area');
        $qb->having('contract.area > COALESCE(ROUND(sum(fields.area), 2), 0)');

        return $qb->getQuery()->getResult();
    }
}
