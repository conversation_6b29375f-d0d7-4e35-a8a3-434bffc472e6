<?php

namespace App\Repository\Contract;

use App\Entity\Contract\Price;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|Price find($id, $lockMode = null, $lockVersion = null)
 * @method null|Price findOneBy(array $criteria, array $orderBy = null)
 * @method Price[] findAll()
 * @method Price[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PriceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Price::class);
    }

    public function findByContract($value)
    {
        return $this->createQueryBuilder('price')
            ->andWhere('price.contract = :val')
            ->setParameter('val', $value)
            ->orderBy('price.period', 'ASC')
            ->getQuery()
            ->getResult();
    }
}
