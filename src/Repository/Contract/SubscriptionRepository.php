<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Repository\Contract;

use App\Entity\Contract\Subscription;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|Subscription find($id, $lockMode = null, $lockVersion = null)
 * @method null|Subscription findOneBy(array $criteria, array $orderBy = null)
 * @method Subscription[] findAll()
 * @method Subscription[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SubscriptionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Subscription::class);
    }

    public function getFilteredAndSortedQb(array $filters = null, array $sort = null): QueryBuilder
    {
        return parent::createQueryBuilder('subscription');
    }

    public function getAllContractsWithExpiredEndDate()
    {
        $currentDate = date('Y-m-d');

        $qb = $this->createQueryBuilder('Subscription');
        $qb->where('Subscription.endDate < :currentDate')->setParameter('currentDate', $currentDate);
        $qb->andWhere('Subscription.contractDate IS NOT NULL');
        $qb->andWhere('Subscription.status = \'Active\'');
        $qb->andWhere('Subscription.id = 128472');

        return $qb->getQuery()->getResult();
    }
}
