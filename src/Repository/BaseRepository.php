<?php

namespace App\Repository;

use App\Entity\Activity;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|Activity find($id, $lockMode = null, $lockVersion = null)
 * @method null|Activity findOneBy(array $criteria, array $orderBy = null)
 * @method Activity[] findAll()
 * @method Activity[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
abstract class BaseRepository extends ServiceEntityRepository
{
    /**
     * @param string $entityClass The class name of the entity this repository manages
     */
    public function __construct(ManagerRegistry $registry, $entityClass)
    {
        parent::__construct($registry, $entityClass);
    }

    /**
     * @return Query
     */
    public function findByFieldsQuery(array $params)
    {
        $qb = $this->createQueryBuilder('entityClass');

        $qb->select('entityClass');
        foreach ($params as $param) {
            $qb->andWhere('entityClass.' . $param['field'] . ' ' . $param['type'] . ' (:' . $param['field'] . ')');
            $qb->setParameter('' . $param['field'] . '', $param['value']);
        }

        return $qb->getQuery();
    }
}
