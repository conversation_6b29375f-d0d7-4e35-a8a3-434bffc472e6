<?php

namespace App\Repository;

use App\Entity\Package;
use Doctrine\ORM\Query\Expr;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|Package find($id, $lockMode = null, $lockVersion = null)
 * @method null|Package findOneBy(array $criteria, array $orderBy = null)
 * @method Package[] findAll()
 * @method Package[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PackageRepository extends BaseRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Package::class);
    }

    public function getPackagesByPlot(string $plotUuid, $status = 'Active'): array
    {
        $qb = $this->createQueryBuilder('p');

        $qb->select(
            'p.id',
            'p.slug',
            'p.slugShort',
            'p.containFields',
            'p.hasStation',
            'p.integration',
            'p.isSampling',
            'p.isVRA',
            'p.isFullSampling',
            'p.isSatellite'
        )
            ->leftJoin('p.subscriptionPackages', 'subP', Expr\Join::WITH, $qb->expr()->andX(
                $qb->expr()->eq('subP.package', 'p'),
                $qb->expr()->eq('subP.status', ':status')
            ))
            ->leftJoin('p.servicePackages', 'serP', Expr\Join::WITH, $qb->expr()->andX(
                $qb->expr()->eq('serP.package', 'p'),
                $qb->expr()->eq('serP.status', ':status')
            ))
            ->leftJoin('subP.subscriptionPackageFields', 'subPF')
            ->leftJoin('serP.servicePackageFields', 'serPF')
            ->where('p.isActive = true')
            ->andWhere(
                $qb->expr()->orX(
                    'subPF.plotUuid = :plotUuid',
                    'serPF.plotUuid = :plotUuid'
                )
            )
            ->setParameter('plotUuid', $plotUuid)
            ->setParameter('status', $status)
            ->distinct();

        return $qb->getQuery()->getResult();
    }

    /**
     * @param int $serviceProviderid
     */
    public function getPackagesByServiceProvider(int $serviceProviderId, array $filter): array
    {
        $qb = $this->createQueryBuilder('p');

        $qb->select(
            'p.id',
            'p.slug',
            'p.slugShort',
            'p.containFields',
            'p.hasStation',
            'p.integration',
            'p.isSampling',
            'p.isVRA',
            'p.isFullSampling',
            'p.isSatellite'
        )
            ->where('p.isActive = true')
            ->andWhere('p.serviceProvider = :service_provider_id');

        $parameters['service_provider_id'] = $serviceProviderId;

        if (isset($filter['integration'])) {
            $qb->andWhere('p.integration = :integration');
            $parameters['integration'] = $filter['integration'];
        }

        $qb->setParameters(
            $parameters
        );

        return $qb->getQuery()->getResult();
    }

    /**
     * Get packages with optional filters.
     */
    public function getPackages(array $filter = []): array
    {
        $qb = $this->createQueryBuilder('p');
        $parameters = [];

        $qb->where('p.isActive = true');

        if (isset($filter['slug_short']) && !empty($filter['slug_short'])) {
            if (is_array($filter['slug_short'])) {
                $qb->andWhere('p.slugShort IN (:slug_short)');
            } else {
                $qb->andWhere('p.slugShort = :slug_short');
            }
            $parameters['slug_short'] = $filter['slug_short'];
        }

        if (isset($filter['service_provider_id']) && !empty($filter['service_provider_id'])) {
            $qb->andWhere('p.serviceProvider = :service_provider_id');
            $parameters['service_provider_id'] = $filter['service_provider_id'];
        }

        if (!empty($parameters)) {
            $qb->setParameters($parameters);
        }

        return $qb->getQuery()->getResult();
    }
}
