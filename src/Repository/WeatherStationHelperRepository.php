<?php

namespace App\Repository;

use App\Entity\Helper\WeatherStationHelper;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|WeatherStationHelper find($id, $lockMode = null, $lockVersion = null)
 * @method null|WeatherStationHelper findOneBy(array $criteria, array $orderBy = null)
 * @method WeatherStationHelper[] findAll()
 * @method WeatherStationHelper[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class WeatherStationHelperRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, WeatherStationHelper::class);
    }

    public function getSortedDataByPeriod()
    {
        $conn = $this->getEntityManager()->getConnection();
        $sql = '
        select
            wsh.station_start_date || \' - \' || wsh.station_end_date as period,
            json_agg(wsh.id) as stations_id,
            wsh.organization_ident_number,
            wsh.station_start_date,
            wsh.station_end_date,
            wsh.station_period
        from
            weather_station_helper as wsh
        group by
            period,
            wsh.organization_ident_number,
            wsh.station_start_date,
            wsh.station_end_date,
            wsh.station_period
        order by
            period
        ';
        $stmt = $conn->prepare($sql);
        $stmt->execute();

        return $stmt->fetchAll();
    }
}
