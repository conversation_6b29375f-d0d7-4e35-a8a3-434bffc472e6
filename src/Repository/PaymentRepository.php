<?php

namespace App\Repository;

use App\Entity\Payment;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method null|Payment find($id, $lockMode = null, $lockVersion = null)
 * @method null|Payment findOneBy(array $criteria, array $orderBy = null)
 * @method Payment[] findAll()
 * @method Payment[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PaymentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Payment::class);
    }

    // /**
    //  * @return Payment[] Returns an array of Payment objects
    //  */

    public function findByPrice($value)
    {
        return $this->createQueryBuilder('payment')
            ->andWhere('payment.price = :val')
            ->setParameter('val', $value)
            ->orderBy('payment.id', 'DESC');
        /*->getQuery()
        ->getResult();*/
    }
}
