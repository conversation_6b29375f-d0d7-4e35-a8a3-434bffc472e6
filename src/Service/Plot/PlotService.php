<?php

namespace App\Service\Plot;

use App\Repository\Contract\SubscriptionPackageFieldRepository;
use App\Repository\Recommendation\RecommendationRepository;
use App\Service\AbstractService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Security\Core\Security;

class PlotService extends AbstractService
{
    private $em;
    private $security;
    private $subscriptionPackageFieldRepository;
    private $recommendationRepository;

    public function __construct(
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        SubscriptionPackageFieldRepository $subscriptionPackageFieldRepository,
        RecommendationRepository $recommendationRepository,
        Security $security
    ) {
        parent::__construct($formFactory);
        $this->em = $entityManager;
        $this->security = $security;
        $this->subscriptionPackageFieldRepository = $subscriptionPackageFieldRepository;
        $this->recommendationRepository = $recommendationRepository;
    }

    public function getSamplingOrderUuidsByFilters(array $filter)
    {
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();
        $recommendationStatues = json_decode($filter['status'] ?? '[]', true);

        if (count($recommendationStatues)) {
            return array_column($this->recommendationRepository->findRecommendations($serviceProviderId, $filter, false), 'orderUuid');
        }

        return $this->subscriptionPackageFieldRepository->getSamplingPackagesPlotsByFilters($serviceProviderId, $filter);
    }

    /**
     * @throws \Doctrine\DBAL\DBALException
     *
     * @return array
     */
    public function getSamplingPackagesPlotsByOrderUuids(array $orderUuids)
    {
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();

        return $this->subscriptionPackageFieldRepository->getSamplingPackagesPlotsByOrderUuids($serviceProviderId, $orderUuids);
    }
}
