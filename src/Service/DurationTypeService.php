<?php

namespace App\Service;

use App\Entity\Contract\DurationType;
use DateTime;
use DateTimeImmutable;
use Exception;
use League\Period\Exception as LeagueException;
use League\Period\Period;

class DurationTypeService
{
    /**
     * @throws LeagueException
     * @throws Exception
     *
     * @return array[]
     */
    public function calculatePeriods(DurationType $durationType, DateTime $startDateObj, DateTime $endDateObj): iterable
    {
        $slug = $durationType->getSlug();
        $isCalendarPeriod = $durationType->getIsCalendarPeriod();
        switch (true) {
            case (DurationType::DURATION_TYPE_DAY_SLUG == $slug && true === $isCalendarPeriod):
                $result = $this->getDailyPeriods($durationType, $startDateObj, $endDateObj);

                break;
            case (DurationType::DURATION_TYPE_MONTH_SLUG == $slug && true === $isCalendarPeriod):
                $result = $this->getMonthlyPeriods($durationType, $startDateObj, $endDateObj);

                break;
            case (DurationType::DURATION_TYPE_YEAR_SLUG == $slug && true === $isCalendarPeriod):
                $result = $this->getYearlyPeriods($durationType, $startDateObj, $endDateObj);

                break;
            case (false === $isCalendarPeriod):
                $result = $this->getFarmingEarlyPeriods($durationType, $startDateObj, $endDateObj);

                break;
            default:
                throw new Exception('Invalid duration type slug provided');
        }

        return $result;
    }

    public function getMonthlyPeriods(DurationType $durationType, DateTime $startDateObj, DateTime $endDateObj): iterable
    {
        $interval = $startDateObj->diff($endDateObj);

        // format('n') Numeric representation of a month, without leading zeros
        if ($startDateObj->format('n') !== $endDateObj->format('n') && $interval->d > 0) {
            $realEndDateObj = clone $endDateObj;
            $endDateObj->modify('+1 month');
        }

        $period = new Period($startDateObj, $endDateObj, Period::INCLUDE_ALL);
        /** @var Period[] $split */
        $split = $period->split('1 ' . $durationType->getSlug());

        $periods = [];
        foreach ($split as $item) {
            // when using interval it will fully add one month to start and end date
            $startDate = $item->getStartDate();
            $endDate = $item->getEndDate();

            // only first iteration will contain original start date,
            // else will be replaced with begin of month
            if ($startDate != $startDateObj) {
                $startDate = $item->getStartDate()->modify('first day of this  month');
            }

            if (isset($realEndDateObj)) {
                $endDate = $item->getEndDate()->modify('last day of previous  month');
                if ($endDate > $realEndDateObj) {
                    // restore to original interval end date
                    $endDate = $endDate->setDate($realEndDateObj->format('Y'), $realEndDateObj->format('m'), $realEndDateObj->format('d'));
                }
            }

            $this->buildDurationResult($periods, $startDate, $endDate);
        }

        return $periods;
    }

    public function getDailyPeriods(DurationType $durationType, DateTime $startDateObj, DateTime $endDateObj): iterable
    {
        $period = new Period($startDateObj, $endDateObj, Period::INCLUDE_ALL);

        /** @var Period[] $split */
        $split = $period->split('1 ' . $durationType->getSlug());

        $periods = [];
        foreach ($split as $item) {
            $startDate = $item->getStartDate();
            $endDate = clone $startDate;

            $this->buildDurationResult($periods, $startDate, $endDate);
        }

        return $periods;
    }

    public function getYearlyPeriods(DurationType $durationType, DateTime $startDateObj, DateTime $endDateObj): iterable
    {
        $period = new Period($startDateObj, $endDateObj, Period::INCLUDE_ALL);

        /** @var Period[] $split */
        $split = $period->split('1 ' . $durationType->getSlug());
        $periods = [];

        foreach ($split as $item) {
            $startDate = $item->getStartDate();
            $endDate = $item->getEndDate();

            // modify all periods end dates excluding period endDate
            if ($endDate != $endDateObj) {
                $endDate = $endDate->modify('yesterday');
            }

            $this->buildDurationResult($periods, $startDate, $endDate);
        }

        return $periods;
    }

    public function getFarmingEarlyPeriods(DurationType $durationType, DateTime $startDateObj, DateTime $endDateObj): iterable
    {
        $startYear = (int)$startDateObj->format('Y');
        $endYear = (int)$endDateObj->format('Y');

        $farmingStartMonth = $startDateObj->format('n');
        $farmingEndMonth = $endDateObj->format('n');

        if ($farmingStartMonth < $durationType->getStartMonth()) {
            --$startYear;
        }

        if ($farmingEndMonth <= $durationType->getEndMonth()) {
            --$endYear;
        }

        $period = $endYear - $startYear + 1;
        $periods = [];

        for ($i = 0; $i <= $period - 1; $i++) {
            $periodStartYear = $startDateObj->format('Y') + $i;
            $periodEndYear = $startYear + $i + 1;

            if ($farmingStartMonth <= $durationType->getEndMonth()
                && $farmingEndMonth <= $durationType->getEndMonth()
                && 0 !== $i) {
                --$periodStartYear;
            }

            if ($farmingStartMonth >= $durationType->getStartMonth()
                && $farmingEndMonth >= $durationType->getStartMonth()
                && $i === $period - 1) {
                --$periodEndYear;
            }

            if ($farmingStartMonth <= $durationType->getEndMonth()
                && $farmingEndMonth >= $durationType->getStartMonth()
                && 0 !== $i) {
                --$periodStartYear;
            }

            if ($farmingStartMonth <= $durationType->getEndMonth()
                && $farmingEndMonth >= $durationType->getStartMonth()
                && $i === $period - 1) {
                --$periodEndYear;
            }

            if (0 == $i) {
                $startMonth = $farmingStartMonth; // m
                $startDay = $startDateObj->format('j'); // d
            } else {
                $startMonth = $durationType->getStartMonth();
                $startDay = $durationType->getStartDay();
            }

            if ($period - 1 == $i) {
                $endMonth = $farmingEndMonth; // m
                $endDay = $endDateObj->format('j'); // d
            } else {
                $endMonth = $durationType->getEndMonth();
                $endDay = $durationType->getEndDay();
            }

            $farmingPeriod = ($startYear + $i) . '/' . ($startYear + $i + 1);
            $periodStartDate = new DateTime("{$periodStartYear}-{$startMonth}-{$startDay}");
            $periodEndDate = new DateTime("{$periodEndYear}-{$endMonth}-{$endDay}");

            $periods[] = [
                'period' => $farmingPeriod,
                'startDate' => $periodStartDate->format('Y-m-d 00:00:00'),
                'endDate' => $periodEndDate->format('Y-m-d 23:59:59'),
            ];
        }

        return $periods;
    }

    public function buildDurationResult(array &$periods, DateTimeImmutable $startDate, DateTimeImmutable $endDate): void
    {
        $periods[] = [
            'period' => $startDate->format('d.m.Y') . ' - ' . $endDate->format('d.m.Y'),
            'startDate' => $startDate->format('Y-m-d 00:00:00'),
            'endDate' => $endDate->format('Y-m-d 23:59:59'),
        ];
    }

    public function formatPeriod(DurationType $durationType, DateTime $startDate, DateTime $endDate): string
    {
        if (false === $durationType->getIsCalendarPeriod()) {
            $startYear = (int) $startDate->format('Y');
            if ($startDate->format('n') < $durationType->getStartMonth()) {
                --$startYear;
            }

            return $startYear . '/' . ($startYear + 1);
        }

        return $startDate->format('d.m.Y') . ' - ' . $endDate->format('d.m.Y');
    }
}
