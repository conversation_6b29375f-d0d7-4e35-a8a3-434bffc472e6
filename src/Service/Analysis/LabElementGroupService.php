<?php

namespace App\Service\Analysis;

use App\Entity\Analysis\LabAnalysisGroupElement;
use App\Entity\Analysis\LabAnalysisPackageGroup;
use App\Entity\Analysis\LabElementGroup;
use App\Entity\Contract\SubscriptionPackage;
use App\EntityGeoscan\ServiceProviderGeoScan;
use App\Repository\Analysis\LabAnalysisGroupElementRepository;
use App\Repository\Analysis\LabElementGroupRepository;
use App\Repository\Contract\SubscriptionPackageRepository;
use App\Service\AbstractService;
use App\Service\GeoSCANAPIClient;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Exception;
use Symfony\Component\Form\FormFactoryInterface;

class LabElementGroupService extends AbstractService
{
    private EntityManagerInterface $em;
    private GeoSCANAPIClient $geoScanAPIClient;

    private ManagerRegistry $managerRegistry;

    public function __construct(
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        ManagerRegistry $managerRegistry,
        GeoSCANAPIClient $geoSCANAPIClient
    ) {
        parent::__construct($formFactory);
        $this->em = $entityManager;
        $this->managerRegistry = $managerRegistry;
        $this->geoScanAPIClient = $geoSCANAPIClient;
    }

    public function create($arrToInsert)
    {
        $this->em->getConnection()->beginTransaction();

        try {
            $now = new DateTime();

            foreach ($arrToInsert as $value) {
                $labAnalysisPackageGroup = $this->em->getRepository(LabAnalysisPackageGroup::class)->find($value['labAnalysisPackageGroupId']);
                $labElementGroup = new LabElementGroup();
                $labElementGroup->setPackageId($value['packageId']);
                $labElementGroup->setPackageType($value['packageType']);
                $labElementGroup->setPlotUuid($value['plotUuid']);
                $labElementGroup->setLabAnalysisPackageGroup($labAnalysisPackageGroup);
                $labElementGroup->setStateUpdatedAt($now);

                $this->em->persist($labElementGroup);
            }

            $this->em->flush();
            $this->em->clear(LabElementGroup::class);
            $this->em->getConnection()->commit();
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }
    }

    public function remove($data)
    {
        $firstData = reset($data);
        $packageType = $firstData['package_type'] ?? null;

        $arrPackageIds = array_map(function ($item) {
            return $item['package_id'];
        }, $data);
        $packageIds = array_values(array_unique($arrPackageIds));

        $arrPlotUuids = array_map(function ($item) {
            return $item['plot_uuid'];
        }, $data);
        $plotUuids = array_values(array_unique($arrPlotUuids));

        $this->em->getConnection()->beginTransaction();

        try {
            $dql = 'SELECT eg FROM App\Entity\Analysis\LabElementGroup eg WHERE eg.packageId IN(:packageIds) AND eg.plotUuid IN(:plotUuids) AND eg.packageType = :packageType';
            $query = $this->em->createQuery($dql)
                ->setParameter('packageIds', $packageIds)
                ->setParameter('plotUuids', $plotUuids)
                ->setParameter('packageType', $packageType);
            $iterableResult = $query->iterate();

            foreach ($iterableResult as $row) {
                $this->em->remove($row[0]);
            }
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }

        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();
    }

    public function sendSoilMapData(LabElementGroup $labElementGroup)
    {
        /** @var LabElementGroupRepository */
        $labElementGroupRepo = $this->em->getRepository(LabElementGroup::class);

        /** @var SubscriptionPackageRepository */
        $subscriptionPackageRepo = $this->em->getRepository(SubscriptionPackage::class);

        $subscriptionPackage = $subscriptionPackageRepo->find($labElementGroup->getPackageId());
        $contract = $subscriptionPackage->getContract();
        $serviceProviderId = $contract->getServiceProviderId();
        $geoscanEm = $this->managerRegistry->getManager('geoscan');
        $serviceProvider = $geoscanEm->getRepository(ServiceProviderGeoScan::class)->find($serviceProviderId);
        $country = $serviceProvider->getCountry();

        $countryCode = strtolower($country->getIsoAlpha2Code());

        $plotUuid = $labElementGroup->getPlotUuid();

        $soilMapData = $labElementGroupRepo->getSoilMapData($serviceProvider->getId(), $plotUuid, $labElementGroup->getId());

        if (!count($soilMapData['points'])) {
            return false;
        }

        $data = [
            'plot_uuid' => $plotUuid,
            'points' => $soilMapData['points'],
            'element_interpretations' => $soilMapData['element_interpretations'],
            'service_provider' => [
                'id' => $serviceProvider->getId(),
                'slug' => $serviceProvider->getSlug(),
            ],
            'country' => strtoupper($countryCode),
        ];

        $this->geoScanAPIClient->makeSoilMap($data);
    }

    /**
     * @deprecated version
     */
    public function sendSoilMapDataFromCommand(string $plotUuid, string $countryCode)
    {
        $labElementGroupRepo = $this->em->getRepository(LabElementGroup::class);
        // TODO: Pass serviceProviderId parameter to make it works.
        $soilMapData = $labElementGroupRepo->getSoilMapData(null, $plotUuid);

        if (!count($soilMapData['points'])) {
            throw new Exception('No points data for the plotUuid: ' . $plotUuid . ' in the cms database.', 503);
        }

        $data = [
            'country' => [
                'country' => strtoupper($countryCode),
            ],
            'soilData' => [
                'plot_uuid' => $plotUuid,
                'points' => $soilMapData['points'],
                'element_interpretations' => $soilMapData['element_interpretations'],
            ],
        ];

        $this->geoScanAPIClient->makeSoilMap($data);
    }

    public function elementsForSoilMap(array $filter): array
    {
        /**
         * @var LabAnalysisGroupElementRepository
         */
        $labAnalysisGroupElementRepo = $this->em->getRepository(LabAnalysisGroupElement::class);

        return $labAnalysisGroupElementRepo->elementsForSoilMap($filter);
    }

    public function getElementsUnit()
    {
        /**
         * @var LabAnalysisGroupElementRepository
         */
        $labAnalysisGroupElementRepo = $this->em->getRepository(LabAnalysisGroupElement::class);

        return $labAnalysisGroupElementRepo->getElementsUnit();
    }
}
