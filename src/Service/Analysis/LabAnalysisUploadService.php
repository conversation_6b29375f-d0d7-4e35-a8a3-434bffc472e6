<?php

namespace App\Service\Analysis;

use App\Entity\Analysis\LabAnalysisUploads;
use App\Entity\ServiceProvider;
use App\Form\Analysis\LabAnalysisUploadsType;
use App\Service\AbstractService;
use Behat\Transliterator\Transliterator;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Security\Core\Security;

class LabAnalysisUploadService extends AbstractService
{
    private $em;
    private $targetDirectory;
    private $security;
    private $templateUploadElements;

    public function __construct(
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        $targetDirectory,
        Security $security,
        $templateUploadElements
    ) {
        parent::__construct($formFactory);
        $this->em = $entityManager;
        $this->targetDirectory = $targetDirectory;
        $this->security = $security;
        $this->templateUploadElements = $templateUploadElements;
    }

    public function upload($uploadedFile)
    {
        $data = ['file' => $uploadedFile];
        $formType = LabAnalysisUploadsType::class;
        $labAnalysisUpload = new LabAnalysisUploads();

        $this->handleData($formType, $labAnalysisUpload, $data);

        if (!$uploadedFile) {
            throw new BadRequestHttpException('Please select file to upload.', null, 400);
        }

        $originalFilename = pathinfo($uploadedFile->getClientOriginalName(), PATHINFO_FILENAME);
        $safeFileName = Transliterator::transliterate($originalFilename);
        $fileName = $safeFileName . '-' . uniqid() . '.' . $uploadedFile->guessExtension();

        // Move the file to the directory where analysis are stored
        try {
            $uploadedFile->move(
                $this->getTargetDirectory(),
                $fileName
            );
        } catch (FileException $e) {
            throw new FileException($e->getMessage());
        }

        return $fileName;
    }

    public function create($fileName)
    {
        $this->em->getConnection()->beginTransaction();

        try {
            $now = new DateTime();
            $user = $this->security->getUser();

            $readerXlsx = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
            $spreadSheet = $readerXlsx->load($this->targetDirectory . $fileName);

            $isValidTemplate = $this->isValidTemplate($spreadSheet);
            $upload = new LabAnalysisUploads();
            $upload->setUserId($user->getId());

            $serviceProvider = $user->getServiceProvider();
            $serviceProvider = $this->em->getRepository(ServiceProvider::class)->find($serviceProvider->getId());

            $upload->setServiceProvider($serviceProvider);
            $upload->setDate($now);
            $upload->setFilePath($this->targetDirectory . $fileName);
            $upload->setState('Error');
            if ($isValidTemplate) {
                $records = $spreadSheet->getActiveSheet()->getHighestDataRow() - 1;
                $upload->setRecords($records);
                $upload->setState('Success');
            }
            $this->em->persist($upload);
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }
        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();

        if (!$isValidTemplate) {
            throw new BadRequestHttpException('Invalid file template', null, 400);
        }

        return ['upload' => $upload, 'spreadSheet' => $spreadSheet];
    }

    public function getAllSorted(array $sort = null)
    {
        $serviceProvider = $this->security->getUser()->getServiceProvider();

        $serviceProvider = $this->em->getRepository(ServiceProvider::class)->find($serviceProvider->getId());
        $uploadsRepository = $this->em->getRepository(LabAnalysisUploads::class);

        return $uploadsRepository->getByServiceProvider($serviceProvider, $sort);
    }

    public function setErrorState($upload)
    {
        $uploadsRepository = $this->em->getRepository(LabAnalysisUploads::class);
        $upload = $uploadsRepository->find($upload->getId());
        $upload->setState('Error');
        $this->em->persist($upload);
        $this->em->flush();
    }

    public function getTargetDirectory()
    {
        return $this->targetDirectory;
    }

    public function getTemplateUploadElements()
    {
        return $this->templateUploadElements;
    }

    public static function getColumnNames($spreadSheet)
    {
        $worksheet = $spreadSheet->getActiveSheet();
        $rowIndex = 0;
        $arrColumnNames = [];
        foreach ($worksheet->getRowIterator() as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false); // This loops through all cells,

            if (0 != $rowIndex) {
                continue;
            }

            $cellIndex = 0;
            foreach ($cellIterator as $cell) {
                if (0 == $cellIndex) {
                    $cellIndex = $cellIndex + 1;

                    continue;
                }

                if ($cell->getValue()) {
                    $arrColumnNames[] = trim($cell->getValue());
                }
                $cellIndex = $cellIndex + 1;
            }
            $rowIndex = $rowIndex + 1;
        }

        return $arrColumnNames;
    }

    private function isValidTemplate($spreadSheet)
    {
        $arrColumnNames = $this->getColumnNames($spreadSheet);

        $strColumnNames = implode(',', $arrColumnNames);
        $pattern = $this->getTemplateUploadElements();

        preg_match_all($pattern, $strColumnNames, $matches, PREG_SET_ORDER, 0);

        return (count($arrColumnNames) === count($matches)) ? true : false;
    }
}
