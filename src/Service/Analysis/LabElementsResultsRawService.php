<?php

namespace App\Service\Analysis;

use App\Entity\Analysis\LabAnalysisUploads;
use App\Entity\Analysis\LabElementsCalculations;
use App\Entity\Analysis\LabElementsResultsRaw;
use App\Service\AbstractService;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Form\FormFactoryInterface;

class LabElementsResultsRawService extends AbstractService
{
    private $em;

    public function __construct(
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager
    ) {
        parent::__construct($formFactory);
        $this->em = $entityManager;
    }

    public function create($arrResult)
    {
        $upload = $arrResult['upload'];
        $uploadsRepository = $this->em->getRepository(LabAnalysisUploads::class);
        $newUpload = $uploadsRepository->find($upload->getId());

        $this->em->getConnection()->beginTransaction();

        try {
            if ('Error' == $upload->getState()) {
                return;
            }
            $spreadSheet = $arrResult['spreadSheet'];
            $worksheet = $spreadSheet->getActiveSheet();
            $arrColumnNames = LabAnalysisUploadService::getColumnNames($spreadSheet);
            $arrColumnsFound = $this->columnsFound($arrColumnNames);
            $arrToInsert = $this->getDataToInsert($arrColumnsFound, $arrColumnNames, $worksheet);
            // e.g. the case with TMN = NO3 + NH4
            $arrToInsert = $this->checkSpecialCaseTMN($arrToInsert);

            foreach ($arrToInsert as $arrData) {
                foreach ($arrData as $value) {
                    if (!isset($value['value']) || !isset($value['element']) || !isset($value['lab_number'])) {
                        continue;
                    }

                    if (!is_numeric(trim($value['value'])) || !trim($value['element']) || !trim($value['lab_number'])) {
                        continue;
                    }
                    $resultsRaw = new LabElementsResultsRaw();
                    $resultsRaw->setLabAnalisysUploads($newUpload);
                    $resultsRaw->setLabNumber(trim($value['lab_number']));
                    $resultsRaw->setElement(trim($value['element']));
                    $resultsRaw->setValue(trim($value['value']));
                    $this->em->persist($resultsRaw);
                }
            }
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();
            $this->em->clear();
            $this->updateUploadState($upload, 'Error');

            throw $e;
        }
        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();

        return $upload;
    }

    private function updateUploadState(LabAnalysisUploads $upload, string $state)
    {
        $uploadsRepository = $this->em->getRepository(LabAnalysisUploads::class);
        $analysisUpload = $uploadsRepository->find($upload->getId());
        $analysisUpload->setState($state);
        $this->em->persist($analysisUpload);
        $this->em->flush();
        $this->em->clear();
    }

    private function checkSpecialCaseTMN($arrToInsert)
    {
        if (
            !(array_key_exists('NO3', $arrToInsert) && array_key_exists('NH4', $arrToInsert))
            || array_key_exists('TMN', $arrToInsert)
        ) {
            return $arrToInsert;
        }

        $arrToInsert['TMN'] = [];
        $arrNO3 = $arrToInsert['NO3'];

        foreach ($arrNO3 as $key => $NO3) {
            $valueTMN = null;
            $valueTMN = $NO3['value'] ?? $valueTMN;

            if (!isset($arrToInsert['NH4'][$key])) {
                $arrToInsert['TMN'][] = [
                    'lab_number' => $NO3['lab_number'],
                    'element' => 'TMN',
                    'value' => $valueTMN,
                ];

                continue;
            }

            $NH4 = $arrToInsert['NH4'][$key];
            $valueTMN = isset($NH4['value']) ? $valueTMN + $NH4['value'] : $valueTMN;

            $arrToInsert['TMN'][] = [
                'lab_number' => $NO3['lab_number'],
                'element' => 'TMN',
                'value' => $valueTMN,
            ];
        }

        return $arrToInsert;
    }

    private function columnsFound($arrColumnNames)
    {
        $strColumnNames = implode(',', $arrColumnNames);
        $calculationsRepository = $this->em->getRepository(LabElementsCalculations::class);
        $arrCalculations = $calculationsRepository->findAll();

        $arrColumnsFound = [];
        foreach ($arrCalculations as $calculation) {
            $pattern = '/' . $calculation->getTemplateColumn() . '/m';
            preg_match($pattern, $strColumnNames, $matches);

            if (0 === count($matches)) {
                continue;
            }

            $arrColumnsFound[$matches[0]] = ['pattern' => $pattern, 'element' => $calculation->getElement()];
        }

        return $arrColumnsFound;
    }

    private function getDataToInsert($arrColumnsFound, $arrColumnNames, $worksheet)
    {
        $arrToInsert = [];
        foreach ($arrColumnsFound as $templateColumn => $elementData) {
            $currentColumnKey = 0;
            foreach ($arrColumnNames as $key => $arrColumnName) {
                preg_match($elementData['pattern'], $arrColumnName, $matches);

                if (0 === count($matches)) {
                    continue;
                }

                $currentColumnKey = ++$key;
            }

            if (0 === $currentColumnKey) {
                continue;
            }

            $arrCurrent = [];
            $rowIndex = 0;
            foreach ($worksheet->getRowIterator() as $row) {
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(false); // This loops through all cells
                // even if a cell value is not set.
                // By default, only cells that have a value
                // set will be iterated.
                $cellIndex = 0;
                foreach ($cellIterator as $cell) {
                    if (null === $cell->getValue()) {
                        $cellIndex = $cellIndex + 1;

                        continue;
                    }

                    if (0 === $cellIndex && 0 !== $rowIndex) {
                        $arrCurrent[$rowIndex]['lab_number'] = $cell->getValue();
                        $arrCurrent[$rowIndex]['element'] = $elementData['element'];
                    }

                    if ($cellIndex === $currentColumnKey && 0 !== $rowIndex) {
                        $arrCurrent[$rowIndex]['value'] = $cell->getValue();
                    }

                    $cellIndex = $cellIndex + 1;
                }
                $rowIndex = $rowIndex + 1;
            }

            $isColumnEmpty = is_null(array_reduce($arrCurrent, function ($current, $item) {
                if (!array_key_exists('value', $item)) {
                    return $current;
                }

                return $current + $item['value'];
            }, null));

            if ($isColumnEmpty) {
                continue;
            }

            $arrToInsert[$templateColumn] = $arrCurrent;
        }

        return $arrToInsert;
    }
}
