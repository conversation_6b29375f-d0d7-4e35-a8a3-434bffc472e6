<?php

namespace App\Service\Analysis;

use App\Entity\Analysis\LabAnalysisGroupElement;
use App\Entity\Analysis\LabElementGroup;
use App\Entity\Analysis\LabElementsResults;
use App\Entity\Analysis\LabElementsResultsRaw;
use App\Entity\Analysis\MetaElementsGroups;
use App\Entity\Analysis\PackageGridPoints;
use App\Entity\AnalysisRecommendationConfig\LabElementInterpretationsConfig;
use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Contract\SubscriptionPackageField;
use App\Repository\Analysis\LabAnalysisGroupElementRepository;
use App\Repository\Analysis\LabElementsResultsRepository;
use App\Service\AbstractService;
use App\Service\Workflow\Analysis\LabElementsResultsWorkflowService;
use DateTime;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use PDO;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Security\Core\Security;

class LabElementsResultsService extends AbstractService
{
    private $em;
    private $labElementsResultsWorkflowService;
    private $security;

    public function __construct(
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        LabElementsResultsWorkflowService $labElementsResultsWorkflowService,
        Security $security
    ) {
        parent::__construct($formFactory);
        $this->em = $entityManager;
        $this->labElementsResultsWorkflowService = $labElementsResultsWorkflowService;
        $this->security = $security;
    }

    public function create($arrToInsert)
    {
        $this->em->getConnection()->beginTransaction();

        try {
            $now = new DateTime();

            $arrChunked = array_chunk($arrToInsert, 1000);

            foreach ($arrChunked as $chunk) {
                foreach ($chunk as $value) {
                    $labElementGroup = $this->em->getRepository(LabElementGroup::class)->find($value['lab_element_group_id']);
                    $packageGridPoints = $this->em->getRepository(PackageGridPoints::class)->find($value['package_grid_points_id']);

                    $labElementsResults = new LabElementsResults();
                    $labElementsResults->setElement($value['element']);
                    $labElementsResults->setLabElementGroup($labElementGroup);
                    $labElementsResults->setPackageGridPoints($packageGridPoints);
                    $labElementsResults->setStateUpdatedAt($now);

                    $this->em->persist($labElementsResults);
                }
                $this->em->flush();
                $this->em->clear(LabElementsResults::class);
            }

            $this->em->getConnection()->commit();
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }
    }

    public function remove($data)
    {
        $arrpPackageGridPointsIds = array_map(function ($item) {
            return $item['package_grid_points_id'];
        }, $data);
        $packageGridPointsIds = array_values(array_unique($arrpPackageGridPointsIds));

        $this->em->getConnection()->beginTransaction();

        try {
            $dql = 'SELECT er FROM App\Entity\Analysis\LabElementsResults er WHERE er.packageGridPoints IN(:packageGridPoints)';
            $query = $this->em->createQuery($dql)->setParameter('packageGridPoints', $packageGridPointsIds);
            $iterableResult = $query->iterate();

            foreach ($iterableResult as $row) {
                $this->em->remove($row[0]);
            }
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }

        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();
    }

    public function updateCalculatedValues($uploadId)
    {
        $this->em->getConnection()->beginTransaction();

        try {
            $labElementsResultsRawRepository = $this->em->getRepository(LabElementsResultsRaw::class);
            $qb = $labElementsResultsRawRepository->getCalculatedValuesQb();
            $qb->andWhere('LabElementsResultsRaw.labAnalisysUploads = :labAnalisysUploadsId')->setParameter('labAnalisysUploadsId', $uploadId);
            $arrResult = $qb->getQuery()->getResult();
            $this->setCalculatedValuesAndStates($arrResult, $labElementsResultsRawRepository);
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }

        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();
    }

    public function updateCalculatedValuesByLabNumbers($barcodesData)
    {
        $arrLabNumbers = array_map(function ($sample) {
            return $sample['value']['labnumber'];
        }, $barcodesData);

        if (!$arrLabNumbers) {
            return;
        }

        $this->em->getConnection()->beginTransaction();

        try {
            $labElementsResultsRawRepository = $this->em->getRepository(LabElementsResultsRaw::class);
            $qb = $labElementsResultsRawRepository->getCalculatedValuesQb();
            $qb->andWhere('PackageGridPoints.labNumber IN (:labNumbers)')->setParameter('labNumbers', $arrLabNumbers);
            $arrResult = $qb->getQuery()->getResult();
            $this->setCalculatedValuesAndStates($arrResult, $labElementsResultsRawRepository);
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }

        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();
    }

    public function getElementsResults($filters, $withPagination, $page, $limit, $elementsGroups)
    {
        /**
         * @var LabElementsResultsRepository
         */
        $labElementsResultsRepository = $this->em->getRepository(LabElementsResults::class);
        $elementsResults = $labElementsResultsRepository->getElementsResults($filters, $withPagination, $page, $limit);

        $elementsResult = reset($elementsResults);

        if (is_bool($elementsResult)) {
            return ['items' => [], 'total_items' => 0];
        }

        $totalItems = $elementsResult['total_count'];

        // Map results
        $elementsResults = array_map(function ($result) use ($elementsGroups) {
            $row = $result['row'];
            $arrResult = json_decode($row, true);
            $elements = $arrResult['elements'];
            // Add null elements
            $arrResult['elements'] = array_map(function ($elementGroup) use ($elements) {
                $key = array_search($elementGroup['name'], array_column($elements, 'name'));

                if ('integer' == gettype($key)) {
                    return $elements[$key];
                }

                $elementGroup += array_fill_keys(['file', 'state', 'value', 'uploaded_by', 'uploaded_date', 'id', 'color'], null);
                $elementGroup['previous_values'] = [];

                return $elementGroup;
            }, $elementsGroups);

            return $arrResult;
        }, $elementsResults);

        return ['items' => $elementsResults, 'total_items' => $totalItems];
    }

    public function getElementsResultsForCSV($filters)
    {
        if (!isset($filters['visible_columns'])) {
            $filters['visible_columns'] = $this->em->getRepository(MetaElementsGroups::class)->getAllElements();
        }

        $labElementsResultsRepository = $this->em->getRepository(LabElementsResults::class);

        return $labElementsResultsRepository->getElementsResultsForCSV($filters);
    }

    public function getElementsResultsForApprove(array $filter)
    {
        /**
         * @var LabElementsResultsRepository
         */
        $labElementsResultsRepository = $this->em->getRepository(LabElementsResults::class);

        return $labElementsResultsRepository->getElementsResultsForApprove($filter);
    }

    public function getResultsByCustomerIdentification(array $customerIdentifications, $state)
    {
        $labElementsGroupRepo = $this->em->getRepository(LabElementGroup::class);

        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();
        $qb = $labElementsGroupRepo->getResultsByCustomerIdentificationQbGroup($customerIdentifications, $serviceProviderId, $state);

        return $qb->getQuery()->getSingleScalarResult();
    }

    public function samplesContent(string $plotUuId, string $orderUuid, array $samplingTypeIds)
    {
        $subscriptionPackageRepository = $this->em->getRepository(SubscriptionPackage::class);
        $subscriptionPackageFieldRepository = $this->em->getRepository(SubscriptionPackageField::class);

        $package = $subscriptionPackageRepository->packageForSamplesContent($plotUuId, $orderUuid);

        if (!$package) {
            return [];
        }
        $subscriptionPackageField = $subscriptionPackageFieldRepository->findOneBy(['id' => $package['subscriptionPackageFieldId']]);

        $elementsResultsClasses = $this->getElementsResultsWithClasses($subscriptionPackageField, $samplingTypeIds);
        $elementsInterpretations = $this->getElementsResultsInterpretations($subscriptionPackageField, $samplingTypeIds);
        $elementsInterpretationsClasses = $this->getElementInterpretationClasses();

        return [
            'results' => $elementsResultsClasses,
            'comments' => $elementsInterpretations,
            'legend' => $elementsInterpretationsClasses,
        ];
    }

    /**
     * @throws Exception
     */
    public function samplesContentByElement(string $plotUuid, string $orderUuid, string $element, array $samplingTypeIds = []): array
    {
        $subscriptionPackageFieldRepository = $this->em->getRepository(SubscriptionPackageField::class);
        $subscriptionPackageField = $subscriptionPackageFieldRepository->findOneBy(['plotUuid' => $plotUuid, 'orderUuid' => $orderUuid]);

        /**
         * @var LabAnalysisGroupElementRepository
         */
        $labAnalysisGroupElementRepository = $this->em->getRepository(LabAnalysisGroupElement::class);
        $elementExists = $labAnalysisGroupElementRepository->elementExists($element);

        if (!$subscriptionPackageField) {
            throw new Exception('Cannot find subscription package field!', 404);
        }

        if (!$elementExists) {
            throw new Exception("Invalid element '{$element}'!", 400);
        }

        return $this->geResultsByFieldAndElement($subscriptionPackageField, $element, $samplingTypeIds);
    }

    public function geResultsByFieldAndElement(SubscriptionPackageField $field, string $element, array $samplingTypeIds = []): array
    {
        $conn = $this->em->getConnection();
        $qb = new QueryBuilder($conn);

        $params = [
            'plotUuid' => $field->getPlotUuid(),
            'packageId' => $field->getSubscriptionPackage()->getId(),
            'packageType' => 'subscription',
            'serviceProvicerId' => $this->security->getUser()->getServiceProvider()->getId(),
            'element' => $element,
            'samplingTypeIds' => $samplingTypeIds,
        ];

        $paramTypes = [
            'plotUuid' => ParameterType::STRING,
            'packageId' => ParameterType::INTEGER,
            'packageType' => ParameterType::STRING,
            'serviceProvicerId' => ParameterType::INTEGER,
            'element' => ParameterType::STRING,
            'samplingTypeIds' => Connection::PARAM_INT_ARRAY,
        ];

        $elementResultsQb = $qb->select([
            'cell_id AS cell',
            'lab_id AS lab_number',
            'value',
            'st.type as soil_layer_cm',
            'element_unit AS unit',
            'color',
        ])
            ->from('get_lab_elements_results_classes(array[:plotUuid], :packageId, :packageType, :serviceProvicerId)', 'lerc')
            ->join('lerc', 'sampling_type', 'st', 'lerc.sampling_type_id = st.id')
            ->where('"element" = :element')
            ->andWhere($qb->expr()->or(
                'lerc.sampling_type_id = ANY(ARRAY[:samplingTypeIds]::int[])',
                'COALESCE(:samplingTypeIds) ISNULL'
            ))
            ->orderBy('cell_id', 'ASC')
            ->addOrderBy('st.id', 'ASC')
            ->setParameters($params, $paramTypes);

        $elementResults = $elementResultsQb->execute()->fetchAllAssociative();

        return array_map(function ($elementResult) {
            $elementResult['value'] = floatval($elementResult['value']);

            return $elementResult;
        }, $elementResults);
    }

    public function getElementsResultsWithClasses(SubscriptionPackageField $subscriptionPackageField, array $samplingTypeIds = [])
    {
        $conn = $this->em->getConnection();

        $sql = "
            WITH lab_elements_results_classes AS (
                SELECT
                    lerc.lab_id,
                    (:plotUuid) AS plot_uuid,
                    lerc.cell_id,
                    st.\"type\" AS soil_layer_cm,
                    lerc.\"date\",
                    lerc.\"element\", 
                    JSONB_BUILD_OBJECT(
                            'lab_element_result_id', lerc.lab_element_result_id,
                            'value', ROUND(lerc.value::NUMERIC, 3),
                            'class', JSON_BUILD_OBJECT(
                                'id', lerc.class_id,
                                'slug', lerc.slug,
                                'description', lerc.description,
                                'color', lerc.color
                            )
                    ) AS value
                FROM 
                    get_lab_elements_results_classes(ARRAY[:plotUuid], :packageId, :packageType, :serviceProviderId) AS lerc
                LEFT JOIN sampling_type st
                    ON st.id = lerc.sampling_type_id
                WHERE
                    lerc.sampling_type_id = ANY(ARRAY[:samplingTypeIds]::int[])
                    OR COALESCE(:samplingTypeIds) ISNULL
            ),
            \"element_groups\" AS (
                SELECT
                    JSONB_BUILD_OBJECT(
                    'name', lerc.\"element\",
                    'label', trim(concat(lerc.\"element\", ' ', lage.unit)),
                    'meta_group', mg.\"name\") as \"json\"
                FROM
                    lab_elements_results_classes lerc
                JOIN lab_analysis_group_element lage 
                    ON lage.\"element\" = lerc.\"element\"
                JOIN lab_analysis_group_element_visual_order evo 
                    ON evo.lab_analysis_group_element_id = lage.id
                    AND evo.service_provider_id = :serviceProviderId
                LEFT JOIN meta_elements_groups meg
                    ON meg.\"element\" = lerc.\"element\"
                LEFT JOIN meta_groups mg
                    ON mg.id = meg.group_id
                GROUP BY 
                    lerc.\"element\", 
                    mg.\"name\", 
                    lage.unit,
                    evo.visual_order
                ORDER BY evo.visual_order
            ),
            \"group_element_groups\" as (
                select JSONB_AGG(\"element_groups\".\"json\") as \"element_groups\"
                from \"element_groups\"
            ),
            \"rows\" AS (
                SELECT
                    JSON_BUILD_OBJECT(
                        'lab_id', lab_id,
                        'plot_uuid', plot_uuid,
                        'cell_id', cell_id,
                        'soil_layer_cm', soil_layer_cm,
                        'date', \"date\",
                        'elements', JSONB_OBJECT_AGG(
                            \"element\", value
                        )	
                    ) AS \"json\"
                FROM
                    lab_elements_results_classes
                GROUP BY
                    plot_uuid,
                    lab_id,
                    cell_id,
                    \"date\",
                    soil_layer_cm
                ORDER BY
                    plot_uuid,
                    cell_id,
                    soil_layer_cm
            )
            SELECT
                \"group_element_groups\".\"element_groups\" as \"element_groups\",
                JSONB_AGG(\"rows\".\"json\") AS \"rows\"
            FROM 
                \"group_element_groups\", \"rows\"
                group by \"group_element_groups\".\"element_groups\"
        ";

        $params = [
            'serviceProviderId' => $this->security->getUser()->getServiceProvider()->getId(),
            'plotUuid' => $subscriptionPackageField->getPlotUuid(),
            'samplingTypeIds' => $samplingTypeIds,
            'packageId' => $subscriptionPackageField->getSubscriptionPackage()->getId(),
            'packageType' => 'subscription',
        ];

        $paramTypes = [
            'serviceProviderId' => ParameterType::INTEGER,
            'plotUuid' => ParameterType::STRING,
            'samplingTypeIds' => Connection::PARAM_INT_ARRAY,
            'packageId' => ParameterType::INTEGER,
            'packageType' => ParameterType::STRING,
        ];

        $stmt = $conn->executeQuery($sql, $params, $paramTypes);
        $result = $stmt->fetchAssociative();

        return [
            'header' => isset($result['element_groups']) ? json_decode($result['element_groups'], true) : [],
            'rows' => isset($result['rows']) ? json_decode($result['rows'], true) : [],
        ];
    }

    /**
     * Get element classes and comments depending on the elements results of the specified field.
     *
     * @param SubscriptionPackageField $subscriptionPackageFieldId
     */
    public function getElementsResultsInterpretations(SubscriptionPackageField $subscriptionPackageField, array $samplingTypeIds = [])
    {
        $conn = $this->em->getConnection();

        $sql = '
            SELECT
                lerc.plot_uuid,
                lerc.package_id,
                lerc.package_type,
                st.type as soil_layer_cm,
                lerc.element_id,
                lerc."element",
                array_to_json(lerc.class_ids) AS class_ids,
                comment_text
            FROM
                get_lab_elements_results_aggregated_classes(array[:plotUuid], :packageId, :packageType, :serviceProviderId) AS lerc
            LEFT JOIN sampling_type st
                ON st.id = lerc.sampling_type_id
            LEFT JOIN lab_aggregated_element_interpetations_config laeic
                ON laeic.service_provider_id = :serviceProviderId
                AND laeic.element_id = lerc.element_id
                AND COALESCE(laeic.class_ids, ARRAY[-1]) = COALESCE(lerc.class_ids, ARRAY[-1])
            WHERE
                comment_text NOTNULL
                AND (
                    lerc.sampling_type_id = ANY(ARRAY[:samplingTypeIds]::int[])
                    OR coalesce(:samplingTypeIds) ISNULL -- empty array is binded as null
                )
            ORDER BY
                lerc.plot_uuid,
                lerc.package_id,
                lerc.element_id,
                st.id
        ';

        $params = [
            'serviceProviderId' => $this->security->getUser()->getServiceProvider()->getId(),
            'plotUuid' => $subscriptionPackageField->getPlotUuid(),
            'samplingTypeIds' => $samplingTypeIds,
            'packageId' => $subscriptionPackageField->getSubscriptionPackage()->getId(),
            'packageType' => 'subscription',
        ];

        $paramTypes = [
            'serviceProviderId' => ParameterType::INTEGER,
            'plotUuid' => ParameterType::STRING,
            'samplingTypeIds' => Connection::PARAM_INT_ARRAY,
            'packageId' => ParameterType::INTEGER,
            'packageType' => ParameterType::STRING,
        ];

        $stmt = $conn->executeQuery($sql, $params, $paramTypes);
        $rows = $stmt->fetchAllAssociative();

        return array_map(function ($row) {
            if (isset($row['class_ids'])) {
                $row['class_ids'] = json_decode($row['class_ids']);
            }

            return $row;
        }, $rows);
    }

    public function getAnalysesReport(array $filter, ?int $limit, ?int $page)
    {
        $labElementsResults = $this->em->getRepository(LabElementsResults::class);

        $qb = $labElementsResults->getAnalysesReportQB($filter);
        $qb->select('
                    pgp.plot_uuid,
                    spf.order_uuid,
                    sp.id as package_id, 
                    p.slug as package_name,
                    concat(to_char(sp.start_date, \'YYYY\') , \'/\', to_char(sp.end_date, \'YYYY\')) as period,
                    max(to_char(pgp.state_updated_at, \'DD.MM.YYYY\')) as date, 
                    pgp.sample_id,
                    st.type as soil_layer_cm,
                    pgp.barcode,
                    pgp.lab_number as lab_number,
                    json_object_agg(ler.element, ROUND(CAST(ler.value as decimal),2)) as elements
             ')
            ->groupBy('sp.id, p.slug, pgp.barcode, pgp.sample_id, pgp.barcode, pgp.plot_uuid, pgp.lab_number, spf.order_uuid, st.id');

        $qb->addOrderBy('pgp.plot_uuid')
            ->addOrderBy('sp.id')
            ->addOrderBy('pgp.sample_id')
            ->addOrderBy('st.id');

        if ($limit && $page) {
            $report = $this->paginateDbalQueryBuilder($qb, $page, $limit);
        } else {
            $report['items'] = $qb->execute()->fetchAll();
        }

        $report['items'] = array_map(function ($row) {
            $row['elements'] = json_decode($row['elements'], true);

            return $row;
        }, $report['items']);

        return $report;
    }

    public function getAnalysesReportHeader(array $filter)
    {
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();
        $labElementsResults = $this->em->getRepository(LabElementsResults::class);

        $qb = $labElementsResults->getAnalysesReportQB($filter);
        $qb->join('leg', 'lab_analysis_group_element', 'lage', 'lage.element = ler.element')
            ->join('lage', 'lab_analysis_group_element_visual_order', 'evo', 'evo.lab_analysis_group_element_id = lage.id and evo.service_provider_id = :service_provider_id')
            ->setParameter('service_provider_id', $serviceProviderId, ParameterType::INTEGER)
            ->groupBy('ler.element, evo.visual_order')
            ->addOrderBy('evo.visual_order')
            ->select('ler.element');

        return $qb->execute()->fetchAll(PDO::FETCH_COLUMN);
    }

    /**
     * @param array $filter
     */
    public function getElementInterpretationClasses()
    {
        $labElementInterpretationsConfigRepository = $this->em->getRepository(LabElementInterpretationsConfig::class);
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();

        return $labElementInterpretationsConfigRepository->getElementInterpretationClasses($serviceProviderId);
    }

    private function setCalculatedValuesAndStates($arrResult, $labElementsResultsRawRepository)
    {
        foreach ($arrResult as $result) {
            $labElementsResults = $result['labElementsResults'];
            $labElementsResults->setValue($result['calc_value']);
            $labElementsResultsRaw = $labElementsResultsRawRepository->find($result['LabElementsResultsRawId']);
            $labElementsResults->setLabElementsResultsRaw($labElementsResultsRaw);
            // Set state to 'Analysed' with workflow
            $this->labElementsResultsWorkflowService->applyTransition(LabElementsResults::WORKFLOW_NAME, LabElementsResults::TRANSITION_ANALYSED, $labElementsResults);
            // Set state to 'For Approve' with workflow
            $labElementGroup = $labElementsResults->getLabElementGroup();
            $this->labElementsResultsWorkflowService->applyTransition(LabElementGroup::WORKFLOW_NAME, LabElementGroup::TRANSITION_FOR_APPROVE, $labElementGroup);
        }
    }
}
