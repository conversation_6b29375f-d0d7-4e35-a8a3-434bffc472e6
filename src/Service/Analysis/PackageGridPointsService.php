<?php

namespace App\Service\Analysis;

use App\Entity\Analysis\PackageGridPoints;
use App\Entity\Contract\SubscriptionPackage;
use App\EntityGeoscan\ServiceProviderGeoScan;
use App\Repository\Analysis\PackageGridPointsRepository;
use App\Service\AbstractService;
use App\Service\AgroLab\AgroLabService;
use App\Service\Workflow\Analysis\LabElementGroupWorkflowService;
use App\Service\Workflow\Analysis\LabElementsResultsWorkflowService;
use App\Service\Workflow\Analysis\PackageGridPointsWorkflowService;
use App\Service\Workflow\SubscriptionFieldService;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Exception;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Security\Core\Security;

class PackageGridPointsService extends AbstractService
{
    private $em;
    private $gsEm;
    private $agroLabService;
    private $packageGridPointsWorkflowService;
    private $labElementsResultsWorkflowService;
    private $labElementGroupWorkflowService;
    private $workflowSubscriptionFieldService;
    private $managerRegistry;
    private $security;

    public function __construct(
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        AgroLabService $agroLabService,
        PackageGridPointsWorkflowService $packageGridPointsWorkflowService,
        LabElementsResultsWorkflowService $labElementsResultsWorkflowService,
        LabElementGroupWorkflowService $labElementGroupWorkflowService,
        SubscriptionFieldService $workflowSubscriptionFieldService,
        ManagerRegistry $managerRegistry,
        Security $security
    ) {
        parent::__construct($formFactory);
        $this->em = $entityManager;
        $this->gsEm = $managerRegistry->getManager('geoscan');
        $this->agroLabService = $agroLabService;
        $this->packageGridPointsWorkflowService = $packageGridPointsWorkflowService;
        $this->labElementsResultsWorkflowService = $labElementsResultsWorkflowService;
        $this->labElementGroupWorkflowService = $labElementGroupWorkflowService;
        $this->workflowSubscriptionFieldService = $workflowSubscriptionFieldService;
        $this->managerRegistry = $managerRegistry;
        $this->security = $security;
    }

    public function create($data)
    {
        $packages = array_values(array_unique(array_column($data, 'package_id')));
        $subscriptionPackagesRepo = $this->em->getRepository(SubscriptionPackage::class);
        $subscriptionPackages = $subscriptionPackagesRepo->findBy(['id' => $packages]);

        $this->em->getConnection()->beginTransaction();

        try {
            $now = new DateTime();

            foreach ($subscriptionPackages as $subscriptionPackage) {
                $packageGridPoints = array_values(array_filter($data, function ($packageGridPoint) use ($subscriptionPackage) {
                    return $packageGridPoint['package_id'] === $subscriptionPackage->getId();
                }));

                $packageSamplingTypes = $subscriptionPackage->getPackage()->getSamplingTypes();
                foreach ($packageSamplingTypes as $samplingType) {
                    foreach ($packageGridPoints as $value) {
                        $packageGridPoint = new PackageGridPoints();
                        $packageGridPoint->setPointUuid($value['point_uuid']);
                        $packageGridPoint->setSampleId($value['sample_id']);
                        $packageGridPoint->setPlotUuid($value['plot_uuid']);
                        $packageGridPoint->setPackageType($value['package_type']);
                        $packageGridPoint->setGridType($value['grid_type']);
                        $packageGridPoint->setStateUpdatedAt($now);
                        $packageGridPoint->setPackage($subscriptionPackage);
                        $packageGridPoint->setSamplingType($samplingType);
                        $this->em->persist($packageGridPoint);
                    }
                }
            }
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }
        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();

        return;
    }

    public function remove($data)
    {
        $arrPackageIds = array_map(function ($item) {
            return $item['package_id'];
        }, $data);
        $packageIds = array_values(array_unique($arrPackageIds));

        $arrPlotUuids = array_map(function ($item) {
            return $item['plot_uuid'];
        }, $data);
        $plotUuids = array_values(array_unique($arrPlotUuids));

        $arrPointUuids = array_map(function ($item) {
            return $item['point_uuid'];
        }, $data);
        $pointUuids = array_values(array_unique($arrPointUuids));

        $this->em->getConnection()->beginTransaction();

        try {
            $dql = 'SELECT p FROM App\Entity\Analysis\PackageGridPoints p WHERE p.packageId IN(:packageIds) AND p.plotUuid IN(:plotUuids) AND p.pointUuid IN(:pointUuids)';
            $query = $this->em->createQuery($dql)
                ->setParameter('packageIds', $packageIds)
                ->setParameter('plotUuids', $plotUuids)
                ->setParameter('pointUuids', $pointUuids);
            $iterableResult = $query->iterate();

            foreach ($iterableResult as $row) {
                $this->em->remove($row[0]);
            }
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }

        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();
    }

    public function saveBarcodes($data)
    {
        $arrPointUuids = array_column($data, 'point_uuid');

        if (!$arrPointUuids) {
            return;
        }

        $this->em->getConnection()->beginTransaction();

        try {
            $dql = 'SELECT p FROM App\Entity\Analysis\PackageGridPoints p WHERE p.pointUuid IN(:pointUuids)';
            $query = $this->em->createQuery($dql)->setParameter('pointUuids', $arrPointUuids);
            $iterableResult = $query->iterate();

            $arrRelationData = [];
            foreach ($iterableResult as $row) {
                $packageGridPoint = $row[0];

                $foundData = array_filter($data, function ($item) use ($packageGridPoint) {
                    return $item['point_uuid'] == $packageGridPoint->getPointUuid()
                        && $item['sampling_type_id'] == $packageGridPoint->getSamplingType()->getId();
                });
                $foundData = reset($foundData);

                if (!isset($foundData['barcode'])) {
                    continue;
                }

                $packageGridPoint->setBarcode($foundData['barcode']);
                // Set state to 'Sampled' with workflow
                $this->packageGridPointsWorkflowService->applyTransition(PackageGridPoints::WORKFLOW_NAME, PackageGridPoints::TRANSITION_SAMPLED, $packageGridPoint);
            }
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }

        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();

        return;
    }

    public function syncLabNumbersFromCouch()
    {
        $serviceProviderRepo = $this->gsEm->getRepository(ServiceProviderGeoScan::class);
        $serviceProviders = $serviceProviderRepo->findAll();

        $arrDataAll = [];

        foreach ($serviceProviders as $serviceProvider) {
            $config = $serviceProvider->getCouchDBConfig();

            $subscriptionPackageRepo = $this->em->getRepository(SubscriptionPackage::class);
            $packages = $subscriptionPackageRepo->findBy(['serviceProvider' => $serviceProvider]);

            foreach ($packages as $package) {
                $barcodes = $package->getPackageGridPoints()->filter(function (PackageGridPoints $packageGridPoints) {
                    return PackageGridPoints::STATE_SAMPLED === $packageGridPoints->getState();
                })->map(function (PackageGridPoints $packageGridPoints) {
                    return $packageGridPoints->getBarcode();
                })->toArray();

                if (!$barcodes) {
                    continue;
                }

                $arrData = $this->agroLabService->getCouchDbLabNumbers($barcodes, $config);

                foreach ($arrData as $data) {
                    array_push($arrDataAll, $data);
                }
            }
        }

        $this->saveLabNumbers($arrDataAll);
    }

    public function saveLabNumbers($barcodesData)
    {
        if (!$barcodesData) {
            return;
        }

        $arrBarcodesChunks = array_chunk($barcodesData, 50);
        foreach ($arrBarcodesChunks as $barcode) {
            $arrBarcodes = array_column($barcode, null, 'key');

            $dql = 'SELECT p FROM App\Entity\Analysis\PackageGridPoints p WHERE p.barcode IN(:barcodes)';
            $query = $this->em->createQuery($dql)->setParameter('barcodes', array_keys($arrBarcodes));
            $packageGridPoints = $query->iterate();
            foreach ($packageGridPoints as $row) {
                $packageGridPoint = $row[0];
                $labNumber = $arrBarcodes[$packageGridPoint->getBarcode()]['value']['labnumber'] ?? null;

                if (is_null($labNumber)) {
                    continue;
                }

                $packageGridPoint->setLabNumber($labNumber);
                // Set state to 'ReceivedInLab' with workflow
                $this->packageGridPointsWorkflowService->applyTransition(PackageGridPoints::WORKFLOW_NAME, PackageGridPoints::TRANSITION_RECEIVED_IN_LAB, $packageGridPoint);
            }

            $this->em->flush();
            $this->em->clear();
        }
    }

    public function getElementGroupData($data)
    {
        $params = $this->getParamsFrom($data);

        $packageGridPointsRepository = $this->em->getRepository(PackageGridPoints::class);

        return $packageGridPointsRepository->getElementGroupData($params['packageId'], $params['packageType'], $params['plotUuids']);
    }

    public function getElementsResultsData($data)
    {
        $params = $this->getParamsFrom($data);

        /**
         * @var PackageGridPointsRepository
         */
        $packageGridPointsRepository = $this->em->getRepository(PackageGridPoints::class);

        return $packageGridPointsRepository->getElementsResultsData($params['packageId'], $params['packageType'], $params['plotUuids']);
    }

    public function getBarcodes(string $customerIdentification, string $barcodeSearch)
    {
        $packageGridPointsRepository = $this->em->getRepository(PackageGridPoints::class);

        return $packageGridPointsRepository->getBarcodes($customerIdentification, $barcodeSearch);
    }

    public function getLabNumbers(string $customerIdentification, string $labNumberSearch)
    {
        $packageGridPointsRepository = $this->em->getRepository(PackageGridPoints::class);

        return $packageGridPointsRepository->getLabNumbers($customerIdentification, $labNumberSearch);
    }

    public function updateColumnStateUpdateAt(array $barcodesData): bool
    {
        if (!$barcodesData) {
            return false;
        }

        $arrBarcodesChunks = array_chunk($barcodesData, 50);
        foreach ($arrBarcodesChunks as $barcode) {
            $arrBarcodes = array_column($barcode, 'value', 'key');
            $repo = $this->em->getRepository(PackageGridPoints::class);
            $qb = $repo->getPackageGridPintsByBarcodes(array_keys($arrBarcodes));
            $packageGridPoints = $qb->getQuery()->getResult();

            foreach ($packageGridPoints as $row) {
                $packageGridPoint = $row;
                $date = $arrBarcodes[$packageGridPoint->getBarcode()]['date'] ?? null;

                if (is_null($date)) {
                    continue;
                }

                $dateTime = new DateTime($date);
                if ($dateTime == $packageGridPoint->getStateUpdatedAt()) {
                    continue;
                }

                $packageGridPoint->setStateUpdatedAt($dateTime);
            }
            $this->em->flush();
            $this->em->clear();
        }

        return true;
    }

    public function getCellsForSamplingWithElements(array $filter)
    {
        $repo = $this->em->getRepository(PackageGridPoints::class);

        return $repo->cellsForSamplingWithElements($filter);
    }

    private function getParamsFrom($data)
    {
        $firstData = reset($data);
        $packageType = $firstData['package_type'] ?? null;

        $arrPackageId = array_values(array_unique(array_column($data, 'package_id')));
        $packageId = $arrPackageId[0];

        $arrPlotUuids = array_column($data, 'plot_uuid');
        $plotUuids = array_values(array_unique($arrPlotUuids));

        return ['packageType' => $packageType, 'packageId' => $packageId, 'plotUuids' => $plotUuids];
    }
}
