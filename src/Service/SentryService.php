<?php

namespace App\Service;

use Symfony\Component\Security\Core\User\UserInterface;

class SentryService
{
    private static $transactionId;

    public function __construct() {}

    /**
     * @return null|string
     */
    public static function getTransactionId()
    {
        return self::$transactionId;
    }

    public function configureScope(UserInterface $user)
    {
        if (class_exists('Sentry\SentryBundle\SentryBundle')) {
            self::$transactionId = substr(bin2hex(random_bytes(20)), 2, 9);
            \Sentry\configureScope(function (\Sentry\State\Scope $scope) use ($user): void {
                $scope->setUser([
                    'id' => $user->getId(),
                    'name' => $user->getUserIdentifier(),
                ]);
                $scope->setTag('transaction_id', self::getTransactionId());
            });
        }
    }
}
