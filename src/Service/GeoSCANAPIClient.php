<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 09/05/2019
 * Time: 12:14.
 */

namespace App\Service;

use App\Entity\Contract;
use App\Entity\Package;
use App\Entity\ServiceProvider;
use App\EntityGeoscan\GlobalUser;
use App\EntityGeoscan\ServiceProviderGeoScan;
use App\Model\Farm\Farm;
use App\Model\GeoSCAN\Ability;
use App\Model\GeoSCAN\History;
use App\Model\GeoSCAN\Organization;
use App\Model\GeoSCAN\Organization\Address;
use App\Model\GeoSCAN\Organization\ContactPerson;
use App\Model\GeoSCAN\Role;
use App\Model\GeoSCAN\User;
use App\Security\OAuthClient\Provider\KeycloakMachineToMachine;
use App\Security\User\GeoSCANUser;
use App\Serializer\OrganizationNormalizer;
use App\Serializer\UserNormalizer;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use stdClass;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\NameConverter\CamelCaseToSnakeCaseNameConverter;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;

class GeoSCANAPIClient
{
    public const APPLICATION_JSON = ['accept' => 'application/json'];
    public const TEXT_HTML = ['accept' => 'text/html'];

    private $guzzleClient;
    private $parameters;
    private $em;
    private $security;
    private $normalizer;
    private $organizationNormalizer;
    private $userNormalizer;
    private $serializer;
    private $keycloakMachineToMachine;

    private ManagerRegistry $managerRegistry;

    public function __construct(
        ParameterBagInterface $parameters,
        EntityManagerInterface $entityManager,
        ManagerRegistry $managerRegistry,
        Security $security,
        KeycloakMachineToMachine $keycloakMachineToMachine
    ) {
        $this->guzzleClient = new Client([
            'base_uri' => $parameters->get('geoscan_api_base_url'),
        ]);
        $this->parameters = $parameters;
        $this->normalizer = new ObjectNormalizer(null, new CamelCaseToSnakeCaseNameConverter());
        $this->organizationNormalizer = new OrganizationNormalizer($this->normalizer);
        $this->userNormalizer = new UserNormalizer($this->normalizer);
        $this->em = $entityManager;
        $this->managerRegistry = $managerRegistry;
        $this->security = $security;
        $encoders = [new JsonEncoder()];
        $this->serializer = new Serializer([$this->normalizer], $encoders);
        $this->keycloakMachineToMachine = $keycloakMachineToMachine;
    }

    /**
     * @deprecated
     *
     * @return GeoSCANUser
     */
    public function getUser(string $token = null)
    {
        try {
            $header = array_merge(self::APPLICATION_JSON, ['token' => $token]);
            $response = $this->makeRequest(Request::METHOD_GET, '/apigs/users/load-user', $header);

            $user = json_decode($response);

            $serviceProvider = $this->em->getRepository(ServiceProvider::class)->findOneBy(['slug' => $user->serviceProvider]);

            return (new GeoSCANUser($user->globalUserId, $user->username, [], $token, $serviceProvider));
        } catch (Exception $ex) {
            throw new UserNotFoundException('Token error');
        }
    }

    /**
     * Get the last chosen organization id.
     *
     * @return int
     */
    public function getLastChosenOrganizationId()
    {
        try {
            $response = $this->makeRequest(Request::METHOD_GET, '/apigs/users/load-user');
            $user = json_decode($response);

            return $user->last_chosen_organization_id ?? null;
        } catch (Exception $ex) {
            throw new UserNotFoundException('Token error');
        }
    }

    /**
     * @param ?string $identityNumber
     * @param ?array $organizationManager
     * @param ?array $serviceManager
     * @param ?int $page
     * @param ?int $limit
     *
     * @throws \Symfony\Component\Serializer\Exception\ExceptionInterface
     *
     * @return null|stdClass
     */
    public function getOrganizations(?int $page, ?int $limit, ?string $identityNumber, ?array $organizationManager = null, ?array $serviceManager = null)
    {
        $organizationManagerId = $organizationManager['oldId'] ?? null;
        $serviceManagerId = $serviceManager['oldId'] ?? null;

        $data = [
            'page' => $page,
            'limit' => $limit,
            'identityNumber' => $identityNumber,
            'organizationManagerId' => $organizationManagerId,
            'serviceManagerId' => $serviceManagerId,
        ];

        $response = $this->makeRequest(Request::METHOD_GET, '/cms/organizations/list', self::APPLICATION_JSON, $data);
        $items = json_decode($response);
        $organizations = [];
        foreach ($items->data as $item) {
            $organizations[] = $this->organizationNormalizer->denormalize($item, Organization::class);
        }

        $return = new stdClass();
        $return->data = $organizations;
        $return->total = $items->total;
        $return->last_page = $items->last_page;

        return $return;
    }

    public function searchOrganizations(string $string)
    {
        $response = $this->makeRequest(Request::METHOD_GET, '/cms/organizations/search', self::APPLICATION_JSON, ['q' => $string]);
        $items = json_decode($response);

        $organizations = [];
        foreach ($items as $item) {
            $organizations[] = $this->normalizer->denormalize($item, Organization::class);
        }

        return $organizations;
    }

    public function saveOrganization(Organization $organization, $isNew = true)
    {
        $data = $this->serializer->serialize($organization, 'json', ['groups' => 'api', 'skip_null_values' => true]);
        $url = $organization->getId() ? '/apigs/organizations/' . $organization->getId() : '/apigs/organizations';
        $method = $isNew ? Request::METHOD_POST : Request::METHOD_PUT;

        $response = $this->makeRequest($method, $url, self::APPLICATION_JSON, ['data' => $data]);

        return $this->normalizer->denormalize(json_decode($response), Organization::class);
    }

    public function getOrganizationsName(array $identityNumbers)
    {
        $response = $this->makeRequest(Request::METHOD_GET, '/apigs/organizations/names', self::APPLICATION_JSON, ['identity_numbers' => $identityNumbers]);

        return json_decode($response, true);
    }

    public function getOrganizationById(int $organizationId)
    {
        $response = $this->makeRequest(Request::METHOD_GET, '/cms/organizations/' . $organizationId, self::APPLICATION_JSON, []);
        $organizations = json_decode($response, true);

        return new ArrayCollection($organizations);
    }

    public function getUsers(array $params)
    {
        $data = $params ?? [
            'limit' => '5',
            'page' => '1',
            'sort' => 'username',
        ];

        $response = $this->makeRequest(Request::METHOD_GET, 'apigs/admin/users', self::APPLICATION_JSON, $data);
        $data = json_decode($response);
        $users = [];
        foreach ($data->rows as $item) {
            $users[] = $this->userNormalizer->denormalize($item, User::class);
        }

        $return = new stdClass();
        $return->data = $users;
        $return->total = $data->total;

        return $return;
    }

    public function getUsersRaw(array $params, $formatObject = false)
    {
        $data = $params ?? [
            'limit' => '5',
            'page' => '1',
            'sort' => 'username',
        ];

        $response = $this->makeRequest(Request::METHOD_GET, 'apigs/admin/users/raw', self::APPLICATION_JSON, $data);
        $data = json_decode($response);

        if (true === $formatObject) {
            $users = [];
            foreach ($data->rows as $item) {
                $item->password = $item->password_raw;
                $u = $this->userNormalizer->denormalize($item, User::class);
                $u->setPasswordReType($u->getPassword());
                $users[] = $u;
            }

            $return = new stdClass();
            $return->data = $users;
            $return->total = $data->total;

            $data = $return;
        }

        return $data;
    }

    public function getUsersByRole(string $role, $params)
    {
        $searchText = $params['searchText'] ?? null;

        $response = $this->makeRequest(Request::METHOD_GET, '/apigs/users/list-by-role', self::APPLICATION_JSON, ['role' => $role, 'searchText' => $searchText]);
        $data = json_decode($response);

        $users = [];
        foreach ($data as $item) {
            $users[] = $this->userNormalizer->denormalize($item, User::class);
        }

        return $users;
    }

    public function getUserHistory($userId, $params)
    {
        $url = 'apigs/admin/users/history/' . $userId;

        return $this->getHistory($url, $params);
    }

    public function getStationHistory($stationId, $params)
    {
        $url = 'apigs/admin/stations/' . $stationId . '/history';

        return $this->getHistory($url, $params);
    }

    public function getHistory($url, $params)
    {
        $response = $this->makeRequest(Request::METHOD_GET, $url, self::APPLICATION_JSON, $params);
        $historyData = json_decode($response, true);

        $rows = [];
        foreach ($historyData['rows'] as $row) {
            $rows[] = $this->normalizer->denormalize($row, History::class);
        }
        $historyData['rows'] = $rows;

        $return = new stdClass();
        $return->historyData = $historyData['rows'];

        return $return;
    }

    public function getUserProfileImgDirPath()
    {
        return $this->makeRequest(Request::METHOD_GET, 'apigs/admin/users/profile-img-dir-path', self::TEXT_HTML);
    }

    public function saveUser(User $user, $isNew = true)
    {
        $data = $this->serializer->serialize($user, 'json', ['groups' => 'api', 'skip_null_values' => true]);
        $url = $user->getId() ? 'apigs/admin/users/' . $user->getId() : '/apigs/admin/users';
        $data = json_decode($data, true);
        $method = $isNew ? Request::METHOD_POST : Request::METHOD_PUT;

        $response = $this->makeRequest($method, $url, self::APPLICATION_JSON, $data);

        return $this->normalizer->denormalize(json_decode($response), User::class);
    }

    public function managePermission($data)
    {
        $data = json_decode($data, true);
        $this->makeRequest(Request::METHOD_POST, '/apigs/admin/users/manage-permission', self::APPLICATION_JSON, $data, false);

        return;
    }

    /**
     * @param string $organization
     *
     * @return bool|string
     */
    public function manageOrgUserRelation($organization)
    {
        $data = json_decode($organization, true);

        return $this->makeRequest(Request::METHOD_POST, '/apigs/admin/organizations/manage', self::APPLICATION_JSON, $data, true);
    }

    public function getUserAbilities($params)
    {
        return $this->makeRequest(Request::METHOD_GET, 'apigs/admin/users/abilities', self::APPLICATION_JSON, $params);
    }

    public function checkUsernameExistence(string $username)
    {
        return $this->makeRequest(Request::METHOD_HEAD, '/apigs/user-management/users', [], ['username' => $username], false);
    }

    public function checkEmailExistence(string $email)
    {
        return $this->makeRequest(Request::METHOD_HEAD, '/apigs/user-management/users', [], ['email' => $email], false);
    }

    public function getRoles()
    {
        $response = $this->makeRequest(Request::METHOD_GET, '/apigs/admin/roles/list', self::APPLICATION_JSON);
        $data = json_decode($response);
        $roles = [];
        foreach ($data as $role) {
            $roles[] = $this->normalizer->denormalize($role, Role::class);
        }

        $return = new stdClass();
        $return->data = $roles;

        return $return;
    }

    public function getOrganizationContactPersons(int $organizationId)
    {
        $url = '/apigs/organization/' . $organizationId . '/contact-persons';
        $response = $this->makeRequest(Request::METHOD_GET, $url, self::APPLICATION_JSON);
        $items = json_decode($response, true);

        $organizationContactPersons = [];
        foreach ($items as $item) {
            $organizationContactPersons[] = $this->normalizer->denormalize($item, ContactPerson::class);
        }

        return $organizationContactPersons;
    }

    public function saveContactPerson(ContactPerson $contactPerson, $organizationId, $contactPersonId, $isNew)
    {
        $data = $this->serializer->serialize($contactPerson, 'json', ['groups' => 'api', 'skip_null_values' => true]);
        $url = '/apigs/organization/' . $organizationId . '/contact-persons' . ($contactPersonId ? '/' . $contactPersonId : '');
        $method = $isNew ? Request::METHOD_POST : Request::METHOD_PUT;
        $response = $this->makeRequest($method, $url, self::APPLICATION_JSON, ['data' => $data]);

        return $this->normalizer->denormalize(json_decode($response), ContactPerson::class);
    }

    public function deleteContactPerson($organizationId, $contactPersonId)
    {
        $url = '/apigs/organization/' . $organizationId . '/contact-persons/' . $contactPersonId;

        return $this->makeRequest(Request::METHOD_DELETE, $url, self::APPLICATION_JSON);
    }

    public function getOrganizationAddresses(int $organizationId)
    {
        $url = '/apigs/organization/' . $organizationId . '/address';
        $response = $this->makeRequest(Request::METHOD_GET, $url, self::APPLICATION_JSON);
        $items = json_decode($response, true);

        $organizationAddresses = [];
        foreach ($items as $item) {
            $organizationAddresses[] = $this->normalizer->denormalize($item, Address::class);
        }

        return $organizationAddresses;
    }

    public function saveAddress(Address $address, $organizationId, $addressId, $isNew)
    {
        $data = $this->serializer->serialize($address, 'json', ['groups' => 'api', 'skip_null_values' => true]);
        $url = '/apigs/organization/' . $organizationId . '/address' . ($addressId ? '/' . $addressId : '');
        $method = $isNew ? Request::METHOD_POST : Request::METHOD_PUT;
        $response = $this->makeRequest($method, $url, self::APPLICATION_JSON, ['data' => $data]);

        return $this->normalizer->denormalize(json_decode($response), Address::class);
    }

    public function deleteAddress($organizationId, $addressId)
    {
        $url = '/apigs/organization/' . $organizationId . '/address/' . $addressId;

        return $this->makeRequest(Request::METHOD_DELETE, $url, self::APPLICATION_JSON);
    }

    public function getOrganizationFarms(int $organizationId)
    {
        $url = "/apigs/organizations/{$organizationId}/farms";
        $response = $this->makeRequest(Request::METHOD_GET, $url, self::APPLICATION_JSON);
        $items = json_decode($response, true);
        $organizationFarms = [];
        foreach ($items as $item) {
            $organizationFarms[] = $this->normalizer->denormalize($item, Farm::class);
        }

        return $organizationFarms;
    }

    public function getFarm(string $farmUuid)
    {
        $url = "/apigs/farms/{$farmUuid}";

        $response = $this->makeRequest(Request::METHOD_GET, $url, self::APPLICATION_JSON);
        $farmData = json_decode($response ?? '[]', true);

        $farmData['organizationId'] = $farmData['organization_id'];
        $farmData['createdBy'] = $farmData['created_by'];
        unset($farmData['organization_id'], $farmData['created_by']);

        return $farmData;
    }

    public function createFarm(Farm $farm): Farm
    {
        $params = $this->normalizer->normalize($farm, 'json', ['groups' => 'gs']);
        $url = '/apigs/farms';

        $response = $this->makeRequest(Request::METHOD_POST, $url, self::APPLICATION_JSON, $params);

        return $this->normalizer->denormalize(json_decode($response), Farm::class);
    }

    public function updateFarm(Farm $farm)
    {
        $params = $this->normalizer->normalize($farm, 'json', ['groups' => 'gs']);
        $farmUuid = $farm->getUuid();
        $url = "/apigs/farms/{$farmUuid}";

        $response = $this->makeRequest(Request::METHOD_PUT, $url, self::APPLICATION_JSON, $params);

        return $this->normalizer->denormalize(json_decode($response), Farm::class);
    }

    public function deleteFarm(string $farmUuid)
    {
        $url = "/apigs/farms/{$farmUuid}";

        return $this->makeRequest(Request::METHOD_DELETE, $url, self::APPLICATION_JSON);
    }

    public function getAbilities($params)
    {
        $data = $params ?? [
            'limit' => '5',
            'page' => '1',
            'sort' => '-id',
        ];
        $response = $this->makeRequest(Request::METHOD_GET, 'apigs/admin/ability', self::APPLICATION_JSON, $data);
        $data = json_decode($response);

        $abilities = [];
        foreach ($data->rows as $ability) {
            $abilities[] = $this->normalizer->denormalize($ability, Ability::class);
        }

        $return = new stdClass();
        $return->data = $abilities;
        $return->total = $data->total;

        return $return;
    }

    public function getCropsByPlotId(int $plotId, string $countryCode, $params = [], bool $callBySystemUser = false)
    {
        if ($callBySystemUser) {
            $data = [
                'country' => $countryCode,
            ];
            $data = array_merge($data, $params);
            $url = '/system/plots/details/' . $plotId . '/crops';

            $response = $this->makeRequest(Request::METHOD_GET, $url, ['token' => $this->keycloakMachineToMachine->getOfflineToken()], $data);
        } else {
            $query = '';
            if (count($params)) {
                $query = '?' . http_build_query($params);
            }
            $url = '/apigs/plots/' . $plotId . '/crops' . $query;
            $response = $this->makeRequest(Request::METHOD_GET, $url, self::APPLICATION_JSON);
        }

        return json_decode($response);
    }

    public function getPlotDetailsByPlotUuId(string $plotUuId, string $countryCode, bool $callBySystemUser = false)
    {
        if ($callBySystemUser) {
            $data = [
                'country' => $countryCode,
            ];
            $url = 'system/plots/details/' . $plotUuId;

            $response = $this->makeRequest(Request::METHOD_GET, $url, ['token' => $this->keycloakMachineToMachine->getOfflineToken()], $data);
        } else {
            $url = 'apigs/plots/details/' . $plotUuId;
            $response = $this->makeRequest(Request::METHOD_GET, $url, self::APPLICATION_JSON);
        }

        return json_decode($response);
    }

    public function getOrganizationUsers(int $organizationId)
    {
        $url = '/apigs/organizations/' . $organizationId . '/users';
        $response = $this->makeRequest(Request::METHOD_GET, $url, self::APPLICATION_JSON);
        $items = json_decode($response);
        $result = [];

        foreach ($items as $item) {
            $result[] = $this->userNormalizer->denormalize($item, User::class);
        }

        return $result;
    }

    public function getOrganizationUsersRaw(int $organizationId)
    {
        $url = '/apigs/organizations/' . $organizationId . '/users-raw';
        $response = $this->makeRequest(Request::METHOD_GET, $url, self::APPLICATION_JSON);
        $items = json_decode($response);
        $result = [];

        foreach ($items as $item) {
            $result[] = $this->userNormalizer->denormalize($item, User::class);
        }

        return $result;
    }

    public function searchUsers(string $value)
    {
        $response = $this->makeRequest(Request::METHOD_GET, '/apigs/admin/users/search', ['accept' => 'application/json'], ['value' => $value]);

        return json_decode($response, true);
    }

    public function organizationUser($organizationId, $userId, $action)
    {
        $url = '/apigs/admin/organizations/' . $organizationId . '/users/' . $userId . '/' . $action . '';
        $response = $this->makeRequest(Request::METHOD_POST, $url, self::APPLICATION_JSON);

        return $this->normalizer->denormalize(json_decode($response), User::class);
    }

    public function addServiceUserToOrganization(Contract $contract)
    {
        $organizationId = $contract->getOrganizationId();

        $responsibleUsers = $contract->getResponsibleUsers()->toArray();
        $serviceUser = array_filter($responsibleUsers, function ($responsibleUser) {
            return 'SAMPLER' !== strtoupper($responsibleUser->getRole());
        });
        $serviceUser = reset($serviceUser);

        $geoscanEm = $this->managerRegistry->getManager('geoscan');
        $instanceUser = $geoscanEm->getRepository(GlobalUser::class)->findOneBy(['id' => $serviceUser->getUserId()]);
        $userId = $instanceUser->getOldId();

        if ($organizationId && $userId) {
            $this->organizationUser($organizationId, $userId, 'attach');
        }
    }

    public function updateFarmsVisibility($user, $data)
    {
        $url = '/apigs/admin/users/' . $user->getId() . '/farms/visibility';
        $response = $this->makeRequest(Request::METHOD_POST, $url, self::APPLICATION_JSON, $data);

        return json_decode($response, true);
    }

    public function manageFarmsAccess($user, $data)
    {
        $url = '/apigs/admin/users/' . $user->getId() . '/farms/manage-access';
        $response = $this->makeRequest(Request::METHOD_POST, $url, self::APPLICATION_JSON, $data);

        return json_decode($response, true);
    }

    public function deactivateStationsByContract(Contract $contract)
    {
        $serviceProviderId = $contract->getServiceProviderId();
        $geoscanEm = $this->managerRegistry->getManager('geoscan');
        $country = $geoscanEm->getRepository(ServiceProviderGeoScan::class)->find($serviceProviderId)->getCountry();
        $data = [
            'contract_id' => $contract->getId(),
            'country' => strtolower($country->getIsoAlpha2Code()),
        ];

        $url = '/system/stations/deactivate-stations-by-contract';
        $response = $this->makeRequest(Request::METHOD_POST, $url, ['token' => $this->keycloakMachineToMachine->getOfflineToken()], $data);

        return json_decode($response, true);
    }

    public function deactivateIntegration(int $organizationId, Package $package, string $countryCode)
    {
        $data = [
            'package_id' => $package->getId(),
            'organization_id' => $organizationId,
            'country' => strtolower($countryCode),
        ];

        $url = '/system/integrations/deactivate-integration';
        $response = $this->makeRequest(Request::METHOD_POST, $url, ['token' => $this->keycloakMachineToMachine->getOfflineToken()], $data);

        return json_decode($response, true);
    }

    public function setContractIdToStations(Contract $contract, array $stationsId)
    {
        $serviceProviderId = $contract->getServiceProviderId();
        $geoscanEm = $this->managerRegistry->getManager('geoscan');
        $country = $geoscanEm->getRepository(ServiceProviderGeoScan::class)->find($serviceProviderId)->getCountry();

        $data = [
            'stations_id' => $stationsId,
            'contract_id' => $contract->getId(),
            'country' => strtolower($country->getIsoAlpha2Code()),
        ];

        $url = '/system/stations/set-contract-to-stations';
        $response = $this->makeRequest(Request::METHOD_POST, $url, ['token' => $this->keycloakMachineToMachine->getOfflineToken()], $data);

        return json_decode($response, true);
    }

    public function getGridPoints(array $soprIds, $countryCode)
    {
        $data = [
            'soprIds' => $soprIds,
            'country' => $countryCode,
        ];

        $url = '/system/plots/grid-points';
        $response = $this->makeRequest(Request::METHOD_GET, $url, ['token' => $this->keycloakMachineToMachine->getOfflineToken()], $data);

        return json_decode($response, true);
    }

    public function getMissingTechnofarmOrganizations()
    {
        $response = $this->makeRequest(Request::METHOD_GET, '/apigs/organizations/get-missing-technofarm-organizations/', self::APPLICATION_JSON, []);

        return json_decode($response, true);
    }

    public function makeSoilMap($data)
    {
        $url = '/system/plots/make-soil-map';
        $response = $this->makeRequest(Request::METHOD_POST, $url, ['token' => $this->keycloakMachineToMachine->getOfflineToken()], $data);

        return json_decode($response, true);
    }

    public function createVraOrder(array $data)
    {
        $url = '/apigs/order-vra/create-order';
        $response = $this->makeRequest(Request::METHOD_POST, $url, self::APPLICATION_JSON, $data);

        return json_decode($response, true);
    }

    public function printRecommendation(int $recommendationId)
    {
        $url = "/cms/recommendations/{$recommendationId}/export/pdf";
        $response = $this->makeRequest(Request::METHOD_POST, $url, self::APPLICATION_JSON, ['recommendationId' => $recommendationId]);

        return ['content' => $response];
    }

    public function updatePlotEditableValue(array $data, bool $status)
    {
        $url = 'apigs/plots/change-editable-status';
        $response = $this->makeRequest(Request::METHOD_POST, $url, self::APPLICATION_JSON, ['plot_uuids' => $data, 'is_editable' => $status]);

        return json_decode($response);
    }

    public function getCardsData(array $requestData)
    {
        $response = $this->makeRequest(Request::METHOD_POST, '/cms/card/get-all-card', self::APPLICATION_JSON, $requestData);

        return json_decode($response, true);
    }

    private function request(string $method, string $uri, array $options = [])
    {
        return $this->guzzleClient->request($method, $uri, $options);
    }

    /**
     * @throws Exception
     * @throws NotFoundHttpException
     *
     * @return bool|string
     */
    private function makeRequest(string $method, string $url, array $header = [], array $data = [], bool $needResponse = true)
    {
        $options = [
            'headers' => [
                'Authorization' => 'Bearer ' . ($header['token'] ?? $this->security->getToken()->getCredentials()),
                'Accept' => $header['accept'] ?? '',
            ],
        ];

        if (in_array($method, [Request::METHOD_GET, Request::METHOD_HEAD])) {
            $options['query'] = $data;
        }

        if (in_array($method, [Request::METHOD_POST, Request::METHOD_PUT])) {
            $options['json'] = $data;
        }

        try {
            $response = $this->request($method, $url, $options);
        } catch (ClientException $e) {
            if (Response::HTTP_NOT_FOUND == $e->getCode()) {
                throw new NotFoundHttpException($e->getMessage(), $e, Response::HTTP_NOT_FOUND);
            }

            throw new Exception($e->getMessage(), $e->getCode());
        }

        if ($needResponse) {
            return $response->getBody()->getContents();
        }

        return true;
    }
}
