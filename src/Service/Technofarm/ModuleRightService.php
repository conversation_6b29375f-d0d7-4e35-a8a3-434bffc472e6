<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service\Technofarm;

use App\Entity\Contract\SubscriptionPackage;

class ModuleRightService
{
    private $apiClient;

    public function __construct(
        TFApiClient $apiClient
    ) {
        $this->apiClient = $apiClient;
    }

    public function revokeModuleRights(SubscriptionPackage $subscriptionPackage)
    {
        $organizationId = $subscriptionPackage->getContract()->getOrganizationId();
        $countryCode = $subscriptionPackage->getServiceProvider()->getCountryCode();

        $moduleRights = array_map(function ($permission) {
            return $permission['value'];
        }, $subscriptionPackage->getPackage()->getAttributes()['permissions']);

        $this->apiClient->revokeModuleRights($organizationId, $moduleRights, $countryCode);
    }
}
