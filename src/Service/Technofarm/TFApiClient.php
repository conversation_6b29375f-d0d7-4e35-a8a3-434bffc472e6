<?php

namespace App\Service\Technofarm;

use App\Model\Farm\Farm;
use App\Model\GeoSCAN\Organization;
use App\Model\GeoSCAN\User;
use App\Security\OAuthClient\Provider\KeycloakMachineToMachine;
use Exception;
use Guz<PERSON><PERSON>ttp\Client;
use GuzzleHttp\Exception\ClientException;
use InvalidArgumentException;
use LogicException;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

class TFApiClient
{
    public const APPLICATION_JSON = ['accept' => 'application/json'];
    public const TEXT_HTML = ['accept' => 'text/html'];

    private $parameters;
    private $security;
    private $httpClient;
    private $normalizer;
    private $keycloakMachineToMachine;

    public function __construct(
        ParameterBagInterface $parameters,
        Security $security,
        NormalizerInterface $normalizer,
        KeycloakMachineToMachine $keycloakMachineToMachine
    ) {
        $this->parameters = $parameters;
        $this->security = $security;
        $this->normalizer = $normalizer;
        $this->keycloakMachineToMachine = $keycloakMachineToMachine;

        $this->httpClient = new Client([
            'base_uri' => $parameters->get('tf_api_base_url'),
        ]);
    }

    /**
     * @param bool $isNew
     *
     * @return array|bool
     */
    public function saveUser(User $user, $isNew)
    {
        $requestData = [
            'method' => Request::METHOD_POST,
            'url' => 'users-rpc=users',
            'body' => $this->getRequestBody(true === $isNew ? 'add' : 'editUser', [
                'username' => $user->getUsername(),
                'email' => $user->getEmail(),
                'name' => $user->getName(),
                'password' => $user->getPassword(),
                'role' => $user->getRole()->getName(),
                'permissions' => $user->getPermissions(),
                'organizations' => array_map(
                    function (Organization $organization) {
                        return [
                            'id' => $organization->getId(),
                            'name' => $organization->getName(),
                            'identityNumber' => $organization->getIdentityNumber(),
                            'farms' => array_map(
                                function (Farm $farm) {
                                    return [
                                        'name' => $farm->getName(),
                                        'uuid' => $farm->getUuid(),
                                    ];
                                },
                                $organization->getFarms()
                            ),
                        ];
                    },
                    $user->getOrganizations()
                ),
                'active' => $user->isActive(),
                'countryCode' => $this->getCountryCode(),
                'keycloakUid' => $user->getKeycloakUid(),
            ]),
        ];

        return $this->makeRequest($requestData['method'], $requestData['url'], $requestData['body']);
    }

    /**
     * @param [type] $username
     *
     * @return iterable
     */
    public function loadUser($username)
    {
        $requestData = [
            'method' => Request::METHOD_POST,
            'url' => 'users-rpc=users',
            'body' => $this->getRequestBody('getUserDataByUsername', [
                'username' => $username,
                'countryCode' => $this->getCountryCode(),
            ]),
        ];

        return $this->makeRequest($requestData['method'], $requestData['url'], $requestData['body']);
    }

    /**
     * @param [type] $username
     *
     * @return iterable
     */
    public function getUserPermissions($username)
    {
        $requestData = [
            'method' => Request::METHOD_POST,
            'url' => 'users-rpc=users',
            'body' => $this->getRequestBody('getUserPermissions', [
                'username' => $username,
                'countryCode' => $this->getCountryCode(),
            ]),
        ];

        return $this->makeRequest($requestData['method'], $requestData['url'], $requestData['body']);
    }

    public function manageOrgUserRelation($params)
    {
        $params = is_array($params) ? $params : json_decode($params, true);

        $requestData = [
            'method' => Request::METHOD_POST,
            'url' => 'users-rpc=users',
            'body' => $this->getRequestBody('manageUserOrganizationRelation', [
                'username' => $params['username'],
                'organizationId' => $params['orgId'],
                'countryCode' => $this->getCountryCode(),
                'action' => $params['action'],
            ]),
        ];

        return $this->makeRequest($requestData['method'], $requestData['url'], $requestData['body']);
    }

    public function updateOrganization(Organization $organization)
    {
        $requestData = [
            'method' => Request::METHOD_POST,
            'url' => 'users-rpc=users',
            'body' => $this->getRequestBody('updateOrganization', [
                'organizationIdentityNumber' => $organization->getIdentityNumber(),
                'organizationId' => $organization->getId(),
                'name' => $organization->getName(),
                'countryCode' => $this->getCountryCode(),
            ]),
        ];

        return $this->makeRequest($requestData['method'], $requestData['url'], $requestData['body']);
    }

    public function getFarm(string $farmUuid): array
    {
        $requestData = [
            'method' => Request::METHOD_POST,
            'url' => 'farming-rpc=farming-grid',
            'body' => $this->getRequestBody('read', ['uuid' => $farmUuid]),
        ];

        $response = $this->makeRequest($requestData['method'], $requestData['url'], $requestData['body']);
        $farmData = $response['rows'][0] ?? [];

        if (isset($farmData['iban_arr'])) {
            $farmData['iban_arr'] = json_decode($farmData['iban_arr'], true);
        }

        if (isset($farmData['post_payment_fields'])) {
            $farmData['post_payment_fields'] = json_decode($farmData['post_payment_fields'], true);
        }

        return $farmData;
    }

    public function createFarm(Farm $farm)
    {
        $params = $this->normalizer->normalize($farm, 'json', ['groups' => 'tf']);
        $params['countryCode'] = $this->getCountryCode();

        $requestData = [
            'method' => Request::METHOD_POST,
            'url' => 'users-rpc=users',
            'body' => $this->getRequestBody('addFarm', $params),
        ];

        return $this->makeRequest($requestData['method'], $requestData['url'], $requestData['body']);
    }

    public function updateFarm(Farm $farm)
    {
        $params = $this->normalizer->normalize($farm, 'json', ['groups' => 'tf']);
        $params['countryCode'] = $this->getCountryCode();

        $requestData = [
            'method' => Request::METHOD_POST,
            'url' => 'users-rpc=users',
            'body' => $this->getRequestBody('editFarm', $params),
        ];

        return $this->makeRequest($requestData['method'], $requestData['url'], $requestData['body']);
    }

    public function deleteFarm(string $farmUuid)
    {
        $requestData = [
            'method' => Request::METHOD_POST,
            'url' => 'users-rpc=users',
            'body' => $this->getRequestBody('deleteFarm', [
                'uuid' => $farmUuid,
            ]),
        ];

        return $this->makeRequest($requestData['method'], $requestData['url'], $requestData['body']);
    }

    public function getEkattes(?string $farmUuid): array
    {
        $requestData = [
            'method' => Request::METHOD_POST,
            'url' => 'common-rpc=ekate-combobox',
            'body' => $this->getRequestBody('readAll', [
                'farm_uuid' => $farmUuid,
            ]),
        ];

        return $this->makeRequest($requestData['method'], $requestData['url'], $requestData['body']);
    }

    public function manageFarmAccess(int $organizationId, string $keycloakUid, array $farms)
    {
        $requestData = [
            'method' => Request::METHOD_POST,
            'url' => 'users-rpc=users',
            'body' => $this->getRequestBody('manageFarmAccess', [
                'organizationId' => $organizationId,
                'countryCode' => $this->getCountryCode(),
                'keycloakUid' => $keycloakUid,
                'farms' => $farms,
            ]),
        ];

        return $this->makeRequest($requestData['method'], $requestData['url'], $requestData['body']);
    }

    public function checkUsernameExistence(string $username)
    {
        $requestData = [
            'method' => Request::METHOD_POST,
            'url' => 'users-rpc=users',
            'body' => $this->getRequestBody('checkUsernameExistence', [
                'username' => $username,
            ]),
        ];

        return $this->makeRequest($requestData['method'], $requestData['url'], $requestData['body']);
    }

    public function checkEmailExistence(string $email)
    {
        $requestData = [
            'method' => Request::METHOD_POST,
            'url' => 'users-rpc=users',
            'body' => $this->getRequestBody('checkEmailExistence', [
                'email' => $email,
            ]),
        ];

        return $this->makeRequest($requestData['method'], $requestData['url'], $requestData['body']);
    }

    public function revokeModuleRights(int $organizationId, array $moduleRights, string $countryCode): void
    {
        $this->makeRequest(
            Request::METHOD_POST,
            'keycloak-system-json=revoke-organization-users-rights',
            [
                'organizationId' => $organizationId,
                'rights' => $moduleRights,
                'countryCode' => $countryCode,
            ],
            [
                'token' => $this->keycloakMachineToMachine->getOfflineToken(),
            ],
            'multipart'
        );
    }

    private function getCountryCode(): string
    {
        return strtolower($this->security->getUser()->getServiceProvider()->getCountry()->getIsoAlpha2Code());
    }

    private function getBaseUrl(): string
    {
        return $this->parameters->get('tf_api_base_url');
    }

    /**
     * @return array
     */
    private function getRequestBody(string $method, array $params)
    {
        return [
            'method' => $method,
            'id' => 1,
            'jsonrpc' => '2.0',
            'params' => [$params],
        ];
    }

    /**
     * @param string $contentType
     *
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     * @throws NotFoundHttpException
     * @throws Exception
     * @throws LogicException
     */
    private function makeRequest(string $method, string $url, array $body, array $header = [], $contentType = 'json')
    {
        try {
            $options = ['timeout' => 120];
            $headers['Authorization'] = 'Bearer ' . ($header['token'] ?? $this->security->getToken()->getCredentials());
            $headers['Accept'] = 'application/json';

            switch ($contentType) {
                case 'json':
                    $options['json'] = $body;

                    break;
                case 'multipart':
                    $options['multipart'] = $this->handleMultipart($body);

                    break;
                default:
                    throw new InvalidArgumentException("Invalid content type: {$contentType}");
            }

            $response = $this->httpClient->request($method, $this->parameters->get('tf_api_base_url') . $url, array_merge(['headers' => $headers], $options));

            $content = json_decode($response->getBody()->getContents(), true);

            if (isset($content['error'])) {
                throw new UnprocessableEntityHttpException($content['error']['message'], null, Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            dd($content['result']);

            return $content['result'];
        } catch (ClientException $e) {
            if (Response::HTTP_NOT_FOUND == $e->getCode()) {
                throw new NotFoundHttpException($e->getMessage(), $e, Response::HTTP_NOT_FOUND);
            }

            throw new Exception($e->getMessage(), $e->getCode(), $e);
        }
    }

    private function handleMultipart(array $body): array
    {
        $multipart = [];

        foreach ($body as $name => $contents) {
            $multipart[] = [
                'name' => $name,
                'contents' => is_array($contents) ? json_encode($contents) : $contents,
            ];
        }

        return $multipart;
    }
}
