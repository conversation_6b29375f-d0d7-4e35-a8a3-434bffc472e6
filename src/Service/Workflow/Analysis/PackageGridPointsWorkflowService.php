<?php

namespace App\Service\Workflow\Analysis;

use App\Entity\Analysis\LabElementGroup;
use App\Entity\Analysis\PackageGridPoints;
use App\Service\Workflow\WorkflowService;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Workflow\Registry;

class PackageGridPointsWorkflowService extends WorkflowService
{
    public function __construct(
        EntityManagerInterface $entityManager,
        Registry $workflow
    ) {
        parent::__construct($workflow, $entityManager);
    }

    public function updatePlotPointsStateToForSamplingOrNotSampled(array $pointsData): bool
    {
        $this->em->getConnection()->beginTransaction();

        try {
            if (isset($pointsData['for_sampling'])) {
                foreach ($pointsData['for_sampling'] as $forSamplingData) {
                    $paramsForSampling = [
                        ['field' => 'packageId', 'value' => $forSamplingData['package_id'], 'type' => '='],
                        ['field' => 'plotUuid', 'value' => $forSamplingData['plot_uuid'], 'type' => '='],
                        ['field' => 'sampleId', 'value' => $forSamplingData['sample_ids'], 'type' => 'IN'],
                    ];
                    $this->updateState(PackageGridPoints::class, PackageGridPoints::WORKFLOW_NAME, $paramsForSampling, PackageGridPoints::TRANSITION_FOR_SAMPLING);
                }
            }

            if (isset($pointsData['not_sampled'])) {
                foreach ($pointsData['not_sampled'] as $notSampledData) {
                    $paramsNotSampled = [
                        ['field' => 'packageId', 'value' => $notSampledData['package_id'], 'type' => '='],
                        ['field' => 'plotUuid', 'value' => $notSampledData['plot_uuid'], 'type' => '='],
                        ['field' => 'sampleId', 'value' => $notSampledData['sample_ids'], 'type' => 'IN'],
                    ];
                    $this->updateState(PackageGridPoints::class, PackageGridPoints::WORKFLOW_NAME, $paramsNotSampled, PackageGridPoints::TRANSITION_NOT_SAMPLED);
                }
            }
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }

        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();

        return true;
    }

    public function updatePlotsSamplingPoints(array $pointsData): bool
    {
        if (isset($pointsData['sampling'])) {
            foreach ($pointsData['sampling'] as $samplingData) {
                $paramsSampling = [
                    ['field' => 'packageId', 'value' => $samplingData['package_id'], 'type' => '='],
                    ['field' => 'plotUuid', 'value' => $samplingData['plot_uuid'], 'type' => '='],
                    ['field' => 'sampleId', 'value' => $samplingData['sample_ids'], 'type' => 'IN'],
                ];
                $this->updateState(PackageGridPoints::class, PackageGridPoints::WORKFLOW_NAME, $paramsSampling, PackageGridPoints::TRANSITION_SAMPLING);
                $this->flushChanges();
            }
        }

        if (isset($pointsData['for_sampling'])) {
            foreach ($pointsData['for_sampling'] as $forSamplingData) {
                $paramsForSampling = [
                    ['field' => 'packageId', 'value' => $forSamplingData['package_id'], 'type' => '='],
                    ['field' => 'plotUuid', 'value' => $forSamplingData['plot_uuid'], 'type' => '='],
                    ['field' => 'sampleId', 'value' => $forSamplingData['sample_ids'], 'type' => 'IN'],
                ];
                $this->updateState(PackageGridPoints::class, PackageGridPoints::WORKFLOW_NAME, $paramsForSampling, PackageGridPoints::TRANSITION_FOR_SAMPLING);
                $this->flushChanges();
            }
        }

        if (isset($pointsData['not_sampled'])) {
            foreach ($pointsData['not_sampled'] as $notSampledData) {
                $paramsNotSampled = [
                    ['field' => 'packageId', 'value' => $notSampledData['package_id'], 'type' => '='],
                    ['field' => 'plotUuid', 'value' => $notSampledData['plot_uuid'], 'type' => '='],
                    ['field' => 'sampleId', 'value' => $notSampledData['sample_ids'], 'type' => 'IN'],
                ];
                $this->updateState(PackageGridPoints::class, PackageGridPoints::WORKFLOW_NAME, $paramsNotSampled, PackageGridPoints::TRANSITION_NOT_SAMPLED);
                $this->flushChanges();
            }
        }

        return true;
    }

    public function getPackageGridPointIdsByLabElementGroup(LabElementGroup $labElementGroup)
    {
        $packageGridPointsRepo = $this->em->getRepository(PackageGridPoints::class);

        return $packageGridPointsRepo->getPackageGridPointIdsByLabElementGroup($labElementGroup);
    }

    public function applyTransition(string $workflowName, string $transition, $objectRow)
    {
        $this->apply($workflowName, $transition, $objectRow);
    }
}
