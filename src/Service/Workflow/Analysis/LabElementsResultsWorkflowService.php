<?php

namespace App\Service\Workflow\Analysis;

use App\Entity\Analysis\LabElementsResults;
use App\Service\Workflow\WorkflowService;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Workflow\Registry;

class LabElementsResultsWorkflowService extends WorkflowService
{
    public function __construct(
        EntityManagerInterface $entityManager,
        Registry $workflow
    ) {
        parent::__construct($workflow, $entityManager);
    }

    public function applyTransition(string $workflowName, string $transition, $objectRow)
    {
        $this->apply($workflowName, $transition, $objectRow);
    }

    public function updateLabElementsResultStates(array $arrData)
    {
        $this->em->getConnection()->beginTransaction();

        try {
            foreach ($arrData as $transition => $arrIds) {
                $repo = $this->em->getRepository(LabElementsResults::class);
                $params[] = ['field' => 'id', 'type' => 'IN', 'value' => $arrIds];
                $query = $repo->findByFieldsQuery($params);
                $result = $query->getResult();

                foreach ($result as $labElementsResult) {
                    $this->apply(LabElementsResults::WORKFLOW_NAME, $transition, $labElementsResult);
                    $this->em->flush();
                }
                $params = [];
            }
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }

        $this->em->clear();
        $this->em->getConnection()->commit();
    }
}
