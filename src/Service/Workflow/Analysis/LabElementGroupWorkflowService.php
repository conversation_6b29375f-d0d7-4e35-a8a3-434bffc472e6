<?php

namespace App\Service\Workflow\Analysis;

use App\Entity\Analysis\LabElementGroup;
use App\Entity\Analysis\PackageGridPoints;
use App\Service\Workflow\WorkflowService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Workflow\Registry;

class LabElementGroupWorkflowService extends WorkflowService
{
    public function __construct(
        EntityManagerInterface $entityManager,
        Registry $workflow
    ) {
        parent::__construct($workflow, $entityManager);
    }

    public function getLabElementGroupIdsByPackageGridPoint(PackageGridPoints $pgp): array
    {
        $labElementGroupRepo = $this->em->getRepository(LabElementGroup::class);

        return $labElementGroupRepo->getLabElementGroupIdsByPackageGridPoint($pgp);
    }
}
