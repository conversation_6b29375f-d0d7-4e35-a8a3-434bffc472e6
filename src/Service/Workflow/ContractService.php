<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service\Workflow;

use App\Entity\Contract;
use App\Entity\Contract\Subscription;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Workflow\Registry;

class ContractService extends WorkflowService
{
    private $subscriptionPackageService;

    public function __construct(
        EntityManagerInterface $entityManager,
        Registry $workflow,
        SubscriptionPackageService $subscriptionPackageService
    ) {
        parent::__construct($workflow, $entityManager);
        $this->subscriptionPackageService = $subscriptionPackageService;
    }

    public function activateContract($contract): Contract
    {
        $this->apply('contract_subscription_state', 'activate_contract', $contract);

        return $contract;
    }

    /**
     * @param bool $selfFlush
     *
     * @return string
     */
    public function deactivateAllExpiredSubscriptionContracts($selfFlush = false)
    {
        $subscriptionRepo = $this->em->getRepository(Subscription::class);

        $contracts = $subscriptionRepo->getAllContractsWithExpiredEndDate();

        if (!$contracts) {
            return;
        }

        foreach ($contracts as $contract) {
            $this->apply('contract_subscription_state', 'deactivate_contract', $contract);
        }

        if ($selfFlush) {
            $this->em->flush();
        }

        return $contracts;
    }
}
