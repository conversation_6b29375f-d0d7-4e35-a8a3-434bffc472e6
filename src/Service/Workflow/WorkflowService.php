<?php

namespace App\Service\Workflow;

use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Contract\SubscriptionPackageField;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Workflow\Registry;

abstract class WorkflowService
{
    protected $workflow;
    protected $em;

    public function __construct(Registry $workflow, EntityManagerInterface $entityManager)
    {
        $this->em = $entityManager;
        $this->workflow = $workflow;
    }

    public function isAllInState(string $stateValue, $class, array $params, $isCompletedTransition = false, $fieldName = 'state'): bool
    {
        $repo = $this->em->getRepository($class);

        $query = $repo->findByFieldsQuery($params);
        $resultAll = $query->getArrayResult();

        $params[] = ['field' => $fieldName, 'type' => '=', 'value' => $stateValue];
        $query = $repo->findByFieldsQuery($params);
        $resultByState = $query->getArrayResult();

        $cntAllResults = $isCompletedTransition ? count($resultAll) : (count($resultAll) - 1);

        return ($cntAllResults === count($resultByState));
    }

    public function isAllInStates(array $stateValues, $class, array $params, $isCompletedStateTransition = false, $fieldName = 'state'): bool
    {
        $repo = $this->em->getRepository($class);

        $query = $repo->findByFieldsQuery($params);
        $resultAll = $query->getArrayResult();

        $params[] = ['field' => $fieldName, 'type' => 'IN', 'value' => $stateValues];
        $query = $repo->findByFieldsQuery($params);
        $resultByState = $query->getArrayResult();

        $cntAllResults = $isCompletedStateTransition ? count($resultAll) : (count($resultAll) - 1);

        return ($cntAllResults === count($resultByState));
    }

    public function atLeastOneAtState(string $stateValue, $class, array $params, $isCompletedTransition = false, $fieldName = 'state'): bool
    {
        $repo = $this->em->getRepository($class);

        $params[] = ['field' => $fieldName, 'type' => '=', 'value' => $stateValue];
        $query = $repo->findByFieldsQuery($params);
        $resultByState = $query->getArrayResult();

        return $isCompletedTransition ? count($resultByState) > 0 : (count($resultByState) - 1) > 0;
    }

    public function findSubscriptionPackageById($packageId): ?object
    {
        $subscriptionPackageRepo = $this->em->getRepository(SubscriptionPackage::class);

        return $subscriptionPackageRepo->find($packageId);
    }

    public function findSubscriptionPackageField($packageId, $plotUuid): ?object
    {
        $subscriptionPackageRepo = $this->em->getRepository(SubscriptionPackageField::class);

        return $subscriptionPackageRepo->findOneBy(['subscriptionPackage' => $packageId, 'plotUuid' => $plotUuid]);
    }

    public function updateState(string $className, string $workflowName, array $params, $transition)
    {
        $repo = $this->em->getRepository($className);
        $query = $repo->findByFieldsQuery($params);
        $results = $query->getResult();

        foreach ($results as $object) {
            $this->apply($workflowName, $transition, $object);
        }
    }

    public function flushChanges()
    {
        $this->em->flush();
    }

    protected function apply(string $workflowName, string $transition, $objectRow)
    {
        $workFlow = $this->workflow->get($objectRow, $workflowName);
        // We need to have this check because it throws an Exception and brakes the logic
        if ($workFlow->can($objectRow, $transition)) {
            $workFlow->apply($objectRow, $transition);

            if (method_exists($objectRow, 'setStateUpdatedAt')) {
                $this->updateStateUpdatedAt($objectRow);
            }
        }
    }

    private function updateStateUpdatedAt($entity): void
    {
        $now = new DateTime();
        $entity->setStateUpdatedAt($now);
    }
}
