<?php

namespace App\Service\Workflow;

use App\Entity\Contract\Price;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Workflow\Registry;

class PaymentWorkflowService extends WorkflowService
{
    public function __construct(
        EntityManagerInterface $entityManager,
        Registry $workflowRegistry
    ) {
        parent::__construct($workflowRegistry, $entityManager);
    }

    public function updatePriceStatus(Price $price)
    {
        $payments = $price->getPayments()->toArray();

        if (!count($payments)) {
            throw new Exception('Payments not found!');
        }

        $totalPaid = 0.0;
        foreach ($payments as $payment) {
            $totalPaid += floatval($payment->getPaid());
        }

        $priceAmount = floatval($price->getAmount());
        $priceStatus = $price->getStatus();

        if ('None' === $priceStatus) {
            if ($totalPaid < $priceAmount) {
                $this->apply('contract_payment_status', 'add_partial_payment', $price);
            } else {
                $this->apply('contract_payment_status', 'add_full_payment', $price);
            }
        }

        if ('Partial' === $priceStatus && ($totalPaid >= $priceAmount)) {
            $this->apply('contract_payment_status', 'complete_payment', $price);
        }

        $this->em->persist($price);
        $this->em->flush();
    }
}
