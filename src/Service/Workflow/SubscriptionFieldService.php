<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service\Workflow;

use App\Entity\Analysis\PackageGridPoints;
use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Contract\SubscriptionPackageField;
use App\Repository\Contract\SubscriptionPackageRepository;
use App\Service\Analysis\LabElementGroupService;
use App\Service\Analysis\LabElementsResultsService;
use App\Service\GeoSCANAPIClient;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Workflow\Registry;

class SubscriptionFieldService extends WorkflowService
{
    private GeoSCANAPIClient $geoSCANAPIClient;
    private SubscriptionPackageRepository $subscriptionPackageRepository;
    private LabElementGroupService $labElementGroupService;
    private LabElementsResultsService $labElementsResultsService;

    public function __construct(
        EntityManagerInterface $entityManager,
        Registry $workflow,
        GeoSCANAPIClient $geoSCANAPIClient,
        SubscriptionPackageRepository $subscriptionPackageRepository,
        LabElementGroupService $labElementGroupService,
        LabElementsResultsService $labElementsResultsService
    ) {
        parent::__construct($workflow, $entityManager);
        $this->geoSCANAPIClient = $geoSCANAPIClient;
        $this->subscriptionPackageRepository = $subscriptionPackageRepository;
        $this->labElementGroupService = $labElementGroupService;
        $this->labElementsResultsService = $labElementsResultsService;
    }

    public function changeSubscriptionPackageFiledState(SubscriptionPackageField $object, string $transition)
    {
        $this->apply('subscription_field_state', $transition, $object);
    }

    public function updateSubscriptionPackageFieldStateByParams(array $params, $transition)
    {
        $this->em->getConnection()->beginTransaction();

        try {
            $this->updateState(SubscriptionPackageField::class, SubscriptionPackageField::WORKFLOW_NAME, $params, $transition);

            $this->em->flush();
            $this->em->clear(SubscriptionPackageField::class);
            $this->em->getConnection()->commit();
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }
    }

    /**
     * When all packaged for each plot is in state Done, then send request.
     *
     * @return mixed|void
     */
    public function updatePlotsEditableStatus($params)
    {
        $repo = $this->em->getRepository(SubscriptionPackageField::class);
        $query = $repo->findByFieldsQuery($params);
        $resultAll = $query->getArrayResult();

        $plotsData = [];
        foreach ($resultAll as $data) {
            $filter['plot_uuid'] = $data['plotUuid'];
            $filter['packages_state'] = json_encode([SubscriptionPackage::STATUS_IN_PROGRESS]);

            $packages = $this->subscriptionPackageRepository->getPackagesCustom($filter);
            if ((count($packages) - 1) === 0) {
                $plotsData[] = $data['plotUuid'];
            }
        }

        if (!empty($plotsData)) {
            return $this->geoSCANAPIClient->updatePlotEditableValue($plotsData, true);
        }

        return true;
    }

    /**
     * @throws Exception
     */
    public function createElementsGroupAndElementsResults($params): bool
    {
        $packageGridPointsRepository = $this->em->getRepository(PackageGridPoints::class);

        $arrToInsertElementGroup = $packageGridPointsRepository->getElementGroupData($params['packageId'], $params['packageType'], $params['plotUuids'], false);
        $this->labElementGroupService->create($arrToInsertElementGroup);

        $arrToInsertElementsResults = $packageGridPointsRepository->getElementsResultsData($params['packageId'], $params['packageType'], $params['plotUuids'], false);
        $this->labElementsResultsService->create($arrToInsertElementsResults);

        return true;
    }
}
