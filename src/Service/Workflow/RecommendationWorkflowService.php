<?php

namespace App\Service\Workflow;

use App\Entity\Recommendation\Recommendation;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Workflow\Exception\LogicException;
use Symfony\Component\Workflow\Registry;

class RecommendationWorkflowService extends WorkflowService
{
    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * RecommendationWorkflowService constructor.
     */
    public function __construct(
        EntityManagerInterface $entityManager,
        Registry $workflowRegistry,
        LoggerInterface $ismhCommandLogger
    ) {
        parent::__construct($workflowRegistry, $entityManager);
        $this->logger = $ismhCommandLogger;
    }

    public function updateStatus(Recommendation $recommendation, string $transition, bool $flush = false)
    {
        try {
            $this->apply(Recommendation::WORKFLOW_NAME, $transition, $recommendation);

            if ($flush) {
                $this->em->flush();
            }
        } catch (LogicException $exception) {
            $this->logger->error("Error RecommendationWorkflowService: \n" . $exception->getMessage());
        }
    }
}
