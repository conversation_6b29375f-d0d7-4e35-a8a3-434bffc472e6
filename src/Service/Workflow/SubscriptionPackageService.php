<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service\Workflow;

use App\Entity\Contract\SubscriptionPackage;
use App\Service\Contract\SubscriptionPackageService as SubPackageService;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Workflow\Registry;

class SubscriptionPackageService extends WorkflowService
{
    private $subscriptionPackageService;

    public function __construct(
        EntityManagerInterface $entityManager,
        Registry $workflow,
        SubPackageService $subscriptionPackageService
    ) {
        parent::__construct($workflow, $entityManager);
        $this->subscriptionPackageService = $subscriptionPackageService;
    }

    /**
     * @param bool $selFlush
     */
    public function activateSubscriptionPackages($subscriptionPackages, $selFlush = false)
    {
        foreach ($subscriptionPackages as $package) {
            $this->apply('subscription_package_status', 'activate_package', $package);
        }

        if ($selFlush) {
            $this->em->flush();
        }

        return $subscriptionPackages;
    }

    /**
     * @param bool $selFlush
     */
    public function deactivateSubscriptionPackagesByContractId($contractId, $selFlush = false)
    {
        $subscriptionPackages = $this->getSubscriptionPackagesByContractId($contractId);

        foreach ($subscriptionPackages as $package) {
            $this->apply('subscription_package_status', 'deactivate_package', $package);
            $this->em->persist($package);
        }

        if ($selFlush) {
            $this->em->flush();
        }

        return $subscriptionPackages;
    }

    /**
     * @return object[]
     */
    public function getSubscriptionPackagesByContractId($contractId)
    {
        return $this->em->getRepository(SubscriptionPackage::class)->findBy(['contract' => $contractId]);
    }

    public function changeSubscriptionPackageState($subscriptionPackage, $transition)
    {
        $this->apply('subscription_package_states', $transition, $subscriptionPackage);

        return $subscriptionPackage;
    }

    public function updateByFields(array $params, $transition)
    {
        $repo = $this->em->getRepository(SubscriptionPackage::class);
        $query = $repo->findByFieldsQuery($params);
        $result = $query->getResult();

        $this->em->getConnection()->beginTransaction();

        try {
            foreach ($result as $object) {
                $this->apply('subscription_package_states', $transition, $object);
            }
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }

        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();
    }

    /**
     * @param bool $selfFlush
     */
    public function deactivateAllExpiredPackages($selfFlush = false)
    {
        $returnData = [];
        $filter['packages_status'] = json_encode([SubscriptionPackage::STATUS_ACTIVE]);
        $packages = $this->subscriptionPackageService->listPackagesByFilter($filter);

        foreach ($packages as $package) {
            $subscriptionPackageRepository = $this->em->getRepository(SubscriptionPackage::class);
            $currentDate = date('Y-m-d');

            if ($package['endDate'] < $currentDate) {
                $returnData[] = $package['id'];
                $subscriptionPackage = $subscriptionPackageRepository->find($package['id']);
                $this->apply('subscription_package_status', 'deactivate_package', $subscriptionPackage);
            }
        }

        if ($selfFlush) {
            $this->em->flush();
        }

        return $returnData;
    }
}
