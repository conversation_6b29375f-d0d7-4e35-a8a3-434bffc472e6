<?php

namespace App\Service\Recommendation;

use App\Entity\Contract;
use App\Entity\Recommendation\Recommendation;
use App\Service\GeoSCANAPIClient;
use App\Service\MailerService;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Throwable;

class RecommendationMailerService extends MailerService
{
    /**
     * @var GeoSCANAPIClient
     */
    private $geoScanAPIClient;

    private $recommendationService;

    /**
     * RecommendationMailerService constructor.
     */
    public function __construct(
        MailerInterface $mailer,
        ParameterBagInterface $parameters,
        GeoSCANAPIClient $geoSCANAPIClient,
        RecommendationService $recommendationService
    ) {
        parent::__construct($mailer, $parameters);
        $this->geoScanAPIClient = $geoSCANAPIClient;
        $this->recommendationService = $recommendationService;
    }

    /**
     * @param int $recommendationId
     * @param string $plotName
     */
    public function sendChangeRecommendationStatusMessage(Recommendation $recommendation)
    {
        $config = $this->parameters->get('mailer');

        $subject = str_replace(['{recommendationId}', '{plotName}'], [$recommendation->getId(), $recommendation->getPlotName()], $config['subject']);

        $email = (new TemplatedEmail())
            ->from(new Address($config['from']))
            ->to(new Address($config['to']))
            ->addTo(new Address($config['addTo']))
            ->subject($subject)
            ->htmlTemplate('emails/recommendation_status_changed.html.twig')
            ->context([
                'recommendationId' => $recommendation->getId(),
                'plotName' => $recommendation->getPlotName(),
                'status' => $recommendation->getStatus(),
                'appBaseUrl' => $config['data']['appBaseUrl'],
            ]);

        try {
            $this->mailer->send($email);
        } catch (Throwable $e) {
        }
    }

    /**
     * @param $contract $contract
     */
    public function sendClientRecommendationDeliveredMessage(Recommendation $recommendation, Contract $contract, string $farmingYear)
    {
        try {
            $config = $this->parameters->get('mailer');
            $recommendationId = $recommendation->getId();
            $plotName = $recommendation->getPlotName();

            $subject = str_replace(['{recommendationId}', '{plotName}'], [$recommendationId, $plotName], $config['subject']);

            $organization = $this->geoScanAPIClient->getOrganizationById(
                $contract->getOrganizationId()
            )->first();

            $representative = current(array_filter($organization['contacts_persons'], function ($contact) {
                return $contact['is_representative'];
            }));

            if (!$representative) {
                return;
            }
            $content = $this->geoScanAPIClient->printRecommendation(
                $recommendation->getId()
            );

            $fileName = 'Recommendation_' . $recommendationId . '_' . $plotName;
            $tempFile = tmpfile();
            $tempFileUri = stream_get_meta_data($tempFile)['uri'];
            file_put_contents($tempFileUri, $content);

            $email = (new TemplatedEmail())
                ->from(new Address($config['from']))
                ->to(new Address($representative['email']))
                ->subject($subject)
                ->htmlTemplate('emails/delivered_recommendation.html.twig')
                ->context([
                    'recommendationId' => $recommendationId,
                    'plotName' => $plotName,
                    'farmYear' => $farmingYear,
                    'appBaseUrl' => $config['data']['appBaseUrl'],
                ])
                ->attachFromPath($tempFileUri, $fileName, 'application/pdf');

            $this->mailer->send($email);
        } catch (Throwable $e) {
        }
    }
}
