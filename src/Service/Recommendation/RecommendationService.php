<?php

namespace App\Service\Recommendation;

use App\Entity\Analysis\LabElementGroup;
use App\Entity\Analysis\PackageGridPoints;
use App\Entity\AnalysisRecommendationConfig\RecommendationModelsConfig;
use App\Entity\Contract\SubscriptionPackageField;
use App\Entity\Log\Recommendation\RecommendationCommentLog;
use App\Entity\Log\Recommendation\RecommendationElementsResultsLog;
use App\Entity\Log\Recommendation\RecommendationElementsSuscesLog;
use App\Entity\Log\Recommendation\RecommendationLog;
use App\Entity\Log\Recommendation\RecommendationVraOrdersLog;
use App\Entity\Package\SamplingType;
use App\Entity\Recommendation\Recommendation;
use App\Entity\Recommendation\RecommendationComment;
use App\Entity\Recommendation\RecommendationElementsResults;
use App\Entity\Recommendation\RecommendationElementsSusces;
use App\Entity\Recommendation\RecommendationVraOrders;
use App\Repository\AnalysisRecommendationConfig\LabInterpretationClassesConfigRepository;
use App\Repository\Log\Recommendation\RecommendationCommentLogRepository;
use App\Repository\Log\Recommendation\RecommendationElementsResultsLogRepository;
use App\Repository\Log\Recommendation\RecommendationElementsSuscesLogRepository;
use App\Repository\Log\Recommendation\RecommendationLogRepository;
use App\Repository\Log\Recommendation\RecommendationVraOrdersLogRepository;
use App\Repository\Recommendation\RecommendationReportRepository;
use App\Repository\Recommendation\RecommendationRepository;
use App\Service\AbstractService;
use App\Service\Workflow\RecommendationWorkflowService;
use DateTime;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use PDO;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;
use Symfony\Component\Security\Core\Security;

class RecommendationService extends AbstractService
{
    /**
     * @var EntityManagerInterface
     */
    private $em;

    /**
     * @var RecommendationRepository
     */
    private $recommendationManager;

    /**
     * @var RecommendationReportRepository
     */
    private $recommendationReportManager;

    /**
     * @var RecommendationWorkflowService
     */
    private $recommendationWorkflowService;

    private $security;

    /**
     * @var LabInterpretationClassesConfigRepository
     */
    private $labInterpretationClassesConfigRepository;

    public function __construct(
        FormFactoryInterface $formFactory,
        RecommendationRepository $recommendationManager,
        EntityManagerInterface $entityManager,
        RecommendationWorkflowService $recommendationWorkflowService,
        Security $security,
        LabInterpretationClassesConfigRepository $labInterpretationClassesConfigRepository,
        RecommendationReportRepository $recommendationReportManager
    ) {
        parent::__construct($formFactory);
        $this->em = $entityManager;
        $this->recommendationManager = $recommendationManager;
        $this->security = $security;
        $this->recommendationWorkflowService = $recommendationWorkflowService;
        $this->labInterpretationClassesConfigRepository = $labInterpretationClassesConfigRepository;
        $this->recommendationReportManager = $recommendationReportManager;
    }

    public function getLabElementGroupsByRecommendation(int $recommendationId)
    {
        return $this->recommendationManager->getLabElementGroups($recommendationId);
    }

    /**
     * Get all models by service provider.
     */
    public function getModelsByServiceProvider(): array
    {
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();
        $recommendationModelsConfigRepo = $this->em->getRepository(RecommendationModelsConfig::class);

        return $recommendationModelsConfigRepo->getByServiceProvider($serviceProviderId);
    }

    public function getContractByRecommendation(int $recommendationId)
    {
        return $this->recommendationManager->getRecommendationContractQb($recommendationId)
            ->select('c')->getQuery()->getSingleResult();
    }

    public function getRecommendationResults(Recommendation $recommendation)
    {
        $conn = $this->em->getConnection();
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();

        $recommendationResultsSql = '
            SELECT
                results,
                susces,
                comments
            FROM
                get_recommendation_results(:recommendationId, :serviceProviderId)
        ';

        $recommendationResultsStmt = $conn->prepare($recommendationResultsSql);
        $recommendationResultsStmt->bindValue('recommendationId', $recommendation->getId());
        $recommendationResultsStmt->bindValue('serviceProviderId', $serviceProviderId);

        $recommendationResultsStmt->execute();
        $recommendationResults = $recommendationResultsStmt->fetch(PDO::FETCH_ASSOC);

        $subscriptionPackageField = $this->recommendationManager->getSubscriptionPackageField($recommendation);
        $contract = $subscriptionPackageField->getSubscriptionPackage()->getContract();

        return [
            'recommendation' => [
                'subscriptionPackageFieldId' => $subscriptionPackageField->getId(),
                'plotUuid' => $subscriptionPackageField->getPlotUuid(),
                'contractId' => $contract->getId(),
                'orderUuid' => $subscriptionPackageField->getOrderUuid(),
                'contractNumber' => $contract->getNumber(),
                'customerIdentification' => $contract->getCustomerIdentification(),
                'cropId' => $recommendation->getCrop(),
                'createdAt' => $recommendation->getCreatedAt(),
                'yield' => $recommendation->getTargetYield(),
                'samplingTypeIds' => $recommendation->getSamplingTypeIds(),
                'validFrom' => $recommendation->getValidFrom()->format('Y-m-d'),
                'id' => $recommendation->getId(),
            ],
            'results' => isset($recommendationResults['results']) ? json_decode($recommendationResults['results'], true) : [],
            'susces' => isset($recommendationResults['susces']) ? json_decode($recommendationResults['susces'], true) : [],
            'comments' => isset($recommendationResults['comments']) ? json_decode($recommendationResults['comments'], true) : [],
        ];
    }

    /**
     * @param int $cropId ,
     * @param int $modelId ,
     * @param float $humus ,
     */
    public function getRecommendationCalculations(
        SubscriptionPackageField $subscriptionPackageField,
        int $cropId,
        int $modelId,
        float $humus,
        float $yield,
        string $validFrom,
        array $samplingTypeIds = []
    ) {
        $conn = $this->em->getConnection();

        $recommendationCalculationsSql = '
            SELECT
                calculations,
                susces,
                comments
            FROM
                get_recommendation_calculations(
                    :plotUuid,
                    :packageId,
                    :packageType,
                    NULLIF(ARRAY[:samplingTypeIds]::INT[], ARRAY[NULL]::INT[]), -- empty array is binded as NULL
                    :serviceProviderId,
                    :modelId,
                    :cropId,
                    :humus::NUMERIC,
                    :yield,
                    :validFrom
                )
        ';

        $params = [
            'serviceProviderId' => $this->security->getUser()->getServiceProvider()->getId(),
            'plotUuid' => $subscriptionPackageField->getPlotUuid(),
            'packageId' => $subscriptionPackageField->getSubscriptionPackage()->getId(),
            'packageType' => 'subscription',
            'modelId' => $modelId,
            'cropId' => $cropId,
            'humus' => $humus,
            'yield' => $yield,
            'validFrom' => $validFrom,
            'samplingTypeIds' => $samplingTypeIds,
        ];

        $paramTypes = [
            'serviceProviderId' => ParameterType::INTEGER,
            'plotUuid' => ParameterType::STRING,
            'packageId' => ParameterType::INTEGER,
            'packageType' => ParameterType::STRING,
            'modelId' => ParameterType::INTEGER,
            'cropId' => ParameterType::INTEGER,
            'humus' => ParameterType::STRING,
            'yield' => ParameterType::INTEGER,
            'samplingTypeIds' => Connection::PARAM_INT_ARRAY,
            'validFrom' => ParameterType::STRING,
        ];

        $recommendationCalculationsStmt = $conn->executeQuery($recommendationCalculationsSql, $params, $paramTypes);
        $recommendationCalculations = $recommendationCalculationsStmt->fetchAssociative();

        return [
            'calculations' => isset($recommendationCalculations['calculations']) ? json_decode($recommendationCalculations['calculations'], true) : [],
            'susces' => isset($recommendationCalculations['susces']) ? json_decode($recommendationCalculations['susces'], true) : [],
            'comments' => isset($recommendationCalculations['comments']) ? json_decode($recommendationCalculations['comments'], true) : [],
        ];
    }

    /**
     * @throws \Doctrine\DBAL\ConnectionException
     *
     * @return Recommendation
     */
    public function storeRecommendation(
        SubscriptionPackageField $subscriptionPackageField,
        string $plotName,
        int $cropId,
        int $modelId,
        float $humus,
        float $yield,
        string $validFrom,
        array $results,
        array $comments,
        array $susces,
        array $vraOrders,
        array $samplingTypeIds
    ) {
        $this->em->getConnection()->beginTransaction();

        try {
            $recommendationModel = $this->em->getRepository(RecommendationModelsConfig::class)->find($modelId);
            $this->em->getRepository(SamplingType::class)->validateAllSamplingTypesExist($samplingTypeIds);

            $recommendation = new Recommendation();
            $recommendation->setModel($recommendationModel);
            $recommendation->setPlotName($plotName);
            $recommendation->setPlotUuid($subscriptionPackageField->getPlotUuid());
            $recommendation->setPackage($subscriptionPackageField->getSubscriptionPackage()->getId());
            $recommendation->setPackageType('subscription');
            $recommendation->setCrop($cropId);
            $recommendation->setHumus($humus);
            $recommendation->setTargetYield($yield);
            $recommendation->setValidFrom(new DateTime($validFrom));
            $recommendation->setSamplingTypeIds($samplingTypeIds);
            $this->em->persist($recommendation);
            $this->em->flush();

            foreach ($results as $result) {
                $recommendationElementResult = new RecommendationElementsResults();
                $recommendationElementResult->setRecommendation($recommendation);
                $recommendationElementResult->setResultElement($result['result_element']);
                $recommendationElementResult->setValue($result['result_element_value']);
                $this->em->persist($recommendationElementResult);
            }

            foreach ($comments as $comment) {
                $commentType = isset($comment['result_element']) ? RecommendationComment::COMMENT_TYPE_ELEMENT : RecommendationComment::COMMENT_TYPE_CUSTOM;

                $recommendationComment = new RecommendationComment();
                $recommendationComment->setRecommendation($recommendation);
                $recommendationComment->setParam($comment['result_element']);
                $recommendationComment->setCommentType($commentType);
                $recommendationComment->setValue($comment['comment_text']);

                $this->em->persist($recommendationComment);
            }

            foreach ($susces as $susce) {
                $recommendationSusces = new RecommendationElementsSusces();
                $recommendationSusces->setRecommendation($recommendation);
                $recommendationSusces->setElement($susce['element']);
                $recommendationSusces->setValue($susce['value']);
                $this->em->persist($recommendationSusces);
            }

            foreach ($vraOrders as $vraOrder) {
                $recommendationVraOrder = new RecommendationVraOrders();
                $recommendationVraOrder->setRecommendation($recommendation);
                $recommendationVraOrder->setVraOrderId($vraOrder['id']);
                $recommendationVraOrder->setVraOrderType($vraOrder['type']);
                $this->em->persist($recommendationVraOrder);
            }
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }

        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();

        return $recommendation;
    }

    /**
     * @throws \Doctrine\DBAL\ConnectionException
     * @throws \Doctrine\ORM\ORMException
     */
    public function updateRecommendation(Recommendation $recommendation, array $updateData): Recommendation
    {
        $conn = $this->em->getConnection();
        $conn->beginTransaction();

        try {
            if (isset($updateData['crop_id'])) {
                $recommendation->setCrop($updateData['crop_id']);
            }

            if (isset($updateData['yield'])) {
                $recommendation->setTargetYield($updateData['yield']);
            }

            if (isset($updateData['model_id'])) {
                $recommendationModel = $this->em->getRepository(RecommendationModelsConfig::class)->find($updateData['model_id']);
                $recommendation->setModel($recommendationModel);
            }

            if (isset($updateData['humus'])) {
                $recommendation->setHumus($updateData['humus']);
            }

            if (isset($updateData['valid_from'])) {
                $recommendation->setValidFrom(new DateTime($updateData['valid_from']));
            }

            if (isset($updateData['sampling_type_ids']) && is_array($updateData['sampling_type_ids'])) {
                $recommendation->setSamplingTypeIds($updateData['sampling_type_ids']);
            }

            if (isset($updateData['results']) && is_array($updateData['results'])) {
                $this->updateRecommendationResults($recommendation, $updateData['results']);
            }

            if (isset($updateData['comments']) && is_array($updateData['comments'])) {
                $this->updateRecommendationComments($recommendation, $updateData['comments']);
            }

            if (isset($updateData['susces']) && is_array($updateData['susces'])) {
                $this->updateRecommendationSusces($recommendation, $updateData['susces']);
            }

            if (isset($updateData['vra_orders']) && is_array($updateData['vra_orders'])) {
                $this->updateRecommendationVraOrders($recommendation, $updateData['vra_orders']);
            }

            $this->changeRecommendationStatus($recommendation, Recommendation::STATUS_FOR_APPROVE);

            $this->em->flush();
            $conn->commit();
        } catch (Exception $e) {
            $conn->rollBack();

            throw $e;
        }

        return $recommendation;
    }

    public function getRecommendationFarmingYear(int $recommendationId)
    {
        $recommendationDate = $this->recommendationManager->getRecommendationDate($recommendationId);

        $farmYear = (int)date('Y', strtotime($recommendationDate));
        $month = (int)date('n', strtotime($recommendationDate));

        if ($month > 9) {
            $farmYear = $farmYear + 1;
        }

        return ($farmYear - 1) . '/' . $farmYear;
    }

    public function changeRecommendationStatus(Recommendation $recommendation, string $status, string $declineReason = null): Recommendation
    {
        switch ($status) {
            case Recommendation::STATUS_DELIVERED:
                $declineReason = null;
                $this->recommendationWorkflowService->updateStatus($recommendation, Recommendation::TRANSITION_COMPLETE_RECOMMENDATION);

                break;
            case Recommendation::STATUS_DECLINED:
                $this->recommendationWorkflowService->updateStatus($recommendation, Recommendation::TRANSITION_DECLINE_RECOMMENDATION);

                break;
            case Recommendation::STATUS_FOR_APPROVE:
                $declineReason = null;
                $this->recommendationWorkflowService->updateStatus($recommendation, Recommendation::TRANSITION_FOR_APPROVE_RECOMMENDATION);

                break;
        }

        if ($recommendation->getStatus() !== $status) {
            throw new ConflictHttpException('Status not updated', null, 409);
        }

        $recommendation->setDeclineReason($declineReason);
        $this->em->persist($recommendation);
        $this->em->flush();

        return $recommendation;
    }

    /**
     * @throws \Doctrine\DBAL\DBALException
     *
     * @return false|string
     */
    public function findLabElementGroupDate(LabElementGroup $labElementGroup)
    {
        $packageGridPoint = $this->em->getRepository(PackageGridPoints::class)->findOneBy(
            [
                'state' => PackageGridPoints::STATE_RECEIVED_IN_LAB,
                'packageId' => $labElementGroup->getPackageId(),
                'plotUuid' => $labElementGroup->getPlotUuid(),
            ],
            ['stateUpdatedAt' => 'DESC']
        );

        if ($packageGridPoint) {
            return $packageGridPoint->getStateUpdatedAt()->format('Y-m-d');
        }

        return date('Y-m-d');
    }

    /**
     * @param ?array $filters
     *
     * @throws \Doctrine\DBAL\DBALException
     */
    public function search($page, $limit, ?array $filters = null)
    {
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();
        $offset = ($page - 1) * $limit;

        return $this->recommendationManager->findRecommendations($serviceProviderId, $filters, true, $limit, $offset);
    }

    public function getVraOrdersByRecommendationId(int $recommendationId)
    {
        $recommendationVraOrdersRepo = $this->em->getRepository(RecommendationVraOrders::class);

        return $recommendationVraOrdersRepo->getVraOrdersByRecommendationId($recommendationId);
    }

    /**
     * @param ?int $serviceProviderId
     *
     * @throws \Doctrine\DBAL\DBALException
     */
    public function getSoilAnalyzes(array $filters, ?int $serviceProviderId = null)
    {
        if (!$serviceProviderId) {
            $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();
        }

        return $this->labInterpretationClassesConfigRepository->soilAnalyzesData($serviceProviderId, $filters);
    }

    /**
     * @throws \Doctrine\DBAL\DBALException
     *
     * @return array|false|mixed
     */
    public function listByPlot(string $plotUuid, string $fromDate, string $toDate, string $status)
    {
        return $this->recommendationManager->findRecommendationsByPlot($plotUuid, $fromDate, $toDate, $status);
    }

    /**
     * @param ?int $page
     * @param ?int $limit
     *
     * @throws \Doctrine\DBAL\DBALException
     */
    public function findRecommendations(array $filter, string $groupBy, array $orderBy, ?int $page, ?int $limit): array
    {
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();
        $offset = ($page - 1);

        return $this->recommendationReportManager->getRecommendationReport($serviceProviderId, $filter, $groupBy, $orderBy, $limit, $offset);
    }

    public function getRecommendationHistory(Recommendation $recommendation)
    {
        /** @var RecommendationLogRepository  */
        $recommendationLogRepo = $this->em->getRepository(RecommendationLog::class);
        $recommendationHistory = $recommendationLogRepo->getHistory($recommendation);

        /** @var RecommendationCommentLogRepository */
        $recommendationCommentLogRepo = $this->em->getRepository(RecommendationCommentLog::class);
        $recommendationCommentHistory = $recommendationCommentLogRepo->getHistory($recommendation);

        /** @var RecommendationElementsResultsLogRepository */
        $recommendationResultsLogRepo = $this->em->getRepository(RecommendationElementsResultsLog::class);
        $recommendationResultsHistory = $recommendationResultsLogRepo->getHistory($recommendation);

        /** @var RecommendationElementsSuscesLogRepository */
        $recommendationSuscesLogRepo = $this->em->getRepository(RecommendationElementsSuscesLog::class);
        $recommendationSuscesHistory = $recommendationSuscesLogRepo->getHistory($recommendation);

        /** @var RecommendationVraOrdersLogRepository */
        $recommendationVraLogRepo = $this->em->getRepository(RecommendationVraOrdersLog::class);
        $recommendationVraHistory = $recommendationVraLogRepo->getHistory($recommendation);

        // Each history has keys that represents string with date and editor name
        $historyKeys = array_unique(
            array_merge(
                array_keys($recommendationHistory),
                array_keys($recommendationCommentHistory),
                array_keys($recommendationResultsHistory),
                array_keys($recommendationSuscesHistory),
                array_keys($recommendationVraHistory),
            )
        );

        $results = [];
        foreach ($historyKeys as $historyKey) {
            $updatedAt = $recommendationHistory[$historyKey]['updatedAt']
                ?? $recommendationCommentHistory[$historyKey]['updatedAt']
                ?? $recommendationResultsHistory[$historyKey]['updatedAt']
                ?? $recommendationSuscesHistory[$historyKey]['updatedAt']
                ?? $recommendationVraHistory[$historyKey]['updatedAt']
                ?? null;

            $results[$updatedAt] = [
                'recommendation' => $recommendationHistory[$historyKey] ?? null,
                'comments' => $recommendationCommentHistory[$historyKey] ?? null,
                'results' => $recommendationResultsHistory[$historyKey] ?? null,
                'susces' => $recommendationSuscesHistory[$historyKey] ?? null,
                'vraOrders' => $recommendationVraHistory[$historyKey] ?? null,
            ];
        }

        uksort($results, function ($a, $b) {
            return $a < $b;
        });

        return array_values($results);
    }

    private function updateRecommendationResults(Recommendation $recommendation, array $results)
    {
        $results = array_map(function ($result) {
            return [
                'result_element' => $result['result_element'] ?? null,
                'result_element_value' => $result['result_element_value'] ?? null,
            ];
        }, $results);
        $existingResults = $recommendation->getRecommendationElementsResults();

        // Delete the results that are not present in $results array
        foreach ($existingResults as $existingResult) {
            $existingResultData = [
                'result_element' => $existingResult->getResultElement(),
                'result_element_value' => $existingResult->getValue(),
            ];

            if (false !== array_search($existingResultData, $results)) {
                continue;
            }

            $this->em->remove($existingResult);
        }

        // Create the new results that are not present in the db
        $existingResultsData = $existingResults->map(function (RecommendationElementsResults $existingResult) {
            return [
                'result_element' => $existingResult->getResultElement(),
                'result_element_value' => $existingResult->getValue(),
            ];
        })->toArray();

        foreach ($results as $resultData) {
            if (false !== array_search($resultData, $existingResultsData)) {
                continue;
            }

            $recommendationElementResult = new RecommendationElementsResults();
            $recommendationElementResult->setRecommendation($recommendation);
            $recommendationElementResult->setResultElement($resultData['result_element']);
            $recommendationElementResult->setValue($resultData['result_element_value']);
            $this->em->persist($recommendationElementResult);
        }
    }

    private function updateRecommendationComments(Recommendation $recommendation, array $comments)
    {
        $comments = array_map(function ($comment) {
            return [
                'result_element' => $comment['result_element'] ?? null,
                'comment_text' => $comment['comment_text'] ?? null,
            ];
        }, $comments);
        $existingComments = $recommendation->getRecommendationComments();

        // Delete the comments that are not present in the $comments array
        foreach ($existingComments as $existingComment) {
            $existingCommentData = [
                'result_element' => $existingComment->getParam(),
                'comment_text' => $existingComment->getValue(),
            ];

            if (false !== array_search($existingCommentData, $comments)) {
                continue;
            }

            $this->em->remove($existingComment);
        }

        // Create the comments that are not present in the db
        $existingCommentsData = $existingComments->map(function (RecommendationComment $exisitngComment) {
            return [
                'result_element' => $exisitngComment->getParam(),
                'comment_text' => $exisitngComment->getValue(),
            ];
        })->toArray();

        foreach ($comments as $commentData) {
            if (false !== array_search($commentData, $existingCommentsData)) {
                continue;
            }

            $commentType = isset($commentData['result_element'])
                ? RecommendationComment::COMMENT_TYPE_ELEMENT
                : RecommendationComment::COMMENT_TYPE_CUSTOM;

            $recommendationComment = new RecommendationComment();
            $recommendationComment->setRecommendation($recommendation);
            $recommendationComment->setParam($commentData['result_element']);
            $recommendationComment->setCommentType($commentType);
            $recommendationComment->setValue($commentData['comment_text']);
            $this->em->persist($recommendationComment);
        }
    }

    private function updateRecommendationSusces(Recommendation $recommendation, array $susces)
    {
        $susces = array_map(function ($susce) {
            return [
                'element' => $susce['element'] ?? null,
                'value' => $susce['value'] ?? null,
            ];
        }, $susces);
        $existingSusces = $recommendation->getRecommendationElementsSusces();

        // Delete the susces that are not present in the $susces array
        foreach ($existingSusces as $existingSusce) {
            $existingSusceData = [
                'element' => $existingSusce->getElement(),
                'value' => $existingSusce->getValue(),
            ];

            if (false !== array_search($existingSusceData, $susces)) {
                continue;
            }

            $this->em->remove($existingSusce);
        }

        // Create the susces that are not present in the db
        $existingSuscesData = $existingSusces->map(function (RecommendationElementsSusces $existingSusce) {
            return [
                'element' => $existingSusce->getElement(),
                'value' => $existingSusce->getValue(),
            ];
        })->toArray();

        foreach ($susces as $susceData) {
            if (false !== array_search($susceData, $existingSuscesData)) {
                continue;
            }

            $recommendationSusces = new RecommendationElementsSusces();
            $recommendationSusces->setRecommendation($recommendation);
            $recommendationSusces->setElement($susceData['element']);
            $recommendationSusces->setValue($susceData['value']);
            $this->em->persist($recommendationSusces);
        }
    }

    private function updateRecommendationVraOrders(Recommendation $recommendation, array $vraOrders)
    {
        $vraOrders = array_map(function ($vraOrder) {
            return [
                'id' => $vraOrder['id'] ?? null,
                'type' => $vraOrder['type'] ?? null,
            ];
        }, $vraOrders);
        $existingVraOrders = $recommendation->getRecommendationVraOrders();

        // Delete the recommendation vra orders that are not present in the $vraOrders array
        foreach ($existingVraOrders as $existingVraOrder) {
            $existingSusceData = [
                'id' => $existingVraOrder->getVraOrderId(),
                'type' => $existingVraOrder->getVraOrderType(),
            ];

            if (false !== array_search($existingSusceData, $vraOrders)) {
                continue;
            }

            $this->em->remove($existingVraOrder);
        }

        // Create the recommendation vra orders that are not present in the db
        $existingVraOrdersData = $existingVraOrders->map(function (RecommendationVraOrders $existingVraOrder) {
            return [
                'id' => $existingVraOrder->getVraOrderId(),
                'type' => $existingVraOrder->getVraOrderType(),
            ];
        })->toArray();

        foreach ($vraOrders as $vraOrderData) {
            if (false !== array_search($vraOrderData, $existingVraOrdersData)) {
                continue;
            }

            $recommendationVraOrder = new RecommendationVraOrders();
            $recommendationVraOrder->setRecommendation($recommendation);
            $recommendationVraOrder->setVraOrderId($vraOrderData['id']);
            $recommendationVraOrder->setVraOrderType($vraOrderData['type']);
            $this->em->persist($recommendationVraOrder);
        }
    }
}
