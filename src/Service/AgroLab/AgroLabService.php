<?php

namespace App\Service\AgroLab;

use App\Service\AbstractService;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Form\FormFactoryInterface;

class AgroLabService extends AbstractService
{
    private $em;
    private $couchDBService;

    public function __construct(
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        CouchDBClientService $couchDBService
    ) {
        parent::__construct($formFactory);
        $this->em = $entityManager;
        $this->couchDBService = $couchDBService;
    }

    /**
     * @param array{host:string, port:int, dbname:string, user:string, password:string} $config
     */
    public function getCouchDbLabNumbers(array $barcodes, array $config)
    {
        try {
            $this->couchDBService->init($config);
            $query = $this->couchDBService->client->createViewQuery('barcodes', 'barcodes-view');
            $query->setKeys(array_map('intval', $barcodes));
            $results = $query->execute();

            return $results->toArray();
        } catch (Exception $e) {
            throw $e;
        }
    }
}
