<?php

namespace App\Service\AgroLab;

use App\Service\AbstractService;
use Doctrine\CouchDB\MangoClient;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Form\FormFactoryInterface;

class CouchDBClientService extends AbstractService
{
    public $client;

    /**
     * @var array{host:string, port:int, dbname:string, user:string, password:string}
     */
    public $config;
    private $em;
    private $parameters;

    public function __construct(
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        ParameterBagInterface $parameters
    ) {
        parent::__construct($formFactory);
        $this->em = $entityManager;
        $this->parameters = $parameters;
    }

    /**
     * @param array{host:string, port:int, dbname:string, user:string, password:string} $config
     */
    public function init($config)
    {
        $this->config = $config;
        $this->client = MangoClient::create($config);
    }
}
