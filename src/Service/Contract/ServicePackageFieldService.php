<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service\Contract;

use App\Entity\Contract\ServicePackage;
use App\Entity\Contract\ServicePackageField;
use App\Service\AbstractService;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Form\FormFactoryInterface;

class ServicePackageFieldService extends AbstractService
{
    private $em;

    public function __construct(FormFactoryInterface $formFactory, EntityManagerInterface $entityManager)
    {
        parent::__construct($formFactory);
        $this->em = $entityManager;
    }

    /**
     * @throws \Doctrine\DBAL\ConnectionException
     */
    public function addFields($data)
    {
        // TODO:: да се изпозлва ли batchSize ?
        $this->em->getConnection()->beginTransaction();

        try {
            foreach ($data as $packageFieldsData) {
                foreach ($packageFieldsData['params'] as $fieldData) {
                    $servicePackage = $this->em->getRepository(ServicePackage::class)->find($packageFieldsData['package_id']);

                    $packageField = new ServicePackageField();
                    $packageField->setArea($fieldData['area']);
                    $packageField->setPlotUuid($fieldData['plot_uuid']);
                    $packageField->setOrderUuid($fieldData['order_uuid']);
                    $packageField->setServicePackage($servicePackage);
                    $this->em->persist($packageField);
                }
            }
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();
            // TODO:: Custom errors ??
            throw $e;
        }
        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();

        return;
    }

    public function getPlotsIdByContractId($contractId, $filter = null)
    {
        $servicePackageFieldRepository = $this->em->getRepository(ServicePackageField::class);
        $qb = $servicePackageFieldRepository->getServicePackageFieldQueryBuilder($contractId, $filter);

        $qb->select(
            'servicePackageField.plotUuid'
        )->groupBy('servicePackageField.plotUuid');

        return $qb->getQuery()->getResult();
    }

    public function getOrdersIdByContractId($contractId, $filter = null)
    {
        $servicePackageFieldRepository = $this->em->getRepository(ServicePackageField::class);
        $qb = $servicePackageFieldRepository->getServicePackageFieldQueryBuilder($contractId, $filter);
        $qb->select(
            'servicePackageField.orderUuid'
        )->groupBy('servicePackageField.orderUuid');

        return $qb->getQuery()->getResult();
    }
}
