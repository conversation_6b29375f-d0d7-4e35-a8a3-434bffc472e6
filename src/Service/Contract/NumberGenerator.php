<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service\Contract;

use App\Entity\Contract\Number;
use App\Repository\Contract\NumberRepository;
use Doctrine\ORM\EntityManagerInterface;

class NumberGenerator
{
    private $numberRepository;
    private $em;

    public function __construct(NumberRepository $numberRepository, EntityManagerInterface $entityManager)
    {
        $this->numberRepository = $numberRepository;
        $this->em = $entityManager;
    }

    public function generate()
    {
        /** @var Number[] $lastNumbers */
        $lastNumbers = $this->numberRepository->findOneBy([], ['id' => 'DESC']);
        $newNumber = 1;

        if (!is_null($lastNumbers)) {
            $newNumber = $lastNumbers->getLastNumber() + 1;
        }

        $lastNumbers->setLastNumber($newNumber);
        $this->em->flush($lastNumbers);

        return $newNumber;
    }
}
