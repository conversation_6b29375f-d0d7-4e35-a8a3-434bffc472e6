<?php

namespace App\Service\Contract;

use App\Entity\Contract\Price;
use App\Entity\Payment;
use App\Form\Contract\PaymentType;
use App\Service\AbstractService;
use App\Service\Workflow\PaymentWorkflowService;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Security\Core\Security;

class PaymentService extends AbstractService
{
    private $em;
    private $security;
    private $paymentWorkflowService;

    public function __construct(
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        Security $security,
        PaymentWorkflowService $paymentWorkflowService
    ) {
        parent::__construct($formFactory);
        $this->em = $entityManager;
        $this->security = $security;
        $this->paymentWorkflowService = $paymentWorkflowService;
    }

    public function create(Price $price, $data)
    {
        $formType = PaymentType::class;
        $payment = new Payment();
        $this->handleData($formType, $payment, $data);

        $this->em->getConnection()->beginTransaction();

        try {
            $dateTime = new DateTime();
            $user = $this->security->getUser();

            $payment->setPrice($price);
            $payment->setCreatedAt($dateTime);
            $payment->setUsername($user->getUsername());

            $this->em->persist($payment);
            $this->em->flush();

            $this->paymentWorkflowService->updatePriceStatus($price);

            $this->em->getConnection()->commit();
        } catch (Exception $e) {
            $this->em->getConnection()->rollback();

            throw $e;
        }

        return $payment;
    }
}
