<?php

namespace App\Service\Contract;

use App\Entity\Contract\ServicePackage;
use App\Form\Contract\ServicePackageType;
use App\Service\AbstractService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\FormFactoryInterface;

class ServicePackageService extends AbstractService
{
    private $em;

    public function __construct(FormFactoryInterface $formFactory, EntityManagerInterface $entityManager)
    {
        parent::__construct($formFactory);
        $this->em = $entityManager;
    }

    public function addFields(ServicePackage $servicePackage, $data)
    {
        $this->handleData(ServicePackageType::class, $servicePackage, $data);
        $this->em->flush();
    }

    public function listPackagesByFilter(array $filter = []): array
    {
        $servicePackageRepository = $this->em->getRepository(ServicePackage::class);
        $servicePackages = $servicePackageRepository->getPackagesCustom($filter);

        return array_map(function ($servicePack) {
            $servicePack['samplingTypes'] = json_decode($servicePack['samplingTypes'], true);
            $servicePack['style'] = json_decode($servicePack['style'], true);

            return $servicePack;
        }, $servicePackages);
    }

    public function countPackagesByCustomerIdentifications(array $customerIdentifications, bool $availablePackage = false, array $packageStates = [])
    {
        $servicePackageRepository = $this->em->getRepository(ServicePackage::class);

        if ($availablePackage) {
            return $servicePackageRepository->countAvailablePackagesByIdentNumber($customerIdentifications, $packageStates);
        }

        return $servicePackageRepository->countPackagesByIdentNumber($customerIdentifications);
    }
}
