<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service\Contract;

use App\Entity\Contract;
use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Contract\SubscriptionPackageField;
use App\Service\AbstractService;
use App\Service\Protocol\ProtocolPackageFieldService;
use App\Service\Protocol\ProtocolService;
use App\Service\Workflow\SubscriptionFieldService;
use App\Service\Workflow\SubscriptionPackageService as WorkflowSubscriptionPackageService;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Security\Core\Security;

class SubscriptionPackageFieldService extends AbstractService
{
    private $em;
    private $workflowSubscriptionPackageService;
    private $workflowSubscriptionPackageFieldSer<PERSON>;
    private $protocolService;
    private $protocolPackageFieldService;
    private $security;

    public function __construct(
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        WorkflowSubscriptionPackageService $workflowSubscriptionPackageService,
        SubscriptionFieldService $workflowSubscriptionPackageFieldService,
        ProtocolService $protocolService,
        ProtocolPackageFieldService $protocolPackageFieldService,
        Security $security
    ) {
        parent::__construct($formFactory);
        $this->em = $entityManager;
        $this->workflowSubscriptionPackageService = $workflowSubscriptionPackageService;
        $this->workflowSubscriptionPackageFieldService = $workflowSubscriptionPackageFieldService;
        $this->protocolService = $protocolService;
        $this->protocolPackageFieldService = $protocolPackageFieldService;
        $this->security = $security;
    }

    public function getExistingPackageField($data)
    {
        $arrPackageIds = array_column($data, 'package_id');
        $packageIds = array_values(array_unique($arrPackageIds));

        $arrParams = array_column($data, 'params');
        $plotUuids = [];
        foreach ($arrParams as $arrParam) {
            $plotUuids = array_merge($plotUuids, array_column($arrParam, 'plot_uuid'));
        }
        $plotUuids = array_unique($plotUuids);

        $repo = $this->em->getRepository(SubscriptionPackageField::class);
        $params[] = ['field' => 'subscriptionPackage', 'type' => 'IN', 'value' => $packageIds];
        $params[] = ['field' => 'plotUuid', 'type' => 'IN', 'value' => $plotUuids];
        $query = $repo->findByFieldsQuery($params);

        return $query->getArrayResult();
    }

    /**
     * @throws \Doctrine\DBAL\ConnectionException
     *
     * @return array
     */
    public function addFields($data)
    {
        $this->em->getConnection()->beginTransaction();
        $packagesFieldData['package_field_type'] = Contract::TYPE_SUBSCRIPTION;

        try {
            $packagesFieldData['protocol'] = $this->protocolService->create();
            foreach ($data as $packageFieldsData) {
                $subscriptionPackage = $this->em->getRepository(SubscriptionPackage::class)->find($packageFieldsData['package_id']);
                $this->workflowSubscriptionPackageService->changeSubscriptionPackageState($subscriptionPackage, SubscriptionPackage::TRANSITION_IN_PROGRESS_STATE);
                foreach ($packageFieldsData['params'] as $fieldData) {
                    $parentPackageField = null;
                    if (isset($fieldData['parent']['plot_uuid'], $fieldData['parent']['order_uuid'])) {
                        $parentData = [
                            'plotUuid' => $fieldData['parent']['plot_uuid'],
                            'orderUuid' => $fieldData['parent']['order_uuid'],
                        ];
                        $parentPackageField = $this->em->getRepository(SubscriptionPackageField::class)->findOneBy($parentData);
                    }

                    $packageField = new SubscriptionPackageField();
                    $packageField->setArea($fieldData['area']);
                    $packageField->setPlotUuid($fieldData['plot_uuid']);
                    $packageField->setOrderUuid($fieldData['order_uuid']);
                    $packageField->setFarmId($fieldData['farm_id']);
                    $packageField->setSubscriptionPackage($subscriptionPackage);
                    $packageField->setParent($parentPackageField);
                    $this->workflowSubscriptionPackageFieldService->changeSubscriptionPackageFiledState($packageField, SubscriptionPackageField::TRANSITION_PLOT_ACTIVE);
                    $this->workflowSubscriptionPackageFieldService->changeSubscriptionPackageFiledState($packageField, SubscriptionPackageField::TRANSITION_PLOT_GRIDDED);
                    $this->em->persist($packageField);
                    $packagesFieldData['package_field_ids'][] = $packageField->getId();
                }
            }
            $this->protocolPackageFieldService->create($packagesFieldData);
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();
            // TODO:: Custom errors ??
            throw $e;
        }
        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();

        return ['protocolId' => $packagesFieldData['protocol']->getId(), 'protocolDate' => $packagesFieldData['protocol']->getDate()->format('Y-m-d')];
    }

    public function updateFieldFarm(string $plotUuid, int $farmId): bool
    {
        $this->em->getConnection()->beginTransaction();

        try {
            $subscriptionPackageFields = $this->em->getRepository(SubscriptionPackageField::class)->findBy(['plotUuid' => $plotUuid]);

            foreach ($subscriptionPackageFields as $subscriptionPackageField) {
                $subscriptionPackageField->setFarmId($farmId);
            }
        } catch (Exception $e) {
            $this->em->getConnection()->rollBack();

            throw $e;
        }
        $this->em->flush();
        $this->em->clear();
        $this->em->getConnection()->commit();

        return true;
    }

    public function countFieldsByStates($customerIdentifications)
    {
        $servicePackageFieldRepository = $this->em->getRepository(SubscriptionPackageField::class);

        return $servicePackageFieldRepository->countFieldsByStates($customerIdentifications);
    }

    public function getOrdersIdByContractId($contractId, $filter = null)
    {
        $subscriptionPackageFieldRepository = $this->em->getRepository(SubscriptionPackageField::class);

        $qb = $subscriptionPackageFieldRepository->getSubscriptionPackageFieldQueryBuilder($contractId, $filter);
        $qb->select(
            'subscriptionPackageField.orderUuid'
        );

        return $qb->getQuery()->getResult();
    }

    public function getPlotsUuidWithStateByContractId($contractId, $filter = null)
    {
        $subscriptionPackageFieldRepository = $this->em->getRepository(SubscriptionPackageField::class);
        $qb = $subscriptionPackageFieldRepository->getSubscriptionPackageFieldQueryBuilder($contractId, $filter);

        $qb->select(
            'subscriptionPackageField.plotUuid',
            'subscriptionPackageField.fieldState'
        );

        return $qb->getQuery()->getResult();
    }

    public function getFieldsABOverview(array $filers, int $limit = 1, int $page = 1)
    {
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();
        $subscriptionPackageFieldRepository = $this->em->getRepository(SubscriptionPackageField::class);

        if ($page > 0) {
            $page = --$page;
        }

        $total = $subscriptionPackageFieldRepository->getFieldsABOverview($serviceProviderId, $filers, $limit, $page, false, true);
        $fieldData = $subscriptionPackageFieldRepository->getFieldsABOverview($serviceProviderId, $filers, $limit, $page);

        return [
            'total_items' => $total,
            'total_pages' => ceil($total / $limit),
            'items' => $fieldData,
        ];
    }
}
