<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service\Contract;

use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Package;
use App\Form\Contract\SubscriptionPackageType;
use App\Service\AbstractService;
use App\Service\DurationTypeService;
use App\Service\GeoSCANAPIClient;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\FormFactoryInterface;

class SubscriptionPackageService extends AbstractService
{
    private $em;
    private $durationTypeService;
    private $geoSCANAPIClient;

    public function __construct(FormFactoryInterface $formFactory, EntityManagerInterface $entityManager, DurationTypeService $durationTypeService, GeoSCANAPIClient $geoSCANAPIClient)
    {
        parent::__construct($formFactory);
        $this->em = $entityManager;
        $this->durationTypeService = $durationTypeService;
        $this->geoSCANAPIClient = $geoSCANAPIClient;
    }

    public function addFields(SubscriptionPackage $subscriptionPackage, $data)
    {
        $this->handleData(SubscriptionPackageType::class, $subscriptionPackage, $data);
        $this->em->flush();
    }

    public function listPackagesByFilter(array $filter = [], $groupByPackagePeriod = false): array
    {
        $subscriptionPackagesByPeriod = [];

        $subscriptionPackageRepository = $this->em->getRepository(SubscriptionPackage::class);
        $subscriptionPackages = $subscriptionPackageRepository->getPackagesCustom($filter);
        $subscriptionPackages = array_map(function ($subsPack) use (&$subscriptionPackagesByPeriod) {
            $subsPack['samplingTypes'] = json_decode($subsPack['samplingTypes'], true);
            $subsPack['style'] = json_decode($subsPack['style'], true);

            // @TODO should be checked all functions using this method  and rework this value assignment
            $subsPack['startDate'] = $subsPack['packageStartDate'];
            $subsPack['endDate'] = $subsPack['packageEndDate'];

            $subscriptionPackagesByPeriod[$subsPack['periodNumber']][] = $subsPack;

            return $subsPack;
        }, $subscriptionPackages);

        if ($groupByPackagePeriod) {
            return $subscriptionPackagesByPeriod;
        }

        return $subscriptionPackages;
    }

    public function countPackagesByCustomerIdentifications(array $customerIdentifications, bool $availablePackage = false, array $packageStates = [])
    {
        $subscriptionPackageRepository = $this->em->getRepository(SubscriptionPackage::class);

        if ($availablePackage) {
            return $subscriptionPackageRepository->countAvailablePackagesByIdentNumber($customerIdentifications, $packageStates);
        }

        return $subscriptionPackageRepository->countAllPackagesByIdentNumber($customerIdentifications);
    }

    public function deactivateStation(SubscriptionPackage $subscriptionPackage)
    {
        $packageWS = $this->em->getRepository(Package::class)->findOneBy(['slugShort' => 'WS']);

        if (!$packageWS || $packageWS->getId() !== $subscriptionPackage->getPackage()->getId()) {
            return false;
        }

        // Check only WS subscriptions
        $allActivePackagesWS = $this->em->getRepository(SubscriptionPackage::class)->findBy([
            'contract' => $subscriptionPackage->getContract(),
            'package' => $packageWS->getId(),
            'status' => 'Active',
        ]);

        $expiredPackage = array_filter($allActivePackagesWS, function ($subscriptionPackage) {
            return 'Expired' === $subscriptionPackage->getStatus();
        });

        // Deactivate Stations by Contract when the Last subscriptions package become Expired
        if (count($allActivePackagesWS) === count($expiredPackage)) {
            $this->geoSCANAPIClient->deactivateStationsByContract($subscriptionPackage->getContract());
        }
    }

    public function deactivateIntegration(SubscriptionPackage $subscriptionPackage)
    {
        $packageFT = $this->em->getRepository(Package::class)->findOneBy(['slugShort' => 'FT']);

        if (!$packageFT || $packageFT->getId() !== $subscriptionPackage->getPackage()->getId()) {
            dump($packageFT->getId(), $subscriptionPackage->getPackage()->getId());

            return false;
        }
        dump(22222);

        $organizationId = $subscriptionPackage->getContract()->getOrganizationId();

        // Calculate the date from which we need to check for coverage (day after current package expires)
        $gapStartDate = new DateTime($subscriptionPackage->getEndDate()->format('Y-m-d H:i:s'));
        $gapStartDate->modify('+1 day');

        // Check if there are any FT subscription packages for this organization that cover the gap period
        // We need packages that:
        // 1. Start on or before the gap start date (startDateBefore <= gapStartDate)
        // 2. End after the gap start date (endDate > gapStartDate)
        // This finds packages that provide coverage for the gap period
        $subscriptionPackageRepository = $this->em->getRepository(SubscriptionPackage::class);
        $coveringFTPackages = $subscriptionPackageRepository->getActiveSubscriptions([
            'integration' => 'FT',
            'organizationId' => $organizationId,
            'startDateBefore' => $gapStartDate, // Packages that start on or before the gap start date
            'endDate' => $gapStartDate, // Packages that end after the gap start date
        ]);
        dump(33333);

        // If there are no FT packages covering the gap period, deactivate integrations
        if (empty($coveringFTPackages)) {
            // Get country code from the contract's service provider
            $contract = $subscriptionPackage->getContract();
            $serviceProvider = $contract->getServiceProvider();
            $countryCode = $serviceProvider->getCountryCode();

            $this->geoSCANAPIClient->deactivateIntegration($organizationId, $packageFT, $countryCode);
        }
    }
}
