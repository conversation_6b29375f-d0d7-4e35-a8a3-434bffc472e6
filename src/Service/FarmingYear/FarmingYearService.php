<?php

namespace App\Service\FarmingYear;

use App\Service\AbstractService;
use Doctrine\DBAL\ParameterType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\FormFactoryInterface;

class FarmingYearService extends AbstractService
{
    private EntityManagerInterface $em;

    public function __construct(
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager
    ) {
        parent::__construct($formFactory);
        $this->em = $entityManager;
    }

    public function getFarmingYearsByOrganization(int $organizationId)
    {
        $conn = $this->em->getConnection();
        $farmingYearsSql = 'select farm_years_by_organization(:organizationId)';
        $params = [
            'organizationId' => $organizationId,
        ];

        $paramTypes = [
            'organizationId' => ParameterType::INTEGER,
        ];

        $farmingYearsStmt = $conn->executeQuery($farmingYearsSql, $params, $paramTypes);
        $farmingYearsResult = $farmingYearsStmt->fetchOne();

        return json_decode($farmingYearsResult, true);
    }
}
