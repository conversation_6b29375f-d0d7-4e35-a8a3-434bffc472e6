<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service;

use App\Entity\Analysis\LabElementGroup;
use App\Entity\Analysis\PackageGridPoints;
use App\Entity\Contract;
use App\Entity\Contract\Service;
use App\Entity\Contract\ServicePackageField;
use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Contract\SubscriptionPackageField;
use App\Entity\Package;
use App\Factory\ContractFactory;
use App\Factory\ContractFormTypeFactory;
use App\Model\GeoSCAN\Organization;
use App\Service\Analysis\LabElementsResultsService;
use App\Service\Contract\NumberGenerator;
use App\Service\Currency\CurrencyService;
use App\Service\Workflow\SubscriptionPackageService as WorkflowSubscriptionPackageService;
use <PERSON>\ORM\Configuration;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Security;

class ContractService extends AbstractService
{
    private $contractFormTypeFactory;
    private $contractFactory;
    private $numberGenerator;
    private $em;
    private $currencyService;
    private $security;
    private $workFlowSubscriptionPackageService;
    private $labElementsResultsService;
    private $geoSCANAPIClient;

    public function __construct(
        ContractFormTypeFactory $contractFormTypeFactory,
        ContractFactory $contractFactory,
        NumberGenerator $numberGenerator,
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        CurrencyService $currencyService,
        Security $security,
        LabElementsResultsService $labElementsResultsService,
        WorkflowSubscriptionPackageService $workFlowSubscriptionPackageService,
        GeoSCANAPIClient $geoSCANAPIClient
    ) {
        parent::__construct($formFactory);
        $this->contractFormTypeFactory = $contractFormTypeFactory;
        $this->contractFactory = $contractFactory;
        $this->numberGenerator = $numberGenerator;
        $this->em = $entityManager;
        $this->currencyService = $currencyService;
        $this->security = $security;
        $this->workFlowSubscriptionPackageService = $workFlowSubscriptionPackageService;
        $this->labElementsResultsService = $labElementsResultsService;
        $this->geoSCANAPIClient = $geoSCANAPIClient;
    }

    /**
     * @param string $type
     * @param array $data
     *
     * @throws \Doctrine\DBAL\ConnectionException
     *
     * @return null|Contract\Service|Contract\Subscription
     */
    public function create($type, $data)
    {
        try {
            $formType = $this->contractFormTypeFactory->get($type);
            $contract = $this->contractFactory->get($type);
        } catch (Exception $e) {
            throw new NotFoundHttpException(null, null, 404);
        }

        $this->handleData($formType, $contract, $data);
        $this->em->getConnection()->beginTransaction();

        try {
            $currency = $this->currencyService->getCurrency();
            $number = $this->numberGenerator->generate();
            $contract->setStatus(Contract::STATUS_NEW);
            $contract->setNumber($number);
            $contract->setCurrency($currency);
            $this->em->persist($contract);
            $this->em->flush();
            $this->em->getConnection()->commit();
        } catch (Exception $e) {
            $this->em->getConnection()->rollback();

            throw $e;
        }

        return $contract;
    }

    public function update(Contract $contract, $data)
    {
        try {
            $formType = $this->contractFormTypeFactory->getByObject($contract);
        } catch (Exception $e) {
            throw new NotFoundHttpException(null, null, 404);
        }

        $this->handleData($formType, $contract, $data);
        $this->em->flush();
    }

    public function getContractFields(Contract $contract, string $type, int $packageId)
    {
        $contractRepository = $this->em->getRepository(Contract::class);

        return $contractRepository->getContractFields($contract, $type, $packageId);
    }

    public function removeFieldsFromContract(Contract $contract, array $plotUuids, array $orderUuids)
    {
        $this->em->getConnection()->beginTransaction();

        try {
            $packageGridPointsRepo = $this->em->getRepository(PackageGridPoints::class);
            $labElementGroupRepo = $this->em->getRepository(LabElementGroup::class);

            if ($contract instanceof Service) {
                $type = Contract::TYPE_SERVICE;
                $packageFieldRepo = $this->em->getRepository(ServicePackageField::class);
            } else {
                $type = Contract::TYPE_SUBSCRIPTION;
                $packageFieldRepo = $this->em->getRepository(SubscriptionPackageField::class);
            }

            $fieldsToRemove = $packageFieldRepo->getFieldsToRemove($contract, $plotUuids, $orderUuids);

            if (!($fieldsToRemove['canRemove'] ?? false)) {
                throw new Exception('Cannot remove fields', 409);
            }

            // Remove the package grid points (and lab element resulte - CASCADE)
            $packageGridPointsRepo->removeByPackageFields($fieldsToRemove['ids'], $type);

            // Remove the lab element groups
            $labElementGroupRepo->removeByPackageFields($fieldsToRemove['ids'], $type);

            // Remove the fields
            $removedFieldsCount = $packageFieldRepo->removeByIds($fieldsToRemove['ids']);

            /*
             * Set the subscriptionPackage state to 'In Progress' if all fields are removed from the package.
             * (WaitingForPlotStateSubscriptionPackageGuard).
             */
            if ($type = Contract::TYPE_SUBSCRIPTION) {
                $params[] = ['field' => 'id', 'type' => 'in', 'value' => $fieldsToRemove['packageIds']];
                $this->workFlowSubscriptionPackageService->updateByFields($params, SubscriptionPackage::TRANSITION_WAITING_FOR_PLOT_STATE);
            }

            $this->em->flush();
            $this->em->getConnection()->commit();
        } catch (Exception $e) {
            $this->em->getConnection()->rollback();

            throw $e;
        }

        return $removedFieldsCount;
    }

    /**
     * @throws Exception
     */
    public function delete(Contract $contract)
    {
        try {
            $qb = $this->em->getRepository(Contract::class)->createQueryBuilder('contract');
            $qb->where('contract.id = :contractId')->setParameter('contractId', $contract->getId())
                ->delete();

            $qb->getQuery()->getResult();
        } catch (Exception $e) {
            throw new Exception('Error deleting contract.', 409);
        }
    }

    /**
     * @return bool
     */
    public function hasPackage(Contract $contract, $status, $slugShort)
    {
        $hasPackage = false;
        $package = $this->em->getRepository(Package::class)->findOneBy(['slugShort' => $slugShort]);

        if (!$package) {
            return false;
        }

        $subscriptionPackages = $contract->getSubscriptionPackages()->filter(function ($subscriptionPackage) use ($package, $status) {
            return $subscriptionPackage->getStatus() === $status && $subscriptionPackage->getPackage()->getId() === $package->getId();
        });

        if ($subscriptionPackages->count() > 0) {
            $hasPackage = true;
        }

        return $hasPackage;
    }

    public function getSelectForSamplingCardData(array $customerIdentifications)
    {
        $subscriptionPackageFieldRepository = $this->em->getRepository(SubscriptionPackageField::class);

        $filter['fieldsState'] = [
            SubscriptionPackageField::STATE_GRIDDED,
            SubscriptionPackageField::STATE_FOR_SAMPLING,
            SubscriptionPackageField::STATE_CELLS_SELECTED,
            SubscriptionPackageField::STATE_SAMPLING,
            SubscriptionPackageField::STATE_SAMPLED,
        ];

        $filter['is_sampling'] = true;

        return $subscriptionPackageFieldRepository->countFieldsByStates($customerIdentifications, $filter);
    }

    public function getManageWeatherStationsCardData($params)
    {
        $contractRepository = $this->em->getRepository(Contract::class);
        $stationContractsQb = $contractRepository->getStationContractsQb(json_decode($params['filter']['customer_identification']));
        $stationContracts = $stationContractsQb->select('contract.id')->getQuery()->getResult();
        $allItems = $contractRepository->countAllAmountsForContractsByFilter($params)['Amount'];

        return [
            'all_items' => (int)$allItems,
            'station_contracts_id' => array_column($stationContracts, 'id'),
        ];
    }

    public function getActiveIntegrationsByCustomerIdentifications($slug, $customerIdentifications)
    {
        $contractRepository = $this->em->getRepository(Contract::class);

        $qb = $contractRepository->getPackagesBySlugQb($slug, $customerIdentifications, 'Active');
        $qb->select('coalesce(sup.id, sep.id) as packageId');

        $result = $qb->getQuery()->getArrayResult();

        return count($result);
    }

    public function getIntegrationsCardList(int $userId, ?string $customerIdentification, string $packageSlug, int $limit, int $offset, $count = false)
    {
        $config = new Configuration();
        $config->addCustomNumericFunction('ROUND', 'Oro\ORM\Query\AST\Functions\Numeric\Round');
        $config->addCustomStringFunction('CAST', 'Oro\ORM\Query\AST\Functions\Cast');

        $organizations = $this->geoSCANAPIClient->getOrganizations(null, null, null, ['oldId' => $userId])->data ?? [];
        $customerIdentifications = [$customerIdentification];

        if (!$customerIdentification) {
            $customerIdentifications = array_map(function (Organization $organization) {
                return $organization->getIdentityNumber();
            }, $organizations);
        }

        $contractRepository = $this->em->getRepository(Contract::class);
        $qb = $contractRepository->getPackagesBySlugQb($packageSlug, $customerIdentifications);
        $qb->select(
            'CAST(ROUND(sum(coalesce(sup.amount, 0) + coalesce(sep.amount ,0)), 0) as int) as allAmounts',
            'contract.customerIdentification as customerIdentification'
        );
        $qb->groupBy('contract.customerIdentification');

        if ($count) {
            $query = $qb->getQuery();

            return count($query->getResult());
        }

        $qb->setMaxResults($limit);
        $qb->setFirstResult($offset);

        $query = $qb->getQuery();

        return array_map(function ($item) use ($organizations) {
            foreach ($organizations as $organization) {
                if ($organization->getIdentityNumber() === $item['customerIdentification']) {
                    $item['organizationName'] = $organization->getName();
                    $item['organizationId'] = $organization->getId();

                    break;
                }
            }

            return $item;
        }, $query->getResult());
    }

    /**
     * @return array
     */
    public function findOverlapContracts(Contract $contract, string $status, bool $hasStation)
    {
        $contractRepository = $this->em->getRepository(Contract::class);
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();

        return $contractRepository->findOverlapContracts($contract, $serviceProviderId, $status, $hasStation);
    }

    public function getCardsData(int $userId)
    {
        $organizations = $this->geoSCANAPIClient->getOrganizations(null, null, null, ['oldId' => $userId])->data ?? [];
        $customerIdentifications = array_map(function (Organization $organization) {
            return $organization->getIdentityNumber();
        }, $organizations);
        $manageWeatherStationsCardParams['filter'] = [
            'has_station' => 1,
            'contain_fields' => 0,
            'customer_identification' => json_encode($customerIdentifications),
        ];

        $manageWeatherStationsCardData = $this->getManageWeatherStationsCardData($manageWeatherStationsCardParams);
        $requestData = ['station_contracts_id' => $manageWeatherStationsCardData['station_contracts_id']];
        $cardsData = $this->geoSCANAPIClient->getCardsData($requestData);

        if (isset($cardsData['select-for-sampling'])) {
            $cardsData['select-for-sampling']['plotsByState'] = $this->getSelectForSamplingCardData($customerIdentifications);
        }

        if (isset($cardsData['manage-stations'])) {
            $cardsData['manage-stations']['allItems'] = $manageWeatherStationsCardData['all_items'];
        }

        if (isset($cardsData['approve-results'])) {
            $cardsData['approve-results']['allItems'] = $this->labElementsResultsService->getResultsByCustomerIdentification($customerIdentifications, 'For approve');
        }

        return array_values($cardsData);
    }
}
