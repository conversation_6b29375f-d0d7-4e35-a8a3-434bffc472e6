<?php

namespace App\Service\Currency;

use App\Entity\Currency;
use App\Service\AbstractService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Security\Core\Security;

class CurrencyService extends AbstractService
{
    private $em;
    private $security;

    public function __construct(
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        Security $security
    ) {
        parent::__construct($formFactory);
        $this->em = $entityManager;
        $this->security = $security;
    }

    public function getCurrency()
    {
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();

        return $this->em->getRepository(Currency::class)->findOneBy(['serviceProvider' => $serviceProviderId]);
    }
}
