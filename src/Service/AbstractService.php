<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service;

use Doctrine\DBAL\Query\QueryBuilder as DbalQueryBuilder;
use Doctrine\ORM\QueryBuilder as OrmQueryBuilder;
use Pagerfanta\Adapter\DoctrineDbalAdapter;
use Pagerfanta\Adapter\DoctrineORMAdapter;
use Pagerfanta\Pagerfanta;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

abstract class AbstractService
{
    protected $formFactory;

    public function __construct(FormFactoryInterface $formFactory)
    {
        $this->formFactory = $formFactory;
    }

    protected function handleData($formType, $object, array $data = null, array $options = [], $devMode = false)
    {
        $form = $this->formFactory->create($formType, $object, $options);

        $form->submit($data, false);

        if ($devMode) {
            foreach ($form->getErrors(true) as $error) {
                echo $error->getCause();
            }
        }

        if (!$form->isValid()) {
            throw new BadRequestHttpException($form->getErrors(true));
        }

        return $form;
    }

    protected function paginateORMQueryBuilder(OrmQueryBuilder $queryBuilder, int $page = 1, int $limit = 10): array
    {
        $adapter = new DoctrineORMAdapter($queryBuilder);
        $paginator = new Pagerfanta($adapter);
        $paginator->setMaxPerPage($limit);
        $paginator->setCurrentPage($page);

        return [
            'total_items' => $paginator->getNbResults(),
            'total_pages' => $paginator->getNbPages(),
            'items' => $paginator->getCurrentPageResults(),
        ];
    }

    protected function paginateDbalQueryBuilder(DbalQueryBuilder $queryBuilder, int $page = 1, int $limit = 10): array
    {
        $countQueryBuilderModifier = function (DbalQueryBuilder $queryBuilder): void {
            $queryBuilder->select('COUNT(*) over () total_results')->setMaxResults(1);
        };

        $adapter = new DoctrineDbalAdapter($queryBuilder, $countQueryBuilderModifier);
        $paginator = new Pagerfanta($adapter);
        $paginator->setMaxPerPage($limit);
        $paginator->setCurrentPage($page);

        return [
            'total_items' => $paginator->getNbResults(),
            'total_pages' => $paginator->getNbPages(),
            'items' => $paginator->getCurrentPageResults(),
        ];
    }
}
