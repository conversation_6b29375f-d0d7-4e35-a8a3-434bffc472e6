<?php

namespace App\Service\Navigation;

use App\Entity\Navigation\MainNavigation;
use App\Service\AbstractService;
use App\Service\GeoSCANAPIClient;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Security\Core\Security;

class MainNavigationService extends AbstractService
{
    private EntityManagerInterface $em;
    private $geoSCANAPIClient;
    private $security;

    public function __construct(
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        GeoSCANAPIClient $geoSCANAPIClient,
        Security $security
    ) {
        parent::__construct($formFactory);
        $this->em = $entityManager;
        $this->geoSCANAPIClient = $geoSCANAPIClient;
        $this->security = $security;
    }

    public function getMainNavigationMenuItems(string $lang, $instance): array
    {
        $organizationId = $this->geoSCANAPIClient->getLastChosenOrganizationId();
        $mainNavRepo = $this->em->getRepository(MainNavigation::class);
        $serviceProviderId = $this->security->getUser()->getServiceProvider()->getId();

        return $mainNavRepo->getByOrganization($organizationId, $lang, $serviceProviderId, $instance);
    }
}
