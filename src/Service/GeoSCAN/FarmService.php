<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service\GeoSCAN;

use App\Form\Farm\FarmType;
use App\Model\Farm\Farm;
use App\Repository\PackageRepository;
use App\Service\AbstractService;
use App\Service\GeoSCANAPIClient;
use App\Service\Technofarm\TFApiClient;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Uid\Uuid;

class FarmService extends AbstractService
{
    private $geoScanAPIClient;
    private $technofarmApiClient;
    private $packageRepository;
    private $security;

    public function __construct(
        GeoSCANAPIClient $geoSCANAPIClient,
        TFApiClient $technofarmApiClient,
        FormFactoryInterface $formFactory,
        PackageRepository $packageRepository,
        Security $security
    ) {
        parent::__construct($formFactory);
        $this->geoScanAPIClient = $geoSCANAPIClient;
        $this->technofarmApiClient = $technofarmApiClient;
        $this->packageRepository = $packageRepository;
        $this->security = $security;
    }

    public function getByUuid(string $farmUuid): Farm
    {
        $farm = new Farm();
        $tfFarmData = [];

        $isTfIntegrated = count($this->packageRepository->getPackagesByServiceProvider($this->security->getUser()->getServiceProvider()->getId(), ['integration' => 'TF']));
        if ($isTfIntegrated) {
            $tfFarmData = $this->technofarmApiClient->getFarm($farmUuid);
        }

        $gsFarmData = $this->geoScanAPIClient->getFarm($farmUuid);
        $farmData = array_merge($tfFarmData, $gsFarmData);

        $this->handleData(FarmType::class, $farm, $farmData, ['allow_extra_fields' => true]);

        return $farm;
    }

    public function create(array $data): void
    {
        $data['uuid'] = Uuid::v4();
        $data['isVisible'] = true;

        $farm = new Farm();
        $this->handleData(FarmType::class, $farm, $data, ['allow_extra_fields' => true]);

        $this->geoScanAPIClient->createFarm($farm);

        $isTfIntegrated = count($this->packageRepository->getPackagesByServiceProvider($this->security->getUser()->getServiceProvider()->getId(), ['integration' => 'TF']));
        if ($isTfIntegrated) {
            $this->technofarmApiClient->createFarm($farm);
        }
    }

    public function update(array $data): void
    {
        $farm = new Farm();
        $this->handleData(FarmType::class, $farm, $data, ['allow_extra_fields' => true]);

        $isTfIntegrated = count($this->packageRepository->getPackagesByServiceProvider($this->security->getUser()->getServiceProvider()->getId(), ['integration' => 'TF']));
        if ($isTfIntegrated) {
            $this->technofarmApiClient->updateFarm($farm);
        }

        $this->geoScanAPIClient->updateFarm($farm);
    }

    public function delete(string $farmUuid)
    {
        $isTfIntegrated = count($this->packageRepository->getPackagesByServiceProvider($this->security->getUser()->getServiceProvider()->getId(), ['integration' => 'TF']));
        if ($isTfIntegrated) {
            $this->technofarmApiClient->deleteFarm($farmUuid);
        }

        return $this->geoScanAPIClient->deleteFarm($farmUuid);
    }

    public function getEkattes(?string $farmUuid): array
    {
        $isTfIntegrated = count($this->packageRepository->getPackagesByServiceProvider($this->security->getUser()->getServiceProvider()->getId(), ['integration' => 'TF']));
        if ($isTfIntegrated) {
            return $this->technofarmApiClient->getEkattes($farmUuid);
        }

        return [];
    }
}
