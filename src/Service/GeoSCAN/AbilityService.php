<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>.nonchev
 * Date: 7/29/2019
 * Time: 2:48 PM.
 */

namespace App\Service\GeoSCAN;

use App\Service\AbstractService;
use App\Service\GeoSCANAPIClient;
use Symfony\Component\Form\FormFactoryInterface;
use S<PERSON>fony\Component\Serializer\SerializerInterface;

class AbilityService extends AbstractService
{
    private $geoScanAPIClient;
    private $serializer;

    /**
     * AbilityService constructor.
     */
    public function __construct(
        GeoSCANAPIClient $geoSCANAPIClient,
        FormFactoryInterface $formFactory,
        SerializerInterface $serializer
    ) {
        parent::__construct($formFactory);
        $this->geoScanAPIClient = $geoSCANAPIClient;
        $this->serializer = $serializer;
    }
}
