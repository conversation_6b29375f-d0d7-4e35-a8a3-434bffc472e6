<?php

namespace App\Service\GeoSCAN\Organization;

use App\Form\GeoSCAN\Organization\ContactPersonType;
use App\Model\GeoSCAN\Organization\ContactPerson;
use App\Service\AbstractService;
use App\Service\GeoSCANAPIClient;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Serializer\SerializerInterface;

class ContactPersonService extends AbstractService
{
    private $geoScanAPIClient;
    private $serializer;

    public function __construct(
        GeoSCANAPIClient $geoSCANAPIClient,
        FormFactoryInterface $formFactory,
        SerializerInterface $serializer
    ) {
        parent::__construct($formFactory);
        $this->geoScanAPIClient = $geoSCANAPIClient;
        $this->serializer = $serializer;
    }

    public function create(int $organizationId, $data)
    {
        $formType = ContactPersonType::class;
        $contactPerson = new ContactPerson();
        $this->handleData($formType, $contactPerson, $data);

        return $this->save($contactPerson, $organizationId);
    }

    public function update(int $organizationId, int $contactPersonId, $data)
    {
        $formType = ContactPersonType::class;
        $contactPerson = new ContactPerson();
        $this->handleData($formType, $contactPerson, $data);

        return $this->save($contactPerson, $organizationId, $contactPersonId, false);
    }

    public function delete(int $organizationId, int $contactPersonId)
    {
        return $this->geoScanAPIClient->deleteContactPerson($organizationId, $contactPersonId);
    }

    private function save(ContactPerson $contactPerson, int $organizationId, $contactPersonId = null, $isNew = true)
    {
        return $this->geoScanAPIClient->saveContactPerson($contactPerson, $organizationId, $contactPersonId, $isNew);
    }
}
