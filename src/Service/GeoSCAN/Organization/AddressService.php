<?php

namespace App\Service\GeoSCAN\Organization;

use App\Form\GeoSCAN\Organization\AddressType;
use App\Model\GeoSCAN\Organization\Address;
use App\Service\AbstractService;
use App\Service\GeoSCANAPIClient;
use Symfony\Component\Form\FormFactoryInterface;
use <PERSON><PERSON>fony\Component\Serializer\SerializerInterface;

class AddressService extends AbstractService
{
    private $geoScanAPIClient;
    private $serializer;

    public function __construct(
        GeoSCANAPIClient $geoSCANAPIClient,
        FormFactoryInterface $formFactory,
        SerializerInterface $serializer
    ) {
        parent::__construct($formFactory);
        $this->geoScanAPIClient = $geoSCANAPIClient;
        $this->serializer = $serializer;
    }

    public function create(int $organizationId, $data)
    {
        $formType = AddressType::class;
        $address = new Address();
        $this->handleData($formType, $address, $data);

        return $this->save($address, $organizationId);
    }

    public function update(int $organizationId, int $addressId, $data)
    {
        $formType = AddressType::class;
        $address = new Address();
        $this->handleData($formType, $address, $data);

        return $this->save($address, $organizationId, $addressId, false);
    }

    public function delete(int $organizationId, int $addressId)
    {
        return $this->geoScanAPIClient->deleteAddress($organizationId, $addressId);
    }

    private function save(Address $address, int $organizationId, $addressId = null, $isNew = true)
    {
        return $this->geoScanAPIClient->saveAddress($address, $organizationId, $addressId, $isNew);
    }
}
