<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service\GeoSCAN;

use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Package;
use App\Form\GeoSCAN\OrganizationType;
use App\Model\Farm\Farm;
use App\Model\GeoSCAN\Organization;
use App\Model\GeoSCAN\User;
use App\Repository\Contract\SubscriptionPackageRepository;
use App\Repository\PackageRepository;
use App\Service\AbstractService;
use App\Service\GeoSCANAPIClient;
use App\Service\Technofarm\TFApiClient;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Uid\Uuid;

class OrganizationService extends AbstractService
{
    private $geoScanAPIClient;
    private $manager;
    private $technofarmApiClient;
    private $security;
    private $packageRepository;
    private $userService;

    public function __construct(
        GeoSCANAPIClient $geoSCANAPIClient,
        TFApiClient $technofarmApiClient,
        FormFactoryInterface $formFactory,
        ManagerRegistry $manager,
        Security $security,
        PackageRepository $packageRepository,
        UserService $userService
    ) {
        parent::__construct($formFactory);
        $this->geoScanAPIClient = $geoSCANAPIClient;
        $this->technofarmApiClient = $technofarmApiClient;
        $this->manager = $manager;
        $this->security = $security;
        $this->packageRepository = $packageRepository;
        $this->userService = $userService;
    }

    public function create($data)
    {
        $formType = OrganizationType::class;
        $organization = new Organization();
        $this->handleData($formType, $organization, $data, ['exclude_users_and_farms' => false]);

        return $this->save($organization);
    }

    public function update(Organization $organization, $data)
    {
        $formType = OrganizationType::class;
        $this->handleData($formType, $organization, $data);

        return $this->save($organization, false);
    }

    public function search($string)
    {
        return $this->geoScanAPIClient->searchOrganizations($string);
    }

    public function manageOrgUserRelation($data)
    {
        $securityUser = $this->security->getuser();
        $isTfIntegrated = count($this->packageRepository->getPackagesByServiceProvider($securityUser->getServiceProvider()->getId(), ['integration' => 'TF']));
        if ($isTfIntegrated) {
            $this->technofarmApiClient->manageOrgUserRelation($data);
        }
        $this->geoScanAPIClient->manageOrgUserRelation($data);
    }

    public function getActiveSubscriptions(array $filter)
    {
        $userId = array_key_exists('userId', $filter) ? $filter['userId'] : null;
        if ($userId) {
            $organizations = $this->geoScanAPIClient->getOrganizations(null, null, null, ['oldId' => $userId]);
            $filter['identityNumbers'] = array_map(function (Organization $organization) {
                return $organization->getIdentityNumber();
            }, $organizations->data ?? []);
        }

        /** @var SubscriptionPackageRepository */
        $subscriptionPackageRepository = $this->manager->getRepository(SubscriptionPackage::class);

        return $subscriptionPackageRepository->getActiveSubscriptions(
            $filter
        );
    }

    /**
     * @param int $userId
     */
    public function organizationUserRel(int $organizationId, string $userId, string $action): User
    {
        $user = $this->geoScanAPIClient->organizationUser($organizationId, $userId, $action);

        $isTfIntegrated = count($this->packageRepository->getPackagesByServiceProvider($this->security->getUser()->getServiceProvider()->getId(), ['integration' => 'TF']));
        if ($isTfIntegrated) {
            $this->technofarmApiClient->manageOrgUserRelation([
                'orgId' => $organizationId,
                'username' => $user->getUsername(),
                'action' => $action,
            ]);
        }

        return $user;
    }

    public function updateTechnfoarmOrganization(Organization $oranization)
    {
        return $this->technofarmApiClient->updateOrganization($oranization);
    }

    /**
     *  Add/edit organization and set Organization Mangager in GS and TF projects
     *  Add user to Kecyloak.
     *
     * @param bool $isNew
     */
    private function save(Organization $organization, $isNew = true)
    {
        $securityUser = $this->security->getuser();
        $isTfIntegrated = count($this->packageRepository->getPackagesByServiceProvider($securityUser->getServiceProvider()->getId(), ['integration' => 'TF']));

        if (true === $isNew) {
            // Set uuid to organization's farms
            $organizationFarms = array_map(function (Farm $farm) {
                $farm->setUuid(Uuid::v4());

                return $farm;
            }, $organization->getFarms());
            $organization->setFarms($organizationFarms);

            $user = current($organization->getUsersAssigned());
            $user->setActive(true);
            $user->setPasswordReType($user->getPassword());
            $locale = strtolower($this->security->getUser()->getServiceProvider()->getCountry()->getIsoAlpha2Code());
            // add user to KC
            $this->userService->saveKeycloakUser($user, true, $locale);
            $keycloakUser = $this->userService->getKeycloakUser($user->getUsername());
            $user->setKeycloakUid($keycloakUser->getId());

            // save organization and organization manager to GS
            $gs = $this->geoScanAPIClient->saveOrganization($organization, true);
            $organizationManager = current($gs->getUsersAssigned());
            $user->setId((int)$organizationManager->id);

            if ($isTfIntegrated) {
                // if the service provider has package with TF integration,
                // the organisation is created both in GS and in TF.
                // If no TF integration packages are available - only in GS.

                $organization->setId($gs->getId());
                $user->setOrganizations([$organization]);
                $tf = $this->userService->saveTechnofarmUser($user, true);
                $user->setTechnofarmUserId((int)$tf['user']);
            }

            // update kc user gs and tf attributes
            $this->userService->saveKeycloakUser($user, false, $locale);

            return $gs;
        }

        // update user in TF and GS
        $gs = $this->geoScanAPIClient->saveOrganization($organization, $isNew);
        if ($isTfIntegrated) {
            $this->updateTechnfoarmOrganization($organization);
        }

        return $gs;
    }
}
