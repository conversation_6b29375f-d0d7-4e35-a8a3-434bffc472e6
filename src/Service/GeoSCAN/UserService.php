<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service\GeoSCAN;

use App\EntityGeoscan\GlobalUser;
use App\Form\GeoSCAN\UserType;
use App\Model\GeoSCAN\User;
use App\Repository\PackageRepository;
use App\Security\OAuthClient\Provider\KeycloakAdminProvider;
use App\Security\User\KeycloakUser;
use App\Service\AbstractService;
use App\Service\GeoSCANAPIClient;
use App\Service\Technofarm\TFApiClient;
use Doctrine\Persistence\ManagerRegistry;
use Exception;
use LogicException;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use <PERSON>\Component\Security\Core\Security;

class UserService extends AbstractService
{
    private $geoScanAPIClient;
    private $technofarmApiCLient;
    private $keycloakAdminProvider;
    private $security;
    private $packageRepository;
    private $doctrine;

    public function __construct(
        GeoSCANAPIClient $geoSCANAPIClient,
        TFApiClient $technofarmApiCLient,
        KeycloakAdminProvider $keycloakAdminProvider,
        FormFactoryInterface $formFactory,
        ManagerRegistry $manager,
        Security $security,
        PackageRepository $packageRepository
    ) {
        parent::__construct($formFactory);

        $this->geoScanAPIClient = $geoSCANAPIClient;
        $this->technofarmApiCLient = $technofarmApiCLient;
        $this->keycloakAdminProvider = $keycloakAdminProvider;
        $this->security = $security;
        $this->packageRepository = $packageRepository;
        $this->doctrine = $manager;
    }

    public function updateFarmsVisibility($user, $data)
    {
        $formType = UserType::class;
        $this->handleData($formType, $user, $data);

        return $this->geoScanAPIClient->updateFarmsVisibility($user, $data);
    }

    public function manageFarmsAccess($user, $data)
    {
        $formType = UserType::class;
        $this->handleData($formType, $user, $data);

        $response = $this->geoScanAPIClient->manageFarmsAccess($user, $data);

        $isTfIntegrated = count($this->packageRepository->getPackagesByServiceProvider($this->security->getUser()->getServiceProvider()->getId(), ['integration' => 'TF']));
        if ($isTfIntegrated) {
            $user = $this->doctrine->getRepository(GlobalUser::class, 'geoscan')->findOneBy(['old_id' => $user->getId()]);
            $this->technofarmApiCLient->manageFarmAccess($data['organizationId'], $user->getKeycloakUid(), $data['farms']);
        }

        return $response;
    }

    public function create(array $data)
    {
        return $this->save($data);
    }

    public function update($data)
    {
        return $this->save($data, false);
    }

    public function checkUsernameExistence(string $username): bool
    {
        $token = $this->keycloakAdminProvider->getToken();
        $kc = $this->keycloakAdminProvider->checkUsernameExistence($token, $username);
        $gs = $this->geoScanAPIClient->checkUsernameExistence($username);

        $tf = false;
        $isTfIntegrated = count($this->packageRepository->getPackagesByServiceProvider($this->security->getUser()->getServiceProvider()->getId(), ['integration' => 'TF']));
        if ($isTfIntegrated) {
            $tf = $this->technofarmApiCLient->checkUsernameExistence($username);
        }

        return (bool) ($gs || $kc || $tf);
    }

    public function checkEmailExistence(string $email): bool
    {
        $token = $this->keycloakAdminProvider->getToken();
        $kc = $this->keycloakAdminProvider->checkEmailExistence($token, $email);

        if (!$kc) {
            throw new NotFoundHttpException('User not found', null, Response::HTTP_NOT_FOUND);
        }

        return (bool) $kc;
    }

    public function saveTechnofarmUser(User $user, bool $isNew)
    {
        return $this->technofarmApiCLient->saveUser($user, $isNew);
    }

    /**
     * @return ?string the KeyCloak user ID
     */
    public function saveKeycloakUser(User $user, bool $isNew, string $locale)
    {
        try {
            $token = $this->keycloakAdminProvider->getToken();

            return $this->keycloakAdminProvider->saveUser($token, $user, $isNew, $locale);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function getKeycloakUser(string $username): ?KeycloakUser
    {
        try {
            $token = $this->keycloakAdminProvider->getToken();

            return $this->keycloakAdminProvider->getUser($token, ['username' => $username]);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public static function splitName(string $name): array
    {
        $name = trim($name);
        $lastName = (false === strpos($name, ' ')) ? '' : preg_replace('#.*\s([\w-]*)$#', '$1', $name);
        $firstName = trim(preg_replace('#' . preg_quote($lastName, '#') . '#', '', $name));

        return [$firstName, $lastName];
    }

    public function getTechnofarmUserPermissions(string $username)
    {
        return $this->technofarmApiCLient->getUserPermissions($username);
    }

    public function search(string $value, int $serviceProviderId)
    {
        return $this->doctrine->getRepository(GlobalUser::class, 'geoscan')->search($value, $serviceProviderId);
    }

    /**
     * @return object
     */
    private function save(array $data, bool $isNew = true)
    {
        $formType = UserType::class;
        $user = new User();

        $this->handleData($formType, $user, $data);

        if ($user->getPassword() !== $user->getPasswordReType()) {
            throw new LogicException('The passwords do not match!', 400);
        }

        if (!$user->getOrganizations()) {
            throw new LogicException('Not found organization! The user needs to have at least one organization attached to it!', 404);
        }

        $locale = strtolower($this->security->getUser()->getServiceProvider()->getCountry()->getIsoAlpha2Code());

        try {
            $this->saveKeycloakUser($user, $isNew, $locale);

            $keycloakUser = $this->getKeycloakUser($user->getUsername());
            $user->setKeycloakUid($keycloakUser->getId());

            $gs = $this->geoScanAPIClient->saveUser($user, $isNew);
            $user->setId($gs->getId());

            $isTfIntegrated = count($this->packageRepository->getPackagesByServiceProvider($this->security->getUser()->getServiceProvider()->getId(), ['integration' => 'TF']));
            if ($isTfIntegrated) {
                $tf = $this->saveTechnofarmUser($user, $isNew);
                if (true === $isNew) {
                    $user->setTechnofarmUserId((int) $tf['user']);
                }
            }

            if (true === $isNew) {
                $this->saveKeycloakUser($user, false, $locale);
            }
        } catch (Exception $exception) {
            throw new LogicException($exception->getMessage(), 400);
        }

        return $gs;
    }
}
