<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service\Protocol;

use App\Entity\Protocol;
use App\Service\AbstractService;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Serializer\SerializerInterface;

class ProtocolService extends AbstractService
{
    private $em;
    private $serializer;
    private $security;

    public function __construct(
        FormFactoryInterface $formFactory,
        SerializerInterface $serializer,
        Security $security,
        EntityManagerInterface $entityManager
    ) {
        parent::__construct($formFactory);
        $this->serializer = $serializer;
        $this->security = $security;
        $this->em = $entityManager;
    }

    /**
     * @throws Exception
     *
     * @return Protocol
     */
    public function create()
    {
        $now = new DateTime();
        $user = $this->security->getUser();

        $protocol = new Protocol();
        $protocol->setDate($now);
        $protocol->setUserId($user->getId());
        $this->em->persist($protocol);
        $this->em->flush();

        return $protocol;
    }
}
