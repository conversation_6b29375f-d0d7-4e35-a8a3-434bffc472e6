<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Service\Protocol;

use App\Entity\Protocol\ProtocolPackageField;
use App\Service\AbstractService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Serializer\SerializerInterface;

class ProtocolPackageFieldService extends AbstractService
{
    private $em;
    private $serializer;

    public function __construct(
        FormFactoryInterface $formFactory,
        SerializerInterface $serializer,
        EntityManagerInterface $entityManager
    ) {
        parent::__construct($formFactory);
        $this->serializer = $serializer;
        $this->em = $entityManager;
    }

    public function create(array $ppfData)
    {
        $packFieldType = $ppfData['package_field_type'];
        $protocol = $ppfData['protocol'];

        foreach ($ppfD<PERSON>['package_field_ids'] as $packFieldId) {
            $ppf = new ProtocolPackageField();
            $ppf->setProtocol($protocol);
            $ppf->setPackageFieldType($packFieldType);
            $ppf->setPackageFieldId($packFieldId);
            $this->em->persist($ppf);
        }
        $this->em->flush();

        return;
    }
}
