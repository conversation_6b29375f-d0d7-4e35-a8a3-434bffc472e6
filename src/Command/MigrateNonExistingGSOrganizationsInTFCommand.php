<?php

namespace App\Command;

use App\Helpers\Log;
use App\Model\GeoSCAN\Organization;
use App\Security\OAuthClient\Provider\KeycloakMachineToMachine;
use App\Service\GeoSCAN\OrganizationService;
use App\Service\GeoSCAN\UserService;
use App\Service\GeoSCANAPIClient;
use App\Service\Technofarm\TFApiClient;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Security\Core\Security;

class MigrateNonExistingGSOrganizationsInTFCommand extends Command
{
    protected static $defaultName = 'app:migrate-non-existing-gs-organizations-in-tf';

    private EntityManagerInterface $em;
    private ParameterBagInterface $parameters;
    private GeoSCANAPIClient $geoscanApiClient;
    private Security $security;
    private KeycloakMachineToMachine $m2m;
    private TFApiClient $technofarmApiClient;
    private OrganizationService $organizationService;
    private UserService $userService;
    private $io;
    private $migratedUsersEmails = [];
    private $tokenStorage;

    public function __construct(
        EntityManagerInterface $entityManager,
        ParameterBagInterface $parameters,
        GeoSCANAPIClient $geoSCANAPIClient,
        Security $security,
        KeycloakMachineToMachine $keycloakMachineToMachine,
        TFApiClient $technofarmApiClient,
        OrganizationService $organizationService,
        UserService $userService
    ) {
        $this->em = $entityManager;
        $this->parameters = $parameters;
        $this->geoscanApiClient = $geoSCANAPIClient;
        $this->security = $security;
        $this->m2m = $keycloakMachineToMachine;
        $this->technofarmApiClient = $technofarmApiClient;
        $this->userService = $userService;
        $this->organizationService = $organizationService;

        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setDescription('Migrate organisations which exists in GS but does not exists in TF');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $this->m2m->impersonateUser('admin_bg');

        try {
            $io->title('Start migration');

            [$organizations, $migratedOrganizations] = $this->geoscanApiClient->getMissingTechnofarmOrganizations();

            $migratedOrganizationIds = array_column($migratedOrganizations, 'id');

            $cnt = 0;
            foreach ($organizations as $missingOrganization) {
                // 1164 //1237
                // if ($missingOrganization['id'] != 913) {
                //    continue;
                // }

                if (in_array($missingOrganization['id'], $migratedOrganizationIds)) {
                    $io->note(sprintf('Skip organization: %s ', $missingOrganization['name']));

                    continue;
                }

                $cnt++;
                $io->note($cnt);
                $io->note('Migrate organization id : ' . $missingOrganization['id']);
                $io->note(sprintf('Migrate organization: %s ', $missingOrganization['name']));

                $organization = $this->getOrganization($missingOrganization);
                $users = $organization->getUsersAssigned();
                $migratableUsers = $this->getManagers($users);

                $manager = $migratableUsers['organizationManager'];
                if (null === $manager) {
                    $manager = $migratableUsers['organizationManagerNVRA'];
                }
                $organization->setUsersAssigned([$manager]);

                $io->note(sprintf('Add user: %s ', $manager->getName()));

                $this->migrateOrganization($organization);
            }

            $io->success([
                'Users migrated !',
            ]);
        } catch (Exception $exception) {
            Log::toFile('z_ex', [$exception->getMessage(), $exception->getLine(), $exception->getFile(), $missingOrganization ?? null]);

            $io->error($exception->getMessage());

            return 1;
        }

        $io->success('All is Done!');

        return 0;
    }

    private function getOrganization($missingOrganization)
    {
        try {
            $organization = current($this->geoscanApiClient->getOrganizations(1, 10, $missingOrganization['identity_number'], null, null)->data);

            $contactPersons = $this->geoscanApiClient->getOrganizationContactPersons($missingOrganization['id']);
            $addresses = $this->geoscanApiClient->getOrganizationAddresses($missingOrganization['id']);
            $users = $this->geoscanApiClient->getOrganizationUsersRaw($missingOrganization['id']);
            $farms = $this->geoscanApiClient->getOrganizationFarms($missingOrganization['id']);

            $organization
                ->setContactPersons($contactPersons)
                ->setAddresses($addresses)
                ->setUsersAssigned($users)
                ->setFarms($farms);

            return $organization;
        } catch (Exception $exception) {
            Log::toFile('z_ex', [$exception->getMessage(), $exception->getLine(), $exception->getFile(), $missingOrganization]);

            throw $exception;
        }
    }

    private function migrateOrganization(Organization $organization)
    {
        try {
            $organizationManager = current($organization->getUsersAssigned());

            if (!$organizationManager) {
                throw new Exception('Missing organization manager');
            }

            $organizationManager->setPasswordReType($organizationManager->getPassword());

            $organization->setUsersAssigned([]);
            $organizationManager->setOrganizations([$organization]);

            $this->addUser($organizationManager);
        } catch (Exception $exception) {
            Log::toFile('z_ex', [$exception->getMessage(), $exception->getLine(), $exception->getFile(), $organization->getId(), $organization->getName()]);
        }
    }

    private function addUser($user)
    {
        try {
            if (in_array($user->getEmail(), $this->migratedUsersEmails)) {
                $user->setEmail('');
                Log::toFile('z_ex_service_providers_duplicated_emails', $user->getUsername());
            }

            $this->userService->saveKeycloakUser($user, true, 'bg');
            $keycloakUser = $this->userService->getKeycloakUser($user->getUsername());
            $user->setKeycloakUid($keycloakUser->getId());

            // add user with kc uid
            $tf = $this->userService->saveTechnofarmUser($user, true);
            $user->setTechnofarmUserId((int) $tf['user']);

            // update with kc uid
            $gs = $this->geoscanApiClient->saveUser($user, false);

            // update kc user with gs_user_id and tf_user_id attributes
            $kc = $this->userService->saveKeycloakUser($user, false, 'bg');

            $this->migratedUsersEmails[] = $user->getEmail();
        } catch (Exception $exception) {
            $this->migratedUsersEmails[] = $user->getEmail();
            Log::toFile('z_ex', [$exception->getMessage(), $exception->getLine(), $exception->getFile(), $user->getUsername(), $user->getName(), $user->getId(), $user->getEmail()]);
        }
    }

    private function getManagers($users)
    {
        $migratableUsers = [
            'organizationManager' => null,
            'organizationManagerNVRA' => null,
        ];

        foreach ($users as $key => $user) {
            if ('ORGANIZATION_MANAGER' == $user->getRole()->getName()) {
                $migratableUsers['organizationManager'] = $user;

                continue;
            }

            if ('ORGANIZATION_MANAGER_N_VRA' == $user->getRole()->getName()) {
                $migratableUsers['organizationManagerNVRA'] = $user;
            }
        }

        if (null != $migratableUsers['organizationManager'] && null != $migratableUsers['organizationManagerNVRA']) {
            Log::toFile('z_migrate_both', [
                $migratableUsers['organizationManager'],
                $migratableUsers['organizationManagerNVRA'],
            ]);
        }

        return $migratableUsers;
    }

    private function getMigratableUsers($users)
    {
        $migratableUsers = [
            'organizationManager' => null,
            'users' => [],
        ];

        foreach ($users as $key => $user) {
            if ('ORGANIZATION_MANAGER' == $user->getRole()->getName()) {
                $migratableUsers['organizationManager'] = $user;

                continue;
            }

            if (in_array(
                $user->getRole()->getName(),
                [
                    'STAFF', 'ORGANIZATION_MANAGER_N_VRA', 'SUPER_ADMIN', 'SELLER_ADMIN', 'SERVICE', 'SERVICE_ADMIN',
                ]
            )) {
                $migratableUsers['users'][] = $user;
            }
        }

        return $migratableUsers;
    }

    private function migrateUser($user, $organization)
    {
        $user->setPasswordReType($user->getPassword());
        $organization->setUsersAssigned([]);

        $user->setOrganizations([$organization]);

        $this->addUser($user);
    }
}
