<?php

namespace App\Command;

use App\Helpers\Log;
use App\Model\GeoSCAN\Organization;
use App\Security\OAuthClient\Provider\KeycloakMachineToMachine;
use App\Serializer\UserNormalizer;
use App\Service\GeoSCAN\OrganizationService;
use App\Service\GeoSCAN\UserService;
use App\Service\GeoSCANAPIClient;
use App\Service\Technofarm\TFApiClient;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Serializer\NameConverter\CamelCaseToSnakeCaseNameConverter;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class ValidateMigrationUserCommand extends Command
{
    protected static $defaultName = 'app:validate-migration-user';

    private EntityManagerInterface $em;
    private ParameterBagInterface $parameters;
    private GeoSCANAPIClient $geoscanApiClient;
    private Security $security;
    private KeycloakMachineToMachine $m2m;
    private TFApiClient $technofarmApiClient;
    private OrganizationService $organizationService;
    private UserService $userService;
    private $io;
    private $migratedUsersEmails = [];
    private $normalizer;
    private $userNormalizer;

    private $serviceProviders = [
        'admin_ro',
        'Razvan_Agricost',
        'zornica_it',
        'admin_ua',
    ];

    public function __construct(
        EntityManagerInterface $entityManager,
        ParameterBagInterface $parameters,
        GeoSCANAPIClient $geoSCANAPIClient,
        Security $security,
        KeycloakMachineToMachine $keycloakMachineToMachine,
        TFApiClient $technofarmApiClient,
        OrganizationService $organizationService,
        UserService $userService
    ) {
        $this->em = $entityManager;
        $this->parameters = $parameters;
        $this->geoscanApiClient = $geoSCANAPIClient;
        $this->security = $security;
        $this->m2m = $keycloakMachineToMachine;
        $this->technofarmApiClient = $technofarmApiClient;
        $this->userService = $userService;
        $this->organizationService = $organizationService;
        $this->normalizer = new ObjectNormalizer(null, new CamelCaseToSnakeCaseNameConverter());
        $this->userNormalizer = new UserNormalizer($this->normalizer);

        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setDescription('Migrate foreign service providers users !');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $this->m2m->impersonateUser('admin_bg');

        $organizations = $this->geoscanApiClient->getMissingTechnofarmOrganizations();

        try {
            foreach ($organizations as $missingOrganization) {
                // 1164 //1237
                if (913 != $missingOrganization['id']) {
                    continue;
                }

                $io->note(sprintf('Validate organization: %s ', $missingOrganization['name']));

                $organization = $this->getOrganization($missingOrganization);
                $users = $organization->getUsersAssigned();
                $migratableUsers = $this->getManagers($users);

                $manager = $migratableUsers['organizationManager'];
                if (null === $manager) {
                    $manager = $migratableUsers['organizationManagerNVRA'];
                }

                $organization->setUsersAssigned([$manager]);

                $this->validateUser($organization);
            }
        } catch (Exception $exception) {
        }
    }

    private function validateUser(Organization $organization)
    {
        try {
            $organizationManager = current($organization->getUsersAssigned());

            if (!$organizationManager) {
                throw new Exception('Missing organization manager');
            }

            $technofarmfUser = $this->technofarmApiClient->loadUser($organizationManager->getUsername());
            $keycloakUser = $this->userService->getKeycloakUser($organizationManager->getUsername());
        } catch (Exception $exception) {
            Log::toFile('z_ex', [$exception->getMessage(), $exception->getLine(), $exception->getFile(), $organization->getId(), $organization->getName()]);
        }
    }

    private function getOrganization($missingOrganization)
    {
        try {
            $organization = current($this->geoscanApiClient->getOrganizations(1, 10, $missingOrganization['identity_number'], null, null)->data);

            $users = $this->geoscanApiClient->getOrganizationUsersRaw($missingOrganization['id']);

            $organization
                ->setUsersAssigned($users);

            return $organization;
        } catch (Exception $exception) {
            Log::toFile('z_ex', [$exception->getMessage(), $exception->getLine(), $exception->getFile(), $missingOrganization]);

            throw $exception;
        }
    }

    private function getManagers($users)
    {
        $migratableUsers = [
            'organizationManager' => null,
            'organizationManagerNVRA' => null,
        ];

        foreach ($users as $key => $user) {
            if ('ORGANIZATION_MANAGER' == $user->getRole()->getName()) {
                $migratableUsers['organizationManager'] = $user;

                continue;
            }

            if ('ORGANIZATION_MANAGER_N_VRA' == $user->getRole()->getName()) {
                $migratableUsers['organizationManagerNVRA'] = $user;
            }
        }

        if (null != $migratableUsers['organizationManager'] && null != $migratableUsers['organizationManagerNVRA']) {
            Log::toFile('z_migrate_both', [
                $migratableUsers['organizationManager'],
                $migratableUsers['organizationManagerNVRA'],
            ]);
        }

        return $migratableUsers;
    }
}
