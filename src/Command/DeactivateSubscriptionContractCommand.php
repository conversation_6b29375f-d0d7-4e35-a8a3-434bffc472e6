<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>.nonchev
 * Date: 11/11/2019
 * Time: 2:29 PM.
 */

namespace App\Command;

use App\Service\Workflow\ContractService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Workflow\Exception\LogicException;

class DeactivateSubscriptionContractCommand extends Command
{
    protected static $defaultName = 'app:deactivate-sub-contract';
    private $subscriptionService;

    public function __construct(ContractService $contractService)
    {
        $this->subscriptionService = $contractService;

        parent::__construct();
    }

    protected function configure()
    {
        $this
            // the short description shown while running "php bin/console list"
            ->setDescription('Deactivate all contracts and related subscription packages, when end data is lower then current date.');
    }

    /**
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        // try {
        $result = $this->subscriptionService->deactivateAllExpiredSubscriptionContracts(true);
        // } catch (LogicException $exception) {
        // throw $exception;
        //     $output->writeln([
        //         'Error',
        //         '============',
        //         $exception->getMessage(),
        //     ]);

        //     return 1;
        // }

        if ($result) {
            // dd($result);
            $output->writeln([
                'Result',
                '============',
                'Contracts and their packages have been deactivated',
            ]);

            return 0;
        }

        $output->writeln([
            'Result',
            '============',
            'There are no deactivation contracts',
        ]);

        return 0;
    }
}
