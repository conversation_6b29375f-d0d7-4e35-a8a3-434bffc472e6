<?php

namespace App\Command;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Summary of CreateOrganizationsFromTF.
 */
class FillSubscriptionPackageAmountCommand extends AbstractTFIntegrationCommand
{
    protected static $defaultName = 'app:fill-suscription-package-amount';

    public function __construct(
        EntityManagerInterface $em
    ) {
        parent::__construct($em);
    }

    /**
     * Summary of execute.
     */
    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $organizaitons = $this->getOrganizations();

        foreach ($organizaitons as $organization) {
            $tfSusiMainConnString = $this->getMainDbConfig();

            // Check if the database exists
            $databaseName = $organization['database'];
            $sql = "
                SELECT 1 FROM dblink(?, 'SELECT 1 FROM pg_database WHERE datname = ''' || ? || '''') AS t(exists INT)
            ";
            $stmt = $this->em->getConnection()->executeQuery($sql, [$tfSusiMainConnString, $databaseName]);

            if ($stmt->fetchOne()) {
                $ekattesCount = $this->getOrganziationEkattesCount($databaseName);
                $this->updateOrganizationContract($organization['organization_id'], $ekattesCount);
            }
        }

        return 0;
    }

    protected function getOrganizations()
    {
        $tfSusiMainConnString = $this->getMainDbConfig();

        $sql = "
            SELECT * FROM dblink(?, $$
                SELECT
                    u.group_id AS group_id, 
                    u.database,
                    SUBSTRING(u.database FROM 'db_bg_(.*)') AS organization_id
                FROM su_users u
                WHERE
                    u.database like '%db_bg_%'
                    and
                    u.level = 2
                GROUP BY u.group_id,u.database
                ORDER BY database
            $$) t(group_id int, database text, organization_id text)
        ";

        $stmt = $this->em->getConnection()->executeQuery($sql, [$tfSusiMainConnString]);

        return $stmt->fetchAllAssociative();
    }

    private function updateOrganizationContract(int $organizationId, int $ekattesCount): void
    {
        $dql = "
            SELECT sp
            FROM App\Entity\Contract\SubscriptionPackage sp
            JOIN sp.contract c
            JOIN sp.package p
            WHERE c.organizationId = :organizationId AND p.slug = :slug
        ";
        $params = [
            'slug' => 'Administrative map',
            'organizationId' => $organizationId,
        ];
        $query = $this->em->createQuery($dql);
        $query->setParameters($params);
        $subscriptionPackages = $query->getResult();

        foreach ($subscriptionPackages as $subscriptionPackage) {
            $subscriptionPackage->setAmount($ekattesCount);
            $this->em->persist($subscriptionPackage);
        }

        $this->em->flush();
    }
}
