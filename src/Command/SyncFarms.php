<?php

namespace App\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class SyncFarms extends Command
{
    protected static $defaultName = 'app:sync-farms';

    protected function configure()
    {
        $this->setDescription('Sync farms from external API');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $output->writeln([
            'Sync farms',
            '============',
            '',
        ]);

        $this->syncFarms();

        $output->writeln('Farms synced');

        return Command::SUCCESS;
    }

    private function syncFarms()
    {
        $farms = $this->getFarmsFromExternalApi();

        foreach ($farms as $farm) {
            $this->saveFarm($farm);
        }
    }

    private function getFarmsFromExternalApi()
    {
        // Call external API and return farms
    }

    private function saveFarm($farm)
    {
        // Save farm to database
    }
}
