<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>.nonchev
 * Date: 3/18/2020
 * Time: 1:05 PM.
 */

namespace App\Command;

use App\Service\Workflow\SubscriptionPackageService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Workflow\Exception\LogicException;

class DeactivateSubscriptionPackagesCommand extends Command
{
    protected static $defaultName = 'app:deactivate-sub-packages';
    private $subscriptionService;

    public function __construct(SubscriptionPackageService $subscriptionPackageService)
    {
        $this->subscriptionService = $subscriptionPackageService;

        parent::__construct();
    }

    protected function configure()
    {
        $this
            // the short description shown while running "php bin/console list"
            ->setDescription('Deactivate all subscription packages and related subscription packages fields, when end data is lower then current date.');
    }

    /**
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        try {
            $results = $this->subscriptionService->deactivateAllExpiredPackages(true);
        } catch (LogicException $exception) {
            return $output->writeln([
                'Error',
                '============',
                $exception->getMessage(),
            ]);
        }

        if ($results) {
            $string = '';
            foreach ($results as $result) {
                $string = $string . ' ' . $result;
            }

            return $output->writeln([
                'Result',
                '============',
                'Packages ' . $string . ' and their fields have been expired',
            ]);
        }

        $output->writeln([
            'Result',
            '============',
            'There are no deactivation packages',
        ]);
    }
}
