import re
from time import sleep
import requests as r
from scrapy import Selector
import csv

updated_list = []
with open('org_tf_updated.csv', 'w', newline='') as f:
    writer = csv.writer(f, delimiter=';')

    with open('/Users/<USER>/Downloads/org_tf.csv') as f:
        reader = csv.reader(f, delimiter=';')
        for row in reader:
            username, email, name, eik, *other = row
            print (f"EIK: {eik}")
            base_url = "https://papagal.bg"
            resp = r.get(f"{base_url}/autocomplete/?query={eik}", verify=False)
            if resp.status_code == 404:
                print("EIK not found")
                continue
            elif resp.status_code != 200:
                print("Error!")
                continue
            found_eiks = resp.json()
            if len(found_eiks) == 0:
                print("no result")
                continue
            found_eik = found_eiks[0]
            full_info_resp = r.get(f"{base_url}{found_eik['url']}")
            html_content = full_info_resp.text

            sel = Selector(text=html_content, type='text', root=None)
            company_name = re.sub(' +', ' ', sel.xpath("/html/body/div[1]/div[2]/div/h1/text()").get().replace('\n', '').strip())
            company_address = re.sub(' +', ' ', sel.xpath("//dt[contains(text(),\"Седалище адрес\")]/following::dd[1]/text()").get().split("\n")[1].strip())
            # updated_list.append([username, email, eik, company_name, company_address])
            writer.writerow([username, email, eik, company_name, company_address])
            print(f"{company_name}, {company_address}")
            sleep(.5)



