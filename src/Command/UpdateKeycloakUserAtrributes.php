<?php

namespace App\Command;

use App\Security\OAuthClient\Provider\KeycloakAdminProvider;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class UpdateKeycloakUserAtrributes extends Command
{
    protected static $defaultName = 'app:update-kc-user-attributes';

    private EntityManagerInterface $em;

    private $keycloakAdminProvider;

    public function __construct(
        EntityManagerInterface $entityManager,
        KeycloakAdminProvider $keycloakAdminProvider
    ) {
        $this->em = $entityManager;
        $this->keycloakAdminProvider = $keycloakAdminProvider;

        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setDescription('Migrate foreign service providers users !');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $token = $this->keycloakAdminProvider->getToken();

        $first = 0;
        $step = 10;
        $max = 11;
        $hasMoreUsers = true;
        do {
            $kcUsers = $this->keycloakAdminProvider->getUsers($token, $first, $max);
            $hasMoreUsers = count($kcUsers) > 0;

            if ($hasMoreUsers) {
                $gsUsers = $this->getGeoscanUsers($kcUsers);

                foreach ($gsUsers as $user) {
                    $kcUsers = $this->keycloakAdminProvider->updateUserAttributes($token, $user);
                    $io->note('Update attributes for user : ' . $user['username']);
                }
            }

            $first += $step;
        } while (true === $hasMoreUsers);

        $io->note('Commmand End');

        return 1;
    }

    private function getGeoscanUsers(array $kcUsers): array
    {
        $usernames = sprintf("'%s'", implode("','", array_column($kcUsers, 'username')));

        $env = getenv();
        $gsMainDBConnectionString
            = ' host=' . $env['GS_MAIN_DB_HOST']
            . ' port=' . $env['GS_MAIN_DB_PORT']
            . ' dbname=' . $env['GS_MAIN_DB_NAME']
            . ' user=' . $env['GS_MAIN_DB_USER']
            . ' password=' . $env['GS_MAIN_DB_PASS'];

        $tfSusiMainConnString
            = ' host=' . $env['TF_MAIN_DB_HOST']
            . ' port=' . $env['TF_MAIN_DB_PORT']
            . ' dbname=' . $env['TF_MAIN_DB_NAME']
            . ' user=' . $env['TF_MAIN_DB_USER']
            . ' password=' . $env['TF_MAIN_DB_PASS'];

        $sql = "SELECT
                    global_user.*,
                    tf_user.*
                from dblink(:mainDBConnectionString, $$
                                select 
                                    gsu.id,
                                    gsu.old_id,
                                    gsu.username,
                                    gsu.name,
                                    gsu.email,
                                    gsu.service_provider_id,
                                    gsu.keycloak_uid ,
                                    sp.name as service_provider_name,
                                    sp.slug as service_provider_slug,
                                    countries.id as country_id,
                                    countries.iso_alpha_2_code
                                from su_users gsu
                                inner join service_providers as sp on sp.id = gsu.service_provider_id
                                inner join countries on countries.id = sp.country_id
                                where
                                    gsu.active is true 
                    $$) as global_user (
                        id int,
                        old_id int,
                        username varchar,
                        name varchar,
                        email varchar,
                        service_provider_id int,
                        keycloak_uid uuid,
                        service_provider_name varchar,    
                        service_provider_slug varchar,
                        country_id int,
                        iso_alpha_2_code varchar            
                )
                LEFT JOIN 
                    dblink(:tfSusiMainConnString, $$
                                select
                                    tfsu.id as tf_user_id,
                                    tfsu.username as tf_username,
                                    tfsu.keycloak_uid as tf_keycloak_uid,
                                    tfsu.database as tf_database
                                from su_users tfsu
                    $$) as tf_user(
                        tf_user_id int,
                        tf_username varchar,
                        tf_keycloak_uid uuid,
                        tf_database varchar
                    ) on tf_user.tf_username = global_user.username
                WHERE
                    global_user.username in ({$usernames})   
            ";

        $stmt = $this->em->getConnection()->executeQuery(
            $sql,
            [
                'mainDBConnectionString' => $gsMainDBConnectionString,
                'tfSusiMainConnString' => $tfSusiMainConnString,
            ]
        );

        return $stmt->fetchAllAssociative();
    }
}
