<?php

namespace App\Command;

use App\EntityGeoscan\GlobalUser;
use App\Form\Farm\FarmType;
use App\Form\GeoSCAN\OrganizationType;
use App\Form\GeoSCAN\UserPermissionsType;
use App\Form\GeoSCAN\UserType;
use App\Helpers\Log;
use App\Model\Farm\Farm;
use App\Model\GeoSCAN\Organization;
use App\Model\GeoSCAN\Role;
use App\Model\GeoSCAN\User;
use App\Model\GeoSCAN\UserPermissions;
use App\Security\OAuthClient\Provider\KeycloakMachineToMachine;
use App\Service\ContractService;
use App\Service\GeoSCAN\OrganizationService;
use App\Service\GeoSCAN\UserService;
use App\Service\GeoSCANAPIClient;
use App\Service\Technofarm\TFApiClient;
use App\Service\Workflow\ContractService as WorkflowContractService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use LogicException;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Form\FormFactory;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Security;
use Throwable;

/**
 * Summary of CreateOrganizationsFromTF.
 */
class CreateOrganizationsFromTF extends AbstractTFIntegrationCommand
{
    public const KEYCLOAK_EMAIL_ALREADY_EXISTS = 'User exists with same email';
    public const KEYCLOAK_USERNAME_EXISTS = 'User exists with same username';
    public const TF_MIGRATOR_BASE_URL = 'http://migrator.technofarm.bg/index.php?';
    protected static $defaultName = 'app:create-org-from-tf';
    /**
     * Summary of geoSCANAPIClient.
     *
     * @var GeoSCANAPIClient
     */
    private $geoSCANAPIClient;

    /**
     * Summary of geoSCANAPIClient.
     *
     * @var OrganizationService
     */
    private $organizationService;

    /**
     * Summary of security.
     *
     * @var Security
     */
    private $security;

    /**
     * Summary of tokenStorage.
     *
     * @var TokenStorageInterface
     */
    private $tokenStorage;

    /**
     * Summary of tokenStorage.
     *
     * @var FormFactory
     */
    private $formFactory;
    /**
     * Summary of tokenStorage.
     *
     * @var UserService
     */
    private $userService;
    /**
     * Summary of tokenStorage.
     *
     * @var TFApiClient
     */
    private $tfApiClient;
    /**
     * Summary of tokenStorage.
     *
     * @var ManagerRegistry
     */
    private $doctrine;

    /**
     * Summary of tokenStorage.
     *
     * @var ContractService
     */
    private $contractService;
    /**
     * Summary of tokenStorage.
     *
     * @var WorkflowContractService
     */
    private $workflowContractService;

    private OutputInterface $output;

    private $httpClient;

    private KeycloakMachineToMachine $keycloakMachineToMachine;

    public function __construct(
        GeoSCANAPIClient $geoSCANAPIClient,
        TFApiClient $tfApiClient,
        OrganizationService $organizationService,
        UserService $userService,
        ContractService $contractService,
        Security $security,
        TokenStorageInterface $tokenStorage,
        EntityManagerInterface $em,
        FormFactoryInterface $formFactory,
        ManagerRegistry $doctrine,
        WorkflowContractService $workflowContractService,
        KeycloakMachineToMachine $keycloakMachineToMachine
    ) {
        parent::__construct($em);
        $this->geoSCANAPIClient = $geoSCANAPIClient;
        $this->tfApiClient = $tfApiClient;
        $this->organizationService = $organizationService;
        $this->userService = $userService;
        $this->contractService = $contractService;
        $this->security = $security;
        $this->tokenStorage = $tokenStorage;
        $this->doctrine = $doctrine;
        $this->formFactory = $formFactory;
        $this->workflowContractService = $workflowContractService;
        $this->keycloakMachineToMachine = $keycloakMachineToMachine;

        $this->httpClient = new Client([
            'base_uri' => self::TF_MIGRATOR_BASE_URL,
        ]);
    }

    /**
     * Summary of execute.
     *
     * @return int
     */
    public function execute(InputInterface $input, OutputInterface $output)
    {
        $this->output = $output;
        $selectedEik = $input->getOption('eik');
        $executeByCron = filter_var($input->getOption('execute_by_cron'), FILTER_VALIDATE_BOOLEAN);
        $database = $input->getOption('org_tf_base');

        $adminUser = $this->doctrine->getRepository(GlobalUser::class, 'geoscan')->findOneBy(['username' => 'admin_bg']);
        $filter = $this->em->getFilters()->enable('service_provider');
        $filter->setParameter('serviceProvider', $adminUser->getServiceProvider()->getId());

        $tfSusiMainConnString = $this->getMainDbConfig();

        $this->keycloakMachineToMachine->impersonateUser('admin_bg');

        if (true === $executeByCron) {
            $mainQuery = $this->getTFUsersWaitingGSIntegration();
        } else {
            $mainQuery = $this->getOrgTfUsers($selectedEik, $database);
        }

        $stmt = $this->em->getConnection()->executeQuery($mainQuery, [$tfSusiMainConnString]);
        $tfAccounts = $stmt->fetchAllAssociative();

        for ($i = 0; $i < count($tfAccounts); $i++) {
            // for ($i = 0; $i < 50; $i++) {
            try {
                $tfAccount = $tfAccounts[$i];
                $this->output->writeln(
                    sprintf(
                        '<info>Start Processing %s EIK: %s; Username: %s; Name: %s; GroupID: %s</info>',
                        $i,
                        $tfAccount['eik'],
                        $tfAccount['username'],
                        $tfAccount['name'],
                        $tfAccount['group_id']
                    )
                );

                $orgs = $this->geoSCANAPIClient->getOrganizations(0, 10, $tfAccount['eik']);
                $databaseName = "db_{$tfAccount['username']}";
                $orgId = null;

                if ($orgs->total > 0) {
                    $output->writeln('<comment>This identity number exists in GS</comment>');

                    $orgId = $orgs->data[0]->getId();
                    $newTfOrgUsername = "bg_{$orgId}";
                    $existingTfUser = $this->tfApiClient->loadUser($newTfOrgUsername);

                    if (isset($existingTfUser['id'])) {
                        $output->writeln('<error>The organization already exists in TF.</error>');

                        continue;
                    }
                }

                if (empty($tfAccount['salesperson_id'])) {
                    $output->writeln('<error>Error: There is no salespersno set.</error>');

                    continue;
                }

                $database = $this->getIsDatabaseExists($databaseName, $tfSusiMainConnString);

                if (empty($database)) {
                    $output->writeln("<error>Error: TF user database dosn't exist.</error>");

                    continue;
                }

                $organizationRaw = json_decode($tfAccount['organization_data'], true);
                $organization = $this->validateOrganization($organizationRaw);

                if (!$organization || 0 === count($organization->getUsersAssigned())) {
                    $output->writeln('<error>no users to create. skipping org</error>');

                    continue;
                }

                $executeGetUsers = false;
                if ($orgId) {
                    // users in gs (should include newly created tf users in gs
                    $gsUsers = $this->geoSCANAPIClient->getUsersRaw([
                        'limit' => 200,
                        'page' => 1,
                        'sort' => 'name',
                        'organizations' => "[{$orgId}]",
                    ], true);

                    $organization->setId($orgId);
                    $organization = $this->createOrganizationUsers($organization, $organizationRaw, $gsUsers);
                    $this->addFarms($organization, $organizationRaw);
                    $executeGetUsers = true;
                } else {
                    $organization = $this->createOrganization($organization);
                    $orgId = $organization->getId();

                    $users = $organization->getUsersAssigned();
                    $gsUsers = $this->geoSCANAPIClient->getUsersRaw([
                        'limit' => 200,
                        'page' => 1,
                        'sort' => 'name',
                        'organizations' => "[{$orgId}]",
                    ], true);

                    for ($idx = 0; $idx < count($users); $idx++) {
                        $user = $users[$idx];

                        if ($this->checkUsernameExistence($user->getUsername())) {
                            for ($c = 0; $c < count($gsUsers->data); $c++) {
                                $gsUser = $gsUsers->data[$c];

                                if ($gsUser->getUsername() == $user->getUsername()) {
                                    $user->setId((int) $gsUser->getId());

                                    break;
                                }
                            }

                            $this->addUserToKeycloak($user);
                        }
                    }
                }

                if (true == $executeGetUsers) {
                    // execute aggain to include all user ids
                    $gsUsers = $this->geoSCANAPIClient->getUsersRaw([
                        'limit' => 200,
                        'page' => 1,
                        'sort' => 'name',
                        'organizations' => "[{$orgId}]",
                    ], true);
                }

                $usersAssigned = $organization->getUsersAssigned();

                for ($a = 0; $a < count($usersAssigned); $a++) {
                    $userAssigned = $usersAssigned[$a];
                    for ($b = 0; $b < count($gsUsers->data); $b++) {
                        if ($userAssigned->getUsername() === $gsUsers->data[$b]->getUsername()) {
                            $userAssigned->setId($gsUsers->data[$b]->getId());
                        }
                    }
                }

                // Update tf level 2 to gs organization_id
                $newUsername = "bg_{$orgId}";
                $newDbName = "db_{$newUsername}";

                $output->writeln('kill coonections to  db');

                $this->em->getConnection()->executeQuery(
                    "
                    SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE pid <> pg_backend_pid() AND datname = '{$databaseName}';"
                );

                $output->writeln('Rename db');

                $this->em->getConnection()->executeQuery("SELECT 
                    * 
                FROM 
                    dblink(?, $$
                ALTER DATABASE {$databaseName} RENAME TO {$newDbName}
                $$) t(
                    updated text)", [$tfSusiMainConnString]);

                // Update the current level 2 user.
                $this->em->getConnection()->executeQuery("SELECT 
                    * 
                FROM dblink(?, $$
                UPDATE 
                    su_users 
                SET 
                    username = '{$newUsername}',
                    identity_number = '{$tfAccount['eik']}',
                    waiting_gs_integration = false
                WHERE 
                    username = '{$tfAccount['username']}'
                $$) t(
                    updated text)", [$tfSusiMainConnString]);

                // Update the current level 2 user and its child account database name.
                $this->em->getConnection()->executeQuery(
                    "SELECT 
                    * 
                FROM dblink(?, $$
                UPDATE 
                    su_users 
                SET 
                    \"database\" = '{$newDbName}',
                    identity_number = '{$tfAccount['eik']}',
                    gs_organization_id = {$orgId}
                WHERE 
                    group_id = '{$tfAccount['group_id']}'
                $$) t(
                    updated text)",
                    [$tfSusiMainConnString]
                );

                $output->writeln("<info>ORG ID: {$orgId}</info>");

                // create level 3 user. This user was level 2 in the past
                $newRole = new Role();
                $newRole->setName('EMPLOYEE');
                $newRole->setTitle('EMPLOYEE');
                $orgManagerUser = current($usersAssigned);

                $organization->setId($orgId);
                $organization->setIdentityNumber($tfAccount['eik']);

                $orgManagerUser->setPasswordReType($orgManagerUser->getPassword());
                $orgManagerUser->setActive(true);
                $orgManagerUser->setRole($newRole);
                $orgManagerUser->setOrganizations([$organization]);
                $this->mapTfUserPermissions($orgManagerUser, $organization, $tfAccount);
                $tf = $this->tfApiClient->saveUser($orgManagerUser, true);
                // update wiht new id
                $organizationRaw['usersAssigned'][0]['tf_id'] = $tf['user'];

                if (true == $tfAccount['active']) {
                    $this->createContract($orgId, $tfAccount, $newDbName);
                }

                // map tf usernames. Will be used in validation later
                $tfUsernames = array_map(function ($user) {
                    return $user->getUsername();
                }, $usersAssigned);

                // GS users goes to technofarm  will update and kc_uid. All good
                for ($c = 0; $c < count($gsUsers->data); $c++) {
                    $gsUser = $gsUsers->data[$c];
                    if (in_array($gsUser->getUsername(), $tfUsernames)) {
                        continue;
                    }
                    if ('ORGANIZATION_MANAGER' != $gsUser->getRole()->getName()) {
                        continue;
                    }

                    try {
                        $this->userService->saveKeycloakUser($gsUser, true, 'bg');
                    } catch (Throwable $th) {
                        if (self::KEYCLOAK_EMAIL_ALREADY_EXISTS === $th->getMessage()) {
                            $gsUser->setEmail('');
                            $this->userService->saveKeycloakUser($gsUser, true, 'bg');
                        }
                    }

                    $kcUser = $this->userService->getKeycloakUser($gsUser->getUsername());
                    $gsUser->setKeycloakUid($kcUser->getId());
                    // set kc id to gs user
                    $this->geoSCANAPIClient->saveUser($gsUser, $isNew = false);

                    $this->mapTfUserPermissions($gsUser, $organization, $tfAccount);

                    $gsEmployeeRole = new Role();
                    $gsEmployeeRole->setName('EMPLOYEE');
                    $gsUser->setRole($gsEmployeeRole);
                    $tf = $this->userService->saveTechnofarmUser($gsUser, true);

                    $gsUser->setTechnofarmUserId($tf['user']);
                    $this->userService->saveKeycloakUser($gsUser, false, 'bg');
                }

                // tova mai ne ni trebe
                for ($j = 0; $j < count($usersAssigned); $j++) {
                    $user = $usersAssigned[$j];
                    $user->setTechnofarmUserId($organizationRaw['usersAssigned'][$j]['tf_id']);
                    $user->setActive(true);
                    $user->setPasswordReType($user->getPassword());

                    $this->output->writeln(sprintf('<info>TF ID: %s; GS ID: %s</info>', $user->getTechnofarmUserId(), $user->getId()));
                    if (!($user->getTechnofarmUserId() || $user->getId())) {
                        $this->output->writeln(sprintf('<error>Missing id: TF ID: %s; GS ID: %s</error>', $user->getTechnofarmUserId(), $user->getId()));

                        continue;
                    }

                    try {
                        $this->userService->saveKeycloakUser($user, true, 'bg');
                    } catch (Throwable $th) {
                        if (self::KEYCLOAK_EMAIL_ALREADY_EXISTS === $th->getMessage()) {
                            $user->setEmail('');
                            $this->userService->saveKeycloakUser($user, true, 'bg');
                        }

                        if (self::KEYCLOAK_USERNAME_EXISTS === $th->getMessage()) {
                            // set tf_user_id attribute
                            $this->userService->saveKeycloakUser($user, false, 'bg');
                        }
                    }

                    $kcUser = $this->userService->getKeycloakUser($organizationRaw['usersAssigned'][$j]['username']);

                    // kc_uid can be related to renamed in tf level2 user
                    $this->em->getConnection()->executeQuery(
                        "SELECT 
                        * 
                    FROM dblink(?, $$
                    UPDATE 
                        su_users 
                    SET 
                        keycloak_uid = '{$kcUser->getId()}' 
                    WHERE 
                        username = '{$user->getUsername()}'
                    $$) t(
                        updated text)",
                        [$tfSusiMainConnString]
                    );

                    $output->writeln("<info>KC ID: {$kcUser->getId()}</ >");
                }
            } catch (Throwable $th) {
                $this->output->writeln(
                    sprintf(
                        "<error>EIK: %s; Username: %s; Name: %s; GroupID: %s;\n
                        Exception: %s\n
                        Trace:\n
                        %s
                        </error>",
                        $tfAccount['eik'],
                        $tfAccount['username'],
                        $tfAccount['name'],
                        $tfAccount['group_id'],
                        $th->getMessage(),
                        $th->getTraceAsString()
                    )
                );

                $this->log($th, $tfAccount);

                continue;
            }
            $stmt = $this->em->getConnection()->executeStatement("SELECT 
                * 
            FROM dblink(?, $$
                UPDATE org_tf set passed=true where real_eik='{$tfAccount['eik']}' $$) t(
                        updated text)", [$tfSusiMainConnString]);
            $this->output->writeln('<info>End Processing</info>');

            $this->updateGoogleSheet($tfAccount['username']);
        }

        if (true === $executeByCron) {
            $this->processContracts();
        }

        return self::SUCCESS;
    }

    public function createOrganizationUsers(Organization $organization, array $tfAccount, object $gsUsers)
    {
        for ($i = 0; $i < count($tfAccount['usersAssigned']); $i++) {
            $user = $tfAccount['usersAssigned'][$i];

            $orgUser = [
                'organizations' => [
                    [
                        'id' => $organization->getId(),
                        'name' => $organization->getName(),
                        'identityNumber' => $organization->getIdentityNumber(),
                        'vatNumber' => $organization->getVatNumber(),
                    ],
                ],
                'role' => [
                    'name' => $user['role']['name'],
                ],
                'username' => $user['username'],
                'password' => $user['password'],
                'passwordReType' => $user['password'],
                'name' => $user['name'],
                'email' => $user['email'],
                'phone' => $user['phone'],
                'active' => $user['active'],
            ];

            $formType = UserType::class;
            $user = new User();
            $form = $this->formFactory->create($formType, $user);
            $form->submit($orgUser, false);

            if ($this->checkUsernameExistence($user->getUsername())) {
                for ($c = 0; $c < count($gsUsers->data); $c++) {
                    $gsUser = $gsUsers->data[$c];

                    if ($gsUser->getUsername() == $user->getUsername()) {
                        $user->setId((int)$gsUser->getId());
                    }
                }

                $this->addUserToKeycloak($user);
                $this->output->writeln("<comment>User already exist {$user->getUsername()}</comment>");

                continue;
            }

            $this->geoSCANAPIClient->saveUser($user, true);
        }

        return $organization;
    }

    public function addFarms(Organization $organization, array $tfAccount)
    {
        $existingFarms = $this->geoSCANAPIClient->getOrganizationFarms($organization->getId());
        $existingFarmsUuids = array_map(function ($farm) {
            return $farm->getUuid();
        }, $existingFarms);

        $formFactory = $this->formFactory;
        foreach ($tfAccount['farms'] as $farm) {
            if (in_array($farm['uuid'], $existingFarmsUuids)) {
                continue;
            }
            $farmObj = new Farm();
            $form = $formFactory->create(FarmType::class, $farmObj);
            $form->submit([
                'organizationId' => $organization->getId(),
                'name' => $farm['name'],
                'uuid' => $farm['uuid'],
            ], false);
            $this->geoSCANAPIClient->createFarm($farmObj);
        }
    }

    public function mapTfUserPermissions(User $user, Organization $organization, array $tfAccoun)
    {
        $usersPermissions
            = [
                'organizationId' => $organization->getId(),
                'organizationIdentityNumber' => $organization->getIdentityNumber(),
                'userPermissions' => [
                ],
            ];
        if ($tfAccoun['has_map']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'MAP_RIGHTS_R',
                'value' => 1,
                'checked' => true,
            ];
            $usersPermissions['userPermissions'][] = [
                'name' => 'MAP_RIGHTS_RW',
                'value' => 12,
                'checked' => true,
            ];
        }
        if ($tfAccoun['has_plots']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'PLOT_RIGHTS_R',
                'value' => 2,
                'checked' => true,
            ];

            $usersPermissions['userPermissions'][] = [
                'name' => 'PLOT_RIGHTS_RW',
                'value' => 14,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_warehouse']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'WAREHOUSE_RIGHTS',
                'value' => 27,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_equity']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'EQUITY_RIGHTS',
                'value' => 10,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_agro_tech']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'AGRO_RIGHTS',
                'value' => 4,
                'checked' => true,
            ];
            $usersPermissions['userPermissions'][] = [
                'name' => 'AGRO_RIGHTS_RW',
                'value' => 16,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_export']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'EXPORT_MASS_PAYMENT_RIGHTS',
                'value' => 29,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_cadastre']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'CADASTRE_RIGHTS',
                'value' => 30,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_slope']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'SLOPE_RIGHTS',
                'value' => 31,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_cow']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'CONTRACTS_OWN_WRITE_RIGHTS',
                'value' => 9,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_kvs_c']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'KVS_CUTTING_RIGHTS',
                'value' => 28,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_crr']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'COLLECTIONS_RIGHTS',
                'value' => 24,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_crw']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'COLLECTIONS_RIGHTS_RW',
                'value' => 25,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_tmrr']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'THEMATIC_MAPS_RIGHTS_R',
                'value' => 22,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_tmrw']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'THEMATIC_MAPS_RIGHTS_RW',
                'value' => 23,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_hrr']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'HYPOTHECS_RIGHTS_R',
                'value' => 20,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_hrw']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'HYPOTHECS_RIGHTS_RW',
                'value' => 21,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_scr']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'SALES_CONTRACTS_RIGHTS_R',
                'value' => 18,
                'checked' => true,
            ];
        }

        if ($tfAccoun['has_scrw']) {
            $usersPermissions['userPermissions'][] = [
                'name' => 'SALES_CONTRACTS_RIGHTS_RW',
                'value' => 19,
                'checked' => true,
            ];
        }

        $up = new UserPermissions();
        $form = $this->formFactory->create(UserPermissionsType::class, $up);
        $form->submit($usersPermissions);

        $user->setPermissions([$up]);
    }

    public function getIsDatabaseExists(string $databaseName, string $tfSusiMainConnString)
    {
        $databaseExistStmt = $this->em->getConnection()->executeQuery("SELECT 
            * 
        FROM dblink(?, $$
            SELECT datname FROM pg_catalog.pg_database WHERE lower(datname) = lower('{$databaseName}');
        $$) t(
            name text)", [$tfSusiMainConnString]);

        return $databaseExistStmt->fetchFirstColumn();
    }

    public function validateOrganization(array $data)
    {
        $formType = OrganizationType::class;
        $organization = new Organization();
        $form = $this->formFactory->create($formType, $organization, ['exclude_users_and_farms' => false, 'allow_extra_fields' => true]);

        $form->submit($data, false);

        foreach ($form->getErrors(true) as $error) {
            $this->output->writeln('<error>' . $error->getCause() . '</error>');
        }

        if (!$form->isValid()) {
            throw new Exception($form->getErrors(true));
        }

        return $organization;
    }

    public function createOrganization(Organization $organization)
    {
        $organizationToReturn = clone ($organization);
        $users = $organization->getUsersAssigned();

        $noneExistingUsers = [];
        for ($i = 0; $i < count($users); $i++) {
            $user = $users[$i];

            if ($this->checkUsernameExistence($user->getUsername())) {
                continue;
            }

            $noneExistingUsers[] = $user;
        }
        $orgManager = current($users);
        $orgManager->setPasswordReType($orgManager->getPassword());
        // add only non existant users
        $organization->setUsersAssigned($noneExistingUsers);
        $gs = $this->geoSCANAPIClient->saveOrganization($organization, true);

        $orgManager->setOrganizations([$organization]);

        // return all users to organization, we will add them later in kc
        $organizationToReturn->setId($gs->getId());

        return $organizationToReturn;
    }

    public function createContract(
        int $orgId,
        array $tfAccoun,
        string $databaseName
    ) {
        $ekattesCount = $this->getOrganziationEkattesCount($databaseName);

        $salesPersonsMap = [
            10 => [
                'id' => 16067,
                'username' => 'ivelina_yovcheva',
            ],
            11 => [
                'id' => 16339,
                'username' => 'miroslava_todorova',
            ],
            12 => [
                'id' => 16418,
                'username' => 'milena_vasileva',
            ],
            14 => [
                'id' => 16848,
                'username' => 'todor.garev',
            ],
            8 => [
                'id' => 15631,
                'username' => 'chavdar_sabev',
            ],
            15 => [
                'id' => 16843,
                'username' => 'zhivko.kanchev',
            ],
            16 => [
                'id' => 16845,
                'username' => 'plamen.arnaudov',
            ],
            17 => [
                'id' => 16935,
                'username' => 'kalin_milkov',
            ],
            7 => [
                'id' => 15156,
                'username' => 'marta',
            ],
        ];

        $serviceUser = $salesPersonsMap[$tfAccoun['salesperson_id']];

        $subscriptionPackages = [];
        if ($tfAccoun['has_map']) {
            $subscriptionPackages[] = [
                'period' => 1,
                'package' => 'Administrative map',
                'startDate' => '2023-10-01 00:00:00',
                'endDate' => '2024-5-31 23:59:59',
                'amount' => $ekattesCount,
            ];
        }

        if ($tfAccoun['has_plots']) {
            $subscriptionPackages[] = [
                'period' => 1,
                'package' => 'Land properties',
                'startDate' => '2023-10-01 00:00:00',
                'endDate' => '2024-5-31 23:59:59',
                'amount' => 1,
            ];
        }

        if ($tfAccoun['has_warehouse']) {
            $subscriptionPackages[] = [
                'period' => 1,
                'package' => 'Warehouse',
                'startDate' => '2023-10-01 00:00:00',
                'endDate' => '2024-5-31 23:59:59',
                'amount' => 1,
            ];
        }
        if ($tfAccoun['has_equity']) {
            $subscriptionPackages[] = [
                'period' => 1,
                'package' => 'Shared capital',
                'startDate' => '2023-10-01 00:00:00',
                'endDate' => '2024-5-31 23:59:59',
                'amount' => 1,
            ];
        }
        if ($tfAccoun['has_agro_tech']) {
            $subscriptionPackages[] = [
                'period' => 1,
                'package' => 'Agrotechnics',
                'startDate' => '2023-10-01 00:00:00',
                'endDate' => '2024-5-31 23:59:59',
                'amount' => 1,
            ];
        }

        $data = [
            'startDate' => '2023-10-01 00:00:00',
            'endDate' => '2024-5-31 23:59:59',
            'customerIdentification' => $tfAccoun['eik'],
            'organizationId' => $orgId,
            'responsibleUsers' => [
                [
                    'username' => $serviceUser['username'],
                    'role' => 'SERVICE',
                    'userId' => $serviceUser['id'],
                ],
                [
                    'username' => 'milen.georgiev',
                    'role' => 'SAMPLER',
                    'userId' => 15150,
                ],
            ],
            'duration' => 1,
            'durationType' => 'farming_year',
            'prices' => [
                [
                    'period' => 1,
                    'amount' => 0,
                ],
            ],
            'subscriptionPackages' => $subscriptionPackages,
            'contractDate' => date('y-m-d'),
            'area' => 0,
        ];

        $contract = $this->contractService->create('subscription', $data);

        $this->workflowContractService->activateContract($contract);
        $this->doctrine->getManager()->flush();

        $this->updateGoogleSheet($tfAccoun['username'], true);

        $this->geoSCANAPIClient->addServiceUserToOrganization($contract);
    }

    /**
     * Summary of configure.
     */
    protected function configure()
    {
        $this
            // the short description shown while running "php bin/console list"
            ->setDescription('Creates all organizations from TF which doesn\'t exist in GS.')
            ->addOption('execute_by_cron', 'c', InputOption::VALUE_OPTIONAL, '', true)
            ->addOption('org_tf_base', 'b', InputOption::VALUE_OPTIONAL, 'Database with organization data', 'org_tf')
            ->addOption('eik', 'k', InputOption::VALUE_OPTIONAL, 'A specific EIK.');
    }

    private function processContracts(): void
    {
        $tfSusiMainConnString = $this->getMainDbConfig();
        $mainQuery = $this->getUsersForContractsCreation();
        $stmt = $this->em->getConnection()->executeQuery($mainQuery, [$tfSusiMainConnString]);
        $tfAccounts = $stmt->fetchAllAssociative();

        for ($i = 0; $i < count($tfAccounts); $i++) {
            try {
                $tfAccount = $tfAccounts[$i];

                $database = $tfAccount['database'];
                $orgId = str_replace('db_bg_', '', $database);

                $this->createContract($orgId, $tfAccount, $database);

                $this->em->getConnection()->executeQuery(
                    "SELECT 
                    * 
                    FROM dblink(?, $$
                    UPDATE 
                        su_users 
                    SET 
                        create_cms_contracts = false
                    WHERE 
                        group_id = '{$tfAccount['group_id']}'
                    $$) t(
                        updated text)",
                    [$tfSusiMainConnString]
                );
            } catch (Throwable $th) {
                $this->log($th, $tfAccount);
            }
        }
    }

    private function getUsersForContractsCreation()
    {
        $mainQuery = "SELECT * from dblink(?, $$ SELECT
            su.identity_number AS eik,
            su.id AS group_id,
            su.active,
            su.username,
            su.database,
            su.\"name\",
            ARRAY[1,12] && array_agg(DISTINCT sur.right_id) AS has_map,
            ARRAY[2,14] && array_agg(DISTINCT sur.right_id) AS has_plots,
            ARRAY[10] && array_agg(DISTINCT sur.right_id) AS has_equity,
            ARRAY[27] && array_agg(DISTINCT sur.right_id) AS has_warehouse,
            ARRAY[4,16] && array_agg(DISTINCT sur.right_id) AS has_agro_tech,
            ARRAY[18] && array_agg(DISTINCT sur.right_id) AS has_scr,
            ARRAY[19] && array_agg(DISTINCT sur.right_id) AS has_scrw,
            ARRAY[20] && array_agg(DISTINCT sur.right_id) AS has_hrr,
            ARRAY[21] && array_agg(DISTINCT sur.right_id) AS has_hrw,
            ARRAY[22] && array_agg(DISTINCT sur.right_id) AS has_tmrr,
            ARRAY[23] && array_agg(DISTINCT sur.right_id) AS has_tmrw,
            ARRAY[24] && array_agg(DISTINCT sur.right_id) AS has_crr,
            ARRAY[25] && array_agg(DISTINCT sur.right_id) AS has_crw,
            ARRAY[9] && array_agg(DISTINCT sur.right_id) AS has_cow,
            ARRAY[28] && array_agg(DISTINCT sur.right_id) AS has_kvs_c,
            ARRAY[29] && array_agg(DISTINCT sur.right_id) AS has_export,
            ARRAY[30] && array_agg(DISTINCT sur.right_id) AS has_cadastre,
            ARRAY[31] && array_agg(DISTINCT sur.right_id) AS has_slope,
            sus.id AS salesperson_id,
            sus.\"name\" AS salesperson,
            jsonb_build_object(
                'name', su.name,
                'identityNumber', su.identity_number,
                'vatNumber', su.identity_number,
                'addresses', '[]'::jsonb,
                'farms', jsonb_agg (DISTINCT jsonb_build_object('name', suf.\"name\")),
                'contactPersons', '[]'::jsonb,
                'usersAssigned', jsonb_build_array(
                    jsonb_build_object(
                        'tf_id', su.id,
                        'organizations', '[]'::jsonb,
                        'role', jsonb_build_object(
                            'name', 'ORGANIZATION_MANAGER'
                        ),
                        'permissions', '[]'::jsonb,
                        'username', su.username,
                        'email', su.email,
                        'password', ssu.\"password\",
                        'name', su.name,
                        'phone', su.phone,
                        'active', su.active
                    )
                ) || 
                CASE 
                    WHEN max(sub.id) isnull THEN '[]'::jsonb
                ELSE jsonb_agg (
                    DISTINCT jsonb_build_object(
                        'tf_id', sub.id,
                        'organizations', '[]'::jsonb,
                        'role', jsonb_build_object(
                            'name', 'EMPLOEE'
                        ),
                        'permissions', '[]'::jsonb,
                        'username', sub.username,
                        'email', sub.email,
                        'password', ssub.password,
                        'name', sub.name,
                        'phone', su.phone,
                        'active', sub.active
                    )
                )
                END
                ) AS organization_data
            FROM
                su_users su
            LEFT JOIN su_system_users ssu ON su.id = ssu.user_id
            LEFT JOIN su_users_salesman sus ON sus.id =su.salesperson_id 
            LEFT JOIN su_users_rights sur ON sur.user_id = su.id 
            LEFT JOIN su_users_farming suf ON suf.user_id = su.id
            LEFT JOIN su_users sub ON sub.group_id = su.id and sub.\"level\" = 3 AND sub.id != su.id 
            LEFT JOIN su_system_users ssub ON sub.id = ssub.user_id
            WHERE 
                su.\"level\" = 2 
                AND su.\"create_cms_contracts\" is true
                AND  LOWER(su.\"database\") LIKE '%db_bg_%'            
        ";

        $mainQuery .= '
            GROUP BY 
                    su.id, su.username, su.name, su.identity_number, su.email, ssu."password", sus.id;
            $$) AS t(eik varchar, group_id int, active boolean, username varchar, database varchar, name varchar, has_map boolean, has_plots boolean, has_equity boolean, 
            has_warehouse boolean, has_agro_tech boolean,
            has_scr boolean, has_scrw boolean,
            has_hrr boolean, has_hrw boolean,
            has_tmrr boolean, has_tmrw boolean,
            has_crr boolean, has_crw boolean,
            has_cow boolean, has_kvs_c boolean,
            has_export boolean, has_cadastre boolean,
            has_slope boolean,
            salesperson_id int, salesperson varchar, organization_data json)
        ';

        return $mainQuery;
    }

    private function getTFUsersWaitingGSIntegration()
    {
        $mainQuery = "SELECT * from dblink(?, $$ SELECT
                su.identity_number AS eik,
                su.id AS group_id,
                su.active,
                su.username,
                su.\"name\",
                ARRAY[1,12] && array_agg(DISTINCT sur.right_id) AS has_map,
                ARRAY[2,14] && array_agg(DISTINCT sur.right_id) AS has_plots,
                ARRAY[10] && array_agg(DISTINCT sur.right_id) AS has_equity,
                ARRAY[27] && array_agg(DISTINCT sur.right_id) AS has_warehouse,
                ARRAY[4,16] && array_agg(DISTINCT sur.right_id) AS has_agro_tech,
                ARRAY[18] && array_agg(DISTINCT sur.right_id) AS has_scr,
                ARRAY[19] && array_agg(DISTINCT sur.right_id) AS has_scrw,
                ARRAY[20] && array_agg(DISTINCT sur.right_id) AS has_hrr,
                ARRAY[21] && array_agg(DISTINCT sur.right_id) AS has_hrw,
                ARRAY[22] && array_agg(DISTINCT sur.right_id) AS has_tmrr,
                ARRAY[23] && array_agg(DISTINCT sur.right_id) AS has_tmrw,
                ARRAY[24] && array_agg(DISTINCT sur.right_id) AS has_crr,
                ARRAY[25] && array_agg(DISTINCT sur.right_id) AS has_crw,
                ARRAY[9] && array_agg(DISTINCT sur.right_id) AS has_cow,
                ARRAY[28] && array_agg(DISTINCT sur.right_id) AS has_kvs_c,
                ARRAY[29] && array_agg(DISTINCT sur.right_id) AS has_export,
                ARRAY[30] && array_agg(DISTINCT sur.right_id) AS has_cadastre,
                ARRAY[31] && array_agg(DISTINCT sur.right_id) AS has_slope,
                sus.id AS salesperson_id,
                sus.\"name\" AS salesperson,
                jsonb_build_object(
                    'name', su.name,
                    'identityNumber', su.identity_number,
                    'vatNumber', su.identity_number,
                    'addresses', '[]'::jsonb,
                    'farms', jsonb_agg (DISTINCT jsonb_build_object('name', suf.\"name\", 'uuid', suf.\"uuid\")),
                    'contactPersons', '[]'::jsonb,
                    'usersAssigned', jsonb_build_array(
                        jsonb_build_object(
                            'tf_id', su.id,
                            'organizations', '[]'::jsonb,
                            'role', jsonb_build_object(
                                'name', 'ORGANIZATION_MANAGER'
                            ),
                            'permissions', '[]'::jsonb,
                            'username', su.username,
                            'email', su.email,
                            'password', ssu.\"password\",
                            'name', su.name,
                            'phone', su.phone,
                            'active', su.active
                        )
                    ) || 
                    CASE 
                        WHEN max(sub.id) isnull THEN '[]'::jsonb
                    ELSE jsonb_agg (
                        DISTINCT jsonb_build_object(
                            'tf_id', sub.id,
                            'organizations', '[]'::jsonb,
                            'role', jsonb_build_object(
                                'name', 'EMPLOEE'
                            ),
                            'permissions', '[]'::jsonb,
                            'username', sub.username,
                            'email', sub.email,
                            'password', ssub.password,
                            'name', sub.name,
                            'phone', su.phone,
                            'active', sub.active
                        )
                    )
                    END
                    ) AS organization_data
                FROM
                    su_users su
                LEFT JOIN su_system_users ssu ON su.id = ssu.user_id
                LEFT JOIN su_users_salesman sus ON sus.id =su.salesperson_id 
                LEFT JOIN su_users_rights sur ON sur.user_id = su.id 
                LEFT JOIN su_users_farming suf ON suf.user_id = su.id
                LEFT JOIN su_users sub ON sub.group_id = su.id and sub.\"level\" =3 AND sub.id != su.id 
                LEFT JOIN su_system_users ssub ON sub.id = ssub.user_id
                WHERE 
                    su.\"level\" = 2 
                    AND su.\"waiting_gs_integration\" is true
                    AND LOWER(su.\"database\") NOT LIKE '%db_bg_%'
                    AND su.identity_number NOTNULL          
            ";

        $mainQuery .= '
            GROUP BY 
                    su.id, su.username, su.name, su.identity_number, su.email, ssu."password", sus.id;
            $$) AS t(eik varchar, group_id int, active boolean, username varchar, name varchar, has_map boolean, has_plots boolean, has_equity boolean, 
            has_warehouse boolean, has_agro_tech boolean,
            has_scr boolean, has_scrw boolean,
            has_hrr boolean, has_hrw boolean,
            has_tmrr boolean, has_tmrw boolean,
            has_crr boolean, has_crw boolean,
            has_cow boolean, has_kvs_c boolean,
            has_export boolean, has_cadastre boolean,
            has_slope boolean,
            salesperson_id int, salesperson varchar, organization_data json)
        ';

        return $mainQuery;
    }

    private function getOrgTfUsers($selectedEik, string $database): string
    {
        $mainQuery = "SELECT * from dblink(?, $$ SELECT
                ot.real_eik AS eik,
                su.id AS group_id,
                su.active,
                su.username,
                su.\"name\",
                ARRAY[1,12] && array_agg(DISTINCT sur.right_id) AS has_map,
                ARRAY[2,14] && array_agg(DISTINCT sur.right_id) AS has_plots,
                ARRAY[10] && array_agg(DISTINCT sur.right_id) AS has_equity,
                ARRAY[27] && array_agg(DISTINCT sur.right_id) AS has_warehouse,
                ARRAY[4,16] && array_agg(DISTINCT sur.right_id) AS has_agro_tech,
                ARRAY[18] && array_agg(DISTINCT sur.right_id) AS has_scr,
                ARRAY[19] && array_agg(DISTINCT sur.right_id) AS has_scrw,
                ARRAY[20] && array_agg(DISTINCT sur.right_id) AS has_hrr,
                ARRAY[21] && array_agg(DISTINCT sur.right_id) AS has_hrw,
                ARRAY[22] && array_agg(DISTINCT sur.right_id) AS has_tmrr,
                ARRAY[23] && array_agg(DISTINCT sur.right_id) AS has_tmrw,
                ARRAY[24] && array_agg(DISTINCT sur.right_id) AS has_crr,
                ARRAY[25] && array_agg(DISTINCT sur.right_id) AS has_crw,
                ARRAY[9] && array_agg(DISTINCT sur.right_id) AS has_cow,
                ARRAY[28] && array_agg(DISTINCT sur.right_id) AS has_kvs_c,
                ARRAY[29] && array_agg(DISTINCT sur.right_id) AS has_export,
                ARRAY[30] && array_agg(DISTINCT sur.right_id) AS has_cadastre,
                ARRAY[31] && array_agg(DISTINCT sur.right_id) AS has_slope,
                sus.id AS salesperson_id,
                sus.\"name\" AS salesperson,
                jsonb_build_object(
                    'name', ot.real_name,
                    'identityNumber', ot.real_eik,
                    'vatNumber', ot.real_eik,
                    'addresses', '[]'::jsonb,
                    'farms', jsonb_agg (DISTINCT jsonb_build_object('name', suf.\"name\", 'uuid', suf.\"uuid\")),
                    'contactPersons', '[]'::jsonb,
                    'usersAssigned', jsonb_build_array(
                        jsonb_build_object(
                            'tf_id', su.id,
                            'organizations', '[]'::jsonb,
                            'role', jsonb_build_object(
                                'name', 'ORGANIZATION_MANAGER'
                            ),
                            'permissions', '[]'::jsonb,
                            'username', su.username,
                            'email', ot. \"e-mail\",
                            'password', ssu.\"password\",
                            'name', ot.real_name,
                            'phone', su.phone,
                            'active', su.active
                        )
                    ) || 
                    CASE 
                        WHEN max(sub.id) isnull THEN '[]'::jsonb
                    ELSE jsonb_agg (
                        DISTINCT jsonb_build_object(
                            'tf_id', sub.id,
                            'organizations', '[]'::jsonb,
                            'role', jsonb_build_object(
                                'name', 'EMPLOEE'
                            ),
                            'permissions', '[]'::jsonb,
                            'username', sub.username,
                            'email', sub.email,
                            'password', ssub.password,
                            'name', sub.name,
                            'phone', su.phone,
                            'active', sub.active
                        )
                    )
                    END
                    ) AS organization_data
                FROM
                    su_users su
                LEFT JOIN su_system_users ssu ON su.id = ssu.user_id
                LEFT JOIN su_users_salesman sus ON sus.id =su.salesperson_id 
                LEFT JOIN su_users_rights sur ON sur.user_id = su.id 
                LEFT JOIN {$database} ot ON ot.username = su.username
                LEFT JOIN su_users_farming suf ON suf.user_id = su.id
                LEFT JOIN su_users sub ON sub.group_id = su.id and sub.\"level\" =3 AND sub.id != su.id 
                LEFT JOIN su_system_users ssub ON sub.id = ssub.user_id
                WHERE 
                    su.\"level\" = 2 
                    AND ot.real_eik NOTNULL
                    AND ot.passed = false
            ";

        if ($selectedEik) {
            $mainQuery .= " AND ot.real_eik='{$selectedEik}' ";
        }

        $mainQuery .= '
            GROUP BY 
                    su.id, ot.username, ot.real_name, ot.real_eik, ot. "e-mail", ssu."password", sus.id;
            $$) AS t(eik varchar, group_id int, active boolean, username varchar, name varchar, has_map boolean, has_plots boolean, has_equity boolean, 
            has_warehouse boolean, has_agro_tech boolean,
            has_scr boolean, has_scrw boolean,
            has_hrr boolean, has_hrw boolean,
            has_tmrr boolean, has_tmrw boolean,
            has_crr boolean, has_crw boolean,
            has_cow boolean, has_kvs_c boolean,
            has_export boolean, has_cadastre boolean,
            has_slope boolean,
            salesperson_id int, salesperson varchar, organization_data json)
        ';

        return $mainQuery;
    }

    private function addUserToKeycloak($user)
    {
        try {
            $this->userService->saveKeycloakUser($user, true, 'bg');
        } catch (Throwable $th) {
            if (self::KEYCLOAK_EMAIL_ALREADY_EXISTS === $th->getMessage()) {
                $user->setEmail('');
                $this->userService->saveKeycloakUser($user, true, 'bg');
            }
        }

        $kcUser = $this->userService->getKeycloakUser($user->getUsername());
        $user->setKeycloakUid($kcUser->getId());

        // set kc id to gs user
        if (!$user->getId()) {
            Log::toFile('z_diff_in_eik', [$user->getUsername()]);

            return;
        }

        // recursion unset user organizations users
        $organizations = $user->getOrganizations();
        foreach ($organizations as $organization) {
            $organization->setUsersAssigned([]);
        }
        $user->setOrganizations([$organizations]);

        $this->geoSCANAPIClient->saveUser($user, $isNew = false);
    }

    private function checkUsernameExistence($username)
    {
        try {
            $this->geoSCANAPIClient->checkUsernameExistence($username);

            return true;
        } catch (NotFoundHttpException $ex) {
            return false;
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    private function log(Throwable $th, $tfAccount)
    {
        $this->updateGoogleSheet($tfAccount['username'], false, $th);
        Log::toFile('z_err', [$th->getMessage(), $th->getTraceAsString(), $th->getLine(), $tfAccount['eik'], $tfAccount['username'], $tfAccount['name'], $tfAccount['group_id']]);
    }

    private function updateGoogleSheet($username, $contractsCreationStatus = false, $ex = null)
    {
        $requestData = [
            'method' => Request::METHOD_POST,
            'url' => 'json=google-sheets',
            'multipart' => [
                [
                    'name' => 'username',
                    'contents' => $username,
                ],
            ],
        ];

        if ($ex instanceof Throwable) {
            $requestData['multipart'][] = [
                'name' => 'exception',
                'contents' => $ex->getMessage(),
            ];

            $requestData['multipart'][] = [
                'name' => 'exception_code',
                'contents' => $ex->getCode(),
            ];
        }

        if (true === $contractsCreationStatus) {
            $requestData['multipart'][] = [
                'name' => 'contracts_created',
                'contents' => $contractsCreationStatus,
            ];
        }

        return $this->makeRequest($requestData['method'], $requestData['url'], $requestData['multipart']);
    }

    /**
     * @param $body
     *
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    private function makeRequest($method, $url, $multipart)
    {
        try {
            $response = $this->httpClient->request($method, self::TF_MIGRATOR_BASE_URL . $url, [
                'multipart' => $multipart,
            ]);

            $responseContent = json_decode($response->getBody()->getContents(), true);

            if (isset($responseContent['error'])) {
                throw new LogicException($responseContent['error']['message'], 422);
            }

            return $responseContent['result'];
        } catch (ClientException $e) {
            if (404 == $e->getCode()) {
                return false;
            }
        }
    }
}
