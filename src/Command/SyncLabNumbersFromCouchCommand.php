<?php

namespace App\Command;

use App\Entity\Analysis\PackageGridPoints;
use App\Entity\Contract\SubscriptionPackage;
use App\EntityGeoscan\ServiceProviderGeoScan;
use App\Service\AgroLab\AgroLabService;
use App\Service\Analysis\LabElementsResultsService;
use App\Service\Analysis\PackageGridPointsService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class SyncLabNumbersFromCouchCommand extends Command
{
    protected static $defaultName = 'app:sync-lab-numbers-from-couch';
    private EntityManagerInterface $em;
    private EntityManagerInterface $gsEm;
    private AgroLabService $agroLabService;
    private PackageGridPointsService $packageGridPointsService;
    private LabElementsResultsService $labElementsResultsService;

    public function __construct(
        PackageGridPointsService $packageGridPointsService,
        EntityManagerInterface $entityManager,
        ManagerRegistry $managerRegistry,
        AgroLabService $agroLabService,
        LabElementsResultsService $labElementsResultsService
    ) {
        $this->em = $entityManager;
        $this->gsEm = $managerRegistry->getManager('geoscan');
        $this->agroLabService = $agroLabService;
        $this->packageGridPointsService = $packageGridPointsService;
        $this->labElementsResultsService = $labElementsResultsService;

        parent::__construct();
    }

    protected function configure()
    {
        $this->setDescription('Sync Lab Numbers From Couch.');
    }

    /**
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        try {
            $packageGridPointsRepo = $this->em->getRepository(PackageGridPoints::class);
            $serviceProviderRepo = $this->gsEm->getRepository(ServiceProviderGeoScan::class);

            $serviceProviders = $serviceProviderRepo->findAll();

            foreach ($serviceProviders as $serviceProvider) {
                $config = $serviceProvider->getCouchDBConfig();

                if (!$config) {
                    continue;
                }

                $subscriptionPackageRepo = $this->em->getRepository(SubscriptionPackage::class);
                $packages = $subscriptionPackageRepo->findBy(['serviceProvider' => $serviceProvider]);

                foreach ($packages as $package) {
                    $barcodes = $packageGridPointsRepo->getBarcodesBy($package->getId(), PackageGridPoints::STATE_SAMPLED);
                    $barcodes = array_column($barcodes, 'barcode');

                    if (!$barcodes) {
                        continue;
                    }

                    $barcodesChunk = array_chunk($barcodes, 450);
                    foreach ($barcodesChunk as $barcodesRow) {
                        $arrData = $this->agroLabService->getCouchDbLabNumbers($barcodesRow, $config);
                        $this->packageGridPointsService->saveLabNumbers($arrData);
                        $this->labElementsResultsService->updateCalculatedValuesByLabNumbers($arrData);
                    }
                }
            }
        } catch (Exception $exception) {
            $output->writeln([
                'Error',
                '============',
                $exception->getMessage(),
            ]);

            return 1;
        }

        $output->writeln([
            'Result',
            '============',
            'Done.',
        ]);

        return 0;
    }
}
