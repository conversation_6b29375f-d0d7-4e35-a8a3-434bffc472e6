<?php

namespace App\Command;

use App\Entity\Analysis\PackageGridPoints;
use App\Entity\Contract\SubscriptionPackage;
use App\EntityGeoscan\ServiceProviderGeoScan;
use App\Service\AgroLab\AgroLabService;
use App\Service\Analysis\LabElementsResultsService;
use App\Service\Analysis\PackageGridPointsService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class UpdateStateUpdateAtColumnFromCouchCommand extends Command
{
    protected static $defaultName = 'app:update-state-updated-at-column-from-couch';
    private EntityManagerInterface $em;

    private EntityManagerInterface $gsEm;

    private ParameterBagInterface $parameters;
    private AgroLabService $agroLabService;
    private PackageGridPointsService $packageGridPointsService;
    private LabElementsResultsService $elementsResultsService;

    public function __construct(
        EntityManagerInterface $entityManager,
        ParameterBagInterface $parameters,
        AgroLabService $agroLabService,
        PackageGridPointsService $packageGridPointsService,
        LabElementsResultsService $elementsResultsService,
        ManagerRegistry $managerRegistry
    ) {
        $this->em = $entityManager;
        $this->gsEm = $managerRegistry->getManager('geoscan');
        $this->parameters = $parameters;
        $this->agroLabService = $agroLabService;
        $this->packageGridPointsService = $packageGridPointsService;
        $this->elementsResultsService = $elementsResultsService;

        parent::__construct();
    }

    protected function configure()
    {
        $this->setDescription('Update column state_updated_at in package_grid_point from couchDB.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        try {
            $packageGridPointsRepo = $this->em->getRepository(PackageGridPoints::class);
            $serviceProviderRepo = $this->gsEm->getRepository(ServiceProviderGeoScan::class);
            $serviceProviders = $serviceProviderRepo->findAll();

            foreach ($serviceProviders as $serviceProvider) {
                $io->title("Get packages for service provider {$serviceProvider->getSlug()}");

                $config = $serviceProvider->getCouchDBConfig();
                $subscriptionPackageRepo = $this->em->getRepository(SubscriptionPackage::class);
                $packages = $subscriptionPackageRepo->findBy(
                    [
                        'serviceProvider' => $serviceProvider,
                        'status' => SubscriptionPackage::STATUS_ACTIVE,
                        'state' => SubscriptionPackage::STATUS_IN_PROGRESS,
                    ]
                );

                foreach ($packages as $package) {
                    $io->comment([
                        "Get barcodes for package id {$package->getId()}",
                    ]);

                    $barcodes = $packageGridPointsRepo->getBarcodesBy($package->getId(), 'ReceivedInLab');
                    $barcodes = array_column($barcodes, 'barcode');

                    if (!$barcodes) {
                        $io->note([
                            "no packet barcodes found for package {$package->getId()}",
                        ]);

                        continue;
                    }

                    $barcodesChunk = array_chunk($barcodes, 450);
                    foreach ($barcodesChunk as $barcodesRow) {
                        $arrData = $this->agroLabService->getCouchDbLabNumbers($barcodesRow, $config);
                        $io->note([
                            'Update state_updated_at column for package grid point',
                        ]);
                        $this->packageGridPointsService->updateColumnStateUpdateAt($arrData);
                    }

                    $io->success([
                        'The state_updated_at has been updated!',
                    ]);
                }
            }
        } catch (Exception $exception) {
            $io->error($exception->getMessage());

            return 1;
        }

        $io->success('All is Done!');

        return 0;
    }
}
