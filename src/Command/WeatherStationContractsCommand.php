<?php

namespace App\Command;

use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Currency;
use App\Entity\Helper\WeatherStationHelper;
use App\Entity\ServiceProvider;
use App\Service\ContractService;
use App\Service\DurationTypeService;
use App\Service\GeoSCANAPIClient;
use App\Service\Workflow\ContractService as WorkflowContractService;
use App\Service\Workflow\SubscriptionPackageService as WorkFlowSubscriptionPackageService;
use DateTime;
use DateTimeZone;
use Doctrine\ORM\EntityManagerInterface;
use Proxies\__CG__\App\Entity\Contract\DurationType;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Security\Core\Security;

class WeatherStationContractsCommand extends Command
{
    protected static $defaultName = 'app:weather-station-contracts';

    private $em;
    private $durationTypeService;
    private $security;
    private $contractService;
    private $workflowContractService;
    private $workFlowSubscriptionPackageService;
    private $geoSCANAPIClient;

    public function __construct(
        EntityManagerInterface $entityManager,
        DurationTypeService $durationTypeService,
        Security $security,
        ContractService $contractService,
        WorkflowContractService $workflowContractService,
        WorkFlowSubscriptionPackageService $workFlowSubscriptionPackageService,
        GeoSCANAPIClient $geoSCANAPIClient
    ) {
        $this->em = $entityManager;
        $this->durationTypeService = $durationTypeService;
        $this->security = $security;
        $this->contractService = $contractService;
        $this->workflowContractService = $workflowContractService;
        $this->workFlowSubscriptionPackageService = $workFlowSubscriptionPackageService;
        $this->geoSCANAPIClient = $geoSCANAPIClient;

        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setDescription('Command for creating weather station contracts with data from weather_station_helper database(GPS-227/229)')
            ->addArgument('service_provider_name', InputArgument::OPTIONAL, 'Service provider name to retrieve the data of the provider to which you want the script to run');
    }

    /**
     * Undocumented function.
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $serviceProviderName = $input->getArgument('service_provider_name');

        if (!$serviceProviderName) {
            $io->error("Parameter 'service_provider_name' is required!");

            return 0;
        }

        $confirm = $io->confirm('Are you sure you want to execute this script. It will generate contracts for weather station packages');

        if (!$confirm) {
            return 0;
        }

        $io->title('Command for creating weather station contracts');
        $io->note(sprintf('You passed an argument for service provider name: %s', $serviceProviderName));
        $serviceProvider = $this->em->getRepository(ServiceProvider::class)->findOneBy(['name' => $serviceProviderName]);

        if (!$serviceProvider) {
            $io->error('Service provider name is not found!');

            return 0;
        }

        $wsh = $this->em->getRepository(WeatherStationHelper::class)->getSortedDataByPeriod();
        $currency = $this->em->getRepository(Currency::class)->findOneBy(['serviceProvider' => $serviceProvider]);

        if (!$wsh) {
            $io->note(sprintf('No data found from helper database'));

            return 0;
        }

        if (!$currency) {
            $io->note(sprintf('No data found for currency by %s provider ', $serviceProviderName));

            return 0;
        }

        $arrContractsId = [];
        foreach ($wsh as $key => $stationData) {
            $durationType = $this->em->getRepository(DurationType::class)->findOneBy(['slug' => $stationData['station_period']]);

            $startDate = DateTime::createFromFormat('Y-m-d', $stationData['station_start_date'])->setTimezone(new DateTimeZone('UTC'))->setTime(0, 0, 0);
            $endDate = DateTime::createFromFormat('Y-m-d', $stationData['station_end_date'])->setTimezone(new DateTimeZone('UTC'))->setTime(23, 59, 59);
            $periods = $this->durationTypeService->calculatePeriods($durationType, $startDate, $endDate);

            $contract = [
                'contractDate' => $stationData['station_start_date'],
                'startDate' => $stationData['station_start_date'] . ' 00:00:00',
                'endDate' => $stationData['station_end_date'] . ' 23:59:59',
                'currency' => $currency->getSlug(),
                'serviceProvider' => $serviceProvider->getId(),
                'area' => 0,
                'customerIdentification' => $stationData['organization_ident_number'],
                'responsibleUsers' => [
                    [
                        'role' => 'SERVICE',
                        'username' => 'Please select',
                    ],
                    [
                        'role' => 'SAMPLER',
                        'username' => 'Please select',
                    ],
                ],
                'duration' => count($periods),
                'durationType' => $durationType->getSlug(),
                'subscriptionPackages' => [],
            ];

            foreach ($periods as $key => $period) {
                $contract['prices'][] = [
                    'period' => ++$key,
                    'amount' => 0,
                    'serviceProvider' => $serviceProvider->getId(),
                ];

                $contract['subscriptionPackages'][] = [
                    'period' => $key,
                    'package' => 'Weather stations',
                    'amount' => count(json_decode($stationData['stations_id'])),
                    'serviceProvider' => $serviceProvider->getId(),
                ];
            }

            $contract = $this->contractService->create('subscription', $contract);

            $this->workflowContractService->activateContract($contract);
            $this->em->flush();

            $arrContractsId[] = $contract->getId();

            $this->geoSCANAPIClient->setContractIdToStations($contract, json_decode($stationData['stations_id']));

            $io->note(sprintf('Contract: %s was created', $contract->getId()));
        }

        $params[] = ['field' => 'contract', 'type' => 'IN', 'value' => $arrContractsId];
        $this->workFlowSubscriptionPackageService->updateByFields($params, SubscriptionPackage::TRANSITION_IN_PROGRESS_STATE);

        $io->success('All contracts have been successfully created');

        return 0;
    }
}
