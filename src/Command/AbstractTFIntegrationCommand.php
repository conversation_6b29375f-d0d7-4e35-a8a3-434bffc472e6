<?php

namespace App\Command;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;

abstract class AbstractTFIntegrationCommand extends Command
{
    protected $em;

    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;

        parent::__construct();
    }

    protected function getOrganziationEkattesCount($database)
    {
        $userDbConnString = $this->getUserDbConfig($database);

        $sql = '
            SELECT * FROM dblink(?, $$
                SELECT 
                    COUNT(DISTINCT ekate_combobox.ekate) as ekatte_count
                from ekate_combobox
            $$) t(ekatte_count int)
        ';

        $stmt = $this->em->getConnection()->executeQuery($sql, [$userDbConnString]);
        $ekattes = $stmt->fetchAssociative();

        return $ekattes['ekatte_count'];
    }

    protected function getUserDbConfig($database)
    {
        $env = getenv();

        return 'host=' . $env['TF_MAIN_DB_HOST']
            . ' port=' . $env['TF_MAIN_DB_PORT']
            . ' dbname=' . $database
            . ' user=' . $env['TF_MAIN_DB_USER']
            . ' password=' . $env['TF_MAIN_DB_PASS'];
    }

    protected function getMainDbConfig()
    {
        $env = getenv();

        return 'host=' . $env['TF_MAIN_DB_HOST']
            . ' port=' . $env['TF_MAIN_DB_PORT']
            . ' dbname=' . $env['TF_MAIN_DB_NAME']
            . ' user=' . $env['TF_MAIN_DB_USER']
            . ' password=' . $env['TF_MAIN_DB_PASS'];
    }
}
