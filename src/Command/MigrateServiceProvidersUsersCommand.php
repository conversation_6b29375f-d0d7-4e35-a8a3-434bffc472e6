<?php

namespace App\Command;

use App\Helpers\Log;
use App\Model\GeoSCAN\User;
use App\Security\OAuthClient\Provider\KeycloakMachineToMachine;
use App\Serializer\UserNormalizer;
use App\Service\GeoSCAN\OrganizationService;
use App\Service\GeoSCAN\UserService;
use App\Service\GeoSCANAPIClient;
use App\Service\Technofarm\TFApiClient;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Serializer\NameConverter\CamelCaseToSnakeCaseNameConverter;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class MigrateServiceProvidersUsersCommand extends Command
{
    protected static $defaultName = 'app:migrate-service-providers-users';

    private EntityManagerInterface $em;
    private ParameterBagInterface $parameters;
    private GeoSCANAPIClient $geoscanApiClient;
    private Security $security;
    private KeycloakMachineToMachine $m2m;
    private TFApiClient $technofarmApiClient;
    private OrganizationService $organizationService;
    private UserService $userService;
    private $io;
    private $migratedUsersEmails = [];
    private $normalizer;
    private $userNormalizer;

    private $serviceProviders = [
        'admin_ro',
        'Razvan_Agricost',
        'zornica_it',
        'admin_ua',
    ];

    public function __construct(
        EntityManagerInterface $entityManager,
        ParameterBagInterface $parameters,
        GeoSCANAPIClient $geoSCANAPIClient,
        Security $security,
        KeycloakMachineToMachine $keycloakMachineToMachine,
        TFApiClient $technofarmApiClient,
        OrganizationService $organizationService,
        UserService $userService
    ) {
        $this->em = $entityManager;
        $this->parameters = $parameters;
        $this->geoscanApiClient = $geoSCANAPIClient;
        $this->security = $security;
        $this->m2m = $keycloakMachineToMachine;
        $this->technofarmApiClient = $technofarmApiClient;
        $this->userService = $userService;
        $this->organizationService = $organizationService;
        $this->normalizer = new ObjectNormalizer(null, new CamelCaseToSnakeCaseNameConverter());
        $this->userNormalizer = new UserNormalizer($this->normalizer);

        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setDescription('Migrate foreign service providers users !');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        try {
            foreach ($this->serviceProviders as $serviceProvider) {
                if ('admin_ro' != $serviceProvider) {
                    continue;
                }

                try {
                    $this->m2m->impersonateUser($serviceProvider);
                    $data = $this->geoscanApiClient->getUsersRaw(
                        ['limit' => PHP_INT_MAX, 'page' => 1, 'active' => true],
                    );

                    foreach ($data->rows as $item) {
                        $item->password = $item->password_raw;
                        $user = $this->userNormalizer->denormalize($item, User::class);
                        $user->setPasswordReType($user->getPassword());

                        // if ($item->id != 636) {
                        //    continue;
                        // }

                        if (in_array($user->getEmail(), $this->migratedUsersEmails)) {
                            $user->setEmail('');
                            Log::toFile('z_ex_service_providers_duplicated_emails', $user->getUsername());
                        }

                        $io->note(sprintf('Migrate user: %s ', $user->getUsername()));

                        try {
                            // add user to keycloak
                            try {
                                $this->userService->saveKeycloakUser($user, true);
                            } catch (Exception $exception) {
                                // continue migration
                            }

                            $keycloakUser = $this->userService->getKeycloakUser($user->getUsername());
                            $user->setKeycloakUid($keycloakUser->getId());

                            // update gs with keycloak uid
                            $gs = $this->geoscanApiClient->saveUser($user, false);

                            $this->migratedUsersEmails[] = $user->getEmail();
                        } catch (Exception $exception) {
                            // continue with migration and log erro
                            $this->migratedUsersEmails[] = $user->getEmail();
                            Log::toFile('z_ex_service_providers', [$exception->getMessage(), $exception->getLine(), $exception->getFile(), $user->getUsername()]);
                        }
                    }
                } catch (Exception $exception) {
                    // contnue with migration
                    Log::toFile('z_ex_service_providers', [$exception->getMessage(), $exception->getLine(), $exception->getFile()]);
                }
            }

            $io->success([
                'Users migrated !',
            ]);
        } catch (Exception $exception) {
            Log::toFile('z_ex_service_providers', [$exception->getMessage(), $exception->getLine(), $exception->getFile(), $user->getUsername()]);

            $io->error($exception->getMessage());
        }

        $io->success('All is Done!');

        return 0;
    }
}
