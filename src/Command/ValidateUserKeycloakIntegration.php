<?php

namespace App\Command;

use App\Helpers\Log;
use App\Security\OAuthClient\Provider\KeycloakAdminProvider;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class ValidateUserKeycloakIntegration extends Command
{
    protected static $defaultName = 'app:validate-user-kc-integration';

    private EntityManagerInterface $em;

    private $keycloakAdminProvider;

    public function __construct(
        EntityManagerInterface $entityManager,
        KeycloakAdminProvider $keycloakAdminProvider
    ) {
        $this->em = $entityManager;
        $this->keycloakAdminProvider = $keycloakAdminProvider;

        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setDescription('Migrate foreign service providers users !');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $token = $this->keycloakAdminProvider->getToken();

        $first = 0;
        $step = 10;
        $max = 11;
        $hasMoreUsers = true;
        do {
            $kcUsers = $this->keycloakAdminProvider->getUsers($token, $first, $max);

            $hasMoreUsers = count($kcUsers) > 0;

            if ($hasMoreUsers) {
                $gsUsers = $this->getGeoscanUsers($kcUsers);

                foreach ($gsUsers as $user) {
                    $kcUser = $kcUsers[array_search($user['gs_username'], array_column($kcUsers, 'username'))];
                    $this->validateUser($kcUser, $user);
                }
            }

            $first += $step;
        } while (true === $hasMoreUsers);

        $io->note('Commmand End');

        return 1;
    }

    private function getGeoscanUsers(array $kcUsers): array
    {
        $usernames = sprintf("'%s'", implode("','", array_column($kcUsers, 'username')));

        $env = getenv();
        $gsMainDBConnectionString
            = ' host=' . $env['GS_MAIN_DB_HOST']
            . ' port=' . $env['GS_MAIN_DB_PORT']
            . ' dbname=' . $env['GS_MAIN_DB_NAME']
            . ' user=' . $env['GS_MAIN_DB_USER']
            . ' password=' . $env['GS_MAIN_DB_PASS'];

        $tfSusiMainConnString
            = ' host=' . $env['TF_MAIN_DB_HOST']
            . ' port=' . $env['TF_MAIN_DB_PORT']
            . ' dbname=' . $env['TF_MAIN_DB_NAME']
            . ' user=' . $env['TF_MAIN_DB_USER']
            . ' password=' . $env['TF_MAIN_DB_PASS'];

        $sql = "SELECT
                    global_user.*,
                    tf_user.*
                from dblink(:mainDBConnectionString, $$
                                select 
                                    gsu.id as gs_user_id,
                                    gsu.old_id as gs_old_user_id,
                                    gsu.username as gs_username,
                                    gsu.name as gs_name,
                                    gsu.email as gs_email,
                                    gsu.service_provider_id as gs_service_provider_id,
                                    gsu.keycloak_uid as gs_keycloak_uid,
                                    countries.iso_alpha_2_code
                                from su_users gsu
                                inner join service_providers as sp on sp.id = gsu.service_provider_id
                                inner join countries on countries.id = sp.country_id
                                where
                                    gsu.active is true 
                    $$) as global_user (
                        gs_id int,
                        gs_old_id int,
                        gs_username varchar,
                        gs_name varchar,
                        gs_email varchar,
                        gs_service_provider_id int,
                        gs_keycloak_uid uuid,
                        iso_alpha_2_code varchar        
                )
                LEFT JOIN 
                    dblink(:tfSusiMainConnString, $$
                                select
                                    tfsu.id as tf_user_id,
                                    tfsu.username as tf_username,
                                    tfsu.keycloak_uid as tf_keycloak_uid,
                                    tfsu.database as tf_database
                                from su_users tfsu
                    $$) as tf_user(
                        tf_user_id int,
                        tf_username varchar,
                        tf_keycloak_uid uuid,
                        tf_database varchar
                    ) on tf_user.tf_username = global_user.gs_username
                WHERE
                    global_user.gs_username in ({$usernames})   
            ";

        $stmt = $this->em->getConnection()->executeQuery(
            $sql,
            [
                'mainDBConnectionString' => $gsMainDBConnectionString,
                'tfSusiMainConnString' => $tfSusiMainConnString,
            ]
        );

        return $stmt->fetchAllAssociative();
    }

    private function validateUser($kcUser, $gsUser)
    {
        if ('BG' == $gsUser['iso_alpha_2_code']) {
            if ($gsUser['tf_keycloak_uid'] != $gsUser['gs_keycloak_uid']) {
                $this->logUser(
                    $kcUser,
                    $gsUser,
                    'tf_uid not same with gs_uid'
                );
            }

            if ($kcUser['id'] != $gsUser['tf_keycloak_uid']
                || $kcUser['id'] != $gsUser['gs_keycloak_uid']
            ) {
                $this->logUser(
                    $kcUser,
                    $gsUser,
                    'kc uid not same with gs_uid or tf_uid'
                );
            }

            return;
        }

        if ($kcUser['id'] != $gsUser['gs_keycloak_uid']) {
            if ($gsUser['tf_user_id']) {
                $this->logUser(
                    $kcUser,
                    $gsUser,
                    'kc uid not same with gs_uid'
                );
            }

            return;
        }
    }

    private function logUser($kcUser, $gsUser, $message)
    {
        $msg = sprintf(
            'Error: %s. kc_uuid: [%s]; gs_user_id: [%s]; gs_kc_uuid: [%s]; tf_user_id: [%s]; tf_kc_uuid: [%s];',
            $message,
            $kcUser['id'],
            $gsUser['gs_old_id'],
            $gsUser['gs_keycloak_uid'],
            $gsUser['tf_user_id'],
            $gsUser['tf_keycloak_uid']
        );

        Log::toFile('z_kc_user_problem', $msg);
    }
}
