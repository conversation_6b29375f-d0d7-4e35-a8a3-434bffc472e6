<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Security;

use App\Security\User\SystemUser;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\CustomCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;

class SystemTokenAuthenticator extends TokenAuthenticator
{
    /**
     * Called on every request to decide if this authenticator should be
     * used for the request. Returning `false` will cause this authenticator
     * to be skipped.
     */
    public function supports(Request $request): ?bool
    {
        return !($this->accessTokenExtractor->extractAccessToken($request));
    }

    public function authenticate(Request $request): Passport
    {
        $apiToken = $this->accessTokenExtractor->extractAccessToken($request);
        if (null === $apiToken) {
            // The token header was empty, authentication fails with HTTP Status
            // Code 401 "Unauthorized"
            throw new CustomUserMessageAuthenticationException('No API token provided');
        }

        return new Passport(new UserBadge($apiToken, function () use ($apiToken) {
            $user = new SystemUser();
            $user->setToken($apiToken);

            return $user;
        }), new CustomCredentials(
            // If this function returns anything else than `true`, the credentials
            // are marked as invalid.
            // The $credentials parameter is equal to the next argument of this class
            function ($apiToken) {
                return $this->validateToken($apiToken);
            },

            // The custom credentials
            $apiToken
        ));
    }

    public function validateToken(string $accessToken)
    {
        $certsUrl = $this->parameters->get('keycloak_certs_url');

        $decoded = Token::decode($accessToken, $certsUrl, $this->cache);

        return (bool) ($decoded);
    }
}
