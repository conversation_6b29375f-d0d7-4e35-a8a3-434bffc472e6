<?php

namespace App\Security\OAuthClient\Provider;

use App\Model\GeoSCAN\User;
use App\Service\GeoSCAN\UserService;
use Exception;
use League\OAuth2\Client\Provider\AbstractProvider;
use League\OAuth2\Client\Provider\ResourceOwnerInterface;
use League\OAuth2\Client\Token\AccessToken;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use UnexpectedValueException;

class KeycloakAdminProvider extends Keycloak
{
    public function __construct(array $options = [], array $collaborators = [])
    {
        parent::__construct($options, $collaborators);
    }

    public function getToken(array $options = []): AccessToken
    {
        return parent::getAccessToken('client_credentials', $options);
    }

    /**
     * @param array $userDetails
     *
     * @throws Exception
     * @throws UnexpectedValueException
     *
     * @return ?string the KeyCloak user ID
     */
    public function saveUser(AccessToken $token, User $user, bool $isNew, string $locale)
    {
        [$firstName, $lastName] = UserService::splitName($user->getName());

        $userDetails = [
            'username' => $user->getUsername(),
            'email' => $user->getEmail(),
            'firstName' => $firstName,
            'lastName' => $lastName,
            'enabled' => $user->isActive(),
        ];

        if ($user->getTechnofarmUserId()) {
            $userDetails['attributes']['tf_user_id'] = [$user->getTechnofarmUserId()];
        }

        if ($user->getId()) {
            $userDetails['attributes']['gs_user_id'] = [$user->getId()];
            $userDetails['attributes']['locale'] = [$locale];
        }

        $options['headers'] = ['Content-Type' => 'application/json'];
        $options['body'] = json_encode($userDetails);

        try {
            $keycloakUser = (true === $isNew) ? null : $this->getUser($token, ['username' => $user->getUsername()]);

            $request = $this->getAuthenticatedRequest(
                (true === $isNew) ? Request::METHOD_POST : Request::METHOD_PUT,
                (true === $isNew) ? $this->getUsersUrl() : $this->getUsersUrl() . '/' . $keycloakUser->getId(),
                $token,
                $options
            );

            $response = $this->getParsedResponse($request);

            if (isset($response['errorMessage'])) {
                throw new Exception($response['errorMessage']);
            }

            if ($user->getPassword() && ($user->getPassword() === $user->getPasswordReType())) {
                $response = $this->resetUserPassword($token, $user->getUsername(), $user->getPassword());
            }
        } catch (Exception $e) {
            throw new UnexpectedValueException($e->getMessage());
        }
    }

    /**
     * @param string $username
     * @param string $password
     *
     * @return string
     */
    public function resetUserPassword(AccessToken $token, $username, $password)
    {
        $keycloakUser = $this->getUser($token, ['username' => $username]);
        $url = $this->getUsersUrl() . '/' . $keycloakUser->getId() . '/reset-password';

        $options['headers'] = ['Content-Type' => 'application/json'];
        $options['body'] = json_encode([
            'temporary' => 'false',
            'type' => 'password',
            'value' => $password,
        ]);

        $request = $this->getAuthenticatedRequest('PUT', $url, $token, $options);
        $response = $this->getParsedResponse($request);

        if (is_array($response) && isset($response['errorMessage'])) {
            throw new Exception($response['errorMessage']);
        }

        return $keycloakUser->getId();
    }

    /**
     * @param string $username
     *
     * @return ResourceOwnerInterface
     */
    public function getUser(AccessToken $token, array $params): ?ResourceOwnerInterface
    {
        $url = $this->getUsersUrl() . '/?exact=true';
        $url .= isset($params['username']) ? '&username=' . $params['username'] : '';
        $url .= isset($params['email']) ? '&email=' . $params['email'] : '';

        $request = $this->getAuthenticatedRequest(AbstractProvider::METHOD_GET, $url, $token);
        $response = $this->getParsedResponse($request);

        if (false === is_array($response)) {
            throw new UnexpectedValueException('Invalid response received from Authorization Server. Expected JSON.');
        }

        if (empty($response)) {
            throw new NotFoundHttpException('User not found', null, 404);
        }

        return $this->createResourceOwner(current($response), $token);
    }

    /**
     * @param int $first
     * @param int $max
     */
    public function getUsers(AccessToken $token, $first = 0, $max = 11): array
    {
        $url = $this->getUsersUrl() . "/?{$this->realm}/users&first={$first}&max={$max}";
        $request = $this->getAuthenticatedRequest(AbstractProvider::METHOD_GET, $url, $token);

        return $this->getParsedResponse($request);
    }

    public function updateUserAttributes(AccessToken $token, array $attributes): void
    {
        try {
            $keycloakUser = $this->getUser($token, ['username' => $attributes['username']]);

            $userDetails['attributes'] = $keycloakUser->getAttributes();
            $userDetails['attributes']['service_provider_id'] = [$attributes['service_provider_id']];
            $userDetails['attributes']['service_provider_name'] = [$attributes['service_provider_name']];
            $userDetails['attributes']['service_provider_slug'] = [$attributes['service_provider_slug']];
            $userDetails['attributes']['tf_database'] = [$attributes['tf_database']];
            $userDetails['attributes']['iso_alpha_2_code'] = [$attributes['iso_alpha_2_code']];

            $options['headers'] = ['Content-Type' => 'application/json'];
            $options['body'] = json_encode($userDetails);

            $request = $this->getAuthenticatedRequest(
                Request::METHOD_PUT,
                $this->getUsersUrl() . '/' . $keycloakUser->getId(),
                $token,
                $options
            );

            $response = $this->getParsedResponse($request);

            if (isset($response['errorMessage'])) {
                throw new Exception($response['errorMessage']);
            }
        } catch (Exception $e) {
            throw new UnexpectedValueException($e->getMessage());
        }
    }

    /**
     * @param [string] $username
     */
    public function checkUsernameExistence(AccessToken $token, $username): bool
    {
        try {
            $this->getUser($token, ['username' => $username]);

            return true;
        } catch (NotFoundHttpException $ex) {
            return false;
        }
    }

    public function checkEmailExistence(AccessToken $token, string $email): bool
    {
        try {
            $this->getUser($token, ['email' => $email]);

            return true;
        } catch (NotFoundHttpException $ex) {
            return false;
        }
    }

    private function getBaseAdminUrl(): string
    {
        return $this->authServerUrl . '/admin/realms/' . $this->realm;
    }

    private function getUsersUrl(): string
    {
        return $this->getBaseAdminUrl() . '/users';
    }
}
