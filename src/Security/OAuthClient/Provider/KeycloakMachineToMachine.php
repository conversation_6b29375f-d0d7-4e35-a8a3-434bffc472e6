<?php

namespace App\Security\OAuthClient\Provider;

use App\EntityGeoscan\GlobalUser;
use App\Security\JWTPostAuthenticationToken;
use App\Security\TokenAuthenticator;
use Doctrine\Persistence\ManagerRegistry;
use GuzzleHttp\Client;
use League\OAuth2\Client\Grant\ClientCredentials;
use League\OAuth2\Client\Token\AccessToken;
use Symfony\Bundle\SecurityBundle\Security\UserAuthenticator;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Http\Authentication\UserAuthenticatorInterface;

class KeycloakMachineToMachine extends Keycloak
{
    private $parameters;
    private $doctrine;
    private UserAuthenticatorInterface $userAuthenticator;
    private TokenAuthenticator $tokenAuthenticator;
    private TokenStorageInterface $tokenStorage;

    public function __construct(array $options = [], array $collaborators = [], ParameterBagInterface $parameters, ManagerRegistry $doctrine, UserAuthenticatorInterface $userAuthenticator, TokenAuthenticator $tokenAuthenticator, TokenStorageInterface $tokenStorage)
    {
        parent::__construct($options, $collaborators);

        $this->parameters = $parameters;
        $this->doctrine = $doctrine;
        $this->userAuthenticator = $userAuthenticator;
        $this->tokenAuthenticator = $tokenAuthenticator;
        $this->tokenStorage = $tokenStorage;
    }

    public function getOfflineToken(array $options = [])
    {
        $options = array_merge($options, $this->getScopes());

        return parent::getAccessToken(new ClientCredentials(), $options);
    }

    public function impersonateUser(string $username): void
    {
        $user = $this->doctrine->getRepository(GlobalUser::class, 'geoscan')->findOneBy(['username' => $username]);

        $impersonatedUserToken = $this->exchangeToken($this->getOfflineToken(), $username);

        $user->setAccessToken($impersonatedUserToken->getToken());

        $this->authUser($user);
    }

    public function exchangeToken(AccessToken $token, string $username): AccessToken
    {
        $url = $this->getBaseAccessTokenUrl([]);

        $client = new Client([
            'base_uri' => $url,
        ]);

        $response = $client->request('POST', $url, [
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'form_params' => [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:token-exchange',
                'subject_token' => $token->getToken(),
                'subject_token_type' => 'urn:ietf:params:oauth:token-type:access_token',
                'requested_subject' => $username,
                'client_id' => $this->clientId,
                'client_secret' => $this->clientSecret,
                'scope' => implode($this->getScopeSeparator(), $this->getDefaultScopes()),
            ],
        ]);

        return new AccessToken(json_decode($response->getBody()->getContents(), true));
    }

    /**
     * @see https://wjw465150.gitbooks.io/keycloak-documentation/content/server_admin/topics/sessions/offline.html
     *
     * @return string[]
     */
    protected function getScopes(): iterable
    {
        $options['scope'] = implode($this->getScopeSeparator(), [parent::SCOPE_SYSTEM]);

        return $options;
    }

    private function authUser(UserInterface $user): void
    {
        $this->tokenStorage->setToken(new JWTPostAuthenticationToken($user, 'main', [], $user->getAccessToken()));

        /*
        $this->userAuthenticator->authenticateUser(
            $user,
            $this->tokenAuthenticator,
            new Request()
        );
        */
    }
}
