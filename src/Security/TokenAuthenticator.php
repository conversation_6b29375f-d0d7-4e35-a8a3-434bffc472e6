<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Security;

use App\EntityGeoscan\GlobalUser;
use App\Security\OAuthClient\Provider\Keycloak;
use App\Security\User\KeycloakUser;
use Doctrine\Persistence\ManagerRegistry;
use Exception;
use GuzzleHttp\Client;
use League\OAuth2\Client\Token\AccessToken;
use LogicException;
use Psr\Cache\CacheItemPoolInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\Authenticator\Passport\PassportInterface;
use Symfony\Component\Security\Http\Authenticator\Passport\SelfValidatingPassport;
use UnexpectedValueException;

class TokenAuthenticator extends AbstractAuthenticator
{
    protected $accessTokenExtractor;
    protected $parameters;
    protected $cache;
    protected $keycloakProvider;
    private $security;
    private $doctrine;

    public function __construct(
        Security $security,
        ParameterBagInterface $parameters,
        CacheItemPoolInterface $cache,
        ManagerRegistry $doctrine,
        AccessTokenExtractorInterface $accessTokenExtractor,
        Keycloak $keycloakProvider
    ) {
        $this->security = $security;
        $this->parameters = $parameters;
        $this->cache = $cache;
        $this->doctrine = $doctrine;
        $this->accessTokenExtractor = $accessTokenExtractor;
        $this->keycloakProvider = $keycloakProvider;
    }

    /**
     * Called on every request to decide if this authenticator should be
     * used for the request. Returning `false` will cause this authenticator
     * to be skipped.
     */
    public function supports(Request $request): ?bool
    {
        return !($this->security->getUser() && $this->accessTokenExtractor->extractAccessToken($request));
    }

    public function authenticate(Request $request): Passport
    {
        $accessToken = $this->accessTokenExtractor->extractAccessToken($request);

        if (null === $accessToken) {
            // The token header was empty, authentication fails with HTTP Status
            // Code 401 "Unauthorized"
            throw new CustomUserMessageAuthenticationException('No API token provided');
        }

        $passport = new SelfValidatingPassport(new UserBadge($accessToken, function () use ($accessToken) {
            /** @var KeycloakUser $keycloakUser */
            $keycloakUser = $this->fetchUserFromToken(
                new AccessToken(['access_token' => $accessToken])
            );

            $username = $keycloakUser->getUsername();

            // 1) have they logged in with Keycloak before?

            /**
             * @todo enable after column keycloak_id creation
             * $existingUser = $this->doctrine->getRepository(GlobalUser::class)->findOneBy(['keycloakId' => $keycloakUser->getId()]);
             *
             * if ($existingUser) {
             *     return $existingUser;
             * }
             */

            // 2) do we have a matching user by username?
            $user = $this->doctrine->getRepository(GlobalUser::class, 'geoscan')->findOneByUsername($username);

            $user->setAccessToken($accessToken);
            // 3) Maybe you just want to "register" them by creating
            // a User object

            /*
             *  @todo enable after column keycloak_id creation
             *  $user->setKeycloakId($keycloakUser->getId());
             *  $this->entityManager->persist($user);
             *  $this->entityManager->flush();
             */

            return $user;
        }));

        $passport->setAttribute('token', $accessToken);

        return $passport;
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        $data = [
            'message' => strtr($exception->getMessageKey(), $exception->getMessageData()),
        ];

        return new JsonResponse($data, Response::HTTP_UNAUTHORIZED);
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        // on success, let the request continue

        return null;
    }

    public function supportsRememberMe()
    {
        // TODO: Implement supportsRememberMe() method.
    }

    public function createAuthenticatedToken(PassportInterface $passport, string $firewallName): TokenInterface
    {
        if (!$passport instanceof Passport) {
            throw new LogicException(sprintf('Expected "%s" but got "%s".', Passport::class, get_debug_type($passport)));
        }

        return new JWTPostAuthenticationToken($passport->getUser(), $firewallName, $passport->getUser()->getRoles(), $passport->getAttribute('token'));
    }

    public function createToken(Passport $passport, string $firewallName): TokenInterface
    {
        return new JWTPostAuthenticationToken($passport->getUser(), $firewallName, $passport->getUser()->getRoles(), $passport->getAttribute('token'));
    }

    /**
     * Get public json web keys from identity provider.
     *
     * @return iterrable
     */
    protected function getJwk(): iterable
    {
        try {
            $httpClient = new Client([
                'base_uri' => $this->parameters->get('keycloak_base_url'),
            ]);

            $response = $httpClient->request('GET', $this->getCertsUrl(), []);

            $response = json_decode($response->getBody()->getContents(), true);

            $jwk = array_filter($response['keys'], function ($value) {
                if ($value['alg'] === $this->parameters->get('keycloak_alg')) {
                    return $value;
                }
            }, ARRAY_FILTER_USE_BOTH);

            return ['keys' => $jwk];
        } catch (Exception $exception) {
            throw new Exception($exception->getMessage());
        }
    }

    /**
     * Build identity provider url for public keys.
     */
    protected function getCertsUrl(): string
    {
        return 'realms/' . $this->parameters->get('keycloak_realm') . '/protocol/openid-connect/certs';
    }

    private function fetchUserFromToken(AccessToken $accessToken): KeycloakUser
    {
        try {
            return $this->keycloakProvider->getResourceOwner($accessToken);
        } catch (UnexpectedValueException $ex) {
            // token is invalid/еxpired or user is disabled
            throw new CustomUserMessageAuthenticationException('Invalid token');
        } catch (Exception $ex) {
            throw $ex;
        }
    }
}
