<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Security;

use Symfony\Component\Validator\Constraints as Assert;

class UserModel
{
    /**
     * @var string
     *
     * @Assert\NotBlank
     *
     * @Assert\Length(min="3")
     */
    private $username;

    /**
     * @var string
     *
     * @Assert\NotBlank
     *
     * @Assert\Length(min="3")
     */
    private $password;

    /**
     * @return string
     */
    public function getUsername()
    {
        return $this->username;
    }

    public function setUsername(string $username): UserModel
    {
        $this->username = $username;

        return $this;
    }

    /**
     * @return string
     */
    public function getPassword()
    {
        return $this->password;
    }

    public function setPassword(string $password): UserModel
    {
        $this->password = $password;

        return $this;
    }
}
