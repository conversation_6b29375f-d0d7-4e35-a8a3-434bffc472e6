<?php

namespace App\Security\User;

use League\OAuth2\Client\Provider\ResourceOwnerInterface;

class Key<PERSON>loakUser implements ResourceOwnerInterface
{
    private $data;

    public function __construct($response)
    {
        $this->data = $response;
    }

    public function getId()
    {
        return ($id = $this->getField('id')) ? $id : $this->getField('sub');
    }

    public function getUsername()
    {
        return $this->getField('preferred_username');
    }

    public function getAttributes()
    {
        return $this->getField('attributes');
    }

    public function toArray()
    {
        return $this->data;
    }

    private function getField(string $key)
    {
        return $this->data[$key] ?? null;
    }
}
