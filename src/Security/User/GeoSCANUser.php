<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 10/05/2019
 * Time: 10:24.
 */

namespace App\Security\User;

use App\Entity\ServiceProvider;
use Symfony\Component\Security\Core\User\EquatableInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class GeoSCANUser implements EquatableInterface, UserInterface
{
    private $globalUserId;
    private $username;
    private $password;
    private $salt;
    private $roles;
    private $geoSCANAccessToken;
    private $serviceProvider;

    public function __construct(
        $globalUserId = null,
        $username = null,
        array $roles = null,
        $accessToken = null,
        ServiceProvider $serviceProvider = null
    ) {
        $this->globalUserId = $globalUserId;
        $this->username = $username;
        $this->roles = $roles;
        $this->geoSCANAccessToken = $accessToken;
        $this->serviceProvider = $serviceProvider;
    }

    public function getGlobalUserId()
    {
        return $this->globalUserId;
    }

    /**
     * @return array
     */
    public function getRoles()
    {
        return $this->roles;
    }

    public function getPassword(): ?string
    {
        return $this->password;
    }

    /**
     * @return GeoSCANUser
     */
    public function setPassword($password)
    {
        $this->password = $password;

        return $this;
    }

    public function getSalt(): ?string
    {
        return $this->salt;
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    /**
     * @param null $username
     *
     * @return GeoSCANUser
     */
    public function setUsername($username)
    {
        $this->username = $username;

        return $this;
    }

    public function getGeoSCANAccessToken()
    {
        return $this->geoSCANAccessToken;
    }

    public function getServiceProvider()
    {
        return $this->serviceProvider;
    }

    /**
     * The public representation of the user (e.g. a username, an email address, etc.).
     *
     * @see UserInterface
     */
    public function getUserIdentifier(): string
    {
        return (string) $this->username;
    }

    public function eraseCredentials() {}

    /**
     * @return bool
     */
    public function isEqualTo(UserInterface $user) {}
}
