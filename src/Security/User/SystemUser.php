<?php

namespace App\Security\User;

use Symfony\Component\Security\Core\User\UserInterface;

class SystemUser implements UserInterface
{
    private $token;

    /**
     * @return array
     */
    public function getRoles()
    {
        return [];
    }

    public function getPassword(): ?string
    {
        return null;
    }

    /**
     * @param [type] $password
     */
    public function setPassword($password)
    {
        $this->password = $password;

        return $this;
    }

    public function getSalt(): ?string
    {
        return $this->salt;
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    /**
     * @param [type] $username
     */
    public function setUsername($username)
    {
        $this->username = $username;

        return $this;
    }

    public function getToken()
    {
        return $this->token;
    }

    public function setToken(string $accessToken): void
    {
        $this->token = $accessToken;
    }

    /**
     * The public representation of the user (e.g. a username, an email address, etc.).
     *
     * @see UserInterface
     */
    public function getUserIdentifier(): string
    {
        return (string) $this->token;
    }

    public function eraseCredentials() {}
}
