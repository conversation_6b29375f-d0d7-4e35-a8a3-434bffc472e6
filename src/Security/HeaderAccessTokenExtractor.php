<?php

namespace App\Security;

use Symfony\Component\HttpFoundation\Request;

/**
 * Extracts a token from the request header.
 *
 * @see https://datatracker.ietf.org/doc/html/rfc6750#section-2.1
 */
final class HeaderAccessTokenExtractor implements AccessTokenExtractorInterface
{
    private $regex;
    private $headerParameter;
    private $tokenType;

    public function __construct(
        string $headerParameter = 'Authorization',
        string $tokenType = 'Bearer'
    ) {
        $this->headerParameter = $headerParameter;
        $this->tokenType = $tokenType;

        $this->regex = sprintf(
            '/^%s([a-zA-Z0-9\-_\+~\/\.]+)$/',
            '' === $this->tokenType ? '' : preg_quote($this->tokenType) . '\s+'
        );
    }

    public function extractAccessToken(Request $request): ?string
    {
        if (!$request->headers->has($this->headerParameter) || !\is_string($header = $request->headers->get($this->headerParameter))) {
            return null;
        }

        if (preg_match($this->regex, $header, $matches)) {
            return $matches[1];
        }

        return null;
    }
}
