<?php

namespace App\Security;

use DomainException;
use Exception;
use Firebase\JWT\BeforeValidException;
use Firebase\JWT\CachedKeySet;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\JWT;
use Firebase\JWT\SignatureInvalidException;
use Guz<PERSON>Http\Client;
use GuzzleHttp\Psr7\HttpFactory;
use InvalidArgumentException;
use Psr\Cache\CacheItemPoolInterface;
use UnexpectedValueException;

class Token
{
    /**
     * Decode a JWT token.
     *
     * @param string $token
     * @param $publicKey
     *
     * @return null|mixed
     */
    public static function decode(string $token = null, $jwkUri, CacheItemPoolInterface $cacheItemPool, int $leeway = 0)
    {
        try {
            JWT::$leeway = $leeway;

            $httpFactory = new HttpFactory();
            $httpClient = new Client();

            $keySet = new CachedKeySet(
                $jwkUri,
                $httpClient,
                $httpFactory,
                $cacheItemPool,
                $expiresAfter = 60 * 60, // $expiresAfter int seconds to set the JWKS to expire
                false // $rateLimit  true to enable rate limit of 10 RPS on lookup of invalid keys //true will disable cache storage refresh
            );

            return JWT::decode((string) $token, $keySet);
        } catch (InvalidArgumentException $e) {
            throw $e;
            // provided key/key-array is empty or malformed.
        } catch (DomainException $e) {
            throw $e;
            // provided algorithm is unsupported OR
            // provided key is invalid OR
            // unknown error thrown in openSSL or libsodium OR
            // libsodium is required but not available.
        } catch (SignatureInvalidException $e) {
            throw $e;
            // provided JWT signature verification failed.
        } catch (BeforeValidException $e) {
            throw $e;
            // provided JWT is trying to be used before "nbf" claim OR
            // provided JWT is trying to be used before "iat" claim.
        } catch (ExpiredException $e) {
            throw $e;
            // provided JWT is trying to be used after "exp" claim.
        } catch (UnexpectedValueException $e) {
            throw $e;
            // provided JWT is malformed OR
            // provided JWT is missing an algorithm / using an unsupported algorithm OR
            // provided JWT algorithm does not match provided key OR
            // provided key ID in key/key-array is empty or invalid.
        } catch (Exception $e) {
            throw $e;
        }
    }
}
