<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class DefaultController.
 *
 * @Route(path="/")
 */
class DefaultController extends AbstractController
{
    /**
     * @return Response
     *
     * @Route(methods={"GET"}, name="default_index", path="/")
     */
    public function index()
    {
        return $this->render('default/index.html.twig');
    }
}
