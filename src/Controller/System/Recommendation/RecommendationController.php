<?php

namespace App\Controller\System\Recommendation;

use App\Controller\System\AbstractBaseController;
use App\Service\Recommendation\RecommendationService;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Exception\InvalidParameterException;

/**
 * Class RecommendationController.
 *
 * @Route(path="/recommendation")
 */
class RecommendationController extends AbstractBaseController
{
    /**
     * @var RecommendationService
     */
    private $recommendationService;

    public function __construct(
        ParameterBagInterface $parameters,
        RecommendationService $recommendationService
    ) {
        parent::__construct($parameters);
        $this->recommendationService = $recommendationService;
    }

    /**
     * @throws \Doctrine\DBAL\DBALException
     *
     * @Route(methods={"GET"}, name="system-soil-analyzes-data", path="/soil-analyzes-data")
     */
    public function getSoilChartAnalyzes(Request $request): JsonResponse
    {
        $filters = $request->get('filter', []);
        $body = $request->getContent();
        $data = json_decode($body, true);

        if (!isset($data['service_provider_id'])) {
            throw new InvalidParameterException('Missing payload service provider id!');
        }

        $response = $this->recommendationService->getSoilAnalyzes($filters, (int) $data['service_provider_id']);

        return new JsonResponse(json_encode($response), 200, [], true);
    }
}
