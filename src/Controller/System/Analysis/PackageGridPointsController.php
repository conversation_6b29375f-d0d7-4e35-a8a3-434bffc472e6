<?php

namespace App\Controller\System\Analysis;

use App\Controller\System\AbstractBaseController;
use App\Entity\Contract;
use App\Entity\Contract\SubscriptionPackage;
use App\Service\Analysis\LabElementGroupService;
use App\Service\Analysis\LabElementsResultsService;
use App\Service\Analysis\PackageGridPointsService;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use LogicException;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class PackageGridPointsController.
 *
 * @Route(path="/analysis/package-grid-points")
 */
class PackageGridPointsController extends AbstractBaseController
{
    private $packageGridPointsService;
    private $labElementGroupService;
    private $labElementsResultsService;
    private $em;

    public function __construct(
        ParameterBagInterface $parameters,
        PackageGridPointsService $packageGridPointsService,
        LabElementGroupService $labElementGroupService,
        LabElementsResultsService $labElementsResultsService,
        EntityManagerInterface $em
    ) {
        parent::__construct($parameters);
        $this->packageGridPointsService = $packageGridPointsService;
        $this->labElementGroupService = $labElementGroupService;
        $this->labElementsResultsService = $labElementsResultsService;
        $this->em = $em;
    }

    /**
     * @Route(methods={"POST"}, name="system_create_package_grid_points", path="/")
     *
     * @param request $request
     *
     * This endpoint is used to fix BUG, when order is created, subscription_package_field data is set but package_grid_point data is missing
     *
     * @throws Exception
     *
     * @return JsonResponse
     */
    public function createPackageGridPoints(Request $request)
    {
        $body = $request->getContent();
        $requestData = json_decode($body, true);

        try {
            /**
             * Array $data Structure
             *    $data[] = [
             *      'point_uuid' => string,
             *      'sample_id' => number,
             *      'plot_uuid' => string,
             *      'grid_type' => string,
             *    ].
             */
            $arrExistingData = $this->packageGridPointsService->getElementsResultsData($requestData);
            if (count($arrExistingData) > 0) {
                throw new LogicException('Already existing points data.', 400);
            }

            $subsPackage = $this->em->getRepository(SubscriptionPackage::class)->createQueryBuilder('subscriptionPackage')
                ->select(
                    'subscriptionPackage.id'
                )
                ->join('subscriptionPackage.contract', 'contract')
                ->join('subscriptionPackage.subscriptionPackageFields', 'fields')
                ->where('fields.orderUuid = :orderUuid')->setParameter('orderUuid', $requestData[0]['order_uuid'])
                ->andWhere('fields.plotUuid = :plotUuid')->setParameter('plotUuid', $requestData[0]['plot_uuid'])
                ->getQuery()->getSingleResult();

            $data = array_map(function ($mapData) use ($subsPackage) {
                $mapData['package_id'] = $subsPackage['id'];
                $mapData['package_type'] = Contract::TYPE_SUBSCRIPTION;

                return $mapData;
            }, $requestData);

            $this->packageGridPointsService->create($data);

            $arrToInsert = $this->packageGridPointsService->getElementGroupData($data);
            $this->labElementGroupService->create($arrToInsert);

            $arrToInsertElementsResults = $this->packageGridPointsService->getElementsResultsData($data);
            $this->labElementsResultsService->create($arrToInsertElementsResults);
        } catch (Exception $e) {
            throw $e;
        }

        return new JsonResponse(null, 201);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="system_save_barcodes", path="/save-barcodes")
     */
    public function saveBarcodes(Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        try {
            $this->packageGridPointsService->saveBarcodes($data);
        } catch (Exception $e) {
            return new JsonResponse([$e->getMessage()], 503);
        }

        return new JsonResponse(null, 200);
    }
}
