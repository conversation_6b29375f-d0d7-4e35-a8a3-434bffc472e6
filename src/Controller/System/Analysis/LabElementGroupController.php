<?php

namespace App\Controller\System\Analysis;

use App\Controller\System\AbstractBaseController;
use App\Service\Analysis\LabElementGroupService;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Exception\InvalidParameterException;

/**
 * Class LabElementGroupController.
 *
 * @Route(path="/analysis/lab-element-group")
 */
// class LabElementGroupController  extends AbstractBaseController
class LabElementGroupController extends AbstractBaseController
{
    private $labElementGroupService;

    public function __construct(ParameterBagInterface $parameters, LabElementGroupService $labElementGroupService)
    {
        parent::__construct($parameters);
        $this->labElementGroupService = $labElementGroupService;
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="system_generate_soil_map", path="/generate-soil-map")
     */
    public function generateSoilMap(Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        if (!isset($data['plot_uuid']) || !isset($data['country'])) {
            throw new InvalidParameterException('Missing payload parameteters!');
        }

        $this->labElementGroupService->sendSoilMapDataFromCommand($data['plot_uuid'], $data['country']);

        return new JsonResponse($data, 200);
    }
}
