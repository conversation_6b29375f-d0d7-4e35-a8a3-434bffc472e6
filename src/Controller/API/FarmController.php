<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API;

use App\Service\GeoSCAN\FarmService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class OrganizationController.
 *
 * @Route(path="/")
 */
class FarmController extends AbstractBaseController
{
    private $farmService;

    public function __construct(
        FarmService $farmService,
        SerializerInterface $serializer
    ) {
        parent::__construct($serializer);
        $this->farmService = $farmService;
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_organization_farm_details", path="/farms/{farmUuid}")
     */
    public function index(string $farmUuid)
    {
        $farm = $this->farmService->getByUuid($farmUuid);

        return new JsonResponse($this->serializer->serialize($farm, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="create_organization_farm", path="/farms")
     */
    public function create(Request $request)
    {
        $requestParams = json_decode($request->getContent(), true);
        $this->farmService->create($requestParams);

        return new JsonResponse(null, 201);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"PUT"}, name="update_organization_farms", path="/farms/{farmUuid}")
     */
    public function update(string $farmUuid, Request $request)
    {
        $requestParams = json_decode($request->getContent(), true);
        $requestParams['uuid'] = $farmUuid;

        $this->farmService->update($requestParams);

        return new JsonResponse(null, 204);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"DELETE"}, name="delete_organization_farms", path="/farms/{farmUuid}")
     */
    public function delete(string $farmUuid)
    {
        $result = $this->farmService->delete($farmUuid);

        return new JsonResponse(null, 200);
    }

    /**
     * @Route(methods={"GET"}, name="ekattes", path="/farms/ekattes/list")
     */
    public function getEkattes(Request $request): JsonResponse
    {
        $farmUuid = $request->query->get('farmUuid');

        $ekattes = $this->farmService->getEkattes($farmUuid);

        return new JsonResponse($ekattes, 200);
    }
}
