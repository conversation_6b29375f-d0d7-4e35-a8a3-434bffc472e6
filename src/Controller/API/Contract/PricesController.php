<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API\Contract;

use App\Controller\API\AbstractBaseController;
use App\Entity\Contract;
use App\Entity\Contract\Price;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class ContractController.
 *
 * @Route(path="/contract/{contract}")
 */
class PricesController extends AbstractBaseController
{
    private $geoSCANAPIClient;
    private $addressService;

    public function __construct(
        SerializerInterface $serializer
    ) {
        parent::__construct($serializer);
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_contract_prices_list", path="/prices")
     */
    public function list(Contract $contract)
    {
        $em = $this->getDoctrine()->getManager();

        $packageRepository = $em->getRepository(Price::class);
        $prices = $packageRepository->findByContract($contract->getId());

        $circularReferenceHandler = $this->circularReferenceHandler();

        return new JsonResponse($this->serializer->serialize($prices, 'json', ['groups' => 'api', 'circular_reference_handler' => $circularReferenceHandler]), 200, [], true);
    }
}
