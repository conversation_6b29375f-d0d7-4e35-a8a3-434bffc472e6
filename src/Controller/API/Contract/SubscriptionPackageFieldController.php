<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API\Contract;

use App\Controller\API\AbstractBaseController;
use App\Entity\Contract\SubscriptionPackageField;
use App\Service\Contract\SubscriptionPackageFieldService;
use App\Service\Workflow\SubscriptionFieldService;
use Exception;
use LogicException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Exception\InvalidParameterException;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class SubscriptionPackageFieldController.
 *
 * @Route(path="/contracts/subscription-package-field")
 */
class SubscriptionPackageFieldControll<PERSON> extends AbstractBaseController
{
    private $subscriptionPackageFieldService;
    private $workflowSubscriptionFieldService;

    public function __construct(
        SerializerInterface $serializer,
        SubscriptionPackageFieldService $subscriptionPackageFieldService,
        SubscriptionFieldService $workflowSubscriptionFieldService
    ) {
        parent::__construct($serializer);
        $this->subscriptionPackageFieldService = $subscriptionPackageFieldService;
        $this->workflowSubscriptionFieldService = $workflowSubscriptionFieldService;
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="subscription_orders_id_by_contract_id", path="/orders/contract/{contractId}")
     */
    public function listOrdersIdByContractId(string $contractId, Request $request)
    {
        $filter = $request->get('filter', null);
        $ordersId = $this->subscriptionPackageFieldService->getOrdersIdByContractId($contractId, $filter);

        return new JsonResponse($this->serializer->serialize($ordersId, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="subscription_plots_id_by_contract_id", path="/plots/contract/{contractId}")
     */
    public function listPlotsIdByContractId(string $contractId, Request $request)
    {
        $filter = $request->get('filter', null);
        $plots = $this->subscriptionPackageFieldService->getPlotsUuidWithStateByContractId($contractId, $filter);

        return new JsonResponse($this->serializer->serialize($plots, 'json'), 200, [], true);
    }

    /**
     * @param string $contractId
     *
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="count_fields_by_field_state", path="/count")
     */
    public function countFieldsByState(Request $request)
    {
        if (!$request->get('customer_identification')) {
            throw new InvalidParameterException('Customer identification number is missing', 400);
        }

        $customerIdentifications = json_decode($request->get('customer_identification'), true);
        $response = $this->subscriptionPackageFieldService->countFieldsByStates($customerIdentifications);

        return new JsonResponse($this->serializer->serialize($response, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="update_field_farm", path="/update-farm")
     */
    public function updateFarm(Request $request)
    {
        $data = json_decode($request->getContent(), true);
        $this->subscriptionPackageFieldService->updateFieldFarm($data['plotUuid'], $data['farmId']);

        return new JsonResponse($this->serializer->serialize('', 'json'), 200, [], true);
    }

    /**
     * @throws Exception
     *
     * @Route(methods={"POST"}, name="selected_sampler", path="/selected-sampler")
     */
    public function selectedSampler(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        try {
            $params[] = ['field' => 'plotUuid', 'value' => $data['plotUuids'], 'type' => 'IN'];
            $params[] = ['field' => 'orderUuid', 'value' => $data['orderUuids'], 'type' => 'IN'];
            $this->workflowSubscriptionFieldService->updateSubscriptionPackageFieldStateByParams($params, SubscriptionPackageField::TRANSITION_PLOT_FOR_SAMPLING);
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), 503);
        }

        return new JsonResponse(null, 201);
    }

    /**
     * @param Request $request
     *                         array $data Array containing the necessary keys.
     *                         $data = [
     *                         'plots'     => Array with plot ids [1,2 ...],
     *                         'orders'     => Array with order ids [1,2 ...],
     *                         ]
     *
     * @Route(methods={"POST"}, name="synced_to_mobile", path="/synced-to-mobile")
     */
    public function syncedToMobile(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        try {
            $params[] = ['field' => 'plotUuid', 'value' => $data['plotUuids'], 'type' => 'IN'];
            $params[] = ['field' => 'orderUuid', 'value' => $data['orderUuids'], 'type' => 'IN'];
            $this->workflowSubscriptionFieldService->updateSubscriptionPackageFieldStateByParams($params, SubscriptionPackageField::TRANSITION_PLOT_SAMPLING);
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), 503);
        }

        return new JsonResponse(null, 200);
    }

    /**
     * @throws Exception
     *
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="get fields with grid packaged, group elements and grid points with elemtns", path="/ab-overview")
     */
    public function getFieldsABOverview(Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        try {
            $fieldData = $this->subscriptionPackageFieldService->getFieldsABOverview($data['filter'], $data['limit'], $data['page']);
        } catch (Exception $e) {
            throw $e;
        }

        return new JsonResponse($fieldData, 200);
    }

    /**
     * @throws Exception
     *
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="create_subscription_package_fields", path="/")
     */
    public function createPackageFields(Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        try {
            $arrExistingData = $this->subscriptionPackageFieldService->getExistingPackageField($data);
            if (count($arrExistingData) > 0) {
                throw new LogicException('Already existing fields data.', 400);
            }
            $protocolData = $this->subscriptionPackageFieldService->addFields($data);
        } catch (Exception $e) {
            throw $e;
        }

        return new JsonResponse($protocolData, 201);
    }
}
