<?php

namespace App\Controller\API\Contract;

use App\Controller\API\AbstractBaseController;
use App\Entity\Contract\ServicePackage;
use App\Service\Contract\ServicePackageService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class ServicePackageController.
 *
 * @Route(path="/contracts/service-package")
 */
class ServicePackageController extends AbstractBaseController
{
    private $servicePackageService;

    public function __construct(SerializerInterface $serializer, ServicePackageService $servicePackageService)
    {
        parent::__construct($serializer);
        $this->servicePackageService = $servicePackageService;
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="add_remove_fields_to_contract_service_package", path="/{servicePackage}")
     */
    public function fields(ServicePackage $servicePackage, Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        $this->servicePackageService->addFields($servicePackage, $data);

        return new JsonResponse(
            $this->serializer->serialize($servicePackage, 'json', ['groups' => 'api']),
            200,
            [],
            true
        );
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="service-package_by_filter", path="/filter")
     */
    public function listPackagesByFilter(Request $request)
    {
        $filter = $request->get('filter', []);
        $servicePackages = $this->servicePackageService->listPackagesByFilter($filter);

        return new JsonResponse($this->serializer->serialize($servicePackages, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_service-package_by_contract_id", path="/contract/{contractId}")
     */
    public function listPackagesByContract(string $contractId, Request $request)
    {
        // TODO:: think to implement @ParamConverter for filters about Repository
        $filter = $request->get('filter', []);
        $filter['contract_id'] = json_encode([$contractId]);
        $servicePackages = $this->servicePackageService->listPackagesByFilter($filter);

        return new JsonResponse($this->serializer->serialize($servicePackages, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="count_service-package_by_customer_indetifications", path="/count/")
     */
    public function countPackagesByCustomerIdentifications(Request $request)
    {
        if (!$request->get('customer_identification')) {
            return new JsonResponse($this->serializer->serialize([], 'json'), 200, [], true);
        }

        $page = $request->get('type_count', 'all');
        $customerIdentifications = json_decode($request->get('customer_identification'), true);

        if ('available' === $page) {
            $packageStates = [ServicePackage::STATUS_IN_PROGRESS, ServicePackage::STATUS_WAITING_FOR_PLOTS];
            $result = $this->servicePackageService->countPackagesByCustomerIdentifications($customerIdentifications, true, $packageStates);

            return new JsonResponse($this->serializer->serialize(['countPackages' => count($result)], 'json'), 200, [], true);
        }

        $result = $this->servicePackageService->countPackagesByCustomerIdentifications($customerIdentifications);

        return new JsonResponse($this->serializer->serialize($result, 'json'), 200, [], true);
    }
}
