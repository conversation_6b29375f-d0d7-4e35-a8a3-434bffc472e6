<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API\Contract;

use App\Controller\API\AbstractBaseController;
use App\Service\Contract\ServicePackageFieldService;
use Exception;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class ServicePackageFieldController.
 *
 * @Route(path="/contracts/service-package-field")
 */
class ServicePackageFieldController extends AbstractBaseController
{
    private $servicePackageFieldService;

    public function __construct(SerializerInterface $serializer, ServicePackageFieldService $servicePackageFieldService)
    {
        parent::__construct($serializer);
        $this->servicePackageFieldService = $servicePackageFieldService;
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="service_orders_id_by_contract_id", path="/orders/contract/{contractId}")
     */
    public function listOrdersIdByContractId(string $contractId, Request $request)
    {
        $filter = $request->get('filter', null);
        $ordersId = $this->servicePackageFieldService->getOrdersIdByContractId($contractId, $filter);

        return new JsonResponse($this->serializer->serialize($ordersId, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="service_plots_id_by_contract_id", path="/plots/contract/{contractId}")
     */
    public function listPlotsIdByContractId(string $contractId, Request $request)
    {
        $filter = $request->get('filter', null);
        $plotUuids = $this->servicePackageFieldService->getPlotsIdByContractId($contractId, $filter);

        return new JsonResponse($this->serializer->serialize($plotUuids, 'json'), 200, [], true);
    }

    /**
     * @throws Exception
     *
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="create_service_package_fields", path="/")
     */
    public function createPackageFields(Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        try {
            $this->servicePackageFieldService->addFields($data);
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), 503);
        }

        return new JsonResponse(null, 201);
    }
}
