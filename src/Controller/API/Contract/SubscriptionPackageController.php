<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API\Contract;

use App\Controller\API\AbstractBaseController;
use App\Entity\Contract\SubscriptionPackage;
use App\Service\Contract\SubscriptionPackageService;
use App\Service\Workflow\SubscriptionPackageService as WorkFlowSubscriptionPackageService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Exception\InvalidParameterException;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class SubscriptionPackageController.
 *
 * @Route(path="/contracts/subscription-package")
 */
class SubscriptionPackageController extends AbstractBaseController
{
    private $subscriptionPackageService;
    private $durationTypeService;
    private $workFlowSubscriptionPackageService;

    public function __construct(
        SerializerInterface $serializer,
        SubscriptionPackageService $subscriptionPackageService,
        WorkFlowSubscriptionPackageService $workFlowSubscriptionPackageService
    ) {
        parent::__construct($serializer);
        $this->subscriptionPackageService = $subscriptionPackageService;
        $this->workFlowSubscriptionPackageService = $workFlowSubscriptionPackageService;
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="add_remove_fields_to_contract_subscription_package", path="/{subscriptionPackage}")
     */
    public function fields(SubscriptionPackage $subscriptionPackage, Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        $this->subscriptionPackageService->addFields($subscriptionPackage, $data);

        return new JsonResponse(
            $this->serializer->serialize($subscriptionPackage, 'json', ['groups' => 'api']),
            200,
            [],
            true
        );
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="subscription-package_by_filter", path="/filter")
     */
    public function listPackagesByFilter(Request $request)
    {
        $filter = $request->get('filter', []);
        $subscriptionPackages = $this->subscriptionPackageService->listPackagesByFilter($filter);

        return new JsonResponse($this->serializer->serialize($subscriptionPackages, 'json'), 200, [], true);
    }

    // TODO: methods listPackagesByContract is the same as listPackagesByFilter. To be used only listPackagesByFilter,
    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="subscription-package_by_contract_id", path="/contract/{contractId}")
     */
    public function listPackagesByContract(string $contractId, Request $request)
    {
        // TODO:: think to implement @ParamConverter for filters about Repository
        $filter = $request->get('filter', []);
        $filter['contract_id'] = json_encode([$contractId]);
        $subscriptionPackages = $this->subscriptionPackageService->listPackagesByFilter($filter);

        return new JsonResponse($this->serializer->serialize($subscriptionPackages, 'json'), 200, [], true);
    }

    /**
     * @param string $contractId
     * @param Request $request
     *
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="station-added", path="/contract/{contractId}/station-added")
     */
    public function stationAdded(int $contractId)
    {
        $params[] = ['field' => 'contract', 'type' => '=', 'value' => $contractId];
        $this->workFlowSubscriptionPackageService->updateByFields($params, SubscriptionPackage::TRANSITION_IN_PROGRESS_STATE);

        return new JsonResponse('Success', 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="count_all_subscription-package_by_customer_indetifications", path="/count/")
     */
    public function countAllPackagesByCustomerIdentifications(Request $request)
    {
        if (!$request->get('customer_identification')) {
            throw new InvalidParameterException('Customer identification number is missing', 400);
        }

        $page = $request->get('type_count', 'all');
        $customerIdentifications = json_decode($request->get('customer_identification'), true);

        if ('available' === $page) {
            $packageStates = [SubscriptionPackage::STATUS_IN_PROGRESS, SubscriptionPackage::STATUS_WAITING_FOR_PLOTS];
            $result = $this->subscriptionPackageService->countPackagesByCustomerIdentifications($customerIdentifications, true, $packageStates);

            return new JsonResponse($this->serializer->serialize(['countPackages' => count($result)], 'json'), 200, [], true);
        }

        $result = $this->subscriptionPackageService->countPackagesByCustomerIdentifications($customerIdentifications);

        return new JsonResponse($this->serializer->serialize($result, 'json'), 200, [], true);
    }
}
