<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API\Contract;

use App\Controller\API\AbstractBaseController;
use App\Entity\Contract\DurationType;
use App\Service\DurationTypeService;
use DateTime;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Exception\InvalidParameterException;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class DurationTypeController.
 *
 * @Route(path="/contracts/duration-types")
 */
class DurationTypeController extends AbstractBaseController
{
    private $durationTypeService;

    public function __construct(DurationTypeService $durationTypeService, SerializerInterface $serializer)
    {
        $this->durationTypeService = $durationTypeService;

        parent::__construct($serializer);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_contract_duration_types", path="/")
     */
    public function list()
    {
        $em = $this->getDoctrine()->getManager();
        $durationTypes = $em->getRepository(DurationType::class)->findAll();

        return new JsonResponse(
            $this->serializer->serialize($durationTypes, 'json', ['groups' => 'api']),
            200,
            [],
            true
        );
    }

    /**
     * @param $durationType DurationType
     * @param $request Request
     *
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="calculate_periods", path="/{id}/calculate-periods")
     */
    public function calculatePeriods(DurationType $durationType, Request $request)
    {
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        if (empty($startDate) || empty($endDate)) {
            throw new InvalidParameterException('Start date and end date is missing');
        }

        $startDate = DateTime::createFromFormat('Y-m-d', $startDate)->setTime(0, 0, 0);
        $endDate = DateTime::createFromFormat('Y-m-d', $endDate)->setTime(23, 59, 59);

        $periods = $this->durationTypeService->calculatePeriods($durationType, $startDate, $endDate);

        return new JsonResponse($periods);
    }
}
