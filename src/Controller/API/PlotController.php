<?php

namespace App\Controller\API;

use App\Entity\Package;
use App\Service\Plot\PlotService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class PlotController.
 *
 * @Route(path="/plots")
 */
class PlotController extends AbstractBaseController
{
    /**
     * @var PlotService
     */
    private $plotService;

    public function __construct(
        SerializerInterface $serializer,
        PlotService $plotService
    ) {
        parent::__construct($serializer);

        $this->plotService = $plotService;
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_plot_soils_filter", path="/soils-filter")
     */
    public function getSoilsFilter(Request $request)
    {
        $filter = $request->get('filter', null);

        $results = $this->plotService->getSamplingOrderUuidsByFilters($filter);

        return new JsonResponse($this->serializer->serialize(['orderUUids' => $results], 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_plot_soils_list", path="/soils-list")
     */
    public function getSoilsList(Request $request)
    {
        $filter = $request->get('filter', null);
        $customerOrderUuids = json_decode($filter['customer_order_uuids'], true);
        $results = $this->plotService->getSamplingPackagesPlotsByOrderUuids($customerOrderUuids);

        return new JsonResponse($this->serializer->serialize($results, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_packages_by_plot", path="/{plotUuid}/packages")
     */
    public function getPackagesByPlot(Request $request, string $plotUuid)
    {
        $status = $request->get('status', 'Active');

        $em = $this->getDoctrine()->getManager();
        $packageRepository = $em->getRepository(Package::class);
        $packages = $packageRepository->getPackagesByPlot($plotUuid, $status);

        return new JsonResponse($this->serializer->serialize($packages, 'json'), 200, [], true);
    }
}
