<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>.nonchev
 * Date: 7/25/2019
 * Time: 3:10 PM.
 */

namespace App\Controller\API;

use App\Service\GeoSCANAPIClient;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class RoleController.
 *
 * @Route(path="/roles")
 */
class RoleController extends AbstractBaseController
{
    private $geoSCANAPIClient;

    public function __construct(
        SerializerInterface $serializer,
        GeoSCANAPIClient $geoSCANAPIClient
    ) {
        parent::__construct($serializer);
        $this->geoSCANAPIClient = $geoSCANAPIClient;
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_all_roles", path="/")
     */
    public function list()
    {
        $roles = $this->geoSCANAPIClient->getRoles();

        $return = [
            'roles' => $roles->data,
        ];

        return new JsonResponse(
            $this->serializer->serialize($return, 'json', ['groups' => 'api']),
            200,
            [],
            true
        );
    }
}
