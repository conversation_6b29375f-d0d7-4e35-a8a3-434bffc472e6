<?php

namespace App\Controller\API;

use App\Entity\Contract;
use App\Service\GeoSCANAPIClient;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class StationController.
 *
 * @Route(path="/stations")
 */
class StationController extends AbstractBaseController
{
    private $geoSCANAPIClient;

    public function __construct(
        SerializerInterface $serializer,
        GeoSCANAPIClient $geoSCANAPIClient
    ) {
        parent::__construct($serializer);
        $this->geoSCANAPIClient = $geoSCANAPIClient;
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_station_contracts_by_customer_identifications", path="/contracts")
     */
    public function getStationContractsByCustomerIdentifications(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $contractRepository = $em->getRepository(Contract::class);
        $customerIdentifications = $request->get('customer_identifications', null);
        $contracts = $contractRepository->getStationContractsByCustomerIdentifications($customerIdentifications);
        $circularReferenceHandler = $this->circularReferenceHandler();

        return new JsonResponse($this->serializer->serialize($contracts, 'json', ['groups' => 'api', 'circular_reference_handler' => $circularReferenceHandler]), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_station_history", path="/{stationId}/history")
     */
    public function getHistory(Request $request, $stationId)
    {
        $requestParams = [
            'limit' => $request->query->get('limit', 5),
            'page' => $request->query->get('page', 0),
        ];

        $stationHistory = $this->geoSCANAPIClient->getStationHistory($stationId, $requestParams);

        $response = [
            'rows' => $stationHistory->historyData,
        ];

        return new JsonResponse($this->serializer->serialize($response, 'json'), 200, [], true);
    }
}
