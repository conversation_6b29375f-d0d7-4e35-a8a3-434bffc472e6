<?php

namespace App\Controller\API;

use App\Entity\Protocol;
use Exception;
use LogicException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use <PERSON>ymfony\Component\Serializer\SerializerInterface;

/**
 * Class ProtocolController.
 *
 * @Route(path="/protocols")
 */
class ProtocolController extends AbstractBaseController
{
    public function __construct(
        SerializerInterface $serializer
    ) {
        parent::__construct($serializer);
    }

    /**
     * @throws Exception
     *
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, path="/list", name="list_protocols")
     */
    public function list(Request $request)
    {
        $page = $request->get('page', 0);
        $limit = $request->get('limit', 10);
        $customerIdentification = $request->get('customer_identification', null);

        try {
            $result = $this->getProtocolData($customerIdentification);
        } catch (Exception $e) {
            throw $e;
        }
        $page = intval($page) + 1;
        $paginatedResult = $this->paginateArray($result, $page, $limit);

        return new JsonResponse($this->serializer->serialize($paginatedResult, 'json', [
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            },
        ]), 200, [], true);
    }

    /**
     * @throws Exception
     *
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, path="/{id}", name="get_protocol")
     */
    public function index(int $id, Request $request)
    {
        $customerIdentification = $request->get('customer_identification', null);

        try {
            $result = $this->getProtocolData($customerIdentification, $id);
        } catch (Exception $e) {
            throw $e;
        }

        return new JsonResponse($this->serializer->serialize($result, 'json', [
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            },
        ]), 200, [], true);
    }

    /**
     * @param int $customerIdentification
     * @param ?int $id
     *
     * @return JsonResponse
     */
    private function getProtocolData(string $customerIdentification, ?int $id = null)
    {
        if (!$customerIdentification) {
            throw new LogicException('The customer_identification field is required', 400);
        }

        $em = $this->getDoctrine()->getManager();
        $protocolRepository = $em->getRepository(Protocol::class);

        return $protocolRepository->getProtocolsData($customerIdentification, $id);
    }
}
