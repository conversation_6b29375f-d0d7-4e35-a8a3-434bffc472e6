<?php

namespace App\Controller\API\Analysis;

use App\Controller\API\AbstractBaseController;
use App\Service\Analysis\LabElementGroupService;
use App\Service\Analysis\LabElementsResultsService;
use App\Service\Analysis\PackageGridPointsService;
use App\Service\Contract\SubscriptionPackageFieldService;
use App\Service\Workflow\Analysis\PackageGridPointsWorkflowService;
use App\Validator\Constraints\PackageGridPointsConstraints;
use Exception;
use LogicException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Exception\InvalidParameterException;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Class PackageGridPointsController.
 *
 * @Route(path="/analysis/package-grid-points")
 */
class PackageGridPointsController extends AbstractBaseController
{
    private PackageGridPointsService $packageGridPointsService;
    private LabElementGroupService $labElementGroupService;
    private LabElementsResultsService $labElementsResultsService;
    private SubscriptionPackageFieldService $subscriptionPackageFieldService;
    private PackageGridPointsWorkflowService $workflowPackageGridPointsService;
    private ValidatorInterface $validator;

    /**
     * PackageGridPointsController constructor.
     */
    public function __construct(
        SerializerInterface $serializer,
        PackageGridPointsService $packageGridPointsService,
        LabElementGroupService $labElementGroupService,
        LabElementsResultsService $labElementsResultsService,
        PackageGridPointsWorkflowService $workflowPackageGridPointsService,
        SubscriptionPackageFieldService $subscriptionPackageFieldService,
        ValidatorInterface $validator
    ) {
        parent::__construct($serializer);
        $this->packageGridPointsService = $packageGridPointsService;
        $this->labElementGroupService = $labElementGroupService;
        $this->labElementsResultsService = $labElementsResultsService;
        $this->workflowPackageGridPointsService = $workflowPackageGridPointsService;
        $this->validator = $validator;
    }

    /**
     * @Route(methods={"GET"}, name="get barcodes", path="/barcodes")
     *
     * @return JsonResponse
     */
    public function getBarcodes(Request $request)
    {
        if (!$request->get('customer_identification')) {
            throw new InvalidParameterException('Customer identification number is missing', 400);
        }

        $requestData = $request->get('barcode_number', null);
        $customerIdentification = $request->get('customer_identification');

        $response = $this->packageGridPointsService->getBarcodes($customerIdentification, $requestData);

        return new JsonResponse($response, 200);
    }

    /**
     * @Route(methods={"GET"}, name="get lab numbers", path="/lab-numbers")
     *
     * @return JsonResponse
     */
    public function getLabNumbers(Request $request)
    {
        if (!$request->get('customer_identification')) {
            throw new InvalidParameterException('Customer identification number is missing', 400);
        }

        $requestData = $request->get('lab_number', null);
        $customerIdentification = $request->get('customer_identification');

        $response = $this->packageGridPointsService->getLabNumbers($customerIdentification, $requestData);

        return new JsonResponse($response, 200);
    }

    /**
     * @Route(methods={"GET"}, name="getCellsForSamplingWithElements", path="/for-sampling")
     */
    public function getCellsForSamplingWithElements(Request $request): JsonResponse
    {
        $filter = $request->get('filter', []);

        $violations = $this->validator->validate($filter, PackageGridPointsConstraints::getCellsForSamplingWithElements(false));

        if ($violations->count()) {
            throw new BadRequestHttpException($violations->get(0)->__toString(), null, 400);
        }

        $result = $this->packageGridPointsService->getCellsForSamplingWithElements($filter);

        return new JsonResponse($this->serializer->serialize($result, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="create_package_grid_points", path="/")
     */
    public function createPackageGridPoints(Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        try {
            /**
             * Array $data Structure
             *    $data[] = [
             *      'point_uuid' => string,
             *      'sample_id' => number,
             *      'plot_uuid' => string,
             *      'package_id' => number,
             *      'package_type' => string,
             *      'grid_type' => string,
             *    ].
             */
            $arrExistingData = $this->packageGridPointsService->getElementsResultsData($data);
            if (count($arrExistingData) > 0) {
                throw new LogicException('Already existing points data.', 400);
            }

            $this->packageGridPointsService->create($data);

            $arrToInsertElementGroup = $this->packageGridPointsService->getElementGroupData($data);
            $this->labElementGroupService->create($arrToInsertElementGroup);

            $arrToInsertElementsResults = $this->packageGridPointsService->getElementsResultsData($data);
            $this->labElementsResultsService->create($arrToInsertElementsResults);
        } catch (Exception $e) {
            throw $e;
        }

        return new JsonResponse(null, 201);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="save_barcodes", path="/save-barcodes")
     */
    public function saveBarcodes(Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        try {
            $this->packageGridPointsService->saveBarcodes($data);
        } catch (Exception $e) {
            return new JsonResponse([$e->getMessage()], 503);
        }

        return new JsonResponse(null, 200);
    }

    /**
     * @Route(methods={"POST"}, name="update_plot_points_state_by_selected_cells_for_sampling", path="/cells-for-sampling")
     */
    public function updatePlotPointsStateBySelectedCellsForSampling(Request $request): JsonResponse
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        try {
            $this->workflowPackageGridPointsService->updatePlotPointsStateToForSamplingOrNotSampled($data);
        } catch (Exception $e) {
            return new JsonResponse([$e->getMessage()], 503);
        }

        return new JsonResponse(null, 200);
    }

    /**
     * @Route(methods={"POST"}, name="update_plots_sampling_points", path="/update-sampling")
     */
    public function updatePlotsSamplingPoints(Request $request): JsonResponse
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        try {
            $this->workflowPackageGridPointsService->updatePlotsSamplingPoints($data);
        } catch (Exception $e) {
            return new JsonResponse([$e->getMessage()], 503);
        }

        return new JsonResponse(null, 200);
    }
}
