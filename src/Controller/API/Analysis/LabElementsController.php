<?php

namespace App\Controller\API\Analysis;

use App\Controller\API\AbstractBaseController;
use App\Entity\Analysis\LabElementsResults;
use App\Entity\Analysis\MetaElementsGroups;
use App\Entity\Package\SamplingType;
use App\Repository\Analysis\LabElementsResultsRepository;
use App\Repository\Analysis\MetaElementsGroupsRepository;
use App\Repository\Package\SamplingTypeRepository;
use App\Service\Analysis\LabElementGroupService;
use App\Service\Analysis\LabElementsResultsService;
use App\Service\Workflow\Analysis\LabElementsResultsWorkflowService;
use App\Validator\Constraints\LabElementsControllerConstraints;
use App\Validator\Constraints\SamplingTypeConstraints;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Class LabElementsController.
 *
 * @Route(path="/analysis/lab-elements")
 */
class LabElementsController extends AbstractBaseController
{
    private LabElementsResultsService $labElementsResultsService;
    private LabElementsResultsWorkflowService $workflowLabElementsResultsService;
    private LabElementGroupService $elementGroupService;
    private ValidatorInterface $validator;
    private ManagerRegistry $managerRegistry;

    public function __construct(
        SerializerInterface $serializer,
        LabElementsResultsService $labElementsResultsService,
        LabElementsResultsWorkflowService $workflowLabElementsResultsService,
        LabElementGroupService $elementGroupService,
        ValidatorInterface $validator,
        ManagerRegistry $managerRegistry
    ) {
        parent::__construct($serializer);
        $this->labElementsResultsService = $labElementsResultsService;
        $this->workflowLabElementsResultsService = $workflowLabElementsResultsService;
        $this->elementGroupService = $elementGroupService;
        $this->validator = $validator;
        $this->managerRegistry = $managerRegistry;
    }

    /**
     * @Route(methods={"GET"}, name="list_lab_elements_results", path="/results")
     */
    public function getResults(Request $request)
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 5);
        $filters = $request->get('filter', []);
        $withPagination = $request->get('withPagination', true);

        /**
         * @var MetaElementsGroupsRepository
         */
        $elementsGroupsRepo = $this->managerRegistry->getRepository(MetaElementsGroups::class);
        $elementsGroups = $elementsGroupsRepo->getSortedElementsWithGroups();

        $result = $this->labElementsResultsService->getElementsResults($filters, $withPagination, $page, $limit, $elementsGroups);

        $result = [
            'columns' => $elementsGroups,
            'rows' => [
                'items' => $result['items'],
                'total_items' => $result['total_items'],
                'total_pages' => ceil($result['total_items'] / $limit),
            ],
        ];

        return new JsonResponse($this->serializer->serialize($result, 'json'), 200, [], true);
    }

    /**
     * @Route(methods={"GET"}, name="list_lab_elements_results_for_approve", path="/results/for-approve")
     */
    public function getElementsResultsForApprove(Request $request)
    {
        $filter = $request->get('filter', []);

        $violations = $this->validator->validate($filter, LabElementsControllerConstraints::getElementsResultsForApprove(false));

        if ($violations->count()) {
            throw new BadRequestHttpException($violations->get(0)->__toString(), null, 400);
        }

        $elementsResultsForApprove = $this->labElementsResultsService->getElementsResultsForApprove($filter);

        return new JsonResponse($this->serializer->serialize($elementsResultsForApprove, 'json'), 200, [], true);
    }

    /**
     * @Route(methods={"GET"}, name="export_csv_lab_elements_results", path="/results/csv")
     */
    public function exportResultsCSV(Request $request)
    {
        $filters = $request->get('filter', []);
        $elementsResults = $this->labElementsResultsService->getElementsResultsForCSV($filters);

        $fileName = 'lab_elements_results_' . date('Y-m-d') . '.csv';
        $content = $this->serializer->encode($elementsResults, 'csv');
        $headers = ['Content-Type' => 'text/csv', 'Content-Disposition' => 'attachment; filename=' . $fileName];

        return new Response($content, 200, $headers);
    }

    /**
     * @Route(methods={"GET"}, name="get_plots_count", path="/plots-count")
     */
    public function getPlotsCount(Request $request)
    {
        $filter = $request->get('filter', []);

        /**
         * @var LabElementsResultsRepository
         */
        $labElementsResultsRepo = $this->managerRegistry->getRepository(LabElementsResults::class);
        $result = $labElementsResultsRepo->getPlotsCountByState($filter)->getResult();

        return new JsonResponse($this->serializer->serialize($result, 'json'), 200, [], true);
    }

    /**
     * @Route(methods={"GET"}, name="get_elements_for_soil_map", path="/soil-map")
     */
    public function elementsForSoilMap(Request $request): JsonResponse
    {
        $filters = $request->get('filter', []);
        $result = $this->elementGroupService->elementsForSoilMap($filters);

        return new JsonResponse($this->serializer->serialize($result, 'json'), 200, [], true);
    }

    /**
     * @Route(methods={"GET"}, name="get_elements_unit", path="/unit")
     */
    public function elementsUnit(Request $request): JsonResponse
    {
        $result = $this->elementGroupService->getElementsUnit();

        return new JsonResponse($this->serializer->serialize($result, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_samples_by_element", path="/{element}/plots/{plotUuId}/soil/samples")
     */
    public function samplesByElement(string $element, string $plotUuId, Request $request)
    {
        $orderUuid = $request->get('order_uuid', '');
        $samplingTypeIds = $request->get('sampling_type_ids', []);

        if (!$orderUuid) {
            return new JsonResponse('Order uuid is required.', 400, [], true);
        }

        $result = $this->labElementsResultsService->samplesContentByElement($plotUuId, $orderUuid, $element, $samplingTypeIds);

        return new JsonResponse(json_encode($result), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_samples_content", path="/plots/{plotUuId}/soil/samples")
     */
    public function samplesContent(string $plotUuId, Request $request)
    {
        $orderUuid = $request->get('order_uuid', '');
        $samplingTypeIds = $request->get('sampling_type_ids', []);

        if (!$orderUuid) {
            return new JsonResponse('Order uuid is required.', 400, [], true);
        }

        $result = $this->labElementsResultsService->samplesContent($plotUuId, $orderUuid, $samplingTypeIds);

        return new JsonResponse(json_encode($result), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_analyses_report", path="/report")
     */
    public function analysesReport(Request $request)
    {
        $filter = $request->get('filter', []);
        $limit = $request->get('limit', null);
        $page = $request->get('page', null);

        $report = $this->labElementsResultsService->getAnalysesReport($filter, $limit, $page);
        $header = $this->labElementsResultsService->getAnalysesReportHeader($filter);

        $response = [
            'total' => $report['total_items'] ?? count($report['items']),
            'rows' => $report['items'],
            'header' => $header ?? [],
        ];

        return new JsonResponse($response, 200);
    }

    /**
     * @Route(methods={"POST"}, name="update_lab_elements_results_state", path="/results/update-states")
     */
    public function updateLabElementsResultStates(Request $request)
    {
        $body = $request->getContent();
        $arrData = json_decode($body, true);

        $this->workflowLabElementsResultsService->updateLabElementsResultStates($arrData);

        return new JsonResponse(null, 200);
    }

    /**
     * @Route(methods={"GET"}, name="get_element_interpretation_classes", path="/interpretation-classes")
     */
    public function getElementInterpretationClasses(Request $request)
    {
        $response = $this->labElementsResultsService->getElementInterpretationClasses();

        return new JsonResponse($response, 200);
    }

    /**
     * @Route(methods={"GET"}, name="get_sampling_types", path="/sampling-types")
     */
    public function getSamplingTypes(Request $request)
    {
        $requestData = json_decode($request->getContent(), true);
        $violations = $this->validator->validate($requestData, SamplingTypeConstraints::getSamplingTypes());

        if ($violations->count()) {
            throw new BadRequestHttpException($violations->get(0)->__toString(), null, 400);
        }

        $filter = $request->get('filter', []);

        /**
         * @var SamplingTypeRepository
         */
        $samplingTypeRepo = $this->managerRegistry->getRepository(SamplingType::class);
        $samplingTypes = $samplingTypeRepo->getSamplingTypes($filter);

        return new JsonResponse($this->serializer->serialize($samplingTypes, 'json'), 200, [], true);
    }
}
