<?php

namespace App\Controller\API\Analysis;

use App\Controller\API\AbstractBaseController;
use App\Service\Analysis\LabAnalysisUploadService;
use App\Service\Analysis\LabElementsResultsRawService;
use App\Service\Analysis\LabElementsResultsService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class LabAnalysisUploadsController.
 *
 * @Route(path="/analysis/uploads")
 */
class LabAnalysisUploadsController extends AbstractBaseController
{
    private $labAnalysisUploadService;
    private $labElementsResultsRawService;
    private $labElementsResultsService;

    public function __construct(
        SerializerInterface $serializer,
        LabAnalysisUploadService $labAnalysisUploadService,
        LabElementsResultsRawService $labElementsResultsRawService,
        LabElementsResultsService $labElementsResultsService
    ) {
        parent::__construct($serializer);
        $this->labAnalysisUploadService = $labAnalysisUploadService;
        $this->labElementsResultsRawService = $labElementsResultsRawService;
        $this->labElementsResultsService = $labElementsResultsService;
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="create_upload", path="/")
     */
    public function createUpload(Request $request)
    {
        $uploadedFile = $request->files->get('file');
        $fileName = $this->labAnalysisUploadService->upload($uploadedFile);
        $arrResult = $this->labAnalysisUploadService->create($fileName);
        $this->labElementsResultsRawService->create($arrResult);
        // Update calculated values at lab_elements_results
        $uploadId = $arrResult['upload']->getId();
        $this->labElementsResultsService->updateCalculatedValues($uploadId);

        return new JsonResponse(null, 201);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="list_uploaded_analysis", path="/list")
     */
    public function list(Request $request)
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 5);
        $sort = $request->get('sort', []);

        $labAnalysisUploads = $this->labAnalysisUploadService->getAllSorted($sort);
        $paginatedResults = $this->paginateArray($labAnalysisUploads, $page, $limit);

        return new JsonResponse($this->serializer->serialize($paginatedResults, 'json', ['groups' => 'api']), 200, [], true);
    }
}
