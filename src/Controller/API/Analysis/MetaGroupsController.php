<?php

namespace App\Controller\API\Analysis;

use App\Controller\API\AbstractBaseController;
use App\Entity\Analysis\MetaGroups;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class MetaGroupsController.
 *
 * @Route(path="/analysis/meta-elements-groups")
 */
class MetaGroupsController extends AbstractBaseController
{
    public function __construct(SerializerInterface $serializer)
    {
        parent::__construct($serializer);
    }

    /**
     * @Route(methods={"GET"}, name="list_meta_groups", path="/list")
     */
    public function list()
    {
        $em = $this->getDoctrine()->getManager();
        $metaGroups = $em->getRepository(MetaGroups::class)->findAll();

        return new JsonResponse($this->serializer->serialize($metaGroups, 'json', ['groups' => 'api']), 200, [], true);
    }
}
