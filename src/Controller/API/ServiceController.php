<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API;

use App\Entity\Service;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class ServiceController.
 *
 * @Route(path="/services")
 */
class ServiceController extends AbstractBaseController
{
    /**
     * @param $request Request
     *
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_services", path="/")
     */
    public function list(Request $request)
    {
        $services = $this->getDoctrine()->getManager()->getRepository(Service::class)->findBy(['isActive' => true]);

        return new JsonResponse($this->serializer->serialize($services, 'json', ['groups' => 'api']), 200, [], true);
    }
}
