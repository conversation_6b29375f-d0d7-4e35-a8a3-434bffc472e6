<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API;

use Closure;
use Doctrine\ORM\QueryBuilder;
use Pagerfanta\Adapter\ArrayAdapter;
use Pagerfanta\Doctrine\ORM\QueryAdapter;
use Pagerfanta\Pagerfanta;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Serializer\SerializerInterface;

class AbstractBaseController extends AbstractController
{
    protected $serializer;

    public function __construct(SerializerInterface $serializer)
    {
        $this->serializer = $serializer;
    }

    protected function paginateORMQueryBuilder(QueryBuilder $queryBuilder, int $page = 1, int $limit = 10): array
    {
        $adapter = new QueryAdapter($queryBuilder);
        $paginator = new Pagerfanta($adapter);
        $pagi<PERSON>or->setMaxPerPage($limit);
        $paginator->setCurrentPage($page);

        return [
            'total_items' => $paginator->getNbResults(),
            'total_pages' => $paginator->getNbPages(),
            'items' => $paginator->getCurrentPageResults(),
        ];
    }

    protected function paginateArray(array $array, int $page = 1, int $limit = 10): array
    {
        $adapter = new ArrayAdapter($array);
        $paginator = new Pagerfanta($adapter);
        $paginator->setMaxPerPage($limit);
        $paginator->setCurrentPage($page);

        return [
            'total_items' => $paginator->getNbResults(),
            'total_pages' => $paginator->getNbPages(),
            'items' => $paginator->getCurrentPageResults(),
        ];
    }

    /**
     * To avoid infinite loops and the exception: "A circular reference has been detected when serializing the object
     * of class App\Entity\Contract\SubscriptionPackage&quot; (configured limit: 1) (500 Internal Server Error) - When subscriptionPackageFields property is not empty.
     *
     * @return Closure
     */
    protected function circularReferenceHandler()
    {
        return function ($object) {
            return $object->getId();
        };
    }
}
