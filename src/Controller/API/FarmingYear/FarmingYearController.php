<?php

namespace App\Controller\API\FarmingYear;

use App\Controller\API\AbstractBaseController;
use App\Service\FarmingYear\FarmingYearService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class OrganizationController.
 *
 * @Route(path="farming-years")
 */
class FarmingYearController extends AbstractBaseController
{
    private FarmingYearService $farmingYearService;

    public function __construct(
        SerializerInterface $serializer,
        FarmingYearService $farmingYearService
    ) {
        parent::__construct($serializer);
        $this->farmingYearService = $farmingYearService;
    }

    /**
     * @Route("/organization/{organizationId}", name="farming_years_by_organization")
     */
    public function listByOrganization(int $organizationId): JsonResponse
    {
        $result = $this->farmingYearService->getFarmingYearsByOrganization($organizationId);

        return new JsonResponse($this->serializer->serialize($result, 'json'), 200, [], true);
    }
}
