<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API;

use App\Entity\Package;
use App\Repository\PackageRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class PackageController.
 *
 * @Route(path="/packages")
 */
class PackageController extends AbstractBaseController
{
    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_packages_list", path="/")
     */
    public function list(Request $request)
    {
        $filter = $request->get('filter', []);

        $em = $this->getDoctrine()->getManager();
        /** @var PackageRepository $packageRepository */
        $packageRepository = $em->getRepository(Package::class);
        $packages = $packageRepository->getPackages($filter);

        return new JsonResponse($this->serializer->serialize($packages, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_package_by_id", path="/{id}")
     */
    public function show(int $id)
    {
        $em = $this->getDoctrine()->getManager();
        $packageRepository = $em->getRepository(Package::class);
        $package = $packageRepository->find($id);

        if (!$package) {
            return new JsonResponse(['error' => 'Package not found'], 404);
        }

        return new JsonResponse($this->serializer->serialize($package, 'json', ['groups' => 'api']), 200, [], true);
    }
}
