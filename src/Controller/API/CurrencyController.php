<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API;

use App\Entity\Currency;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class CurrencyController.
 *
 * @Route(path="/currencies")
 */
class CurrencyController extends AbstractBaseController
{
    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_currencies_list", path="/")
     */
    public function list()
    {
        $em = $this->getDoctrine()->getManager();
        $currencyRepository = $em->getRepository(Currency::class);
        $currencies = $currencyRepository->findAll();

        return new JsonResponse($this->serializer->serialize($currencies, 'json', ['groups' => 'api']), 200, [], true);
    }
}
