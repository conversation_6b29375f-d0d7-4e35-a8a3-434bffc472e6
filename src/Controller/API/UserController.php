<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API;

use App\Entity\Package;
use App\Model\GeoSCAN\User;
use App\Repository\PackageRepository;
use App\Service\GeoSCAN\UserService;
use App\Service\GeoSCANAPIClient;
use Exception;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class UserController.
 *
 * @Route(path="/users")
 */
class UserController extends AbstractBaseController
{
    private Security $security;
    private PackageRepository $packageRepository;
    private GeoSCANAPIClient $geoSCANAPIClient;
    private UserService $userService;

    public function __construct(
        SerializerInterface $serializer,
        GeoSCANAPIClient $geoSCANAPIClient,
        UserService $userService,
        Security $security,
        PackageRepository $packageRepository
    ) {
        parent::__construct($serializer);
        $this->geoSCANAPIClient = $geoSCANAPIClient;
        $this->userService = $userService;
        $this->security = $security;
        $this->packageRepository = $packageRepository;
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_all_users", path="/")
     */
    public function list(Request $request)
    {
        $users = $this->geoSCANAPIClient->getUsers($request->query->all());

        $return = [
            'total_items' => $users->total,
            'users' => $users->data,
        ];

        return new JsonResponse($this->serializer->serialize($return, 'json', ['groups' => 'user']), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_users_by_role", path="/role/{role}")
     */
    public function listByRole($role, Request $request)
    {
        $users = $this->geoSCANAPIClient->getUsersByRole($role, $request->query->all());

        return new JsonResponse($this->serializer->serialize($users, 'json', ['groups' => 'user']), 200, [], true);
    }

    /**
     * @Route(methods={"GET"}, name="get_users_roles", path="/roles")
     */
    public function getUserRoles(): JsonResponse
    {
        $userRoles = $this->getUser()->getRoles();

        return new JsonResponse($userRoles, 200);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_user_abilities", path="/abilities")
     */
    public function getUserAbilities(Request $request)
    {
        try {
            $abilities = $this->geoSCANAPIClient->getUserAbilities($request->query->all());
        } catch (Exception $e) {
            // TODO:: error ???
        }

        return new JsonResponse($abilities, 200);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_user_permissions", path="/permissions")
     */
    public function getTechnofarmUserPermissions(Request $request)
    {
        try {
            $username = $request->get('username');
            $permissions = [];
            $isTfIntegrated = count($this->packageRepository->getPackagesByServiceProvider($this->security->getUser()->getServiceProvider()->getId(), ['integration' => Package::INTEGRATION_TYPE_TECHNOFARM]));
            if ($isTfIntegrated) {
                $permissions = $this->userService->getTechnofarmUserPermissions($username);
            }

            return new JsonResponse($permissions, JsonResponse::HTTP_OK);
        } catch (Exception $ex) {
            return new JsonResponse([], JsonResponse::HTTP_BAD_REQUEST);
        }
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_user_history", path="/history/{userId}")
     */
    public function getUserHistory($userId, Request $request)
    {
        $requestParams = [
            'limit' => $request->query->get('limit', 5),
            'page' => $request->query->get('page', 0),
        ];

        $userHistory = $this->geoSCANAPIClient->getUserHistory($userId, $requestParams);

        $return = [
            'rows' => $userHistory->historyData,
        ];

        return new JsonResponse(
            $this->serializer->serialize($return, 'json', ['groups' => 'api']),
            200,
            [],
            true
        );
    }

    /**
     * @throws Exception
     *
     * @return JsonResponse
     *
     * @Route(methods={"HEAD"}, name="check_user_existence_by_username", path="/check")
     */
    public function checkUserExistence(Request $request)
    {
        $username = $request->get('username');
        $email = $request->get('email');

        if ($username) {
            $this->userService->checkUsernameExistence($username);
        }

        if ($email) {
            $this->userService->checkEmailExistence($email);
        }

        return new JsonResponse(null, Response::HTTP_OK);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="users_search", path="/search")
     */
    public function search(Request $request)
    {
        $value = $request->get('value');
        $users = $this->userService->search($value, $this->security->getUser()->getServiceProvider()->getId());

        return new JsonResponse($users);
    }

    /**
     * @return Response
     *
     * @Route(methods={"GET"}, name="search_users", path="/profile-img-dir-path")
     */
    public function getUserProfileImgDirPath()
    {
        $data = $this->geoSCANAPIClient->getUserProfileImgDirPath();

        return new JsonResponse($data, 200, ['Content-Type: text/html; charset=utf-8']);
    }

    /**
     * @throws Exception
     *
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="create_user", path="/")
     */
    public function create(Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        $user = $this->userService->create($data);

        return new JsonResponse($this->serializer->serialize($user, 'json', ['groups' => 'api']), 201, [], true);
    }

    /**
     * @throws Exception
     *
     * @return JsonResponse
     *
     * @Route(methods={"PUT"}, name="update_user", path="/{userId}")
     */
    public function update($userId, Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        try {
            $user = $this->userService->update($data);
        } catch (Exception $e) {
            throw $e;
        }

        return new JsonResponse($this->serializer->serialize($user, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="change_user_farms_visibility", path="/{userId}/farms/visibility")
     */
    public function farmsVisibility(int $userId, Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);
        $user = new User();
        $user->setId($userId);
        $result = $this->userService->updateFarmsVisibility($user, $data);

        return new JsonResponse($this->serializer->serialize($result, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="change_user_farms_attach_detach", path="/{userId}/farms/manage-access")
     */
    public function manageFarmsAccess(int $userId, Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);
        $user = new User();
        $user->setId($userId);
        $result = $this->userService->manageFarmsAccess($user, $data);

        return new JsonResponse($this->serializer->serialize($result, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @throws Exception
     *
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="manage_permission", path="/manage-permission")
     */
    public function manageUserPermission(Request $request)
    {
        $body = $request->getContent();

        try {
            $this->geoSCANAPIClient->managePermission($body);
        } catch (Exception $e) {
            throw $e;
        }

        return new JsonResponse(null, 204);
    }
}
