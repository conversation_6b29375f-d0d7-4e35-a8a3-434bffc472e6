<?php

namespace App\Controller\API;

use App\Entity\Contract\Price;
use App\Entity\Payment;
use App\Service\Contract\PaymentService;
use Exception;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class UserController.
 *
 * @Route(path="/")
 */
class PaymentController extends AbstractBaseController
{
    private $paymentService;

    public function __construct(
        SerializerInterface $serializer,
        PaymentService $paymentService
    ) {
        parent::__construct($serializer);
        $this->serializer = $serializer;
        $this->paymentService = $paymentService;
    }

    /**
     * @return JsonResponse|Response
     *
     * @Route(methods={"POST"}, name="create_payment", path="/price/{price}/create_payment")
     */
    public function create(Price $price, Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        try {
            $payment = $this->paymentService->create($price, $data);
        } catch (Exception $e) {
            throw $e;
        }

        $circularReferenceHandler = $this->circularReferenceHandler();

        return new JsonResponse($this->serializer->serialize($payment, 'json', ['groups' => 'api', 'circular_reference_handler' => $circularReferenceHandler]), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_payments_list", path="/price/{price}/payments")
     */
    public function list(Price $price, Request $request)
    {
        $page = $request->get('page', 0);
        $page = intval($page) + 1;
        $limit = $request->get('limit', 10);

        $em = $this->getDoctrine()->getManager();

        $packageRepository = $em->getRepository(Payment::class);
        $paymentsQb = $packageRepository->findByPrice($price->getId());

        $paginatedResults = $this->paginateORMQueryBuilder($paymentsQb, $page, $limit);

        $circularReferenceHandler = $this->circularReferenceHandler();

        return new JsonResponse($this->serializer->serialize($paginatedResults, 'json', ['groups' => 'api', 'circular_reference_handler' => $circularReferenceHandler]), 200, [], true);
    }
}
