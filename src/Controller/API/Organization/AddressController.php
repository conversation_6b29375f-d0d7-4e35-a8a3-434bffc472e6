<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API\Organization;

use App\Controller\API\AbstractBaseController;
use App\Service\GeoSCAN\Organization\AddressService;
use App\Service\GeoSCANAPIClient;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class OrganizationController.
 *
 * @Route(path="/organization/{organizationId}")
 */
class AddressController extends AbstractBaseController
{
    private $geoSCANAPIClient;
    private $addressService;

    public function __construct(
        SerializerInterface $serializer,
        GeoSCANAPIClient $geoSCANAPIClient,
        AddressService $addressService
    ) {
        parent::__construct($serializer);
        $this->geoSCANAPIClient = $geoSCANAPIClient;
        $this->addressService = $addressService;
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_organization_address_list", path="/address")
     */
    public function list($organizationId)
    {
        $organizationAddresses = $this->geoSCANAPIClient->getOrganizationAddresses($organizationId);

        return new JsonResponse($this->serializer->serialize($organizationAddresses, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="create_organization_address", path="/address")
     */
    public function create(int $organizationId, Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        $organizationAddress = $this->addressService->create($organizationId, $data);

        return new JsonResponse($this->serializer->serialize($organizationAddress, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"PUT"}, name="update_organization_address", path="/address/{addressId}")
     */
    public function update(int $organizationId, int $addressId, Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        $organizationAddress = $this->addressService->update($organizationId, $addressId, $data);

        return new JsonResponse($this->serializer->serialize($organizationAddress, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     *
     * @Route(methods={"DELETE"}, name="delete_organization_address", path="/address/{addressId}")
     */
    public function delete(int $organizationId, int $addressId)
    {
        $response = $this->addressService->delete($organizationId, $addressId);

        return new JsonResponse($response, 200, [], true);
    }
}
