<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API\Organization;

use App\Controller\API\AbstractBaseController;
use App\Service\GeoSCAN\Organization\ContactPersonService;
use App\Service\GeoSCANAPIClient;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class OrganizationController.
 *
 * @Route(path="/organization/{organizationId}")
 */
class ContactPersonsController extends AbstractBaseController
{
    private $geoSCANAPIClient;
    private $contactPersonService;

    public function __construct(
        SerializerInterface $serializer,
        GeoSCANAPIClient $geoSCANAPIClient,
        ContactPersonService $contactPersonService
    ) {
        parent::__construct($serializer);
        $this->geoSCANAPIClient = $geoSCANAPIClient;
        $this->contactPersonService = $contactPersonService;
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_organization_contact_persons_list", path="/contact-persons")
     */
    public function list($organizationId)
    {
        $organizationContactPersons = $this->geoSCANAPIClient->getOrganizationContactPersons($organizationId);

        return new JsonResponse($this->serializer->serialize($organizationContactPersons, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="create_organization_contact_persons", path="/contact-persons")
     */
    public function create(int $organizationId, Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        $organizationContactPerson = $this->contactPersonService->create($organizationId, $data);

        return new JsonResponse($this->serializer->serialize($organizationContactPerson, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"PUT"}, name="update_organization_contact_persons", path="/contact-persons/{contactPersonId}")
     */
    public function update(int $organizationId, int $contactPersonId, Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        $organizationContactPerson = $this->contactPersonService->update($organizationId, $contactPersonId, $data);

        return new JsonResponse($this->serializer->serialize($organizationContactPerson, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     *
     * @Route(methods={"DELETE"}, name="delete_organization_contact_persons", path="/contact-persons/{contactPersonId}")
     */
    public function delete(int $organizationId, int $contactPersonId)
    {
        $response = $this->contactPersonService->delete($organizationId, $contactPersonId);

        return new JsonResponse($response, 200, [], true);
    }
}
