<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API;

use App\Model\GeoSCAN\Organization;
use App\Service\GeoSCAN\OrganizationService;
use App\Service\GeoSCANAPIClient;
use Exception;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class OrganizationController.
 *
 * @Route(path="/organizations")
 */
class OrganizationController extends AbstractBaseController
{
    private $geoSCANAPIClient;
    private $organizationService;

    public function __construct(
        SerializerInterface $serializer,
        GeoSCANAPIClient $geoSCANAPIClient,
        OrganizationService $organizationService
    ) {
        parent::__construct($serializer);
        $this->geoSCANAPIClient = $geoSCANAPIClient;
        $this->organizationService = $organizationService;
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="search_organizations", path="/search")
     */
    public function search(Request $request)
    {
        $string = $request->get('q');
        $organizations = $this->organizationService->search($string);

        return new JsonResponse($this->serializer->serialize($organizations, 'json', ['groups' => 'user']), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_organizations_list", path="/")
     */
    public function list(Request $request)
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $identityNumber = $request->get('identityNumber');
        $organizationManager = json_decode($request->get('organizationManager'), true);
        $serviceManager = json_decode($request->get('serviceManager'), true);

        $organizations = $this->geoSCANAPIClient->getOrganizations($page, $limit, $identityNumber, $organizationManager, $serviceManager);

        if (empty($organizations)) {
            throw new NotFoundHttpException(null, null, 404);
        }
        $return = [
            'total_items' => $organizations->total,
            'total_pages' => $organizations->last_page,
            'items' => $organizations->data,
        ];

        return new JsonResponse($this->serializer->serialize($return, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="create_organization", path="/")
     */
    public function create(Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);

        $organization = $this->organizationService->create($data);

        return new JsonResponse($this->serializer->serialize($organization, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @Route(methods={"PUT"}, name="update_organizaion", path="/{organizationId}")
     */
    public function update($organizationId, Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);
        $organization = new Organization();
        $organization->setId($organizationId);
        $organization = $this->organizationService->update($organization, $data);

        return new JsonResponse(
            $this->serializer->serialize($organization, 'json', ['groups' => 'api']),
            200,
            [],
            true
        );
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_organization_users_list", path="/{organizationId}/users")
     */
    public function users(int $organizationId)
    {
        $result = $this->geoSCANAPIClient->getOrganizationUsers($organizationId);

        return new JsonResponse($this->serializer->serialize($result, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_organization_farms_list", path="/{organizationId}/farms")
     */
    public function farms($organizationId)
    {
        $result = $this->geoSCANAPIClient->getOrganizationFarms($organizationId);

        return new JsonResponse($this->serializer->serialize($result, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="add_organization_user_relation", path="/{organizationId}/users/{userId}/add")
     */
    public function addUser(int $organizationId, int $userId)
    {
        $result = $this->organizationService->organizationUserRel($organizationId, $userId, 'attach');

        return new JsonResponse($this->serializer->serialize($result, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="remove_organization_user_relation", path="/{organizationId}/users/{userId}/remove")
     */
    public function removeUser(int $organizationId, int $userId)
    {
        $result = $this->organizationService->organizationUserRel($organizationId, $userId, 'detach');

        return new JsonResponse($this->serializer->serialize($result, 'json', ['groups' => 'api']), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="manage_relation_between_organization_and_user", path="/manage-organization")
     */
    public function manageOrgUserRelation(Request $request)
    {
        $body = $request->getContent();

        try {
            $this->organizationService->manageOrgUserRelation($body);
        } catch (Exception $e) {
            throw $e;
        }

        return new JsonResponse(null, 204);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_active_subscriptions", path="/subscriptions")
     */
    public function getActiveSubscriptions(Request $request)
    {
        $identityNumbers = json_decode($request->get('identityNumbers', '[]'));
        $userId = $request->get('userId');
        $integration = $request->get('integration');
        $packageSlug = $request->get('packageSlug');
        $filterPerOrganization = $request->get('filterPerOrganization', false);

        $subscriptions = $this->organizationService->getActiveSubscriptions(
            [
                'identityNumbers' => $identityNumbers,
                'userId' => $userId,
                'integration' => $integration,
                'packageSlug' => $packageSlug,
                'organizationId' => $filterPerOrganization ? $this->geoSCANAPIClient->getLastChosenOrganizationId() : null,
            ]
        );

        return new JsonResponse($subscriptions, JsonResponse::HTTP_OK);
    }
}
