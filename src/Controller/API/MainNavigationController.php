<?php

namespace App\Controller\API;

use App\Entity\Navigation\MainNavigation;
use App\Service\Navigation\MainNavigationService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

class MainNavigationController extends AbstractBaseController
{
    /**
     * @var MainNavigationService
     */
    private $mainNavigationService;

    public function __construct(
        SerializerInterface $serializer,
        MainNavigationService $mainNavigationService
    ) {
        parent::__construct($serializer);
        $this->mainNavigationService = $mainNavigationService;
    }

    /**
     * @Route("/main-navigation/{instance}", name="main_navigation_items")
     */
    public function index($instance, Request $request): JsonResponse
    {
        if (!in_array($instance, MainNavigation::getAvailableInstance())) {
            throw new NotFoundHttpException('Instance not found', null, 404);
        }

        $lang = $request->get('lang', 'en');
        $mainNavigationItems = $this->mainNavigationService->getMainNavigationMenuItems($lang, $instance);

        return new JsonResponse($mainNavigationItems);
    }
}
