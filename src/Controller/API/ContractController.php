<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API;

use App\Entity\Contract;
use App\Entity\Contract\ServicePackage;
use App\Entity\Contract\Subscription;
use App\Entity\Contract\SubscriptionPackage;
use App\Model\GeoSCAN\Organization;
use App\Service\Analysis\LabElementsResultsService;
use App\Service\ContractService;
use App\Service\DurationTypeService;
use App\Service\GeoSCANAPIClient;
use App\Service\Workflow\ContractService as WorkflowContractService;
use App\Service\Workflow\SubscriptionPackageService;
use App\Validator\Constraints\ContractControllerConstraints;
use Doctrine\DBAL\ConnectionException;
use Exception;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Class ContractController.
 *
 * @Route(path="/contracts")
 */
class ContractController extends AbstractBaseController
{
    /**
     * @var ValidatorInterface
     */
    private $validator;

    private $contractService;
    private $geoSCANAPIClient;
    private $durationTypeService;
    private $labElementsResultsService;

    private $workflowContractService;
    private $workflowSubscriptionPackageService;

    public function __construct(
        ValidatorInterface $validator,
        SerializerInterface $serializer,
        ContractService $contractService,
        GeoSCANAPIClient $geoSCANAPIClient,
        DurationTypeService $durationTypeService,
        WorkflowContractService $workflowContractService,
        LabElementsResultsService $labElementsResultsService,
        SubscriptionPackageService $workflowSubscriptionPackageService
    ) {
        parent::__construct($serializer);
        $this->validator = $validator;
        $this->contractService = $contractService;
        $this->geoSCANAPIClient = $geoSCANAPIClient;
        $this->durationTypeService = $durationTypeService;
        $this->workflowContractService = $workflowContractService;
        $this->labElementsResultsService = $labElementsResultsService;
        $this->workflowSubscriptionPackageService = $workflowSubscriptionPackageService;
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_contract_types", path="/types")
     */
    public function getTypes()
    {
        return new JsonResponse(Contract::TYPES);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_valid_contracts_short_data", path="/short")
     */
    public function listValidContractsData(Request $request)
    {
        // TODO:: think to implement @ParamConverter for filters about Repository
        $requestData = [];
        $requestData['offset'] = $request->get('offset', 0);
        $requestData['limit'] = $request->get('limit', null);
        $requestData['filter'] = $request->get('filter', null);
        $groupBy = $request->get('group_by', '');
        $groupByPackagePeriod = 'package_period' === $groupBy ? true : false;

        $em = $this->getDoctrine()->getManager();
        $contractRepository = $em->getRepository(Contract::class);
        $contracts = $contractRepository->listValidContractsWithCalculatedAreaByRules($requestData, $groupByPackagePeriod);

        return new JsonResponse($this->serializer->serialize($contracts, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="count_contracts_by_customer_ident", path="/count")
     */
    public function countContracts(Request $request)
    {
        $data = [];
        $data['filter'] = $request->get('filter', null);

        $em = $this->getDoctrine()->getManager();
        $contractRepository = $em->getRepository(Contract::class);

        $contracts = $contractRepository->listValidContractsWithCalculatedAreaByRules($data);

        $return = [
            'countContracts' => $contracts['total'],
        ];

        return new JsonResponse($this->serializer->serialize($return, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="count_contracts_amount", path="/count/amount")
     */
    public function countContractsAmount(Request $request)
    {
        $data = [];
        $data['filter'] = $request->get('filter', null);

        $em = $this->getDoctrine()->getManager();
        $contractRepository = $em->getRepository(Contract::class);

        $countContracts = $contractRepository->countAllAmountsForContractsByFilter($data);

        $return = [
            'countContractsAmount' => $countContracts['Amount'],
        ];

        return new JsonResponse($this->serializer->serialize($return, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_contracts_stations_amount_by_customer_identifications", path="/stations/amount")
     */
    public function contractsStationsAmountByCustomersIdent(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $contractRepository = $em->getRepository(Contract::class);
        $customerIdentifications = $request->get('customer_identifications', null);
        $limit = $request->get('limit', 6);
        $offset = $request->get('offset', 0);
        $withPagination = filter_var($request->get('withPagination', true), FILTER_VALIDATE_BOOLEAN);

        $response = [
            'items' => $contractRepository->getStationsAmountByCustomerIdentifications($customerIdentifications, $withPagination, $limit, $offset),
            'total' => $contractRepository->getStationsAmountByCustomerIdentifications($customerIdentifications, $withPagination, $limit, $offset, true),
        ];

        return new JsonResponse($this->serializer->serialize($response, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_contracts_integration_card_list", path="/integration/card-list")
     */
    public function contractsIntegrationCardList(Request $request)
    {
        $requestData = [
            'userId' => $request->get('user_id'),
            'packageSlug' => $request->get('package_slug'),
            'customerIdentification' => $request->get('customer_identification', null),
            'limit' => $request->get('limit', 6),
            'offset' => $request->get('offset', 0),
        ];

        $violations = $this->validator->validate($requestData, ContractControllerConstraints::contractsIntegrationCardList());

        if ($violations->count()) {
            throw new BadRequestHttpException($violations->get(0)->__toString(), null, 400);
        }

        $response = [
            'rows' => $this->contractService->getIntegrationsCardList(
                $requestData['userId'],
                $requestData['customerIdentification'],
                $requestData['packageSlug'],
                $requestData['limit'],
                $requestData['offset']
            ),
            'total' => $this->contractService->getIntegrationsCardList(
                $requestData['userId'],
                $requestData['customerIdentification'],
                $requestData['packageSlug'],
                $requestData['limit'],
                $requestData['offset'],
                true
            ),
        ];

        return new JsonResponse($this->serializer->serialize($response, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_data_for_cards", path="/cards")
     */
    public function getCardsData(Request $request)
    {
        $requestData = [
            'userId' => $request->get('user_id'),
        ];

        $violations = $this->validator->validate($requestData, ContractControllerConstraints::contractsGetCardsData());

        if ($violations->count()) {
            throw new BadRequestHttpException($violations->get(0)->getMessage(), null, 400);
        }

        $allCardsData = $this->contractService->getCardsData($requestData['userId']);

        return new JsonResponse($this->serializer->serialize($allCardsData, 'json', [
            'groups' => 'api',
            'circular_reference_handler' => $this->circularReferenceHandler(),
        ]), 200, [], true);
    }

    /**
     * @throws Exception
     *
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_contracts_list", path="/{type}", defaults={"type" = ""})
     */
    public function list(string $type, Request $request)
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $filters = $request->get('filter', []);
        $sort = $request->get('sort', []);

        $em = $this->getDoctrine()->getManager();
        $contractRepository = $em->getRepository(Contract::class);
        $contractsQb = $contractRepository->getFilteredAndSortedQb($type, $filters, $sort);
        $paginatedResults = $this->paginateORMQueryBuilder($contractsQb, $page, $limit);

        $customerIdentifications = [];
        $paginatedResults['packagePeriods'] = [];
        foreach ($paginatedResults['items'] as $item) {
            // @var Contract $item
            $customerIdentifications[] = $item->getCustomerIdentification();

            if ($item instanceof Subscription) {
                /** @var SubscriptionPackage $subscriptionPackage */
                foreach ($item->getSubscriptionPackages() as $subscriptionPackage) {
                    try {
                        $paginatedResults['packagePeriods'][$subscriptionPackage->getId()] = $this->durationTypeService->formatPeriod($item->getDurationType(), $subscriptionPackage->getStartDate(), $subscriptionPackage->getEndDate());
                    } catch (Exception $e) {
                        throw new Exception('Error in calculating package periods.');
                    }
                }
            }
        }

        $customerIdentifications = array_unique($customerIdentifications);
        $customerIdentifications = array_filter($customerIdentifications, function ($element) {
            return !is_null($element);
        });

        $paginatedResults['organization_names'] = [];
        if (!empty($customerIdentifications)) {
            $names = $this->geoSCANAPIClient->getOrganizationsName($customerIdentifications);
            $paginatedResults['organization_names'] = $names;
        }

        $circularReferenceHandler = $this->circularReferenceHandler();

        return new JsonResponse($this->serializer->serialize($paginatedResults, 'json', ['groups' => 'api', 'circular_reference_handler' => $circularReferenceHandler]), 200, [], true);
    }

    /**
     * @throws ConnectionException
     *
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="create_contract", path="/{type}")
     */
    public function create(string $type, Request $request)
    {
        $body = $request->getContent();
        $data = json_decode($body, true);
        $contract = $this->contractService->create($type, $data);

        $this->contractWorkflow($contract);

        // Add Service User to Contract's organization if is not added yet
        $this->geoSCANAPIClient->addServiceUserToOrganization($contract);

        $circularReferenceHandler = $this->circularReferenceHandler();

        return new JsonResponse(
            $this->serializer->serialize($contract, 'json', ['groups' => 'api', 'circular_reference_handler' => $circularReferenceHandler]),
            201,
            [],
            true
        );
    }

    /**
     * @throws LogicException
     *
     * @return JsonResponse
     *
     * @Route(methods={"PUT"}, name="update_contract", path="/{id}")
     */
    public function update(Contract $contract, Request $request)
    {
        if (!$contract->canEdit()) {
            throw new \LogicException('Contract is not editable', Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $body = $request->getContent();
        $data = json_decode($body, true);

        $this->contractService->update($contract, $data);

        $this->contractWorkflow($contract);
        $this->activateNewPackagesWorkflow($contract);

        $circularReferenceHandler = $this->circularReferenceHandler();

        return new JsonResponse(
            $this->serializer->serialize($contract, 'json', ['groups' => 'api', 'circular_reference_handler' => $circularReferenceHandler]),
            200,
            [],
            true
        );
    }

    /**
     * @throws \LogicException
     *
     * @return JsonResponse
     *
     * @Route(methods={"DELETE"}, name="delete_contract", path="/{id}")
     */
    public function delete(Contract $contract, Request $request)
    {
        if (!$contract->canDelete()) {
            throw new \LogicException('Cannot delete contract', 409);
        }

        $this->contractService->delete($contract);

        return new JsonResponse('Success', 200);
    }

    /**
     * @Route(methods={"GET"}, name="get_contract_fields", path="/{id}/{type}/fields")
     *
     * @throws BadRequestHttpException
     */
    public function getContractFields(Contract $contract, string $type, Request $request)
    {
        $packageId = intval($request->get('packageId'));
        $requestData = [
            'contractType' => $type,
            'packageId' => $packageId,
        ];

        $violations = $this->validator->validate($requestData, ContractControllerConstraints::getContractFields(Contract::TYPES));

        if ($violations->count()) {
            throw new BadRequestHttpException($violations->get(0)->__toString(), null, 400);
        }

        $fields = $this->contractService->getContractFields($contract, $type, $packageId);

        return new JsonResponse(
            $this->serializer->serialize($fields, 'json', ['groups' => 'api']),
            200,
            [],
            true
        );
    }

    /**
     * @throws BadRequestHttpException
     *
     * @return JsonResponse
     *
     * @Route(methods={"DELETE"}, name="remove_fields_from_contract", path="/{id}/fields")
     */
    public function removeFieldsFromContract(Contract $contract, Request $request)
    {
        $requestData = [
            'plotUuids' => json_decode($request->get('plotUuids', '[]'), true),
            'orderUuids' => json_decode($request->get('orderUuids', '[]'), true),
        ];

        $violations = $this->validator->validate($requestData, ContractControllerConstraints::removeFieldsFromContract());

        if ($violations->count()) {
            throw new BadRequestHttpException($violations->get(0)->__toString(), null, 400);
        }

        $plotUuids = $requestData['plotUuids'];
        $orderUuids = $requestData['orderUuids'];

        $removedFieldsCount = $this->contractService->removeFieldsFromContract($contract, $plotUuids, $orderUuids);

        if (0 === $removedFieldsCount) {
            return new JsonResponse('Fields not found.', 404);
        }

        return new JsonResponse('Success', 204);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="has_package", path="/{id}/has-package")
     */
    public function hasPackage(Contract $contract, Request $request)
    {
        $status = $request->get('status', 'Active');
        $slugShort = $request->get('slugShort', 'WS');

        $hasPackage = $this->contractService->hasPackage($contract, $status, $slugShort);

        return new JsonResponse($hasPackage, 200);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_packages_for_card", path="/{customerIdentification}/packages")
     */
    public function subscriptionsAndServicesList(string $customerIdentification, Request $request)
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 5);

        $em = $this->getDoctrine()->getManager();
        $subscriptionPackageRepository = $em->getRepository(SubscriptionPackage::class);
        $servicePackageRepository = $em->getRepository(ServicePackage::class);
        $subscriptionPackages = $subscriptionPackageRepository->findByOrganization($customerIdentification);
        $servicePackages = $servicePackageRepository->findByOrganization($customerIdentification);
        $result = $this->paginateArray(array_merge($subscriptionPackages, $servicePackages), $page, $limit);

        return new JsonResponse($result, 200);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_overlap_contracts", path="/{id}/get-overlap")
     */
    public function getOverlap(Contract $contract, Request $request)
    {
        $status = $request->get('status', 'Active');
        $hasStation = filter_var($request->get('has_station', true), FILTER_VALIDATE_BOOLEAN);

        return new JsonResponse($this->contractService->findOverlapContracts($contract, $status, $hasStation), 200);
    }

    private function contractWorkflow(Contract $contract)
    {
        if ($contract instanceof Subscription) {
            $this->workflowContractService->activateContract($contract);
            $this->getDoctrine()->getManager()->flush();
        }

        return;
    }

    private function activateNewPackagesWorkflow(Contract $contract)
    {
        if ($contract instanceof Subscription) {
            if (Contract::STATUS_ACTIVE === $contract->getStatus()) {
                $this->workflowSubscriptionPackageService->activateSubscriptionPackages($contract->getSubscriptionPackages(), true);
            }
        }

        return;
    }
}
