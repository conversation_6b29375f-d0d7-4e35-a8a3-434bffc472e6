<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Controller\API;

use App\Service\GeoSCAN\AbilityService;
use App\Service\GeoSCANAPIClient;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class UserController.
 *
 * @Route(path="/abilities")
 */
class AbilityController extends AbstractBaseController
{
    private $geoSCANAPIClient;
    private $abilityService;

    public function __construct(
        SerializerInterface $serializer,
        GeoSCANAPIClient $geoSCANAPIClient,
        AbilityService $abilityService
    ) {
        parent::__construct($serializer);
        $this->geoSCANAPIClient = $geoSCANAPIClient;
        $this->abilityService = $abilityService;
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_abilities", path="/")
     */
    public function list(Request $request)
    {
        $abilities = $this->geoSCANAPIClient->getAbilities($request->query->all());

        $return = [
            'total_items' => $abilities->total,
            'abilities' => $abilities->data,
        ];

        return new JsonResponse($this->serializer->serialize($return, 'json', ['groups' => 'api']), 200, [], true);
    }
}
