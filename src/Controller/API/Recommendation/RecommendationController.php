<?php

namespace App\Controller\API\Recommendation;

use App\Controller\API\AbstractBaseController;
use App\Entity\Contract\SubscriptionPackageField;
use App\Entity\Recommendation\Recommendation;
use App\Repository\Recommendation\RecommendationRepository;
use App\Service\Analysis\LabElementsResultsService;
use App\Service\Recommendation\RecommendationService;
use App\Validator\Constraints\RecommendationControllerConstraints;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Class RecommendationController.
 *
 * @Route(path="/recommendations")
 */
class RecommendationController extends AbstractBaseController
{
    private ValidatorInterface $validator;

    private RecommendationService $recommendationService;

    private LabElementsResultsService $labElementsResultsService;

    private ManagerRegistry $managerRegistry;

    /**
     * RecommendationController constructor.
     */
    public function __construct(
        SerializerInterface $serializer,
        ValidatorInterface $validator,
        RecommendationService $recommendationService,
        LabElementsResultsService $labElementsResultsService,
        ManagerRegistry $managerRegistry
    ) {
        parent::__construct($serializer);
        $this->recommendationService = $recommendationService;
        $this->labElementsResultsService = $labElementsResultsService;
        $this->validator = $validator;
        $this->managerRegistry = $managerRegistry;
    }

    /**
     * @throws \Doctrine\DBAL\DBALException
     *
     * @Route(methods={"GET"}, name="get_recommendation_report", path="/report")
     */
    public function report(Request $request): JsonResponse
    {
        $requestData = json_decode($request->getContent(), true);
        $violations = $this->validator->validate($requestData, RecommendationControllerConstraints::getRecommendationReport(false));

        if ($violations->count()) {
            throw new BadRequestHttpException($violations->get(0)->__toString(), null, 400);
        }

        $filter = $request->get('filter', []);
        $groupBy = $request->get('group_by', 'plot');
        $limit = $request->get('limit', null);
        $page = $request->get('page', null);
        $orderBy = json_decode($request->get('order_by', '[]'), true);

        $response = $this->recommendationService->findRecommendations($filter, $groupBy, $orderBy, $page, $limit);

        return new JsonResponse($this->serializer->serialize($response, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="list_recommendations", path="/")
     */
    public function list(Request $request)
    {
        // TODO:: [ASCM-436] This logic return agronomic tasks not recommendations. Rename methods and endpoint.
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $filters = $request->get('filter', []);

        $results = $this->recommendationService->search($page, $limit, $filters);
        $response = [
            'total_items' => $results['total'],
            'total_pages' => ceil($results['total'] / $limit),
            'items' => $results['recommendations'],
        ];

        return new JsonResponse($this->serializer->serialize($response, 'json'), 200, [], true);
    }

    /**
     * @throws \Doctrine\DBAL\DBALException
     *
     * @Route(methods={"GET"}, name="list_recommendations_by_plot", path="/plot/{plotUuid}")
     */
    public function listByPlot(string $plotUuid, Request $request): JsonResponse
    {
        $toDate = $request->get('to_date');
        $fromDate = $request->get('from_date');
        $status = $request->get('status', 'Delivered');

        $violations = $this->validator->validate(
            ['from_date' => $fromDate, 'to_date' => $toDate, 'status' => $status],
            RecommendationControllerConstraints::listRecommendationByPlot()
        );

        if ($violations->count()) {
            throw new BadRequestHttpException($violations->get(0)->__toString(), null, 400);
        }

        $results = $this->recommendationService->listByPlot($plotUuid, $fromDate, $toDate, $status);

        return new JsonResponse($this->serializer->serialize($results, 'json'), 200, [], true);
    }

    /**
     * @param int $id The recommendation id
     *
     * @return JsonResponse
     *
     * @Route(methods={"PUT"}, name="update_recommendation", path="/{id}/update")
     */
    public function updateRecommendation(int $id, Request $request)
    {
        $requestData = json_decode($request->getContent(), true);
        $violations = $this->validator->validate($requestData, RecommendationControllerConstraints::updateRecommendation());

        if ($violations->count()) {
            throw new BadRequestHttpException($violations->get(0)->__toString(), null, 400);
        }

        $em = $this->managerRegistry->getManager();

        /** @var RecommendationRepository */
        $recommendationRepo = $em->getRepository(Recommendation::class);
        $recommendation = $recommendationRepo->find($id);

        if (!$recommendation) {
            throw new ResourceNotFoundException("Cannot find recommendation with id {$id}", 404);
        }

        if (Recommendation::STATUS_DELIVERED === $recommendation->getStatus()) {
            throw new ConflictHttpException("Cannot edit recommendation in status 'Delivered'");
        }

        $this->recommendationService->updateRecommendation(
            $recommendation,
            $requestData
        );

        return new JsonResponse(null, 200);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"POST"}, name="store_recommendation", path="/store")
     */
    public function store(Request $request)
    {
        $requestData = json_decode($request->getContent(), true);
        $violations = $this->validator->validate($requestData, RecommendationControllerConstraints::store());

        if ($violations->count()) {
            throw new BadRequestHttpException($violations->get(0)->__toString(), null, 400);
        }

        $em = $this->managerRegistry->getManager();
        $subscriptionPackageFieldRepo = $em->getRepository(SubscriptionPackageField::class);
        $subscriptionPackageField = $subscriptionPackageFieldRepo->find($requestData['subscription_package_field_id']);

        if (!$subscriptionPackageField) {
            throw new ResourceNotFoundException('Cannot find subscription package field with id' . $requestData['subscription_package_field_id'] . '.', 404);
        }

        $this->recommendationService->storeRecommendation(
            $subscriptionPackageField,
            $requestData['plot_name'],
            $requestData['crop_id'],
            $requestData['model_id'],
            $requestData['humus'],
            $requestData['yield'],
            $requestData['valid_from'],
            $requestData['results'],
            $requestData['comments'],
            $requestData['susces'],
            $requestData['vra_orders'] ?? [],
            $requestData['sampling_type_ids']
        );

        return new JsonResponse(null, 200);
    }

    /**
     * @Route(methods={"PUT"}, name="update_recommendation_status", path="/{recommendation}/update-status")
     */
    public function updateStatus(Recommendation $recommendation, Request $request): JsonResponse
    {
        $requestData = json_decode($request->getContent(), true);
        $violations = $this->validator->validate($requestData, RecommendationControllerConstraints::updateStatus());

        if ($violations->count()) {
            throw new BadRequestHttpException($violations->get(0)->__toString(), null, 400);
        }

        $status = $requestData['status'];
        $declineReason = $requestData['decline_reason'] ?? null;

        if (Recommendation::STATUS_DECLINED === $status && !$declineReason) {
            throw new BadRequestHttpException('The decline_reason field is required when setting status to \'Declined\'.', null, 400);
        }

        $this->recommendationService->changeRecommendationStatus($recommendation, $status, $declineReason);

        return new JsonResponse([
            'status' => 'success',
        ], 200);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="list_recommendation_models", path="/models")
     */
    public function getModelsList(Request $request)
    {
        $response = $this->recommendationService->getModelsByServiceProvider();

        return new JsonResponse($this->serializer->serialize($response, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="recommendation_lab_results", path="/{subscriptionPackageFieldId}/lab-results")
     */
    public function getRecommendationLabResults(int $subscriptionPackageFieldId, Request $request)
    {
        $em = $this->managerRegistry->getManager();
        $subscriptionPackageFieldRepo = $em->getRepository(SubscriptionPackageField::class);
        $subscriptionPackageField = $subscriptionPackageFieldRepo->find($subscriptionPackageFieldId);
        if (!$subscriptionPackageField) {
            throw new ResourceNotFoundException("Cannot find subscription package field with id {$subscriptionPackageFieldId}.", 404);
        }

        $requestData = json_decode($request->getContent(), true);
        $violations = $this->validator->validate($requestData, RecommendationControllerConstraints::getRecommendationLabResults());

        if ($violations->count()) {
            throw new BadRequestHttpException($violations->get(0)->__toString(), null, 400);
        }

        $samplingTypeIds = $request->get('sampling_type_ids', []);

        $elementsResultsClasses = $this->labElementsResultsService->getElementsResultsWithClasses($subscriptionPackageField, $samplingTypeIds);
        $elementsInterpretations = $this->labElementsResultsService->getElementsResultsInterpretations($subscriptionPackageField, $samplingTypeIds);

        $response = [
            'results' => $elementsResultsClasses,
            'comments' => $elementsInterpretations,
        ];

        return new JsonResponse($this->serializer->serialize($response, 'json'), 200, [], true);
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="recommendation_vra_orders", path="/{recommendationId}/vra-orders")
     */
    public function getVraOrdersByRecommendationId(int $recommendationId)
    {
        $vraOrders = $this->recommendationService->getVraOrdersByRecommendationId($recommendationId);

        return new JsonResponse($this->serializer->serialize($vraOrders, 'json'), 200, [], true);
    }

    /**
     * @Route(methods={"GET"}, name="soil-analyzes-data", path="/soil-analyzes-data")
     */
    public function getSoilChartAnalyzes(Request $request): JsonResponse
    {
        $filters = $request->get('filter', []);

        $response = $this->recommendationService->getSoilAnalyzes($filters);

        return new JsonResponse($this->serializer->serialize($response, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="recommendation_calculations", path="/{subscriptionPackageFieldId}/calculations")
     */
    public function getRecommendationCalculations(int $subscriptionPackageFieldId, Request $request)
    {
        $requestData = $request->query->all();
        $violations = $this->validator->validate($requestData, RecommendationControllerConstraints::getRecommendationCalculations());

        if ($violations->count()) {
            throw new BadRequestHttpException($violations->get(0)->__toString(), null, 400);
        }

        $em = $this->managerRegistry->getManager();
        $subscriptionPackageFieldRepo = $em->getRepository(SubscriptionPackageField::class);
        $subscriptionPackageField = $subscriptionPackageFieldRepo->find($subscriptionPackageFieldId);

        if (!$subscriptionPackageField) {
            throw new ResourceNotFoundException("Cannot find subscription package field with id {$subscriptionPackageFieldId}.", 404);
        }

        $calculations = $this->recommendationService->getRecommendationCalculations(
            $subscriptionPackageField,
            $requestData['crop_id'],
            $requestData['model_id'],
            $requestData['humus'],
            $requestData['yield'],
            $requestData['valid_from'],
            $requestData['sampling_type_ids'] ?? []
        );

        return new JsonResponse($this->serializer->serialize($calculations, 'json'), 200, [], true);
    }

    /**
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_recommendation_history", path="/{recommendation}/history")
     */
    public function getRecommendationHistory(Recommendation $recommendation)
    {
        $recommendationHistory = $this->recommendationService->getRecommendationHistory($recommendation);

        return new JsonResponse($this->serializer->serialize($recommendationHistory, 'json'), 200, [], true);
    }

    /**
     * @param int $id The recommendation id
     *
     * @return JsonResponse
     *
     * @Route(methods={"GET"}, name="get_recommendation_details", path="/{id}")
     */
    public function getRecommendationResults(int $id, Request $request)
    {
        $em = $this->managerRegistry->getManager();
        $recommendationRepo = $em->getRepository(Recommendation::class);
        $recommendation = $recommendationRepo->find($id);

        if (!$recommendation) {
            throw new ResourceNotFoundException('Cannot find recommendation with id ' . $id . '.', 404);
        }

        $results = $this->recommendationService->getRecommendationResults($recommendation);

        return new JsonResponse($this->serializer->serialize($results, 'json'), 200, [], true);
    }
}
