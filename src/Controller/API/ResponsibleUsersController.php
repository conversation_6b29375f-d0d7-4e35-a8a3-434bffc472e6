<?php

namespace App\Controller\API;

use App\Entity\Contract\ResponsibleUser;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class ResponsibleUsersController.
 */
class ResponsibleUsersController extends AbstractBaseController
{
    protected $serializer;
    private $mr;

    public function __construct(
        ManagerRegistry $mr,
        SerializerInterface $serializer
    ) {
        $this->mr = $mr;
        $this->serializer = $serializer;
    }

    /**
     * @Route(methods={"GET"}, path="/responsible_users/order/{orderUuid}", name="app_responsible_users", requirements={"orderUuid" = "%app.UUID_V4%"})
     */
    public function findByOrderUuid(string $orderUuid, Request $request): JsonResponse
    {
        $em = $this->mr->getManager();
        $responsibleUserRepository = $em->getRepository(ResponsibleUser::class);
        $responsibleUsers = $responsibleUserRepository->findByOrderUuid($orderUuid);

        return new JsonResponse($this->serializer->serialize($responsibleUsers, 'json', [
            'groups' => 'api',
            'circular_reference_handler' => $this->circularReferenceHandler(),
        ]), 200, [], true);
    }
}
