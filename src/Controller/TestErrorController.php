<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Test controller for debugging error handling
 * Remove this controller in production!
 */
class TestErrorController extends AbstractController
{
    /**
     * @Route("/test/error/500", name="test_error_500", methods={"GET"})
     */
    public function testInternalServerError(): JsonResponse
    {
        // This will throw a 500 error
        throw new \RuntimeException('This is a test 500 error with detailed debugging information');
    }

    /**
     * @Route("/test/error/400", name="test_error_400", methods={"GET"})
     */
    public function testBadRequest(): JsonResponse
    {
        // This will throw a 400 error
        throw new BadRequestHttpException('This is a test 400 error - bad request');
    }

    /**
     * @Route("/test/error/404", name="test_error_404", methods={"GET"})
     */
    public function testNotFound(): JsonResponse
    {
        // This will throw a 404 error
        throw new NotFoundHttpException('This is a test 404 error - resource not found');
    }

    /**
     * @Route("/test/error/nested", name="test_error_nested", methods={"GET"})
     */
    public function testNestedError(): JsonResponse
    {
        try {
            $this->causeNestedError();
        } catch (\Exception $e) {
            throw new \RuntimeException('Outer exception with nested cause', 0, $e);
        }
    }

    /**
     * @Route("/test/error/database", name="test_error_database", methods={"GET"})
     */
    public function testDatabaseError(): JsonResponse
    {
        // This will simulate a database error
        $entityManager = $this->getDoctrine()->getManager();
        
        // Try to execute invalid SQL to trigger a database exception
        $connection = $entityManager->getConnection();
        $connection->executeQuery('SELECT * FROM non_existent_table');
        
        return new JsonResponse(['message' => 'This should not be reached']);
    }

    private function causeNestedError(): void
    {
        throw new \InvalidArgumentException('This is the inner/nested exception');
    }
}
