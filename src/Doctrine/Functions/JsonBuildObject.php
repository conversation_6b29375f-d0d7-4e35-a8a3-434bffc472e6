<?php

namespace App\Doctrine\Functions;

use Doctrine\ORM\Query\AST\Functions\FunctionNode;
use Doctrine\ORM\Query\AST\Node;
use Doctrine\ORM\Query\Lexer;
use Doctrine\ORM\Query\Parser;
use Doctrine\ORM\Query\SqlWalker;

class Json<PERSON>uildObject extends FunctionNode
{
    public $exprs;

    public function parse(Parser $parser): void
    {
        $this->exprs = [];

        $lexer = $parser->getLexer();
        $parser->match(Lexer::T_IDENTIFIER);
        $parser->match(Lexer::T_OPEN_PARENTHESIS);
        $this->exprs[] = $parser->ArithmeticPrimary();

        while (Lexer::T_COMMA === $lexer->lookahead['type']) {
            $parser->match(Lexer::T_COMMA);
            $this->exprs[] = $parser->ArithmeticPrimary();
        }

        $parser->match(Lexer::T_CLOSE_PARENTHESIS);
    }

    public function getSql(SqlWalker $sqlWalker): string
    {
        return 'JSONB_BUILD_OBJECT(' . join(',', array_map(function (Node $expr) use ($sqlWalker) {
            return $expr->dispatch($sqlWalker);
        }, $this->exprs)) . ')';
    }
}
