<?php

namespace App\Doctrine\Types;

use App\Model\Types\RangeType;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\Type;
use Exception;
use TypeError;

/**
 * My custom datatype.
 */
class NumrangeType extends Type
{
    public const NUMRANGE_TYPE = 'numrange'; // modify to match your type name

    /**
     * @return string
     */
    public function getSQLDeclaration(array $fieldDeclaration, AbstractPlatform $platform)
    {
        // return the SQL used to create your column type. To create a portable column type, use the $platform.
        return self::NUMRANGE_TYPE;
    }

    /**
     * @param [type] $value
     */
    public function convertToPHPValue($value, AbstractPlatform $platform)
    {
        // This is executed when the value is read from the database. Make your conversions here, optionally using the $platform.
        if (!(is_string($value) && strlen($value) > 0)) {
            throw new Exception("Invalid range type {$value}");
        }

        $templateIncludeMin = "(?'includeMin'\(|\[)";
        $templateIncludeMax = "(?'includeMax'\)|\])";
        $templateMin = "(?'min'[+-]?([0-9]+)([.][0-9]+)?)*";
        $templateMax = "(?'max'[+-]?([0-9]+)([.][0-9]+)?)*";

        $templateRange = '/' . $templateIncludeMin . $templateMin . "(,[\s]*)" . $templateMax . $templateIncludeMax . '/';

        $matches = [];
        preg_match($templateRange, $value, $matches);

        if (
            !isset($matches['includeMin'])
            || !isset($matches['includeMax'])
            || !isset($matches['min'])
            || !isset($matches['max'])
        ) {
            throw new Exception("Cannot parse range type: {$value}");
        }

        $matches = [];
        preg_match($templateRange, $value, $matches);

        $includeMin = null;
        if ('(' === $matches['includeMin']) {
            $includeMin = false;
        } elseif ('[' === $matches['includeMin']) {
            $includeMin = true;
        }

        $includeMax = null;
        if (')' === $matches['includeMax']) {
            $includeMax = false;
        } elseif (']' === $matches['includeMax']) {
            $includeMax = true;
        }

        $min = null;
        if (strlen($matches['min']) > 0) {
            $min = floatval($matches['min']);
        }

        $max = null;
        if (strlen($matches['max']) > 0) {
            $max = floatval($matches['max']);
        }

        return new RangeType($min, $max, $includeMin, $includeMax);
    }

    /**
     * @param [type] $value
     */
    public function convertToDatabaseValue($value, AbstractPlatform $platform)
    {
        // This is executed when the value is written to the database. Make your conversions here, optionally using the $platform.
        $rangeTypeClass = RangeType::class;
        if (!($value instanceof RangeType)) {
            $valueType = get_class($value);

            throw new TypeError("Expected {$rangeTypeClass}, got {$valueType}!");
        }

        $min = $value->getMin();
        $max = $value->getMax();
        $dbValue = '';

        if ($value->includeMin()) {
            $dbValue .= '[';
        } else {
            $dbValue .= '(';
        }

        if (isset($min)) {
            $dbValue .= "{$min}";
        }

        $dbValue .= ',';

        if (isset($max)) {
            $dbValue .= "{$max}";
        }

        if ($value->includeMax()) {
            $dbValue .= ']';
        } else {
            $dbValue .= ')';
        }

        return $dbValue;
    }

    /**
     * @return string
     */
    public function getName()
    {
        return self::NUMRANGE_TYPE;
    }
}
