<?php
/**
 * Created by <PERSON>pStor<PERSON>.
 * User: l.nonchev
 * Date: 7/29/2019
 * Time: 3:49 PM.
 */

namespace App\Serializer;

use App\Model\Farm\Farm;
use App\Model\GeoSCAN\Ability;
use App\Model\GeoSCAN\Organization;
use App\Model\GeoSCAN\Role;
use App\Model\GeoSCAN\User;
use Symfony\Component\Serializer\Exception\BadMethodCallException;
use Symfony\Component\Serializer\Exception\CircularReferenceException;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Serializer\Exception\ExtraAttributesException;
use Symfony\Component\Serializer\Exception\InvalidArgumentException;
use Symfony\Component\Serializer\Exception\LogicException;
use Symfony\Component\Serializer\Exception\RuntimeException;
use Symfony\Component\Serializer\Exception\UnexpectedValueException;
use S<PERSON><PERSON>ny\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class UserNormalizer implements DenormalizerInterface, NormalizerInterface
{
    private $normalizer;

    /**
     * UserNormalizer constructor.
     */
    public function __construct(ObjectNormalizer $normalizer)
    {
        $this->normalizer = $normalizer;
    }

    /**
     * Denormalizes data back into an object of the given class.
     *
     * @param mixed $data Data to restore
     * @param string $class The expected class to instantiate
     * @param string $format Format the given data was extracted from
     * @param array $context Options available to the denormalizer
     *
     * @throws BadMethodCallException Occurs when the normalizer is not called in an expected context
     * @throws InvalidArgumentException Occurs when the arguments are not coherent or not supported
     * @throws UnexpectedValueException Occurs when the item cannot be hydrated with the given data
     * @throws ExtraAttributesException Occurs when the item doesn't have attribute to receive given data
     * @throws LogicException Occurs when the normalizer is not supposed to denormalize
     * @throws RuntimeException Occurs if the class cannot be instantiated
     * @throws ExceptionInterface Occurs for all the other cases of errors
     *
     * @return object
     */
    public function denormalize($data, $class, $format = null, array $context = [])
    {
        $user = $this->normalizer->denormalize($data, $class, $format, $context);

        if (!empty($data->role)) {
            $role = $this->normalizer->denormalize($data->role, Role::class);
            $user->setRole($role);
        }

        if (!empty($data->farms)) {
            $farms = [];
            foreach ($data->farms as $farm) {
                $farms[] = $this->normalizer->denormalize($farm, Farm::class);
            }
            $user->setFarms($farms);
        }

        if (!empty($data->organizations)) {
            $organizations = [];
            foreach ($data->organizations as $organization) {
                $organizations[] = $this->normalizer->denormalize($organization, Organization::class);
            }
            $user->setOrganizations($organizations);
        }

        if (!empty($data->abilities)) {
            $abilities = [];

            foreach ($data->abilities as $ability) {
                $abilities[] = $this->normalizer->denormalize($ability, Ability::class);
            }
            $user->setAbilities($abilities);
        }

        return $user;
    }

    /**
     * Checks whether the given class is supported for denormalization by this normalizer.
     *
     * @param mixed $data Data to denormalize from
     * @param string $type The class to which the data should be denormalized
     * @param string $format The format being deserialized from
     *
     * @return bool
     */
    public function supportsDenormalization($data, $type, $format = null)
    {
        return $data instanceof User;
    }

    /**
     * Normalizes an object into a set of arrays/scalars.
     *
     * @param mixed $object Object to normalize
     * @param string $format Format the normalization result will be encoded as
     * @param array $context Context options for the normalizer
     *
     * @throws InvalidArgumentException Occurs when the object given is not an attempted type for the normalizer
     * @throws CircularReferenceException Occurs when the normalizer detects a circular reference when no circular
     *                                    reference handler can fix it
     * @throws LogicException Occurs when the normalizer is not called in an expected context
     * @throws ExceptionInterface Occurs for all the other cases of errors
     *
     * @return array|bool|float|int|string
     */
    public function normalize($object, $format = null, array $context = [])
    {
        return $this->normalizer->normalize($object, $format, $context);
    }

    /**
     * Checks whether the given class is supported for normalization by this normalizer.
     *
     * @param mixed $data Data to normalize
     * @param string $format The format being (de-)serialized from or into
     *
     * @return bool
     */
    public function supportsNormalization($data, $format = null)
    {
        return $data instanceof User;
    }
}
