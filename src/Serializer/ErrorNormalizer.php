<?php

namespace App\Serializer;

use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Serializer\Normalizer\ProblemNormalizer;
use Throwable;

class ErrorNormalizer extends ProblemNormalizer
{
    private $params;

    public function __construct(ParameterBagInterface $params)
    {
        $this->params = $params;
    }

    public function normalize($object, string $format = null, array $context = [])
    {
        $normalizedData = parent::normalize($object, $format, $context);
        $isDebug = $this->params->get('kernel.debug');

        // Show detailed error messages for 4xx errors (client errors)
        if (
            $object->getCode() >= 400
            && $object->getCode() < 500
            && strlen($object->getMessage()) > 0
        ) {
            $normalizedData['detail'] = $object->getMessage();
        }

        // In debug mode, show detailed information for all errors including 5xx
        if ($isDebug) {
            $normalizedData['detail'] = $object->getMessage();

            // Add additional debugging information
            if ($object instanceof Throwable) {
                $normalizedData['debug'] = [
                    'message' => $object->getMessage(),
                    'file' => $object->getFile(),
                    'line' => $object->getLine(),
                    'trace' => $object->getTraceAsString(),
                    'code' => $object->getCode(),
                    'class' => get_class($object),
                ];

                // If there's a previous exception, include it too
                if ($object->getPrevious()) {
                    $previous = $object->getPrevious();
                    $normalizedData['debug']['previous'] = [
                        'message' => $previous->getMessage(),
                        'file' => $previous->getFile(),
                        'line' => $previous->getLine(),
                        'trace' => $previous->getTraceAsString(),
                        'code' => $previous->getCode(),
                        'class' => get_class($previous),
                    ];
                }
            }
        }

        return $normalizedData;
    }
}
