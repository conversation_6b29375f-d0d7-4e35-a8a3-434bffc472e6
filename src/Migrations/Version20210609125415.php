<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210609125415 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Seed table lab_element_aggregation_area_value_config';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $serviceProviderId = $this->getServiceProviderId('nikas');
        $this->addSql("INSERT INTO lab_element_aggregation_area_value_config (service_provider_id, area_treshold) VALUES ({$serviceProviderId}, 0.7)");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $serviceProviderId = $this->getServiceProviderId('nikas');
        $this->addSql("DELETE FROM lab_element_aggregation_area_value_config WHERE service_provider_id = {$serviceProviderId}");
    }
}
