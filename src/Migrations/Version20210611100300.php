<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210611100300 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Drop table recommendation_comment';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP SEQUENCE recommendation_comment_id_seq');
        $this->addSql('DROP TABLE IF EXISTS recommendation_comment CASCADE');
    }

    public function down(Schema $schema): void {}
}
