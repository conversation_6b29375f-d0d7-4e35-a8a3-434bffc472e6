<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200124095248 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TYPE element_result_states_enum ADD VALUE \'Approved\'');
        $this->addSql('ALTER TYPE element_result_states_enum ADD VALUE \'For reanalysis\'');
        $this->addSql('ALTER TYPE element_result_states_enum ADD VALUE \'Not analysed\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TYPE element_result_states_enum DROP ATTRIBUTE IF EXISTS \'Approved\'');
        $this->addSql('ALTER TYPE element_result_states_enum DROP ATTRIBUTE IF EXISTS \'For reanalysis\'');
        $this->addSql('ALTER TYPE element_result_states_enum DROP ATTRIBUTE IF EXISTS \'Not analysed\'');
    }
}
