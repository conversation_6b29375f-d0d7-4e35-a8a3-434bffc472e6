<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230612153206 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Insert mobile navigations data';
    }

    public function up(Schema $schema): void
    {
        // Insert parent items
        $this->addSql("INSERT INTO main_navigation (label_en, label_bg, label_ro, label_ua, label_it, \"path\", url, no_data_url, instance, icon) VALUES
                    ('Images', 'Снимки', 'Imagini', 'Зображення', 'Immagini', 'fields', '', '', 'mobile', '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">
                        <path d=\"M5 2C3.3 2 2 3.3 2 5V9.9C3.2 9.7 5.3 9.2 6.8 7.7C8.2 6.3 9 4.4 9.2 2H5ZM5 3.5H7.5C7.3 4 7.2 4.4 7 4.8H3.5C3.6 4.1 4.2 3.5 5 3.5ZM3.5 8V6.3H6.1C6 6.4 5.9 6.5 5.8 6.6C5.1 7.3 4.3 7.7 3.5 8Z\" fill=\"#5D6166\"/>
                        <path d=\"M14.5 12C12 14.4 10.5 17.8 10 22H19C20.7 22 22 20.7 22 19V8.09998C19.7 8.59998 16.8 9.69998 14.5 12ZM20.5 11.8H17C18.2 11 19.4 10.4 20.5 9.99998V11.8ZM15.2 13.3H20.5V14.8H14C14.4 14.3 14.8 13.8 15.2 13.3ZM12.5 17.7C12.7 17.2 12.9 16.8 13.1 16.4H20.5V17.7H12.5ZM19 20.5H11.8C11.9 20 12 19.6 12.1 19.2H20.5C20.4 19.9 19.8 20.5 19 20.5Z\" fill=\"#5D6166\"/>
                        <path d=\"M18.9999 2H10.6999C10.4999 4.9 9.4999 7.1 7.7999 8.8C5.7999 10.7 3.3999 11.3 1.8999 11.4V19C1.8999 20.7 3.1999 22 4.8999 22H8.3999C8.8999 17.3 10.4999 13.6 13.3999 10.9C16.0999 8.3 19.3999 7.1 21.8999 6.6V5C21.9999 3.3 20.6999 2 18.9999 2ZM16.9999 3.5V6.6C16.4999 6.8 15.9999 7.1 15.4999 7.4V3.5H16.9999ZM10.8999 7.1V11.5C10.3999 12.2 9.8999 12.9 9.4999 13.6V9.2C10.0999 8.6 10.4999 7.9 10.8999 7.1ZM3.4999 19V12.7C3.9999 12.6 4.4999 12.4 4.9999 12.2V20.5C4.1999 20.5 3.4999 19.8 3.4999 19ZM6.4999 20.5V11.5C6.9999 11.3 7.4999 11 7.9999 10.6V17C7.5999 18.1 7.3999 19.3 7.1999 20.5H6.4999ZM12.3999 9.8V3.5H13.9999V8.4C13.4999 8.9 12.8999 9.3 12.3999 9.8ZM20.4999 5.4C19.7999 5.6 19.1999 5.8 18.4999 6V3.5H18.9999C19.7999 3.5 20.4999 4.2 20.4999 5V5.4Z\" fill=\"#5D6166\"/>
                        </svg>'
                    ),
                    
                    ('Soils', 'Почви', 'Sol', 'Грунти', 'Soils', 'soils', '', '', 'mobile', '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">
                        <path d=\"M3.5 3.5H20.5H21.3C21.7 3.5 22.1 3.2 22.1 2.8C22 2.3 21.7 2 21.3 2H2.7C2.3 2 2 2.3 2 2.7C2 3.1 2.3 3.4 2.7 3.4H3.5V3.5Z\" fill=\"#5D6166\"/>
                        <path d=\"M3.5 22H20.5H21.3C21.7 22 22.1 21.7 22.1 21.2C22.1 20.8 21.8 20.4 21.3 20.4H2.7C2.3 20.4 2 20.7 2 21.2C2 21.7 2.3 22 2.7 22H3.5Z\" fill=\"#5D6166\"/>
                        <path d=\"M3.5 16.4999H20.5H21.3C21.7 16.4999 22.1 16.1999 22.1 15.7999C22.1 15.3999 21.8 15.0999 21.3 15.0999H2.7C2.3 14.9999 2 15.2999 2 15.6999C2 16.0999 2.3 16.3999 2.7 16.3999H3.5V16.4999Z\" fill=\"#5D6166\"/>
                        <path d=\"M4 10C5.10457 10 6 9.10457 6 8C6 6.89543 5.10457 6 4 6C2.89543 6 2 6.89543 2 8C2 9.10457 2.89543 10 4 10Z\" fill=\"#5D6166\"/>
                        <path d=\"M12 13C13.1046 13 14 12.1046 14 11C14 9.89543 13.1046 9 12 9C10.8954 9 10 9.89543 10 11C10 12.1046 10.8954 13 12 13Z\" fill=\"#5D6166\"/>
                        <path d=\"M20 10C21.1046 10 22 9.10457 22 8C22 6.89543 21.1046 6 20 6C18.8954 6 18 6.89543 18 8C18 9.10457 18.8954 10 20 10Z\" fill=\"#5D6166\"/>
                        </svg>'
                    ),
                    
                    ('Machines', 'Машини', 'Utilaje', 'Машини', 'Мacchine', 'machines', '', '', 'mobile', '<svg width=\"24\" height=\"24\" viewBox=\"0 0 26 26\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">
                        <path d=\"M21.5151 15.6C20.2151 15.6 19.0451 16.25 18.3301 17.225H12.3501C12.1551 14.105 9.68515 11.7 6.50015 11.7C3.25015 11.7 0.650146 14.3 0.650146 17.55C0.650146 20.8 3.25015 23.4 6.50015 23.4C9.42515 23.4 11.8301 21.32 12.2851 18.525H17.6801C17.6151 18.85 17.5501 19.175 17.5501 19.5C17.5501 21.645 19.3051 23.4 21.4501 23.4C23.5951 23.4 25.3501 21.645 25.3501 19.5C25.3501 17.355 23.6601 15.6 21.5151 15.6ZM6.56515 22.1C4.03015 22.1 2.01515 20.085 2.01515 17.55C2.01515 15.015 4.03015 13 6.56515 13C9.10015 13 11.1151 15.015 11.1151 17.55C11.1151 20.085 9.10015 22.1 6.56515 22.1ZM21.5151 22.1C20.0851 22.1 18.9151 20.93 18.9151 19.5C18.9151 18.07 20.0851 16.9 21.5151 16.9C22.9451 16.9 24.1151 18.07 24.1151 19.5C24.1151 20.93 22.9451 22.1 21.5151 22.1Z\" fill=\"#5D6166\"/>
                        <path d=\"M2.66523 4.54998C2.66523 4.22498 2.92523 3.89998 3.31523 3.89998H5.91523V8.77497C5.91523 9.16497 6.17523 9.42497 6.56523 9.42497C6.95523 9.42497 7.21523 9.16497 7.21523 8.77497V3.89998H10.4652C10.7902 3.89998 10.9202 4.09498 11.0502 4.35498L12.5452 7.60497C12.6752 7.86497 12.8702 8.18997 13.0652 8.31997L11.0502 10.855C10.8552 11.115 10.8552 11.57 11.1802 11.765C11.3102 11.83 11.4402 11.895 11.5702 11.895C11.7652 11.895 11.9602 11.83 12.0902 11.635L14.3002 9.09997C14.5602 9.09997 22.1002 9.09997 22.1002 9.09997C22.4252 9.09997 22.7502 9.35997 22.7502 9.74997V10.075H19.4352C19.1102 10.075 18.8502 10.335 18.8502 10.725C18.8502 11.115 19.1102 11.375 19.4352 11.375H22.7502V12.025H19.4352C19.1102 12.025 18.8502 12.285 18.8502 12.675C18.8502 13.065 19.1102 13.325 19.4352 13.325H22.7502V14.17C23.2052 14.3 23.6602 14.495 24.0502 14.69V9.74997C24.1152 8.44998 23.4652 7.79997 22.1652 7.79997V7.47498V5.06998C22.1652 3.96498 21.2552 3.05498 20.1502 3.05498H19.5652C19.1752 3.05498 18.9152 3.31498 18.9152 3.70498C18.9152 4.09498 19.1752 4.35498 19.5652 4.35498H20.1502C20.5402 4.35498 20.8652 4.67998 20.8652 5.06998V7.79997H15.0802C14.4952 7.79997 13.9752 7.60497 13.7152 7.08497L12.0902 3.57498C11.7652 2.98998 11.1152 2.59998 10.4652 2.59998H3.31523C2.27523 2.59998 1.36523 3.44498 1.36523 4.54998V12.48C1.75523 12.09 2.21023 11.7 2.66523 11.375V4.54998Z\" fill=\"#5D6166\"/>
                        <path d=\"M6.56514 18.85C7.28311 18.85 7.86514 18.268 7.86514 17.55C7.86514 16.832 7.28311 16.25 6.56514 16.25C5.84717 16.25 5.26514 16.832 5.26514 17.55C5.26514 18.268 5.84717 18.85 6.56514 18.85Z\" fill=\"#5D6166\"/>
                        </svg>'
                    ),
                    
                    ('Weather stations', 'Метеостанции', 'Stații meteo', 'Метеостанції', 'Stazioni meteorologiche', 'weather_stations', '', '', 'mobile', '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">
                        <path d=\"M20 10C19.5 10 19 10.2 18.7 10.5C18.6 10.5 18.6 10.6 18.5 10.6L13.2 8.1L18.6 5.5C19.1 5.8 19.5 6 20 6C21.1 6 22 5.1 22 4C22 2.9 21.1 2 20 2C18.9 2 18 2.9 18 4C18 4.1 18 4.3 18 4.4V4.5V4.6L12.7 7.1V4.2C12.7 3.8 12.4 3.4 11.9 3.4C11.4 3.4 11.1 3.7 11.1 4.2V7.1L5.9 4.6V4.5V4.4C6 4.3 6 4.1 6 4C6 2.9 5.1 2 4 2C2.9 2 2 2.9 2 4C2 5.1 2.9 6 4 6C4.1 6 4.2 6 4.4 6C4.5 6 4.6 5.9 4.7 5.9C4.8 5.9 4.9 5.8 5 5.8C5.1 5.8 5.1 5.7 5.2 5.7L10.7 8.3L5.5 10.8C5.3 10.6 5.2 10.5 4.9 10.4C4.7 10.1 4.3 10 4 10C2.9 10 2 10.9 2 12C2 13.1 2.9 14 4 14C5.1 14 6 13.1 6 12C6 11.9 6 11.8 6 11.7C6 11.6 6 11.6 6 11.5L11.3 9V13C10 13.4 9 14.6 9 16V20.5H8.5H7.8C7.4 20.5 7 20.8 7 21.3C7 21.8 7.3 22 7.8 22H9H15H16.3C16.7 22 17.1 21.7 17.1 21.2C17.1 20.7 16.8 20.4 16.3 20.4H15.5H15V16C15 14.6 14 13.4 12.7 13.1V9.1L18 11.6C18 11.7 18 11.9 18 12C18 13.1 18.9 14 20 14C21.1 14 22 13.1 22 12C22 10.9 21.1 10 20 10ZM13.5 16V20.5H10.5V16C10.5 15.2 11.2 14.5 12 14.5C12.8 14.5 13.5 15.2 13.5 16Z\" fill=\"#5D6166\"/>
                        </svg>'
                    ),
                    
                    ('Irrigation', 'Напояване', 'Irigare', 'Зрошення', 'Irrigazione', 'irrigation', '', '', 'mobile', '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">
                        <path d=\"M12 17.5C12.8 17.5 13.5 18.2 13.5 19C13.5 19.8 12.8 20.5 12 20.5C11.2 20.5 10.5 19.8 10.5 19C10.5 18.2 11.2 17.5 12 17.5ZM12 16C10.3 16 9 17.3 9 19C9 20.7 10.3 22 12 22C13.7 22 15 20.7 15 19C15 17.3 13.7 16 12 16Z\" fill=\"black\"/>
                        <path d=\"M5 14.5C5.8 14.5 6.5 15.2 6.5 16C6.5 16.8 5.8 17.5 5 17.5C4.2 17.5 3.5 16.8 3.5 16C3.5 15.2 4.2 14.5 5 14.5ZM5 13C3.3 13 2 14.3 2 16C2 17.7 3.3 19 5 19C6.7 19 8 17.7 8 16C8 14.3 6.7 13 5 13Z\" fill=\"black\"/>
                        <path d=\"M19 14.5C19.8 14.5 20.5 15.2 20.5 16C20.5 16.8 19.8 17.5 19 17.5C18.2 17.5 17.5 16.8 17.5 16C17.5 15.2 18.2 14.5 19 14.5ZM19 13C17.3 13 16 14.3 16 16C16 17.7 17.3 19 19 19C20.7 19 22 17.7 22 16C22 14.3 20.7 13 19 13Z\" fill=\"black\"/>
                        <path d=\"M14 2H10C9.4 2 9 2.4 9 3C9 3.6 9.4 4 10 4H14C14.6 4 15 3.6 15 3C15 2.4 14.6 2 14 2Z\" fill=\"black\"/>
                        <path d=\"M12 7C12.5523 7 13 6.55228 13 6C13 5.44772 12.5523 5 12 5C11.4477 5 11 5.44772 11 6C11 6.55228 11.4477 7 12 7Z\" fill=\"black\"/>
                        <path d=\"M15 7C15.5523 7 16 6.55228 16 6C16 5.44772 15.5523 5 15 5C14.4477 5 14 5.44772 14 6C14 6.55228 14.4477 7 15 7Z\" fill=\"black\"/>
                        <path d=\"M9 7C9.55228 7 10 6.55228 10 6C10 5.44772 9.55228 5 9 5C8.44772 5 8 5.44772 8 6C8 6.55228 8.44772 7 9 7Z\" fill=\"black\"/>
                        <path d=\"M7.5 11C8.32843 11 9 10.3284 9 9.5C9 8.67157 8.32843 8 7.5 8C6.67157 8 6 8.67157 6 9.5C6 10.3284 6.67157 11 7.5 11Z\" fill=\"black\"/>
                        <path d=\"M12 14C12.8284 14 13.5 13.3284 13.5 12.5C13.5 11.6716 12.8284 11 12 11C11.1716 11 10.5 11.6716 10.5 12.5C10.5 13.3284 11.1716 14 12 14Z\" fill=\"black\"/>
                        <path d=\"M16.5 11C17.3284 11 18 10.3284 18 9.5C18 8.67157 17.3284 8 16.5 8C15.6716 8 15 8.67157 15 9.5C15 10.3284 15.6716 11 16.5 11Z\" fill=\"black\"/>
                        </svg>'
                    ),
                    
                    ('Markers', 'Маркери', 'Marcaje', 'Маркери', 'Markers', 'markers', '', '','mobile', '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">
                        <path d=\"M11.9999 5.45392C10.7366 5.45392 9.54834 5.9461 8.65303 6.83907C7.76006 7.73204 7.26787 8.92032 7.26787 10.1859C7.26787 11.4516 7.76006 12.6375 8.65303 13.5328C9.54599 14.4258 10.7343 14.918 11.9999 14.918C13.2655 14.918 14.4515 14.4258 15.3468 13.5328C16.2397 12.6399 16.7319 11.4516 16.7319 10.1859C16.7319 8.92032 16.2397 7.73439 15.3468 6.83907C14.4515 5.9461 13.2632 5.45392 11.9999 5.45392ZM11.9999 13.2445C10.3124 13.2445 8.94131 11.8734 8.94131 10.1859C8.94131 8.49845 10.3124 7.12735 11.9999 7.12735C13.6874 7.12735 15.0585 8.49845 15.0585 10.1859C15.0585 11.8734 13.6874 13.2445 11.9999 13.2445ZM20.4046 6.43595C19.9288 5.48204 19.2538 4.62423 18.396 3.88361C17.5452 3.15001 16.5655 2.57345 15.4804 2.16798C14.3624 1.75079 13.1929 1.53986 12.0022 1.53986H11.9976C10.8069 1.53986 9.6374 1.75079 8.51943 2.16798C7.43428 2.57345 6.45459 3.15001 5.60381 3.88361C4.746 4.62423 4.06865 5.48439 3.59521 6.43595C3.09834 7.43439 2.84521 8.49142 2.84521 9.57423C2.84521 11.0813 3.21553 12.6305 3.94678 14.1797C4.55146 15.4617 5.40693 16.7555 6.49209 18.0258C8.43271 20.2992 10.6171 21.9258 11.4655 22.5188C11.6226 22.6289 11.8077 22.6875 11.9999 22.6875C12.1897 22.6875 12.3749 22.6289 12.5343 22.5188C13.3827 21.9258 15.5694 20.2992 17.5077 18.0258C18.5905 16.7555 19.4483 15.4617 20.053 14.1797C20.7843 12.6305 21.1546 11.0789 21.1546 9.57423C21.1546 8.48907 20.9015 7.43439 20.4046 6.43595ZM19.4788 9.57657C19.4788 10.8352 19.1624 12.1453 18.5366 13.4695C17.9999 14.6133 17.2241 15.7805 16.2351 16.9406C14.6905 18.7523 12.9608 20.1375 11.9999 20.843C11.039 20.1352 9.30928 18.7523 7.76474 16.9406C6.77568 15.7805 5.9999 14.6109 5.46084 13.4672C4.83506 12.143 4.51865 10.8328 4.51865 9.57423C4.51865 8.74923 4.71084 7.94298 5.09287 7.18126C5.47021 6.42657 6.00928 5.7422 6.69834 5.14689C7.39678 4.5422 8.20771 4.06642 9.10303 3.73126C10.0358 3.38439 11.0108 3.20861 11.9976 3.20861H12.0022C12.9913 3.20861 13.9663 3.38439 14.8944 3.73126C15.7921 4.06642 16.6007 4.5422 17.3015 5.14689C17.9929 5.7422 18.5319 6.42657 18.9046 7.18126C19.2866 7.95001 19.4788 8.75392 19.4788 9.57657Z\" fill=\"black\"/>
                        </svg>
                    '),
                    
                    ('Administrative map', 'Административна карта', 'Hartă administrativă', 'Адміністративна карта', 'Mappa amministrativa', 'administrative_map', '', '', 'mobile', '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">
                        <path d=\"M5 2C3.3 2 2 3.3 2 5V9.9C3.2 9.7 5.3 9.2 6.8 7.7C8.2 6.3 9 4.4 9.2 2H5ZM5 3.5H7.5C7.3 4 7.2 4.4 7 4.8H3.5C3.6 4.1 4.2 3.5 5 3.5ZM3.5 8V6.3H6.1C6 6.4 5.9 6.5 5.8 6.6C5.1 7.3 4.3 7.7 3.5 8Z\" fill=\"#5D6166\"/>
                        <path d=\"M14.5 12C12 14.4 10.5 17.8 10 22H19C20.7 22 22 20.7 22 19V8.09998C19.7 8.59998 16.8 9.69998 14.5 12ZM20.5 11.8H17C18.2 11 19.4 10.4 20.5 9.99998V11.8ZM15.2 13.3H20.5V14.8H14C14.4 14.3 14.8 13.8 15.2 13.3ZM12.5 17.7C12.7 17.2 12.9 16.8 13.1 16.4H20.5V17.7H12.5ZM19 20.5H11.8C11.9 20 12 19.6 12.1 19.2H20.5C20.4 19.9 19.8 20.5 19 20.5Z\" fill=\"#5D6166\"/>
                        <path d=\"M18.9999 2H10.6999C10.4999 4.9 9.4999 7.1 7.7999 8.8C5.7999 10.7 3.3999 11.3 1.8999 11.4V19C1.8999 20.7 3.1999 22 4.8999 22H8.3999C8.8999 17.3 10.4999 13.6 13.3999 10.9C16.0999 8.3 19.3999 7.1 21.8999 6.6V5C21.9999 3.3 20.6999 2 18.9999 2ZM16.9999 3.5V6.6C16.4999 6.8 15.9999 7.1 15.4999 7.4V3.5H16.9999ZM10.8999 7.1V11.5C10.3999 12.2 9.8999 12.9 9.4999 13.6V9.2C10.0999 8.6 10.4999 7.9 10.8999 7.1ZM3.4999 19V12.7C3.9999 12.6 4.4999 12.4 4.9999 12.2V20.5C4.1999 20.5 3.4999 19.8 3.4999 19ZM6.4999 20.5V11.5C6.9999 11.3 7.4999 11 7.9999 10.6V17C7.5999 18.1 7.3999 19.3 7.1999 20.5H6.4999ZM12.3999 9.8V3.5H13.9999V8.4C13.4999 8.9 12.8999 9.3 12.3999 9.8ZM20.4999 5.4C19.7999 5.6 19.1999 5.8 18.4999 6V3.5H18.9999C19.7999 3.5 20.4999 4.2 20.4999 5V5.4Z\" fill=\"#5D6166\"/>
                        </svg>'
                    )
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql("DELETE FROM main_navigation
                            where \"path\" in('fields', 'soils', 'machines', 'weather_stations', 'administrative_map', 'irrigation', 'markers')
                            and instance = 'mobile'");
    }
}
