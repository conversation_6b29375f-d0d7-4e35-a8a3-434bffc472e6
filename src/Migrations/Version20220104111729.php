<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220104111729 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Set on delete cascade for lab_elements_results (package_grid_points fk).';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE lab_elements_results DROP CONSTRAINT FK_2E6761B62BFCFE5E');
        $this->addSql('ALTER TABLE lab_elements_results ADD CONSTRAINT FK_2E6761B62BFCFE5E FOREIGN KEY (package_grid_points_id) REFERENCES package_grid_points (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE lab_elements_results DROP CONSTRAINT fk_2e6761b62bfcfe5e');
        $this->addSql('ALTER TABLE lab_elements_results ADD CONSTRAINT fk_2e6761b62bfcfe5e FOREIGN KEY (package_grid_points_id) REFERENCES package_grid_points (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }
}
