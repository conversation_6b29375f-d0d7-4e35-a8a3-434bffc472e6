<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191203114618 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("CREATE TYPE element_group_states_enum AS ENUM ('Pending', 'In progress', 'For reanalysis', 'For approve', 'For recommendation', 'Delivered')");

        $this->addSql('CREATE SEQUENCE lab_element_group_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE lab_element_group (id INT NOT NULL, package_id INT NOT NULL, package_type VARCHAR(63) NOT NULL, plot_id INT NOT NULL, lab_analysis_group_id INT DEFAULT NULL, state element_group_states_enum, state_updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_858A2F33FC8E2340 ON lab_element_group (lab_analysis_group_id)');
        $this->addSql('ALTER TABLE lab_element_group ADD CONSTRAINT FK_858A2F33FC8E2340 FOREIGN KEY (lab_analysis_group_id) REFERENCES lab_analysis_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE lab_analysis_package_group ALTER id DROP DEFAULT');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP SEQUENCE lab_element_group_id_seq CASCADE');
        $this->addSql('DROP TABLE lab_element_group');
        $this->addSql('CREATE SEQUENCE lab_analysis_package_group_id_seq');
        $this->addSql('SELECT setval(\'lab_analysis_package_group_id_seq\', (SELECT MAX(id) FROM lab_analysis_package_group))');
        $this->addSql('ALTER TABLE lab_analysis_package_group ALTER id SET DEFAULT nextval(\'lab_analysis_package_group_id_seq\')');
    }
}
