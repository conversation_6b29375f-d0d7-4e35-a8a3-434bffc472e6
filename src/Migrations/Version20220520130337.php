<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220520130337 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Updates postgresql function calculate_recommendation_due_date() ';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("
        CREATE OR REPLACE FUNCTION public.calculate_recommendation_due_date(max_date date, due_date_period integer)
            RETURNS date
            LANGUAGE plpgsql
            AS $$
                    BEGIN
                        RETURN (
                            WITH 
                            holydays_in_period AS (
                                SELECT
                                    due_date_days.\"date\"
                                FROM 
                                    generate_series(max_date, max_date + (due_date_period || ' days')::interval, '1 day'::interval) AS due_date_days(\"date\"),
                                    get_holydays(EXTRACT (YEAR FROM max_date)::int) AS holydays
                                WHERE due_date_days.\"date\"::date = ANY(holydays.dts) -- check which days of the period are holydays
                            )
                            SELECT
                                \"max_date\" + ((due_date_period + count(holydays_in_period.\"date\"))::text || ' days')::interval as date
                            FROM holydays_in_period
                        );
                    END
                $$;");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP FUNCTION IF EXISTS calculate_recommendation_due_date(max_date DATE, due_date_period INT)');
    }
}
