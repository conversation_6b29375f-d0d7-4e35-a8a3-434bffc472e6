<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210420105725 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add elements in elements_enum';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS \'CEC\'');
        $this->addSql('ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS \'BS\'');
        $this->addSql('ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS \'K+\'');
        $this->addSql('ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS \'Ca2+\'');
        $this->addSql('ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS \'Mg2+\'');
        $this->addSql('ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS \'Na+\'');
        $this->addSql('ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS \'H+\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
    }
}
