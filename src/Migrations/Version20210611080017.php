<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210611080017 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Seed table recommendations.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("INSERT INTO recommendations (package_id, package_type, model_id, plot_uuid, plot_name, crop_id, status, target_yield, created_at, modified_at) VALUES
            (1, 'subscription', 1, '2aee1b0b-ce1f-48b6-8246-89ba5e702998', 'test - Турски гробища ISO', NULL, 'Pending', NULL, now(), now())
        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql("DELETE FROM recommendations WHERE package_id = 1 and plot_uuid='2aee1b0b-ce1f-48b6-8246-89ba5e702998'");
    }
}
