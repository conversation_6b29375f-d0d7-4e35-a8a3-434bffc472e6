<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210805130943 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
        $this->addSql('UPDATE lab_elements_calculations lec 
            SET template_column=t.template_column 
            FROM 
                (VALUES 
                    (\'K2O\', \'\bK(?!\+)\b\'),
                    (\'Na2O\', \'\bNa(?!\+)\b\')
                ) AS t(element, template_column) 
            WHERE lec.element=t.element::elements_enum');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('UPDATE lab_elements_calculations lec 
            SET template_column=t.template_column 
            FROM 
                (VALUES 
                    (\'K2O\', \'\bK\b\'),
                    (\'Na2O\', \'\bNa\b\')
                ) AS t(element, template_column) 
            WHERE lec.element=t.element::elements_enum');
    }
}
