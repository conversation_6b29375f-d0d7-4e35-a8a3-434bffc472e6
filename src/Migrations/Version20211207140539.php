<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20211207140539 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Set on delete CASCADE for FKs related with the \'contract\' table.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE payment DROP CONSTRAINT FK_6D28840DD614C7E7');
        $this->addSql('ALTER TABLE payment ADD CONSTRAINT FK_6D28840DD614C7E7 FOREIGN KEY (price_id) REFERENCES price (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');

        $this->addSql('ALTER TABLE price DROP CONSTRAINT FK_CAC822D99A1887DC');

        $this->addSql('ALTER TABLE price DROP CONSTRAINT FK_CAC822D92576E0FD');
        $this->addSql('ALTER TABLE price ADD CONSTRAINT FK_CAC822D92576E0FD FOREIGN KEY (contract_id) REFERENCES contract (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');

        $this->addSql('ALTER TABLE subscription_package DROP CONSTRAINT FK_AD7D870E2576E0FD');
        $this->addSql('ALTER TABLE subscription_package ADD CONSTRAINT FK_AD7D870E2576E0FD FOREIGN KEY (contract_id) REFERENCES subscription_contracts (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');

        $this->addSql('ALTER TABLE service_contract_packages DROP CONSTRAINT FK_6415BE8D2576E0FD');
        $this->addSql('ALTER TABLE service_contract_packages ADD CONSTRAINT FK_6415BE8D2576E0FD FOREIGN KEY (contract_id) REFERENCES service_contracts (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');

        $this->addSql('ALTER TABLE responsible_user DROP CONSTRAINT FK_CC91C58F2576E0FD');
        $this->addSql('ALTER TABLE responsible_user ADD CONSTRAINT FK_CC91C58F2576E0FD FOREIGN KEY (contract_id) REFERENCES contract (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE payment DROP CONSTRAINT FK_6D28840DD614C7E7');
        $this->addSql('ALTER TABLE payment ADD CONSTRAINT FK_6D28840DD614C7E7 FOREIGN KEY (price_id) REFERENCES price (id) NOT DEFERRABLE INITIALLY IMMEDIATE');

        $this->addSql('ALTER TABLE price ADD CONSTRAINT FK_CAC822D99A1887DC FOREIGN KEY (contract_id) REFERENCES subscription_contracts (id) NOT DEFERRABLE INITIALLY IMMEDIATE');

        $this->addSql('ALTER TABLE price DROP CONSTRAINT FK_CAC822D92576E0FD');
        $this->addSql('ALTER TABLE price ADD CONSTRAINT FK_CAC822D92576E0FD FOREIGN KEY (contract_id) REFERENCES contract (id) NOT DEFERRABLE INITIALLY IMMEDIATE');

        $this->addSql('ALTER TABLE subscription_package DROP CONSTRAINT FK_AD7D870E2576E0FD');
        $this->addSql('ALTER TABLE subscription_package ADD CONSTRAINT FK_AD7D870E2576E0FD FOREIGN KEY (contract_id) REFERENCES subscription_contracts (id) NOT DEFERRABLE INITIALLY IMMEDIATE');

        $this->addSql('ALTER TABLE service_contract_packages DROP CONSTRAINT FK_6415BE8D2576E0FD');
        $this->addSql('ALTER TABLE service_contract_packages ADD CONSTRAINT FK_6415BE8D2576E0FD FOREIGN KEY (contract_id) REFERENCES service_contracts (id) NOT DEFERRABLE INITIALLY IMMEDIATE');

        $this->addSql('ALTER TABLE responsible_user DROP CONSTRAINT FK_CC91C58F2576E0FD');
        $this->addSql('ALTER TABLE responsible_user ADD CONSTRAINT FK_CC91C58F2576E0FD FOREIGN KEY (contract_id) REFERENCES contract (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }
}
