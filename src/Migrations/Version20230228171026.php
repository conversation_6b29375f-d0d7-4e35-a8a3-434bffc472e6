<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230228171026 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add columns label_bg, label_ro, label_ua and label_it to table main_navigation';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE main_navigation ADD label_bg VARCHAR(255)');
        $this->addSql('ALTER TABLE main_navigation ADD label_ro VARCHAR(255)');
        $this->addSql('ALTER TABLE main_navigation ADD label_ua VARCHAR(255)');
        $this->addSql('ALTER TABLE main_navigation ADD label_it VARCHAR(255)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE main_navigation DROP label_bg');
        $this->addSql('ALTER TABLE main_navigation DROP label_ro');
        $this->addSql('ALTER TABLE main_navigation DROP label_ua');
        $this->addSql('ALTER TABLE main_navigation DROP label_it');
    }
}
