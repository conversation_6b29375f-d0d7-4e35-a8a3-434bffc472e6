<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210928115004 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add configs in table lab_element_interpretations_config for the elements \'NH4-N\', \'NO3-N\'.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("WITH
            elements AS (
                SELECT id
                FROM
                    lab_analysis_group_element
                WHERE 
                    \"element\" IN ('NH4-N'::elements_enum, 'NO3-N'::elements_enum)
            )
            INSERT INTO lab_element_interpretations_config (element_id, service_provider_id, class_id, \"range\")
            SELECT
                elements.id AS element_id,
                sp.id AS service_provider_id,
                licc.id AS class_id,
                '(,)'::numrange AS \"range\"
            FROM
                elements
            JOIN lab_interpretation_classes_config  AS licc
                ON licc.description = 'not available'
            CROSS JOIN service_provider AS sp
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql("WITH
            elements AS (
                    SELECT id
                    FROM
                        lab_analysis_group_element
                    WHERE 
                        \"element\" IN ('NH4-N'::elements_enum, 'NO3-N'::elements_enum)
            )
            DELETE FROM lab_element_interpretations_config leic
            USING elements
            WHERE
                elements.id = leic.element_id
        ");
    }
}
