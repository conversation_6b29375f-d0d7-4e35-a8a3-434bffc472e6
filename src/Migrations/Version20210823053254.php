<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210823053254 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Set lab_element_interpretations_config data for vantage_balkans, agricost, livona, spectr_agro and nik_italia service providers';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $serviceProvidersId = $this->getServiceProvidersId(['vantage_balkans', 'agricost', 'livona', 'spectr_agro', 'nik_italia']);

        $elementIds = $this->getElements();

        foreach ($serviceProvidersId as $serviceProviderId) {
            $this->addSql("
            INSERT INTO lab_element_interpretations_config (element_id, service_provider_id, class_id, range, color)
            VALUES
                ({$elementIds['pH']}, {$serviceProviderId}, 13, '(,3.5]'::numrange, '#F32D2D'),
                ({$elementIds['pH']}, {$serviceProviderId}, 14, '(3.5,4.5]'::numrange, '#F44646'),
                ({$elementIds['pH']}, {$serviceProviderId}, 15, '(4.5,5]'::numrange, '#F55858'),
                ({$elementIds['pH']}, {$serviceProviderId}, 16, '(5,5.5]'::numrange, '#E68282'),
                ({$elementIds['pH']}, {$serviceProviderId}, 17, '(5.5,6]'::numrange, '#E6AAAA'),
                ({$elementIds['pH']}, {$serviceProviderId}, 18, '(6,6.5]'::numrange, '#E1C8C8'),
                ({$elementIds['pH']}, {$serviceProviderId}, 19, '(6.5,7.3]'::numrange, 'C8C8E1'),
                ({$elementIds['pH']}, {$serviceProviderId}, 20, '(7.3,7.8]'::numrange, '#AAAAEB'),
                ({$elementIds['pH']}, {$serviceProviderId}, 21, '(7.8,8.4]'::numrange, '#8282F0'),
                ({$elementIds['pH']}, {$serviceProviderId}, 22, '(8.4,9]'::numrange, '#5B5BF0'),
                ({$elementIds['pH']}, {$serviceProviderId}, 23, '(9,)'::numrange, '#3C3CF0'),

                ({$elementIds['TMN']}, {$serviceProviderId}, 1, '(,20]'::numrange, '#EED3F2'),
                ({$elementIds['TMN']}, {$serviceProviderId}, 2, '(20,40]'::numrange, '#DDA7D5'),
                ({$elementIds['TMN']}, {$serviceProviderId}, 3, '(40,60]'::numrange, '#D785D1'),
                ({$elementIds['TMN']}, {$serviceProviderId}, 4, '(60,80]'::numrange, '#B553B6'),
                ({$elementIds['TMN']}, {$serviceProviderId}, 5, '(80,)'::numrange, '#912499'),

                ({$elementIds['P2O5']}, {$serviceProviderId}, 2, '(,40]'::numrange, '#D6D6F2'),
                ({$elementIds['P2O5']}, {$serviceProviderId}, 6, '(40,80]'::numrange, '#A6A3E2'),
                ({$elementIds['P2O5']}, {$serviceProviderId}, 7, '(80,150]'::numrange, '#6767BF'),
                ({$elementIds['P2O5']}, {$serviceProviderId}, 4, '(150,220]'::numrange, '#34348C'),
                ({$elementIds['P2O5']}, {$serviceProviderId}, 5, '(220,)'::numrange, '#00026F'),

                ({$elementIds['K2O']}, {$serviceProviderId}, 2, '(,85]'::numrange, '#BE9F9D'),
                ({$elementIds['K2O']}, {$serviceProviderId}, 6, '(85,170]'::numrange, '#AD918E'),
                ({$elementIds['K2O']}, {$serviceProviderId}, 7, '(170,220]'::numrange, '#A08081'),
                ({$elementIds['K2O']}, {$serviceProviderId}, 4, '(220,500]'::numrange, '#886261'),
                ({$elementIds['K2O']}, {$serviceProviderId}, 5, '(500,)'::numrange, '#683E40'),

                ({$elementIds['CaO']}, {$serviceProviderId}, 2, '(,1500]'::numrange, '#ECE1BA'),
                ({$elementIds['CaO']}, {$serviceProviderId}, 6, '(1500,2800]'::numrange, '#E0CF90'),
                ({$elementIds['CaO']}, {$serviceProviderId}, 4, '(2800,)'::numrange, '#C6AF3E'),

                ({$elementIds['MgO']}, {$serviceProviderId}, 2, '(,100]'::numrange, '#DFE3ED'),
                ({$elementIds['MgO']}, {$serviceProviderId}, 6, '(100,200]'::numrange, '#C4CBDE'),
                ({$elementIds['MgO']}, {$serviceProviderId}, 4, '(200,)'::numrange, '#9FAAC9'),

                ({$elementIds['S']}, {$serviceProviderId}, 2, '(,5]'::numrange, '#FFFFB3'),
                ({$elementIds['S']}, {$serviceProviderId}, 6, '(5,10]'::numrange, '#FFFF81'),
                ({$elementIds['S']}, {$serviceProviderId}, 4, '(10,20]'::numrange, '#FFFF37'),
                ({$elementIds['S']}, {$serviceProviderId}, 5, '(20,)'::numrange, '#F4EE00'),

                ({$elementIds['B']}, {$serviceProviderId}, 2, '(,0.5]'::numrange, '#CCFF66'),
                ({$elementIds['B']}, {$serviceProviderId}, 6, '(0.5,2]'::numrange, '#A5DD27'),
                ({$elementIds['B']}, {$serviceProviderId}, 4, '(2,)'::numrange, '#6AA818'),

                ({$elementIds['Cu']}, {$serviceProviderId}, 2, '(,0.6]'::numrange, '#CD8637'),
                ({$elementIds['Cu']}, {$serviceProviderId}, 6, '(0.6,2]'::numrange, '#C06F16'),
                ({$elementIds['Cu']}, {$serviceProviderId}, 4, '(2,)'::numrange, '#7E4B00'),

                ({$elementIds['Fe']}, {$serviceProviderId}, 2, '(,2.5]'::numrange, '#F8A990'),
                ({$elementIds['Fe']}, {$serviceProviderId}, 6, '(2.5,5]'::numrange, '#F47C56'),
                ({$elementIds['Fe']}, {$serviceProviderId}, 4, '(5,)'::numrange, '#F04E1C'),

                ({$elementIds['Mn']}, {$serviceProviderId}, 2, '(,5]'::numrange, '#9C968C'),
                ({$elementIds['Mn']}, {$serviceProviderId}, 6, '(5,10]'::numrange, '#69635B'),
                ({$elementIds['Mn']}, {$serviceProviderId}, 4, '(10,)'::numrange, '#3F3C37'),

                ({$elementIds['Zn']}, {$serviceProviderId}, 2, '(,1]'::numrange, '#E7E8E9'),
                ({$elementIds['Zn']}, {$serviceProviderId}, 6, '(1,1.5]'::numrange, '#CFD1D3'),
                ({$elementIds['Zn']}, {$serviceProviderId}, 4, '(1.5,)'::numrange, '#A5A9AD'),

                ({$elementIds['Mo']}, {$serviceProviderId}, 2, '(,0.1]'::numrange, '#E8AABC'),
                ({$elementIds['Mo']}, {$serviceProviderId}, 6, '(0.1,0.3]'::numrange, '#DB7B96'),
                ({$elementIds['Mo']}, {$serviceProviderId}, 4, '(0.3,1]'::numrange, '#D25679'),
                ({$elementIds['Mo']}, {$serviceProviderId}, 5, '(1,)'::numrange, '#A62C4F'),

                ({$elementIds['Humus']}, {$serviceProviderId}, 1, '(,1]'::numrange, '#E0CEC2'),
                ({$elementIds['Humus']}, {$serviceProviderId}, 2, '(1,2]'::numrange, '#CBAD99'),
                ({$elementIds['Humus']}, {$serviceProviderId}, 3, '(2,3]'::numrange, '#B79075'),
                ({$elementIds['Humus']}, {$serviceProviderId}, 7, '(3,4]'::numrange, '#815C43'),
                ({$elementIds['Humus']}, {$serviceProviderId}, 4, '(4,5]'::numrange, '#5B412F'),
                ({$elementIds['Humus']}, {$serviceProviderId}, 5, '(5,)'::numrange, '#3D2B1F'),
                
                ({$elementIds['OM']}, {$serviceProviderId}, 1, '(,1]'::numrange, '#E0CEC2'),
                ({$elementIds['OM']}, {$serviceProviderId}, 2, '(1,2]'::numrange, '#CBAD99'),
                ({$elementIds['OM']}, {$serviceProviderId}, 3, '(2,3]'::numrange, '#B79075'),
                ({$elementIds['OM']}, {$serviceProviderId}, 7, '(3,4]'::numrange, '#815C43'),
                ({$elementIds['OM']}, {$serviceProviderId}, 4, '(4,5]'::numrange, '#5B412F'),
                ({$elementIds['OM']}, {$serviceProviderId}, 5, '(5,)'::numrange, '#3D2B1F')
        ");
        }
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $serviceProvidersId = $this->getServiceProvidersId(['vantage_balkans', 'agricost', 'livona', 'spectr_agro', 'nik_italia']);

        $sql = 'DELETE FROM lab_element_interpretations_config';
        $bindingData = str_repeat('?,', count($serviceProvidersId) - 1) . '?';
        $sql .= ' WHERE service_provider_id in (' . $bindingData . ')';

        $this->addSql($sql, $serviceProvidersId);
    }
}
