<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210823133108 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update pgsql function add_n_fall.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("
            CREATE OR REPLACE FUNCTION add_n_fall (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_n NUMERIC,
                vol_density NUMERIC,
                soil_abs_n NUMERIC,
                organic_n NUMERIC,
                add_organic_n NUMERIC,
                fall_n_part NUMERIC,
                fertiliser_abs_n NUMERIC,
                add_n_reduction_sampling NUMERIC,
                humus NUMERIC,
                sampling_date DATE
            )
                RETURNS NUMERIC
                AS $$
                    DECLARE 
                        result NUMERIC;
                    BEGIN
                        WITH calculations AS (
                            SELECT
                                (
                                    ((yield * uptake_n / 100) 
                                    - (
                                         CASE WHEN sampling_date <=  now() - '2 months'::INTERVAL THEN
                                            (avg_element_result * add_n_reduction_sampling * vol_density * soil_abs_n)
                                        ELSE
                                            (avg_element_result * vol_density * soil_abs_n)
                                        END
                                    ) 
                                    - (humus * organic_n * add_organic_n)) / fertiliser_abs_n
                                ) * fall_n_part AS value,
                                (avg_element_result * vol_density * soil_abs_n) AS value_limit
                        )
                        SELECT
                            (CASE WHEN calculations.value <= calculations.value_limit THEN 0 ELSE calculations.value - calculations.value_limit END)::NUMERIC
                            INTO result
                        FROM
                            calculations;

                        RETURN result;
                    END
                $$ LANGUAGE plpgsql
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql("
            CREATE OR REPLACE FUNCTION add_n_fall (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_n NUMERIC,
                vol_density NUMERIC,
                soil_abs_n NUMERIC,
                organic_n NUMERIC,
                add_organic_n NUMERIC,
                fall_n_part NUMERIC,
                fertiliser_abs_n NUMERIC,
                add_n_reduction_sampling NUMERIC,
                humus NUMERIC,
                sampling_date DATE
            )
                RETURNS NUMERIC
                AS $$
                    DECLARE 
                        result NUMERIC;
                    BEGIN
                        WITH calculations AS (
                            SELECT
                                (
                                    ((yield * uptake_n / 100) 
                                    - (
                                         CASE WHEN sampling_date <=  now() - '2 months'::INTERVAL THEN
                                            (avg_element_result * add_n_reduction_sampling * vol_density * soil_abs_n)
                                        ELSE
                                            (avg_element_result * vol_density * soil_abs_n)
                                        END
                                    ) 
                                    - (humus * organic_n * add_organic_n)) / fertiliser_abs_n
                                ) * fall_n_part AS value,
                                (avg_element_result * vol_density * soil_abs_n) AS value_limit
                        )
                        SELECT
                            (CASE WHEN calculations.value <= calculations.value_limit THEN 0 ELSE calculations.value END)::NUMERIC
                            INTO result
                        FROM
                            calculations;

                        RETURN result;
                    END
                $$ LANGUAGE plpgsql
        ");
    }
}
