<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210611082331 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Seed table recommendation_elements_results.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("INSERT INTO recommendation_elements_results (recommendation_id, result_element, value) VALUES
            (1, 'Add_N_total', 3),
            (1, 'Add_P_total', 3),
            (1, 'Add_K_total', NULL),
            (1, 'Add_S_total', 3),
            (1, 'Add_N_fall', 1),
            (1, 'pH_predominant', 7)
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM recommendation_elements_results WHERE recommendation_id = 1');
    }
}
