<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200408083940 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE TABLE weather_station_helper (id SERIAL NOT NULL, station_id INT NOT NULL, station_name VARCHAR(255) NOT NULL, organization_id INT NOT NULL, organization_name VARCHAR(255) NOT NULL, organization_ident_number VARCHAR(255) DEFAULT NULL, station_install_date DATE DEFAULT NULL, station_start_date DATE DEFAULT NULL, station_end_date DATE DEFAULT NULL, station_period VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP TABLE weather_station_helper');
    }
}
