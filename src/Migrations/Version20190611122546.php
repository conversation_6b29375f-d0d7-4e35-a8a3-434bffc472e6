<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190611122546 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE TABLE service_contracts_packages (contract_id INT NOT NULL, package_id INT NOT NULL, PRIMARY KEY(contract_id, package_id))');
        $this->addSql('CREATE INDEX IDX_3B500F312576E0FD ON service_contracts_packages (contract_id)');
        $this->addSql('CREATE INDEX IDX_3B500F31F44CABFF ON service_contracts_packages (package_id)');
        $this->addSql('ALTER TABLE service_contracts_packages ADD CONSTRAINT FK_3B500F312576E0FD FOREIGN KEY (contract_id) REFERENCES service_contracts (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE service_contracts_packages ADD CONSTRAINT FK_3B500F31F44CABFF FOREIGN KEY (package_id) REFERENCES package (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('DROP TABLE service_contracts_services');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SCHEMA public');
        $this->addSql('CREATE TABLE service_contracts_services (contract_id INT NOT NULL, service_id INT NOT NULL, PRIMARY KEY(contract_id, service_id))');
        $this->addSql('CREATE INDEX idx_d3d72eff2576e0fd ON service_contracts_services (contract_id)');
        $this->addSql('CREATE INDEX idx_d3d72effed5ca9e6 ON service_contracts_services (service_id)');
        $this->addSql('ALTER TABLE service_contracts_services ADD CONSTRAINT fk_d3d72eff2576e0fd FOREIGN KEY (contract_id) REFERENCES service_contracts (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE service_contracts_services ADD CONSTRAINT fk_d3d72effed5ca9e6 FOREIGN KEY (service_id) REFERENCES service (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('DROP TABLE service_contracts_packages');
    }
}
