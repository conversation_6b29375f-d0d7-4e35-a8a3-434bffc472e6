<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210610074717 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create enum type recommendation_crop_model_parameter_enum.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("CREATE TYPE recommendation_crop_model_parameter_enum as ENUM (
            'pH opt low',
            'pH opt high',
            'Uptake N',
            'Uptake P',
            'Uptake K',
            'Uptake S',
            'Uptake CaO',
            'Uptake MgO',
            'Susces Mn',
            'Susces Cu',
            'Susces B',
            'Susces Zn',
            'Susces Mo',
            'Susces Fe',
            'Soil abs N',
            'Soil abs P',
            'Soil abs K',
            'Soil abs S',
            'Vol density',
            'Organic N',
            'Add organic N',
            'N rate reduction',
            'Fertiliser abs N',
            'Fertiliser abs P',
            'Fertiliser abs K',
            'Fertiliser abs S',
            'Fall N part',
            'Sampling period',
            'Add N reduction sampling'
        )");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TYPE IF EXISTS recommendation_crop_model_parameter_enum');
    }
}
