<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191120133823 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE service_contract_packages ADD amount NUMERIC(10, 2) DEFAULT NULL');
        $this->addSql('ALTER TABLE subscription_package ADD amount NUMERIC(10, 2) DEFAULT NULL');
        $this->addSql('ALTER TABLE contract ALTER contract_date DROP NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE subscription_package DROP amount');
        $this->addSql('ALTER TABLE service_contract_packages DROP amount');
        $this->addSql('ALTER TABLE contract ALTER contract_date SET NOT NULL');
    }
}
