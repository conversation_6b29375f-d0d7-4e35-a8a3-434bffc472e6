<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200408113434 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create index_soil_meteo_helper table.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE TABLE index_soil_meteo_helper (id SERIAL NOT NULL, row_number BIGINT NOT NULL, organization VARCHAR(255) NOT NULL, organization_id INT NOT NULL, customer_identification VARCHAR(50) DEFAULT NULL, period VARCHAR(255) NOT NULL, type VARCHAR(255) NOT NULL, year INT NOT NULL, treatment VARCHAR(255) NOT NULL, orders_plots JSON NOT NULL, contract_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, area DOUBLE PRECISION NOT NULL, helper_type VARCHAR(255) DEFAULT NULL, for_delete BOOLEAN DEFAULT NULL, PRIMARY KEY(id))');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP TABLE index_soil_meteo_helper');
    }
}
