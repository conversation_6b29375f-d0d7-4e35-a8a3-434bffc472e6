<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210831074708 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update table recommendations_vra_orders - drop column order_uuid and add new column order_soil_vra_id';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DELETE FROM recommendations_vra_orders');
        $this->addSql('ALTER TABLE recommendations_vra_orders ADD order_soil_vra_id INT NOT NULL');
        $this->addSql('ALTER TABLE recommendations_vra_orders DROP order_uuid');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE recommendations_vra_orders ADD order_uuid VARCHAR(255) NOT NULL');
        $this->addSql('ALTER TABLE recommendations_vra_orders DROP order_soil_vra_id');
    }
}
