<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221003122551 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Seed column parent_id in table \'package\'';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("UPDATE package
            SET parent_id = parent_p.id
            FROM (VALUES
                ('AB ISO control', 1, 'AB ISO full'),
                ('AB VRA control', 1, 'AB VRA full'),
                ('AB Leaf samples' , 1, null),
                ('AB ISO control' , 2, 'AB ISO full'),
                ('AB VRA control' , 2, 'AB VRA full'),
                ('AB Leaf samples' , 2, null),
                ('AB ISO control' , 3, 'AB ISO full'),
                ('AB VRA control' , 3, 'AB VRA full'),
                ('AB Leaf samples' , 3, null),
                ('AB ISO control' , 5, 'AB ISO full'),
                ('AB VRA control' , 5, 'AB VRA full'),
                ('AB ISO control' , 6, 'AB ISO full'),
                ('AB VRA control' , 6, 'AB VRA full'),
                ('AB Leaf samples' , 7, null),
                ('AB ISO control' , 7, 'AB ISO full'),
                ('AB VRA control' , 7, 'AB VRA full'),
                ('Control 0-30', 9, 'ISO 0-30'),
                ('Control 0-30,60',	9, 'ISO 0-30,60'),
                ('Control 0-30,60,90', 9 , 'ISO 0-30,60,90')
            ) AS packages_by_service_provider(package_slug, service_provider_id, parent_package_slug)
            JOIN package p
                ON p.slug = packages_by_service_provider.package_slug
                AND p.service_provider_id = packages_by_service_provider.service_provider_id
            LEFT JOIN package parent_p
                ON parent_p.slug = packages_by_service_provider.parent_package_slug
                AND parent_p.service_provider_id = packages_by_service_provider.service_provider_id
            WHERE
                package.id = p.id
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('UPDATE package set parent_id = NULL');
    }
}
