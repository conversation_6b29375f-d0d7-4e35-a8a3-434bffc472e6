<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191202162836 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SEQUENCE lab_analysis_group_element_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE lab_analysis_group_element (id INT NOT NULL, lab_analysis_group_id INT NOT NULL, element elements_enum, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_2E93F282FC8E2340 ON lab_analysis_group_element (lab_analysis_group_id)');
        $this->addSql('ALTER TABLE lab_analysis_group_element ADD CONSTRAINT FK_2E93F282FC8E2340 FOREIGN KEY (lab_analysis_group_id) REFERENCES lab_analysis_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');

        // Add lab_analysis_group_element data
        $sql = "ALTER TABLE lab_analysis_group_element ALTER COLUMN id SET DEFAULT nextval('lab_analysis_group_element_id_seq'::regclass)";
        $this->addSql($sql);

        $sql = "INSERT INTO lab_analysis_group_element (
            lab_analysis_group_id, element
        )
        VALUES
            (1,'pH'),
            (2,'NO3-N'),
            (2,'NH4-N'),
            (2,'TMN'),
            (3,'P2O5'),
            (4,'K2O'),
            (5,'Ca'),
            (5,'Mg'),
            (5,'Na'),
            (6,'S'),
            (7,'Cu'),
            (7,'Mn'),
            (7,'Zn'),
            (7,'B'),
            (8,'Ctotal'),
            (8,'Corg'),
            (8,'OM'),
            (9,'Ctotal'),
            (9,'Corg'),
            (9,'Cinorg'),
            (9,'TCarbonates'),
            (10,'Acarbonates'),
            (11,'EC'),
            (12,'LeafN'),
            (13,'LeafP'),
            (13,'LeafK'),
            (13,'LeafCa'),
            (13,'LeafMg'),
            (13,'LeafNa'),
            (13,'LeafS'),
            (14,'LeafFe'),
            (14,'LeafCu'),
            (14,'LeafZn'),
            (14,'LeafMn'),
            (14,'LeafB')
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP SEQUENCE lab_analysis_group_element_id_seq CASCADE');
        $this->addSql('DROP TABLE lab_analysis_group_element');
    }
}
