<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210805081537 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Seed the columns \'unit\' and \'method\' in table lab_analysis_group_element.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE lab_analysis_group_element SET unit = NULL, method = \'ISO 10390\' WHERE element = \'pH\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'ISO 14255\' WHERE element = \'NO3-N\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'ISO 14255\' WHERE element = \'NH4-N\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'ISO 14255\' WHERE element = \'TMN\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'EN 16170\' WHERE element = \'P2O5\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'EN 16170\' WHERE element = \'K2O\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'EN 16170\' WHERE element = \'S\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'EN 16170\' WHERE element = \'Cu\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'EN 16170\' WHERE element = \'Mn\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'EN 16170\' WHERE element = \'Zn\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'EN 16170\' WHERE element = \'B\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'LOI\' WHERE element = \'OM\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'ISO 10694\' WHERE element = \'Ctotal\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'ISO 10694\' WHERE element = \'Corg\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'ISO 10694\' WHERE element = \'Cinorg\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'TCarbonates\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'Acarbonates\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'μS/cm\', method = \'ISO 11265\' WHERE element = \'EC\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'LeafN\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'LeafP\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'LeafK\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'LeafCa\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'LeafMg\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'LeafNa\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'LeafS\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'LeafFe\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'LeafCu\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'LeafZn\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'LeafMn\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'LeafB\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'EN 16170\' WHERE element = \'CaO\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'EN 16170\' WHERE element = \'MgO\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'EN 16170\' WHERE element = \'Na2O\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'EN 16170\' WHERE element = \'Fe\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'cmol+/kg\', method = \'ISO 11260\' WHERE element = \'CEC\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'ISO 11260\' WHERE element = \'BS\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'cmol+/kg\', method = \'ISO 11260\' WHERE element = \'K+\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'cmol+/kg\', method = \'ISO 11260\' WHERE element = \'Ca2+\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'cmol+/kg\', method = \'ISO 11260\' WHERE element = \'Mg2+\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'cmol+/kg\', method = \'ISO 11260\' WHERE element = \'Na+\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'cmol+/kg\', method = \'ISO 11260\' WHERE element = \'H+\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'mg/kg\', method = \'EN 16170\' WHERE element = \'Mo\'');
        $this->addSql('UPDATE lab_analysis_group_element SET unit = \'%\', method = \'Internal method\' WHERE element = \'Humus\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('UPDATE lab_analysis_group_element SET unit = NULL, method = NULL');
    }
}
