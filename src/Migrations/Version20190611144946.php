<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190611144946 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE TABLE package_service (package_id INT NOT NULL, service_id INT NOT NULL, PRIMARY KEY(package_id, service_id))');
        $this->addSql('CREATE INDEX IDX_6EC6FF5EF44CABFF ON package_service (package_id)');
        $this->addSql('CREATE INDEX IDX_6EC6FF5EED5CA9E6 ON package_service (service_id)');
        $this->addSql('ALTER TABLE package_service ADD CONSTRAINT FK_6EC6FF5EF44CABFF FOREIGN KEY (package_id) REFERENCES package (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE package_service ADD CONSTRAINT FK_6EC6FF5EED5CA9E6 FOREIGN KEY (service_id) REFERENCES service_contracts (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SCHEMA public');
        $this->addSql('DROP TABLE package_service');
    }
}
