<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210420123545 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Insert data in lab_elements_calculations for elements CEC, BS, K+, Ca2+, Mg2+, Na+, H+, OM ';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $sql = "INSERT INTO lab_elements_calculations (
            element, coefficient, operation, template_column
        )
        VALUES
            ('CEC', 1, '*', 'CEC'),
            ('BS', 1, '*', 'Base Saturation'),
            ('K+', 1, '*', 'K+'),
            ('Ca2+', 1, '*', 'Ca2+'),
            ('Mg2+', 1, '*', 'Mg2+'),
            ('Na+', 1, '*', 'Na+'),
            ('H+', 1, '*', 'H+'),
            ('OM', 1, '*', 'Organic Matter')
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("DELETE FROM lab_elements_calculations 
                            where element in ('CEC', 'BS', 'K+', 'Ca2+', 'Mg2+', 'Na+', 'H+', 'OM')");
    }
}
