<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210623085641 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create postgresql function get_recommendation_elements_results.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("
            CREATE FUNCTION get_recommendation_elements_results (
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                humus NUMERIC,
                yield NUMERIC,
                sampling_date DATE
            )
                RETURNS TABLE (result_element result_element_enum, value NUMERIC)
                AS $$
                    BEGIN
                    RETURN QUERY
                    WITH recommendation_elements_results AS (
                        SELECT
                                CASE WHEN rcmcv.\"element\" = 'P2O5'::elements_enum THEN 
                                    add_p_total(
                                        yield,
                                        (avg(rcmcv.value))::numeric,
                                        (rcmcv.config->'Uptake P'->>'value')::NUMERIC,
                                        (rcmcv.config->'Vol density'->>'value')::NUMERIC,
                                        (rcmcv.config->'Soil abs P'->>'value')::NUMERIC,
                                        (rcmcv.config->'Fertiliser abs P'->>'value')::NUMERIC
                                    ) 
                                END AS apt,
                                CASE WHEN rcmcv.\"element\" = 'K2O'::elements_enum THEN 
                                    add_k_total(
                                        yield,
                                        (avg(rcmcv.value))::numeric,
                                        (rcmcv.config->'Uptake K'->>'value')::NUMERIC,
                                        (rcmcv.config->'Vol density'->>'value')::NUMERIC,
                                        (rcmcv.config->'Soil abs K'->>'value')::NUMERIC,
                                        (rcmcv.config->'Fertiliser abs K'->>'value')::NUMERIC
                                    )
                                END AS akt,
                                CASE WHEN rcmcv.\"element\" = 'S'::elements_enum THEN 
                                    add_s_total(
                                        yield,
                                        (avg(rcmcv.value))::numeric,
                                        (rcmcv.config->'Uptake S'->>'value')::NUMERIC,
                                        (rcmcv.config->'Vol density'->>'value')::NUMERIC,
                                        (rcmcv.config->'Soil abs S'->>'value')::NUMERIC,
                                        (rcmcv.config->'Fertiliser abs S'->>'value')::NUMERIC
                                    ) 
                                END AS ast,
                                CASE WHEN rcmcv.\"element\" = 'TMN'::elements_enum THEN 
                                    add_n_fall (
                                        yield,
                                        (avg(rcmcv.value))::numeric,
                                        (rcmcv.config->'Uptake N'->>'value')::NUMERIC,
                                        (rcmcv.config->'Vol density'->>'value')::NUMERIC,
                                        (rcmcv.config->'Soil abs N'->>'value')::NUMERIC,
                                        (rcmcv.config->'Organic N'->>'value')::NUMERIC,
                                        (rcmcv.config->'Add organic N'->>'value')::NUMERIC,
                                        (rcmcv.config->'Fall N part'->>'value')::NUMERIC,
                                        (rcmcv.config->'Fertiliser abs N'->>'value')::NUMERIC,
                                        (rcmcv.config->'Add N reduction sampling'->>'value')::NUMERIC,
                                        humus,
                                        sampling_date
                                    )
                                END AS anf,
                                CASE WHEN rcmcv.\"element\" = 'TMN'::elements_enum THEN       
                                    add_n_total (
                                        yield,
                                        (avg(rcmcv.value))::numeric,
                                        (rcmcv.config->'Uptake N'->>'value')::NUMERIC,
                                        (rcmcv.config->'Vol density'->>'value')::NUMERIC,
                                        (rcmcv.config->'Soil abs N'->>'value')::NUMERIC,
                                        (rcmcv.config->'Organic N'->>'value')::NUMERIC,
                                        (rcmcv.config->'Add organic N'->>'value')::NUMERIC,
                                        (rcmcv.config->'Fall N part'->>'value')::NUMERIC,
                                        (rcmcv.config->'Fertiliser abs N'->>'value')::NUMERIC,
                                        (rcmcv.config->'Add N reduction sampling'->>'value')::NUMERIC,
                                        humus,
                                        sampling_date
                                    )
                                END AS ant
                        FROM 
                            get_recommendation_crop_model_config_values(plt_uuid, pckg_id, pckg_type, service_provider, model, crop, ARRAY['P2O5', 'K2O', 'TMN', 'S']::elements_enum[]) AS rcmcv
                        GROUP BY 
                            yield,
                            rcmcv.config,
                            rcmcv.\"element\"	
                    ),
                    recommendation_elements_results_agg AS (
                        SELECT
                            json_build_object(
                                'Add_P_total'::result_element_enum, COALESCE(max(apt), 0),
                                'Add_K_total'::result_element_enum, COALESCE(max(akt), 0),
                                'Add_S_total'::result_element_enum, COALESCE(max(ast), 0),
                                'Add_N_fall'::result_element_enum, COALESCE(max(anf), 0),
                                'Add_N_total'::result_element_enum, COALESCE(max(ant), 0)
                            ) results_elements
                        FROM 
                            recommendation_elements_results AS rer
                    )
                    SELECT
                        (re.\"key\")::result_element_enum AS result_element,
                        CASE WHEN (re.value)::text::numeric < 0 THEN 0 ELSE (re.value)::text::numeric END AS value
                    FROM 
                        recommendation_elements_results_agg AS rera,
                        json_each(rera.results_elements) AS re;
                    END
                $$ LANGUAGE plpgsql
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DROP FUNCTION IF EXISTS get_recommendation_elements_results (
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                humus NUMERIC,
                yield NUMERIC,
                sampling_date DATE
            )
        ');
    }
}
