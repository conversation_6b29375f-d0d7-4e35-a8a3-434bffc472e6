<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230626130757 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add column \'target\' to table package_main_navigation';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE package_main_navigation ADD "target" link_target_enum NOT NULL DEFAULT \'_self\'::link_target_enum');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE package_main_navigation DROP "target"');
    }
}
