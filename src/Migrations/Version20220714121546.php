<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220714121546 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update pg function get_lab_element_results_classes to return sampling_type_id.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP FUNCTION IF EXISTS get_lab_elements_results_classes(plot_uuids VARCHAR[], pckg_id INTEGER, pckg_type VARCHAR, service_provider INTEGER)');

        $this->addSql('
            CREATE FUNCTION get_lab_elements_results_classes(plot_uuids VARCHAR[], pckg_id INTEGER, pckg_type VARCHAR, service_provider INTEGER)
            RETURNS TABLE (
                lab_element_result_id INTEGER,
                package_grid_points_id INTEGER,
                lab_id VARCHAR,
                cell_id INTEGER,
                sampling_type_id INTEGER,
                "date" DATE,
                plot_uuid VARCHAR,
                element_id INTEGER,
                "element" elements_enum,
                element_unit VARCHAR,
                value DOUBLE PRECISION,
                "range" NUMRANGE,
                class_id INTEGER,
                slug VARCHAR,
                description VARCHAR,
                color VARCHAR
            ) AS $$
                BEGIN
                    RETURN QUERY
                    SELECT
                        ler.id AS lab_element_result_id,
                        pgp.id AS package_grid_points_id,
                        pgp.lab_number AS lab_id,
                        pgp.sample_id AS cell_id,
                        pgp.sampling_type_id,
                        pgp.state_updated_at::date AS date,
                        leg.plot_uuid,
                        lage.id AS element_id,
                        lage."element",
                        lage.unit AS element_unit,
                        ler.value,
                        leic."range",
                        licc.id AS class_id,
                        licc.slug,
                        licc.description,
                        leic.color
                    FROM
                        lab_elements_results ler
                    JOIN package_grid_points pgp
                        ON pgp.id = ler.package_grid_points_id
                    JOIN lab_element_group leg
                        ON leg.id  = ler.lab_element_group_id
                        AND leg.package_id = pckg_id
                        AND leg.package_type = pckg_type
                        AND leg.plot_uuid = ANY (plot_uuids)
                    JOIN lab_analysis_group_element lage
                        ON lage.lab_analysis_group_id = leg.lab_analysis_group_id
                        AND lage."element" = ler."element"
                    JOIN lab_analysis_group_element_visual_order evo
                        on evo.lab_analysis_group_element_id = lage.id and evo.service_provider_id = service_provider
                    JOIN lab_element_interpretations_config leic 
                        ON leic.element_id = lage.id
                        AND ler.value::numeric <@ leic."range"
                        AND leic.service_provider_id = service_provider
                    JOIN 
                        lab_interpretation_classes_config licc ON licc.id = leic.class_id
                    ORDER BY 
                        leg.plot_uuid,
                        evo.visual_order,
                        licc.id;
                    END
            $$ LANGUAGE plpgsql
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP FUNCTION IF EXISTS get_lab_elements_results_classes(plot_uuids VARCHAR[], pckg_id INTEGER, pckg_type VARCHAR, service_provider INTEGER)');

        $this->addSql('
            CREATE FUNCTION get_lab_elements_results_classes(plot_uuids VARCHAR[], pckg_id INTEGER, pckg_type VARCHAR, service_provider INTEGER)
            RETURNS TABLE (
                lab_element_result_id INTEGER,
                package_grid_points_id INTEGER,
                lab_id VARCHAR,
                cell_id INTEGER,
                "date" DATE,
                plot_uuid VARCHAR,
                element_id INTEGER,
                "element" elements_enum,
                element_unit VARCHAR,
                value DOUBLE PRECISION,
                "range" NUMRANGE,
                class_id INTEGER,
                slug VARCHAR,
                description VARCHAR,
                color VARCHAR
            ) AS $$
                BEGIN
                    RETURN QUERY
                    SELECT
                        ler.id AS lab_element_result_id,
                        pgp.id AS package_grid_points_id,
                        pgp.lab_number AS lab_id,
                        pgp.sample_id AS cell_id,
                        pgp.state_updated_at::date AS date,
                        leg.plot_uuid,
                        lage.id AS element_id,
                        lage."element",
                        lage.unit AS element_unit,
                        ler.value,
                        leic."range",
                        licc.id AS class_id,
                        licc.slug,
                        licc.description,
                        leic.color
                    FROM
                        lab_elements_results ler
                    JOIN package_grid_points pgp
                        ON pgp.id = ler.package_grid_points_id
                    JOIN lab_element_group leg
                        ON leg.id  = ler.lab_element_group_id
                        AND leg.package_id = pckg_id
                        AND leg.package_type = pckg_type
                        AND leg.plot_uuid = ANY (plot_uuids)
                    JOIN lab_analysis_group_element lage
                        ON lage.lab_analysis_group_id = leg.lab_analysis_group_id
                        AND lage."element" = ler."element"
                    JOIN lab_analysis_group_element_visual_order evo
                        on evo.lab_analysis_group_element_id = lage.id and evo.service_provider_id = service_provider
                    JOIN lab_element_interpretations_config leic 
                        ON leic.element_id = lage.id
                        AND ler.value::numeric <@ leic."range"
                        AND leic.service_provider_id = service_provider
                    JOIN 
                        lab_interpretation_classes_config licc ON licc.id = leic.class_id
                    ORDER BY 
                        leg.plot_uuid,
                        evo.visual_order,
                        licc.id;
                    END
            $$ LANGUAGE plpgsql
    ');
    }
}
