<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191219094019 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SEQUENCE sampling_type_id_seq INCREMENT BY 1 MINVALUE 0 START 0');
        $this->addSql('CREATE TABLE sampling_type (id INT NOT NULL, type VARCHAR(50) NOT NULL, PRIMARY KEY(id))');

        $samplingTypeSql = "INSERT INTO sampling_type (
            id,
            type
        )
        VALUES
            (0, '0-30'),
            (1, '30-60'),
            (2, 'Leaf')
        ";
        $this->addSql($samplingTypeSql);

        $this->addSql('CREATE TABLE package_sampling_type (package_id INT NOT NULL, sampling_type_id INT NOT NULL, PRIMARY KEY(package_id, sampling_type_id))');
        $this->addSql('CREATE INDEX IDX_B423C1DDF44CABFF ON package_sampling_type (package_id)');
        $this->addSql('CREATE INDEX IDX_B423C1DD45C8B25E ON package_sampling_type (sampling_type_id)');
        $this->addSql('ALTER TABLE package_sampling_type ADD CONSTRAINT FK_B423C1DDF44CABFF FOREIGN KEY (package_id) REFERENCES package (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE package_sampling_type ADD CONSTRAINT FK_B423C1DD45C8B25E FOREIGN KEY (sampling_type_id) REFERENCES sampling_type (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');

        $sql = "insert into package_sampling_type (package_id, sampling_type_id) (
                select p.id,
                       ( select st.id 
                         from 
                            sampling_type st
                         where 
                            st.\"type\" like '0-30')
                from
                    package p
                where
                    p.is_sampling is true);";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE package_sampling_type DROP CONSTRAINT FK_B423C1DD45C8B25E');
        $this->addSql('DROP SEQUENCE sampling_type_id_seq CASCADE');
        $this->addSql('DROP TABLE sampling_type');
        $this->addSql('DROP TABLE package_sampling_type');
    }
}
