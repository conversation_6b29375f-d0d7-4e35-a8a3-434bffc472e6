<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210928113205 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add lab interpretation class \'not available\' to lab_interpretation_classes_config ';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("INSERT INTO lab_interpretation_classes_config (\"description\") VALUES ('not available')");
    }

    public function down(Schema $schema): void
    {
        $this->addSql("DELETE FROM lab_interpretation_classes_config WHERE \"description\" = 'not available'");
    }
}
