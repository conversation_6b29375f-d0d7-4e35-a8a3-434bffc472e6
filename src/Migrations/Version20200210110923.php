<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200210110923 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        // create table
        $this->addSql('CREATE TABLE meta_elements_groups (id SERIAL, group_id INT DEFAULT NULL, element elements_enum NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_3409E0BFFE54D947 ON meta_elements_groups (group_id)');
        $this->addSql('ALTER TABLE meta_elements_groups ADD CONSTRAINT FK_3409E0BFFE54D947 FOREIGN KEY (group_id) REFERENCES meta_groups (id) NOT DEFERRABLE INITIALLY IMMEDIATE');

        // seed
        $sql = "INSERT INTO meta_elements_groups (element, group_id)
            VALUES 
            ('pH', '1'),
            ('NO3-N', '1'),
            ('NH4-N', '1'),
            ('P2O5', '1'),
            ('K2O', '1'),
            ('CaO', '1'),
            ('MgO', '1'),
            ('Na2O', '1'),
            ('S', '1'),
            ('Cl', '2'),
            ('B', '2'),
            ('Cu', '2'),
            ('Fe', '2'),
            ('Mn', '2'),
            ('Zn', '2'),
            ('Mo', '2')
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE meta_elements_groups DROP CONSTRAINT FK_3409E0BFFE54D947');
        $this->addSql('DROP TABLE meta_elements_groups');
    }
}
