<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250505070422 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Replace НИК Агро Сървис and NIK Agro Service Ltd in templates';
    }

    public function up(Schema $schema): void
    {
        $sql = "UPDATE service_provider
            set name = 'EUROFINS AGRO TESTING BULGARIA', 
            slug = 'eurofins'
            where slug = 'nikas'
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        $sql = "UPDATE service_provider
            set name = 'NIK AgroService', 
            slug = 'nikas'
            where slug = 'eurofins'
        ";
        $this->addSql($sql);
    }
}
