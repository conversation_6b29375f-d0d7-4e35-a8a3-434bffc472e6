<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191106142001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("CREATE TYPE contract_statuses_enum AS ENUM ('New', 'Active', 'Expired')");

        $this->addSql('ALTER TABLE contract DROP CONSTRAINT fk_e98f28596bf700bd');
        $this->addSql('DROP SEQUENCE contract_statuses_id_seq CASCADE');
        $this->addSql('DROP TABLE contract_statuses');
        $this->addSql('DROP INDEX idx_e98f28596bf700bd');
        $this->addSql('ALTER TABLE contract ADD status contract_statuses_enum DEFAULT \'New\'');
        $this->addSql('ALTER TABLE contract DROP status_id');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SEQUENCE contract_statuses_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE contract_statuses (id INT NOT NULL, service_provider_id INT DEFAULT NULL, slug VARCHAR(20) NOT NULL, PRIMARY KEY(id))');

        // contract_statuses
        $sql = "ALTER TABLE contract_statuses ALTER COLUMN id SET DEFAULT nextval('contract_statuses_id_seq'::regclass)";
        $this->addSql($sql);

        $sql = "INSERT INTO contract_statuses (
            slug,
            service_provider_id
        )
        VALUES
            ('New', 1),
            ('Active', 1),
            ('Expired', 1)
        ";
        $this->addSql($sql);

        $this->addSql('CREATE INDEX idx_de4705eec6c98e06 ON contract_statuses (service_provider_id)');
        $this->addSql('ALTER TABLE contract ADD status_id INT DEFAULT \'1\'');
        $this->addSql('ALTER TABLE contract DROP status');
        $this->addSql('ALTER TABLE contract ADD CONSTRAINT fk_e98f28596bf700bd FOREIGN KEY (status_id) REFERENCES contract_statuses (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_e98f28596bf700bd ON contract (status_id)');

        $this->addSql('DROP TYPE IF EXISTS contract_statuses_enum');
    }
}
