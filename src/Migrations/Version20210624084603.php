<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210624084603 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create enum content_class_enum.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("CREATE TYPE content_class_enum AS enum ('Low', 'Medium', 'High')");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TYPE IF EXISTS content_class_enum');
    }
}
