<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210803082607 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add status \'Declined\' to recommendation_status_enum.';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TYPE recommendation_status_enum ADD VALUE \'Declined\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE recommendations ALTER status DROP DEFAULT');
        $this->addSql('CREATE TYPE recommendation_status_enum_new AS ENUM (\'For approve\', \'Delivered\')');
        $this->addSql('ALTER TABLE recommendations ALTER COLUMN status TYPE recommendation_status_enum_new USING (status::text::recommendation_status_enum_new)');
        $this->addSql('DROP TYPE recommendation_status_enum');
        $this->addSql('ALTER TYPE recommendation_status_enum_new RENAME TO recommendation_status_enum');
        $this->addSql('ALTER TABLE recommendations ALTER status SET DEFAULT \'For approve\'');
    }
}
