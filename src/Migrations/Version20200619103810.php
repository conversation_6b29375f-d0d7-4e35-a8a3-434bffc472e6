<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200619103810 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('SELECT setval(\'lab_analysis_package_group_id_seq\', (SELECT MAX(id) FROM lab_analysis_package_group))');
        $this->addSql('ALTER TABLE lab_analysis_package_group ALTER id SET DEFAULT nextval(\'lab_analysis_package_group_id_seq\')');

        $ABSoilpH = $this->getLabAnalysisGroupId('ABSoilpH');
        $ABSoilN = $this->getLabAnalysisGroupId('ABSoilN');
        $ABSoilP = $this->getLabAnalysisGroupId('ABSoilP');
        $ABSoilK = $this->getLabAnalysisGroupId('ABSoilK');

        $abLightId = $this->getPackageId('AB Light');

        $sql = 'INSERT INTO lab_analysis_package_group (
            package_id, package_type, lab_analysis_group_id
        )
        VALUES
            (' . $abLightId . ",'subscription', {$ABSoilpH}),
            (" . $abLightId . ",'subscription', {$ABSoilN}),
            (" . $abLightId . ",'subscription', {$ABSoilP}),
            (" . $abLightId . ",'subscription', {$ABSoilK})
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
    }
}
