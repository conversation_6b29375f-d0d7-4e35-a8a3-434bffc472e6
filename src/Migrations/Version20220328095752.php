<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220328095752 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename slug for ultra acidic and extremely acidic';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("UPDATE lab_interpretation_classes_config
        SET slug = 'UAC'
        WHERE slug = 'UA'
         ");

        $this->addSql("UPDATE lab_interpretation_classes_config
        SET slug = 'EAC'
        WHERE slug = 'ЕA'
         ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("UPDATE lab_interpretation_classes_config
        SET slug = 'UA'
        WHERE slug = 'UAC'
         ");

        $this->addSql("UPDATE lab_interpretation_classes_config
        SET slug = 'ЕA'
        WHERE slug = 'EAC'
         ");
    }
}
