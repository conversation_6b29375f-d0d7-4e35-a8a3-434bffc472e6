<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190925115109 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf(
            'postgresql' !== $this->connection->getDatabasePlatform()->getName(),
            'Migration can only be executed safely on \'postgresql\'.'
        );

        $this->addSql('ALTER TABLE package ADD slug_short VARCHAR(10) DEFAULT NULL');

        $this->addSql("UPDATE package
        SET slug_short = 'ISO'
        WHERE slug = 'AB ISO full'
         ");

        $this->addSql("UPDATE package
        SET slug_short = 'VRA'
        WHERE slug = 'AB VRA full'
         ");

        $this->addSql("UPDATE package
        SET slug_short = 'SAT'
        WHERE slug = 'Satellite imagery'
         ");

        $this->addSql("UPDATE package
        SET slug_short = 'WD'
        WHERE slug = 'Weather data'
         ");

        $this->addSql("UPDATE package
        SET slug_short = 'LS'
        WHERE slug = 'AB Leaf samples'
         ");

        $this->addSql("UPDATE package
        SET slug_short = 'OM'
        WHERE slug = 'AB Humus'
         ");

        $this->addSql("UPDATE package
        SET slug_short = 'WS'
        WHERE slug = 'Weather stations'
         ");

        $this->addSql("UPDATE package
        SET slug_short = 'VRA-N'
        WHERE slug = 'Index VRA-N'
         ");

        $this->addSql("UPDATE package
        SET slug_short = '3D'
        WHERE slug = '3D mapping'
         ");

        $this->addSql("UPDATE package
        SET slug_short = 'TFC'
        WHERE slug = 'TF connect'
         ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf(
            'postgresql' !== $this->connection->getDatabasePlatform()->getName(),
            'Migration can only be executed safely on \'postgresql\'.'
        );

        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE package DROP slug_short');
    }
}
