<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210726125845 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create postgresql function get_recommendation_sampling_date()';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("
            CREATE FUNCTION get_recommendation_sampling_date(plt_uuid VARCHAR, pckg_id INT, pckg_type VARCHAR)
            RETURNS DATE
            AS $$
                BEGIN
                    RETURN (
                        SELECT
                            max(pgp.state_updated_at)::date AS \"date\"
                        FROM
                            package_grid_points AS pgp
                        WHERE
                            pgp.package_id = pckg_id
                            AND pgp.package_type = pckg_type
                            AND pgp.plot_uuid = plt_uuid
                            AND pgp.state = 'ReceivedInLab' 
                    );
                END
            $$ LANGUAGE plpgsql  
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DROP FUNCTION IF EXISTS get_recommendation_sampling_date(plt_uuid VARCHAR, pckg_id INT, pckg_type VARCHAR)
        ');
    }
}
