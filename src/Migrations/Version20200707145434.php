<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Exception;
use PDO;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200707145434 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE lab_analysis_group SET name = \'ABSoilFeCuMnZnB\' WHERE name = \'ABSoilCuMnZnB\'');
        $this->addSql('UPDATE lab_elements_calculations SET coefficient = \'0.71\' WHERE template_column = \'Ca\'');

        $id = $this->getLabAnalysisGroupId('ABSoilCuMnZnB');

        $this->addSql('SELECT setval(\'lab_analysis_group_element_id_seq\', (SELECT MAX(id) FROM lab_analysis_group_element))');
        $sql = "ALTER TABLE lab_analysis_group_element ALTER COLUMN id SET DEFAULT nextval('lab_analysis_group_element_id_seq'::regclass)";
        $this->addSql($sql);
        $this->addSql('INSERT INTO lab_analysis_group_element (lab_analysis_group_id, element) VALUES (' . $id . ', \'Fe\')');

        $this->addSql('SELECT setval(\'lab_elements_calculations_id_seq\', (SELECT MAX(id) FROM lab_elements_calculations))');
        $sql = "ALTER TABLE lab_elements_calculations ALTER COLUMN id SET DEFAULT nextval('lab_elements_calculations_id_seq'::regclass)";
        $this->addSql($sql);
        $this->addSql('INSERT INTO lab_elements_calculations (element, coefficient, operation, template_column) VALUES (\'Fe\', \'1\', \'*\', \'Fe\')');

        $metaGroupId = $this->getMetaGroupId('Micro-elements');
        $this->addSql('INSERT INTO meta_elements_groups (group_id, element) VALUES (' . $metaGroupId . ', \'Fe\')');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('UPDATE lab_analysis_group SET name = \'ABSoilCuMnZnB \' WHERE name = \'ABSoilFeCuMnZnB\'');
        $this->addSql('UPDATE lab_elements_calculations SET coefficient = \'0.7\' WHERE template_column = \'Ca\'');
    }

    private function getLabAnalysisGroupId($name)
    {
        $sql = 'SELECT id FROM lab_analysis_group WHERE name = :name';
        $stmt = $this->connection->prepare($sql);
        $stmt->bindValue('name', $name);
        $stmt->execute();
        $arrResult = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (!$arrResult) {
            throw new Exception('result with name: ' . $name . ' in lab_analysis_group not found!');
        }

        if (count($arrResult) > 1) {
            throw new Exception('Too many results with name: ' . $name . ' in lab_analysis_group!');
        }

        return reset($arrResult);
    }

    private function getMetaGroupId($name)
    {
        $sql = 'SELECT id FROM meta_groups WHERE name = :name';
        $stmt = $this->connection->prepare($sql);
        $stmt->bindValue('name', $name);
        $stmt->execute();
        $arrResult = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (!$arrResult) {
            throw new Exception('result with name: ' . $name . ' in meta_groups not found!');
        }

        if (count($arrResult) > 1) {
            throw new Exception('Too many results with name: ' . $name . ' in meta_groups!');
        }

        return reset($arrResult);
    }
}
