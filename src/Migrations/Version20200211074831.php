<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200211074831 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE lab_elements_results ADD lab_elements_results_raw_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE lab_elements_results ADD CONSTRAINT FK_2E6761B6DEDF4C09 FOREIGN KEY (lab_elements_results_raw_id) REFERENCES lab_elements_results_raw (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_2E6761B6DEDF4C09 ON lab_elements_results (lab_elements_results_raw_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE lab_elements_results DROP CONSTRAINT FK_2E6761B6DEDF4C09');
        $this->addSql('DROP INDEX IDX_2E6761B6DEDF4C09');
        $this->addSql('ALTER TABLE lab_elements_results DROP lab_elements_results_raw_id');
    }
}
