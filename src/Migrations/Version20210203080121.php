<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210203080121 extends AbstractMigration
{
    private $parameters;

    public function getDescription(): string
    {
        return 'Fill column service_provider_id in table lab_analysis_uploads';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $env = getenv();
        $mainDBConnectionString = 'host=' . $env['GS_MAIN_DB_HOST']
            . ' port=' . $env['GS_MAIN_DB_PORT']
            . ' dbname=' . $env['GS_MAIN_DB_NAME']
            . ' user=' . $env['GS_MAIN_DB_USER']
            . ' password=' . $env['GS_MAIN_DB_PASS'];

        $sql = 'UPDATE lab_analysis_uploads AS l
            SET service_provider_id = t.service_provider_id
            FROM (
                SELECT lau.id, global_user.service_provider_id
                FROM
                    lab_analysis_uploads lau 
                JOIN 
                    dblink(:mainDBConnectionString, $$
                                select su.id, service_provider_id 
                                from su_users su
                    $$) as global_user(id int, service_provider_id int) 
                ON global_user.id = lau.user_id
            ) AS t
            WHERE l.id = t.id
        ';

        $this->addSql($sql, ['mainDBConnectionString' => $mainDBConnectionString]);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
        $this->addSql('UPDATE lab_analysis_uploads SET service_provider_id = NULL');
    }
}
