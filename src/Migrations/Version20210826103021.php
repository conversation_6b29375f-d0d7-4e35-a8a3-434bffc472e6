<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210826103021 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Seed table recommendation_crop_model_config_values.';
    }

    public function up(Schema $schema): void
    {
        // Fetch recommendation models configs
        $modelsConfigs = $this->getRecommendationModelsConfigs();

        $this->addSql("
            INSERT INTO recommendation_crop_model_config_values (model_id, crop_ids, \"parameter\", value) VALUES
            ({$modelsConfigs['Vantage']},ARRAY[2],'pH opt low',6.3),
            ({$modelsConfigs['Vantage']},ARRAY[2],'pH opt high',7.8),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Uptake N',2.7),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Uptake P',1.4),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Uptake K',2.7),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Uptake S',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Uptake CaO',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Uptake MgO',0.2),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Susces Mn',1),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Susces Cu',1),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Susces B',0),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Susces Zn',0),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Susces Mo',0),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Susces Fe',0),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[2],'N rate reduction',1),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Fall N part',0.33),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[2],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[5],'pH opt low',5.5),
            ({$modelsConfigs['Vantage']},ARRAY[5],'pH opt high',7.5),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Uptake N',2.75),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Uptake P',1.25),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Uptake K',2.6),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Uptake S',0),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Uptake CaO',0.8),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Uptake MgO',0.2),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Susces Mn',1),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Susces Cu',1),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Susces B',0),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Susces Zn',0),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Susces Mo',0),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Susces Fe',0),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[5],'N rate reduction',1),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Fall N part',0.33),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[5],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[6],'pH opt low',5.5),
            ({$modelsConfigs['Vantage']},ARRAY[6],'pH opt high',7.5),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Uptake N',3.5),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Uptake P',1.5),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Uptake K',3.5),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Uptake S',0),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Uptake CaO',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Uptake MgO',0.15),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Susces Mn',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Susces Cu',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Susces B',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Susces Zn',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Susces Mo',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Susces Fe',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[6],'N rate reduction',1),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Fall N part',0.33),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[6],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[3],'pH opt low',6.8),
            ({$modelsConfigs['Vantage']},ARRAY[3],'pH opt high',7.5),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Uptake N',2.5),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Uptake P',1.4),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Uptake K',2.5),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Uptake S',0),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Uptake CaO',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Uptake MgO',0.15),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Susces Mn',1),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Susces Cu',1),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Susces B',0),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Susces Zn',0),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Susces Mo',0),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Susces Fe',1),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[3],'N rate reduction',1),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Fall N part',0.33),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[3],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[4],'pH opt low',5),
            ({$modelsConfigs['Vantage']},ARRAY[4],'pH opt high',7.7),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Uptake N',2.8),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Uptake P',1.2),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Uptake K',3.15),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Uptake S',0),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Uptake CaO',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Uptake MgO',0.15),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Susces Mn',1),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Susces Cu',1),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Susces B',0),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Susces Zn',0),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Susces Mo',1),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Susces Fe',1),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[4],'N rate reduction',1),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Fall N part',0.33),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[4],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[9],'pH opt low',5),
            ({$modelsConfigs['Vantage']},ARRAY[9],'pH opt high',6),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Uptake N',2.15),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Uptake P',1.1),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Uptake K',2.75),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Uptake S',0),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Uptake CaO',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Uptake MgO',0.15),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Susces Mn',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Susces Cu',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Susces B',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Susces Zn',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Susces Mo',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Susces Fe',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[9],'N rate reduction',1),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Fall N part',0),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[9],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[7],'pH opt low',6),
            ({$modelsConfigs['Vantage']},ARRAY[7],'pH opt high',7),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Uptake N',1.8),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Uptake P',1.1),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Uptake K',2.7),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Uptake S',0.35),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Uptake CaO',0.9),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Uptake MgO',0.25),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Susces Mn',1),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Susces Cu',1),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Susces B',1),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Susces Zn',1),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Susces Mo',0),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Susces Fe',1),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[7],'N rate reduction',1),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Fall N part',0),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[7],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[8],'pH opt low',6),
            ({$modelsConfigs['Vantage']},ARRAY[8],'pH opt high',7.5),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Uptake N',2.7),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Uptake P',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Uptake K',2.4),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Uptake S',0),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Uptake CaO',0.8),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Uptake MgO',0.25),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Susces Mn',1),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Susces Cu',1),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Susces B',0),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Susces Zn',1),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Susces Mo',0),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Susces Fe',1),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[8],'N rate reduction',1),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Fall N part',0),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[8],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[15],'pH opt low',6.4),
            ({$modelsConfigs['Vantage']},ARRAY[15],'pH opt high',7.1),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Uptake N',5),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Uptake P',2),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Uptake K',4.5),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Uptake S',0),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Uptake CaO',3.5),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Uptake MgO',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Susces Mn',1),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Susces Cu',0),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Susces B',1),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Susces Zn',1),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Susces Mo',1),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Susces Fe',1),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[15],'N rate reduction',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Fall N part',0),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[15],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[16],'pH opt low',6),
            ({$modelsConfigs['Vantage']},ARRAY[16],'pH opt high',7),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Uptake N',5.5),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Uptake P',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Uptake K',2.75),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Uptake S',0),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Uptake CaO',3.25),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Uptake MgO',0.65),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Susces Mn',1),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Susces Cu',0),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Susces B',0),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Susces Zn',0),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Susces Mo',1),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Susces Fe',0),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[16],'N rate reduction',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Fall N part',1),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[16],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[17],'pH opt low',6.3),
            ({$modelsConfigs['Vantage']},ARRAY[17],'pH opt high',7.2),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Uptake N',5.9),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Uptake P',1.9),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Uptake K',2.75),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Uptake S',0),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Uptake CaO',3.25),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Uptake MgO',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Susces Mn',1),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Susces Cu',0),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Susces B',0),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Susces Zn',0),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Susces Mo',1),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Susces Fe',0),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[17],'N rate reduction',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Fall N part',0),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[17],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[21],'pH opt low',7),
            ({$modelsConfigs['Vantage']},ARRAY[21],'pH opt high',7.5),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Uptake N',0.25),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Uptake P',0.2),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Uptake K',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Uptake S',0),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Uptake CaO',0.45),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Uptake MgO',0.25),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Susces Mn',1),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Susces Cu',1),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Susces B',1),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Susces Zn',1),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Susces Mo',1),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Susces Fe',1),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[21],'N rate reduction',1),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Fall N part',0),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[21],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[14],'pH opt low',6),
            ({$modelsConfigs['Vantage']},ARRAY[14],'pH opt high',6.8),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Uptake N',4),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Uptake P',2),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Uptake K',4),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Uptake S',0.45),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Uptake CaO',2.5),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Uptake MgO',1.25),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Susces Mn',1),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Susces Cu',1),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Susces B',1),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Susces Zn',0),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Susces Mo',0),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Susces Fe',0),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Add organic N',1.5),
            ({$modelsConfigs['Vantage']},ARRAY[14],'N rate reduction',1),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Fall N part',0),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[14],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[45],'pH opt low',5),
            ({$modelsConfigs['Vantage']},ARRAY[45],'pH opt high',5.5),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Uptake N',0.53),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Uptake P',0.14),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Uptake K',0.73),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Uptake S',0),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Uptake CaO',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Uptake MgO',0.2),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Susces Mn',1),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Susces Cu',0),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Susces B',1),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Susces Zn',1),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Susces Mo',0),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Susces Fe',0),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[45],'N rate reduction',1),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Fall N part',0),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[45],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[11],'pH opt low',5.7),
            ({$modelsConfigs['Vantage']},ARRAY[11],'pH opt high',7),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Uptake N',5),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Uptake P',1.2),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Uptake K',3),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Uptake S',0),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Uptake CaO',1.75),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Uptake MgO',0.8),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Susces Mn',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Susces Cu',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Susces B',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Susces Zn',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Susces Mo',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Susces Fe',NULL),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[11],'N rate reduction',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Fall N part',0),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[11],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[12],'pH opt low',6.8),
            ({$modelsConfigs['Vantage']},ARRAY[12],'pH opt high',7.2),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Uptake N',5),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Uptake P',3),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Uptake K',4.1),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Uptake S',0),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Uptake CaO',1.25),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Uptake MgO',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Susces Mn',1),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Susces Cu',0),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Susces B',1),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Susces Zn',0),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Susces Mo',1),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Susces Fe',0),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[12],'N rate reduction',1),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Fall N part',0.33),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[12],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Vantage']},ARRAY[10],'pH opt low',6.6),
            ({$modelsConfigs['Vantage']},ARRAY[10],'pH opt high',7.1),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Uptake N',7.5),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Uptake P',2.5),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Uptake K',4),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Uptake S',0),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Uptake CaO',4.5),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Uptake MgO',0.9),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Susces Mn',1),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Susces Cu',0),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Susces B',0),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Susces Zn',1),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Susces Mo',1),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Susces Fe',1),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Soil abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Soil abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Soil abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Soil abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Vol density',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Organic N',1.3),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Add organic N',1),
            ({$modelsConfigs['Vantage']},ARRAY[10],'N rate reduction',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Fall N part',0),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Sampling period',2),
            ({$modelsConfigs['Vantage']},ARRAY[10],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[2],'pH opt low',6.3),
            ({$modelsConfigs['Agricost']},ARRAY[2],'pH opt high',7.8),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Uptake N',2.7),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Uptake P',1.4),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Uptake K',2.7),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Uptake S',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Uptake CaO',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Uptake MgO',0.2),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Susces Mn',1),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Susces Cu',1),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Susces B',0),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Susces Zn',0),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Susces Mo',0),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Susces Fe',0),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[2],'N rate reduction',1),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Fall N part',0.33),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[2],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[5],'pH opt low',5.5),
            ({$modelsConfigs['Agricost']},ARRAY[5],'pH opt high',7.5),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Uptake N',2.75),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Uptake P',1.25),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Uptake K',2.6),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Uptake S',0),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Uptake CaO',0.8),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Uptake MgO',0.2),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Susces Mn',1),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Susces Cu',1),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Susces B',0),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Susces Zn',0),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Susces Mo',0),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Susces Fe',0),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[5],'N rate reduction',1),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Fall N part',0.33),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[5],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[6],'pH opt low',5.5),
            ({$modelsConfigs['Agricost']},ARRAY[6],'pH opt high',7.5),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Uptake N',3.5),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Uptake P',1.5),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Uptake K',3.5),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Uptake S',0),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Uptake CaO',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Uptake MgO',0.15),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Susces Mn',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Susces Cu',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Susces B',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Susces Zn',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Susces Mo',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Susces Fe',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[6],'N rate reduction',1),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Fall N part',0.33),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[6],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[3],'pH opt low',6.8),
            ({$modelsConfigs['Agricost']},ARRAY[3],'pH opt high',7.5),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Uptake N',2.5),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Uptake P',1.4),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Uptake K',2.5),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Uptake S',0),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Uptake CaO',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Uptake MgO',0.15),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Susces Mn',1),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Susces Cu',1),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Susces B',0),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Susces Zn',0),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Susces Mo',0),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Susces Fe',1),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[3],'N rate reduction',1),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Fall N part',0.33),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[3],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[4],'pH opt low',5),
            ({$modelsConfigs['Agricost']},ARRAY[4],'pH opt high',7.7),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Uptake N',2.8),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Uptake P',1.2),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Uptake K',3.15),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Uptake S',0),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Uptake CaO',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Uptake MgO',0.15),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Susces Mn',1),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Susces Cu',1),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Susces B',0),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Susces Zn',0),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Susces Mo',1),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Susces Fe',1),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[4],'N rate reduction',1),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Fall N part',0.33),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[4],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[9],'pH opt low',5),
            ({$modelsConfigs['Agricost']},ARRAY[9],'pH opt high',6),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Uptake N',2.15),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Uptake P',1.1),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Uptake K',2.75),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Uptake S',0),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Uptake CaO',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Uptake MgO',0.15),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Susces Mn',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Susces Cu',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Susces B',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Susces Zn',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Susces Mo',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Susces Fe',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[9],'N rate reduction',1),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Fall N part',0),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[9],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[7],'pH opt low',6),
            ({$modelsConfigs['Agricost']},ARRAY[7],'pH opt high',7),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Uptake N',1.8),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Uptake P',1.1),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Uptake K',2.7),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Uptake S',0.35),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Uptake CaO',0.9),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Uptake MgO',0.25),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Susces Mn',1),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Susces Cu',1),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Susces B',1),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Susces Zn',1),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Susces Mo',0),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Susces Fe',1),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[7],'N rate reduction',1),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Fall N part',0),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[7],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[8],'pH opt low',6),
            ({$modelsConfigs['Agricost']},ARRAY[8],'pH opt high',7.5),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Uptake N',2.7),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Uptake P',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Uptake K',2.4),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Uptake S',0),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Uptake CaO',0.8),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Uptake MgO',0.25),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Susces Mn',1),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Susces Cu',1),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Susces B',0),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Susces Zn',1),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Susces Mo',0),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Susces Fe',1),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[8],'N rate reduction',1),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Fall N part',0),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[8],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[15],'pH opt low',6.4),
            ({$modelsConfigs['Agricost']},ARRAY[15],'pH opt high',7.1),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Uptake N',5),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Uptake P',2),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Uptake K',4.5),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Uptake S',0),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Uptake CaO',3.5),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Uptake MgO',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Susces Mn',1),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Susces Cu',0),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Susces B',1),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Susces Zn',1),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Susces Mo',1),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Susces Fe',1),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[15],'N rate reduction',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Fall N part',0),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[15],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[16],'pH opt low',6),
            ({$modelsConfigs['Agricost']},ARRAY[16],'pH opt high',7),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Uptake N',5.5),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Uptake P',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Uptake K',2.75),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Uptake S',0),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Uptake CaO',3.25),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Uptake MgO',0.65),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Susces Mn',1),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Susces Cu',0),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Susces B',0),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Susces Zn',0),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Susces Mo',1),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Susces Fe',0),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[16],'N rate reduction',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Fall N part',1),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[16],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[17],'pH opt low',6.3),
            ({$modelsConfigs['Agricost']},ARRAY[17],'pH opt high',7.2),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Uptake N',5.9),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Uptake P',1.9),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Uptake K',2.75),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Uptake S',0),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Uptake CaO',3.25),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Uptake MgO',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Susces Mn',1),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Susces Cu',0),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Susces B',0),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Susces Zn',0),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Susces Mo',1),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Susces Fe',0),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[17],'N rate reduction',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Fall N part',0),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[17],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[21],'pH opt low',7),
            ({$modelsConfigs['Agricost']},ARRAY[21],'pH opt high',7.5),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Uptake N',0.25),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Uptake P',0.2),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Uptake K',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Uptake S',0),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Uptake CaO',0.45),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Uptake MgO',0.25),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Susces Mn',1),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Susces Cu',1),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Susces B',1),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Susces Zn',1),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Susces Mo',1),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Susces Fe',1),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[21],'N rate reduction',1),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Fall N part',0),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[21],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[14],'pH opt low',6),
            ({$modelsConfigs['Agricost']},ARRAY[14],'pH opt high',6.8),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Uptake N',4),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Uptake P',2),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Uptake K',4),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Uptake S',0.45),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Uptake CaO',2.5),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Uptake MgO',1.25),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Susces Mn',1),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Susces Cu',1),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Susces B',1),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Susces Zn',0),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Susces Mo',0),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Susces Fe',0),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Add organic N',1.5),
            ({$modelsConfigs['Agricost']},ARRAY[14],'N rate reduction',1),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Fall N part',0),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[14],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[45],'pH opt low',5),
            ({$modelsConfigs['Agricost']},ARRAY[45],'pH opt high',5.5),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Uptake N',0.53),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Uptake P',0.14),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Uptake K',0.73),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Uptake S',0),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Uptake CaO',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Uptake MgO',0.2),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Susces Mn',1),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Susces Cu',0),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Susces B',1),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Susces Zn',1),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Susces Mo',0),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Susces Fe',0),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[45],'N rate reduction',1),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Fall N part',0),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[45],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[11],'pH opt low',5.7),
            ({$modelsConfigs['Agricost']},ARRAY[11],'pH opt high',7),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Uptake N',5),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Uptake P',1.2),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Uptake K',3),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Uptake S',0),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Uptake CaO',1.75),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Uptake MgO',0.8),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Susces Mn',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Susces Cu',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Susces B',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Susces Zn',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Susces Mo',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Susces Fe',NULL),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[11],'N rate reduction',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Fall N part',0),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[11],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[12],'pH opt low',6.8),
            ({$modelsConfigs['Agricost']},ARRAY[12],'pH opt high',7.2),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Uptake N',5),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Uptake P',3),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Uptake K',4.1),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Uptake S',0),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Uptake CaO',1.25),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Uptake MgO',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Susces Mn',1),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Susces Cu',0),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Susces B',1),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Susces Zn',0),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Susces Mo',1),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Susces Fe',0),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[12],'N rate reduction',1),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Fall N part',0.33),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[12],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Agricost']},ARRAY[10],'pH opt low',6.6),
            ({$modelsConfigs['Agricost']},ARRAY[10],'pH opt high',7.1),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Uptake N',7.5),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Uptake P',2.5),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Uptake K',4),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Uptake S',0),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Uptake CaO',4.5),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Uptake MgO',0.9),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Susces Mn',1),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Susces Cu',0),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Susces B',0),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Susces Zn',1),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Susces Mo',1),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Susces Fe',1),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Soil abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Soil abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Soil abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Soil abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Vol density',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Organic N',1.3),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Add organic N',1),
            ({$modelsConfigs['Agricost']},ARRAY[10],'N rate reduction',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Fall N part',0),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Sampling period',2),
            ({$modelsConfigs['Agricost']},ARRAY[10],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'pH opt low',6.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'pH opt high',7.8),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Uptake N',2.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Uptake P',1.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Uptake K',2.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Uptake S',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Uptake CaO',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Uptake MgO',0.2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Susces Mn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Susces Cu',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Susces B',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Susces Zn',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Susces Mo',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Susces Fe',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'N rate reduction',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Fall N part',0.33),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[2],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'pH opt low',5.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'pH opt high',7.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Uptake N',2.75),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Uptake P',1.25),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Uptake K',2.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Uptake S',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Uptake CaO',0.8),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Uptake MgO',0.2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Susces Mn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Susces Cu',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Susces B',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Susces Zn',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Susces Mo',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Susces Fe',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'N rate reduction',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Fall N part',0.33),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[5],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'pH opt low',5.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'pH opt high',7.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Uptake N',3.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Uptake P',1.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Uptake K',3.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Uptake S',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Uptake CaO',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Uptake MgO',0.15),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Susces Mn',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Susces Cu',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Susces B',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Susces Zn',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Susces Mo',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Susces Fe',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'N rate reduction',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Fall N part',0.33),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[6],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'pH opt low',6.8),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'pH opt high',7.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Uptake N',2.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Uptake P',1.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Uptake K',2.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Uptake S',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Uptake CaO',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Uptake MgO',0.15),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Susces Mn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Susces Cu',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Susces B',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Susces Zn',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Susces Mo',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Susces Fe',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'N rate reduction',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Fall N part',0.33),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[3],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'pH opt low',5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'pH opt high',7.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Uptake N',2.8),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Uptake P',1.2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Uptake K',3.15),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Uptake S',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Uptake CaO',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Uptake MgO',0.15),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Susces Mn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Susces Cu',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Susces B',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Susces Zn',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Susces Mo',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Susces Fe',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'N rate reduction',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Fall N part',0.33),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[4],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'pH opt low',5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'pH opt high',6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Uptake N',2.15),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Uptake P',1.1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Uptake K',2.75),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Uptake S',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Uptake CaO',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Uptake MgO',0.15),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Susces Mn',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Susces Cu',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Susces B',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Susces Zn',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Susces Mo',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Susces Fe',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'N rate reduction',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Fall N part',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[9],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'pH opt low',6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'pH opt high',7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Uptake N',1.8),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Uptake P',1.1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Uptake K',2.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Uptake S',0.35),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Uptake CaO',0.9),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Uptake MgO',0.25),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Susces Mn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Susces Cu',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Susces B',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Susces Zn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Susces Mo',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Susces Fe',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'N rate reduction',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Fall N part',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[7],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'pH opt low',6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'pH opt high',7.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Uptake N',2.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Uptake P',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Uptake K',2.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Uptake S',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Uptake CaO',0.8),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Uptake MgO',0.25),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Susces Mn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Susces Cu',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Susces B',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Susces Zn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Susces Mo',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Susces Fe',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'N rate reduction',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Fall N part',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[8],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'pH opt low',6.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'pH opt high',7.1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Uptake N',5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Uptake P',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Uptake K',4.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Uptake S',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Uptake CaO',3.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Uptake MgO',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Susces Mn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Susces Cu',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Susces B',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Susces Zn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Susces Mo',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Susces Fe',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'N rate reduction',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Fall N part',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[15],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'pH opt low',6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'pH opt high',7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Uptake N',5.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Uptake P',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Uptake K',2.75),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Uptake S',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Uptake CaO',3.25),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Uptake MgO',0.65),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Susces Mn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Susces Cu',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Susces B',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Susces Zn',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Susces Mo',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Susces Fe',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'N rate reduction',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Fall N part',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[16],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'pH opt low',6.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'pH opt high',7.2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Uptake N',5.9),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Uptake P',1.9),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Uptake K',2.75),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Uptake S',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Uptake CaO',3.25),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Uptake MgO',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Susces Mn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Susces Cu',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Susces B',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Susces Zn',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Susces Mo',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Susces Fe',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'N rate reduction',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Fall N part',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[17],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'pH opt low',7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'pH opt high',7.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Uptake N',0.25),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Uptake P',0.2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Uptake K',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Uptake S',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Uptake CaO',0.45),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Uptake MgO',0.25),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Susces Mn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Susces Cu',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Susces B',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Susces Zn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Susces Mo',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Susces Fe',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'N rate reduction',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Fall N part',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[21],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'pH opt low',6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'pH opt high',6.8),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Uptake N',4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Uptake P',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Uptake K',4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Uptake S',0.45),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Uptake CaO',2.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Uptake MgO',1.25),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Susces Mn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Susces Cu',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Susces B',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Susces Zn',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Susces Mo',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Susces Fe',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Add organic N',1.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'N rate reduction',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Fall N part',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[14],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'pH opt low',5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'pH opt high',5.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Uptake N',0.53),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Uptake P',0.14),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Uptake K',0.73),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Uptake S',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Uptake CaO',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Uptake MgO',0.2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Susces Mn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Susces Cu',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Susces B',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Susces Zn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Susces Mo',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Susces Fe',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'N rate reduction',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Fall N part',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[45],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'pH opt low',5.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'pH opt high',7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Uptake N',5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Uptake P',1.2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Uptake K',3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Uptake S',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Uptake CaO',1.75),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Uptake MgO',0.8),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Susces Mn',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Susces Cu',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Susces B',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Susces Zn',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Susces Mo',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Susces Fe',NULL),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'N rate reduction',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Fall N part',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[11],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'pH opt low',6.8),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'pH opt high',7.2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Uptake N',5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Uptake P',3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Uptake K',4.1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Uptake S',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Uptake CaO',1.25),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Uptake MgO',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Susces Mn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Susces Cu',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Susces B',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Susces Zn',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Susces Mo',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Susces Fe',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'N rate reduction',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Fall N part',0.33),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[12],'Add N reduction sampling',0.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'pH opt low',6.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'pH opt high',7.1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Uptake N',7.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Uptake P',2.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Uptake K',4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Uptake S',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Uptake CaO',4.5),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Uptake MgO',0.9),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Susces Mn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Susces Cu',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Susces B',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Susces Zn',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Susces Mo',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Susces Fe',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Soil abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Soil abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Soil abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Soil abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Vol density',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Organic N',1.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Add organic N',1),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'N rate reduction',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Fertiliser abs N',0.7),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Fertiliser abs P',0.3),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Fertiliser abs K',0.4),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Fertiliser abs S',0.6),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Fall N part',0),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Sampling period',2),
            ({$modelsConfigs['Spectr Аgro']},ARRAY[10],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'pH opt low',6.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'pH opt high',7.8),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Uptake N',2.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Uptake P',1.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Uptake K',2.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Uptake S',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Uptake CaO',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Uptake MgO',0.2),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Susces Mn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Susces Cu',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Susces B',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Susces Zn',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Susces Mo',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Susces Fe',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'N rate reduction',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Fall N part',0.33),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[2],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'pH opt low',5.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'pH opt high',7.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Uptake N',2.75),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Uptake P',1.25),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Uptake K',2.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Uptake S',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Uptake CaO',0.8),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Uptake MgO',0.2),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Susces Mn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Susces Cu',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Susces B',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Susces Zn',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Susces Mo',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Susces Fe',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'N rate reduction',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Fall N part',0.33),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[5],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'pH opt low',5.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'pH opt high',7.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Uptake N',3.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Uptake P',1.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Uptake K',3.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Uptake S',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Uptake CaO',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Uptake MgO',0.15),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Susces Mn',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Susces Cu',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Susces B',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Susces Zn',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Susces Mo',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Susces Fe',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'N rate reduction',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Fall N part',0.33),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[6],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'pH opt low',6.8),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'pH opt high',7.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Uptake N',2.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Uptake P',1.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Uptake K',2.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Uptake S',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Uptake CaO',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Uptake MgO',0.15),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Susces Mn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Susces Cu',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Susces B',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Susces Zn',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Susces Mo',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Susces Fe',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'N rate reduction',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Fall N part',0.33),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[3],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'pH opt low',5),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'pH opt high',7.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Uptake N',2.8),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Uptake P',1.2),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Uptake K',3.15),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Uptake S',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Uptake CaO',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Uptake MgO',0.15),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Susces Mn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Susces Cu',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Susces B',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Susces Zn',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Susces Mo',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Susces Fe',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'N rate reduction',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Fall N part',0.33),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[4],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'pH opt low',5),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'pH opt high',6),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Uptake N',2.15),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Uptake P',1.1),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Uptake K',2.75),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Uptake S',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Uptake CaO',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Uptake MgO',0.15),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Susces Mn',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Susces Cu',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Susces B',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Susces Zn',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Susces Mo',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Susces Fe',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'N rate reduction',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Fall N part',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[9],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'pH opt low',6),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'pH opt high',7),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Uptake N',1.8),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Uptake P',1.1),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Uptake K',2.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Uptake S',0.35),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Uptake CaO',0.9),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Uptake MgO',0.25),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Susces Mn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Susces Cu',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Susces B',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Susces Zn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Susces Mo',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Susces Fe',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'N rate reduction',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Fall N part',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[7],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'pH opt low',6),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'pH opt high',7.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Uptake N',2.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Uptake P',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Uptake K',2.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Uptake S',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Uptake CaO',0.8),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Uptake MgO',0.25),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Susces Mn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Susces Cu',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Susces B',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Susces Zn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Susces Mo',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Susces Fe',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'N rate reduction',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Fall N part',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[8],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'pH opt low',6.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'pH opt high',7.1),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Uptake N',5),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Uptake P',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Uptake K',4.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Uptake S',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Uptake CaO',3.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Uptake MgO',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Susces Mn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Susces Cu',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Susces B',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Susces Zn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Susces Mo',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Susces Fe',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'N rate reduction',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Fall N part',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[15],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'pH opt low',6),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'pH opt high',7),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Uptake N',5.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Uptake P',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Uptake K',2.75),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Uptake S',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Uptake CaO',3.25),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Uptake MgO',0.65),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Susces Mn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Susces Cu',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Susces B',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Susces Zn',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Susces Mo',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Susces Fe',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'N rate reduction',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Fall N part',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[16],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'pH opt low',6.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'pH opt high',7.2),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Uptake N',5.9),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Uptake P',1.9),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Uptake K',2.75),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Uptake S',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Uptake CaO',3.25),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Uptake MgO',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Susces Mn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Susces Cu',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Susces B',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Susces Zn',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Susces Mo',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Susces Fe',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'N rate reduction',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Fall N part',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[17],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'pH opt low',7),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'pH opt high',7.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Uptake N',0.25),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Uptake P',0.2),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Uptake K',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Uptake S',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Uptake CaO',0.45),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Uptake MgO',0.25),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Susces Mn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Susces Cu',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Susces B',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Susces Zn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Susces Mo',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Susces Fe',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'N rate reduction',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Fall N part',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[21],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'pH opt low',6),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'pH opt high',6.8),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Uptake N',4),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Uptake P',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Uptake K',4),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Uptake S',0.45),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Uptake CaO',2.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Uptake MgO',1.25),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Susces Mn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Susces Cu',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Susces B',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Susces Zn',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Susces Mo',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Susces Fe',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Add organic N',1.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'N rate reduction',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Fall N part',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[14],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'pH opt low',5),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'pH opt high',5.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Uptake N',0.53),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Uptake P',0.14),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Uptake K',0.73),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Uptake S',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Uptake CaO',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Uptake MgO',0.2),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Susces Mn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Susces Cu',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Susces B',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Susces Zn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Susces Mo',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Susces Fe',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'N rate reduction',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Fall N part',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[45],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'pH opt low',5.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'pH opt high',7),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Uptake N',5),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Uptake P',1.2),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Uptake K',3),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Uptake S',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Uptake CaO',1.75),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Uptake MgO',0.8),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Susces Mn',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Susces Cu',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Susces B',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Susces Zn',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Susces Mo',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Susces Fe',NULL),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'N rate reduction',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Fall N part',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[11],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'pH opt low',6.8),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'pH opt high',7.2),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Uptake N',5),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Uptake P',3),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Uptake K',4.1),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Uptake S',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Uptake CaO',1.25),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Uptake MgO',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Susces Mn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Susces Cu',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Susces B',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Susces Zn',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Susces Mo',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Susces Fe',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'N rate reduction',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Fall N part',0.33),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[12],'Add N reduction sampling',0.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'pH opt low',6.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'pH opt high',7.1),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Uptake N',7.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Uptake P',2.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Uptake K',4),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Uptake S',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Uptake CaO',4.5),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Uptake MgO',0.9),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Susces Mn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Susces Cu',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Susces B',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Susces Zn',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Susces Mo',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Susces Fe',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Soil abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Soil abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Soil abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Soil abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Vol density',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Organic N',1.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Add organic N',1),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'N rate reduction',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Fertiliser abs N',0.7),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Fertiliser abs P',0.3),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Fertiliser abs K',0.4),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Fertiliser abs S',0.6),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Fall N part',0),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Sampling period',2),
            ({$modelsConfigs['NIK Italia']},ARRAY[10],'Add N reduction sampling',0.5)
        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
