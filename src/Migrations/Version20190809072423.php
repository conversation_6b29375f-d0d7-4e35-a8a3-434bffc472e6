<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190809072423 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // package_states
        $sql = "ALTER TABLE package_states ALTER COLUMN id SET DEFAULT nextval('package_states_id_seq'::regclass)";
        $this->addSql($sql);

        $sql = "INSERT INTO package_states (
            slug,
            service_provider_id
        )
        VALUES
            ('Waiting for plots', 1),
            ('Approve plots', 1),
            ('In progress', 1),
            ('Done', 1),
            ('Expired', 1),
            ('Activate contract', 1),
            ('Add stations', 1),
            ('Add devices', 1)
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
