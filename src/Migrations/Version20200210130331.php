<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200210130331 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE service_package_field ADD plot_uuid VARCHAR(63) DEFAULT NULL');
        $this->addSql('ALTER TABLE service_package_field ADD order_uuid VARCHAR(63) DEFAULT NULL');
        $this->addSql('ALTER TABLE service_package_field DROP field_id');
        $this->addSql('ALTER TABLE service_package_field DROP order_id');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE service_package_field ADD field_id INT NOT NULL');
        $this->addSql('ALTER TABLE service_package_field ADD order_id INT NOT NULL');
        $this->addSql('ALTER TABLE service_package_field DROP plot_uuid');
        $this->addSql('ALTER TABLE service_package_field DROP order_uuid');
    }
}
