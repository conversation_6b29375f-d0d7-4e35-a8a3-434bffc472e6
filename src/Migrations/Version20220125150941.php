<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220125150941 extends AbstractMigration
{
    private $elementColors = [
        'pH' => '#F32D2D',
        'TMN' => '#EED3F2',
        'P2O5' => '#D6D6F2',
        'K2O' => '#BE9F9D',
        'CaO' => '#ECE1BA',
        'MgO' => '#DFE3ED',
        'S' => '#FFFFB3',
        'Cu' => '#CD8637',
        'Mn' => '#9C968C',
        'Zn' => '#E7E8E9',
        'B' => '#CCFF66',
        'OM' => '#E0CEC2',
        'Fe' => '#F8A990',
        'Mo' => '#E8AABC',
        'Humus' => '#E0CEC2',
    ];

    public function getDescription(): string
    {
        return 'Seed column \'color\' in table lab_analysis_group_element.';
    }

    public function up(Schema $schema): void
    {
        foreach ($this->elementColors as $element => $color) {
            $this->addSql("
                UPDATE lab_analysis_group_element
                SET color = '{$color}'
                WHERE element = '{$element}';
            ");
        }
    }

    public function down(Schema $schema): void
    {
        foreach ($this->elementColors as $element => $color) {
            $this->addSql("
                UPDATE lab_analysis_group_element
                SET color = NULL
                WHERE element = '{$element}';
            ");
        }
    }
}
