<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210701101219 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('UPDATE lab_elements_calculations lec 
            SET template_column=t.template_column 
            FROM 
                (VALUES (\'pH\', \'\bpH\b\'),
                        (\'NO3-N\', \'\bNO3\b\'),
                        (\'NH4-N\', \'\bNH4\b\'),
                        (\'TMN\', \'\bTMN\b\'),
                        (\'P2O5\', \'\bP\b\'),
                        (\'K2O\', \'\bK\b\'),
                        (\'S\', \'\bS\b\'),
                        (\'Cu\', \'\bCu\b\'),
                        (\'Mn\', \'\bMn\b\'),
                        (\'Zn\', \'\bZn\b\'),
                        (\'B\', \'\bB\b\'),
                        (\'Ctotal\', \'\bC\b\'),
                        (\'Corg\', \'\bCorg\b\'),
                        (\'Cinorg\', \'\bCinorg\b\'),
                        (\'Acarbonates\', \'\bAcarbonates\b\'),
                        (\'EC\', \'\bEC\b\'),
                        (\'LeafN\', \'\bLeafN\b\'),
                        (\'LeafP\', \'\bLeafP\b\'),
                        (\'LeafK\', \'\bLeafK\b\'),
                        (\'LeafCa\', \'\bLeafCa\b\'),
                        (\'LeafMg\', \'\bL§eafMg\b\'),
                        (\'LeafNa\', \'\bLeafNa\b\'),
                        (\'LeafS\', \'\bLeafS\b\'),
                        (\'LeafFe\', \'\bLeafFe\b\'),
                        (\'LeafCu\', \'\bLeafCu\b\'),
                        (\'LeafZn\', \'\bLeafZn\b\'),
                        (\'LeafMn\', \'\bLeafMn\b\'),
                        (\'LeafB\', \'\bLeafB\b\'),
                        (\'MgO\', \'\bMg\b\'),
                        (\'Na2O\', \'\bNa\b\'),
                        (\'CaO\', \'\bCa\b\'),
                        (\'Fe\', \'\bFe\b\'),
                        (\'CEC\', \'\bCEC\b\'),
                        (\'BS\', \'Base Saturation\'),
                        (\'K+\', \'K\+\'),
                        (\'Ca2+\', \'Ca2\+\'),
                        (\'Mg2+\', \'Mg2\+\'),
                        (\'Na+\', \'Na\+\'),
                        (\'H+\', \'H\+\'),
                        (\'OM\', \'Organic Matter\')
                        ) AS t(element, template_column) 
            WHERE lec.element=t.element::elements_enum');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('UPDATE lab_elements_calculations lec 
            SET template_column=t.template_column 
            FROM 
                (VALUES (\'pH\', \'pH\'),
                        (\'NO3-N\', \'NO3\'),
                        (\'NH4-N\', \'NH4\'),
                        (\'TMN\', \'TMN\'),
                        (\'P2O5\', \'P\'),
                        (\'K2O\', \'K\'),
                        (\'S\', \'S\'),
                        (\'Cu\', \'Cu\'),
                        (\'Mn\', \'Mn\'),
                        (\'Zn\', \'Zn\'),
                        (\'B\', \'B\'),
                        (\'Ctotal\', \'C\'),
                        (\'Corg\', \'Corg\'),
                        (\'Cinorg\', \'Cinorg\'),
                        (\'Acarbonates\', \'Acarbonates\'),
                        (\'EC\', \'EC\'),
                        (\'LeafN\', \'LeafN\'),
                        (\'LeafP\', \'LeafP\'),
                        (\'LeafK\', \'LeafK\'),
                        (\'LeafCa\', \'LeafCa\'),
                        (\'LeafMg\', \'L§eafMg\'),
                        (\'LeafNa\', \'LeafNa\'),
                        (\'LeafS\', \'LeafS\'),
                        (\'LeafFe\', \'LeafFe\'),
                        (\'LeafCu\', \'LeafCu\'),
                        (\'LeafZn\', \'LeafZn\'),
                        (\'LeafMn\', \'LeafMn\'),
                        (\'LeafB\', \'LeafB\'),
                        (\'MgO\', \'Mg\'),
                        (\'Na2O\', \'Na\'),
                        (\'CaO\', \'Ca\'),
                        (\'Fe\', \'Fe\'),
                        (\'CEC\', \'CEC\'),
                        (\'BS\', \'Base Saturation\'),
                        (\'K+\', \'K+\'),
                        (\'Ca2+\', \'Ca2+\'),
                        (\'Mg2+\', \'Mg2+\'),
                        (\'Na+\', \'Na+\'),
                        (\'H+\', \'H+\'),
                        (\'OM\', \'Organic Matter\')
                        ) AS t(element, template_column) 
            WHERE lec.element=t.element::elements_enum');
    }
}
