<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221102131452 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add column sampling_type_id to table lab_analysis_package_group. Seed the column for the existing records. Add new records for the pgp with sampling_type_id <> 0.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE lab_analysis_package_group ADD sampling_type_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE lab_analysis_package_group ADD CONSTRAINT FK_1117BB9545C8B25E FOREIGN KEY (sampling_type_id) REFERENCES sampling_type (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_1117BB9545C8B25E ON lab_analysis_package_group (sampling_type_id)');
        $this->addSql('ALTER TABLE lab_analysis_package_group ADD CONSTRAINT lab_analysis_package_group_unique_columns UNIQUE(package_id, package_type, lab_analysis_group_id, sampling_type_id)');

        // Set the sampling_type_id for the already existing lapg
        $this->addSql('UPDATE lab_analysis_package_group SET sampling_type_id = 0');

        // Insert new lapg for the packages that have package_grid_points
        $this->addSql("INSERT INTO lab_analysis_package_group (package_id, package_type, lab_analysis_group_id, sampling_type_id)
            SELECT DISTINCT
                p.id AS package_id,
                leg.package_type,
                leg.lab_analysis_group_id,
                pgp.sampling_type_id
            FROM 
                lab_elements_results ler
            JOIN lab_element_group leg
                ON leg.id = ler.lab_element_group_id
            JOIN package_grid_points pgp
                ON pgp.id = ler.package_grid_points_id
            LEFT JOIN subscription_package sp 
                ON sp.id = leg.package_id
                AND leg.package_type = 'subscription'
            LEFT JOIN service_contract_packages scp 
                ON scp.id = leg.package_id
                AND leg.package_type = 'service'
            JOIN package p
                ON p.id = sp.package_id
                OR p.id = scp.package_id
            ON CONFLICT 
                ON CONSTRAINT lab_analysis_package_group_unique_columns 
                DO NOTHING
        ");
    }

    public function down(Schema $schema): void
    {
        // Remove the inserted lapg
        $this->addSql('DELETE FROM lab_analysis_package_group WHERE sampling_type_id <> 0');

        // Drop the new column, fk, index and unique constraint
        $this->addSql('ALTER TABLE lab_analysis_package_group DROP CONSTRAINT FK_1117BB9545C8B25E');
        $this->addSql('ALTER TABLE lab_analysis_package_group DROP CONSTRAINT lab_analysis_package_group_unique_columns');
        $this->addSql('DROP INDEX IDX_1117BB9545C8B25E');
        $this->addSql('ALTER TABLE lab_analysis_package_group DROP sampling_type_id');
    }
}
