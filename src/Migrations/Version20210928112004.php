<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210928112004 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Set column slug to nullable (true) for table lab_interpretation_classes_config.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE lab_interpretation_classes_config ALTER COLUMN slug DROP NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE lab_interpretation_classes_config ALTER COLUMN slug SET NOT NULL');
    }
}
