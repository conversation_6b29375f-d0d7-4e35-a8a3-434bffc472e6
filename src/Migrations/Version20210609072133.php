<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210609072133 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add Humus to lab_analysis_group_element ';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TYPE elements_enum ADD VALUE \'Humus\'');
        $this->addSql('INSERT INTO lab_analysis_group_element (lab_analysis_group_id, element) VALUES (
            (SELECT id FROM lab_analysis_group WHERE name = \'ABSoilOM\'), \'Humus\'
        )');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DELETE FROM lab_analysis_group_element WHERE element = \'Humus\'');
    }
}
