<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220718150853 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update function get_recommendation_fertiliser_comments_ph to support filter by sampling_type_ids.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            DROP FUNCTION IF EXISTS get_recommendation_fertiliser_comments_ph (
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                humus NUMERIC,
                yield NUMERIC,
                sampling_date DATE
            )
        ');

        $this->addSql("
            CREATE FUNCTION get_recommendation_fertiliser_comments_ph (
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                sampling_type_ids INTEGER[],
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                humus NUMERIC,
                yield NUMERIC,
                sampling_date DATE
            )
                RETURNS TABLE (rfcpc_id INTEGER, fertiliser VARCHAR, \"range\" NUMRANGE, \"values\" JSONB, comment_text TEXT)
                AS $$
                    BEGIN
                    RETURN QUERY
                        WITH ph_predominant AS (
                            SELECT
                                rfcpc.id,
                                rfcpc.fertiliser_type,
                                rfcpc.\"range\",
                                jsonb_agg(rcmcv.value) AS \"values\",
                                count(rfcpc.id) AS cnt,
                                rfcpc.comment_text
                            FROM 
                                get_recommendation_crop_model_config_values(plt_uuid, pckg_id, pckg_type, sampling_type_ids, service_provider, model, crop, ARRAY['pH']::elements_enum[]) rcmcv,
                                recommendation_fertiliser_comments_ph_config rfcpc
                            WHERE
                                (rcmcv.value)::numeric <@ rfcpc.\"range\"
                                AND rfcpc.model_id = model
                            GROUP BY
                                rfcpc.id
                        ),
                        ph_sum AS (
                            SELECT 
                                fertiliser_type,
                                sum(cnt) AS  sm
                            FROM 
                                ph_predominant
                            GROUP BY
                                fertiliser_type
                        )
                        SELECT
                            ph_predominant.id AS rfcpc_id,
                            ph_predominant.fertiliser_type,
                            ph_predominant.\"range\",
                            ph_predominant.\"values\",
                            ph_predominant.comment_text
                        FROM
                            ph_predominant
                        JOIN ph_sum
                            ON ph_sum.fertiliser_type = ph_predominant.fertiliser_type
                        JOIN lab_element_aggregation_area_value_config AS legavc
                            ON legavc.service_provider_id = service_provider
                        JOIN recommendation_element_comments_config recc
                            ON recc.fertiliser_type = ph_predominant.fertiliser_type
                            AND recc.model_id = model
                            AND (crop = ANY(recc.crop_ids) OR  recc.crop_ids isnull)
                        JOIN get_recommendation_elements_results(plt_uuid, pckg_id, pckg_type, sampling_type_ids, service_provider, model, crop, humus, yield, sampling_date) as rer
                            ON rer.result_element = recc.result_element
                            AND rer.value <@ recc.\"range\"
                        WHERE cnt/sm >= legavc.area_treshold;
                    END
                $$ LANGUAGE plpgsql
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DROP FUNCTION IF EXISTS get_recommendation_fertiliser_comments_ph (
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                sampling_type_ids INTEGER[],
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                humus NUMERIC,
                yield NUMERIC,
                sampling_date DATE
            )
        ');

        $this->addSql("
            CREATE FUNCTION get_recommendation_fertiliser_comments_ph (
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                humus NUMERIC,
                yield NUMERIC,
                sampling_date DATE
            )
                RETURNS TABLE (rfcpc_id INTEGER, fertiliser VARCHAR, \"range\" NUMRANGE, \"values\" JSONB, comment_text TEXT)
                AS $$
                    BEGIN
                    RETURN QUERY
                        WITH ph_predominant AS (
                            SELECT
                                rfcpc.id,
                                rfcpc.fertiliser_type,
                                rfcpc.\"range\",
                                jsonb_agg(rcmcv.value) AS \"values\",
                                count(rfcpc.id) AS cnt,
                                rfcpc.comment_text
                            FROM 
                                get_recommendation_crop_model_config_values(plt_uuid, pckg_id, pckg_type, service_provider, model, crop, ARRAY['pH']::elements_enum[]) rcmcv,
                                recommendation_fertiliser_comments_ph_config rfcpc
                            WHERE
                                (rcmcv.value)::numeric <@ rfcpc.\"range\"
                                AND rfcpc.model_id = model
                            GROUP BY
                                rfcpc.id
                        ),
                        ph_sum AS (
                            SELECT 
                                fertiliser_type,
                                sum(cnt) AS  sm
                            FROM 
                                ph_predominant
                            GROUP BY
                                fertiliser_type
                        )
                        SELECT
                            ph_predominant.id AS rfcpc_id,
                            ph_predominant.fertiliser_type,
                            ph_predominant.\"range\",
                            ph_predominant.\"values\",
                            ph_predominant.comment_text
                        FROM
                            ph_predominant
                        JOIN ph_sum
                            ON ph_sum.fertiliser_type = ph_predominant.fertiliser_type
                        JOIN lab_element_aggregation_area_value_config AS legavc
                            ON legavc.service_provider_id = service_provider
                        JOIN recommendation_element_comments_config recc
                            ON recc.fertiliser_type = ph_predominant.fertiliser_type
                            AND recc.model_id = model
                            AND (crop = ANY(recc.crop_ids) OR  recc.crop_ids isnull)
                        JOIN get_recommendation_elements_results(plt_uuid, pckg_id, pckg_type, service_provider, model, crop, humus, yield, sampling_date) as rer
                            ON rer.result_element = recc.result_element
                            AND rer.value <@ recc.\"range\"
                        WHERE cnt/sm >= legavc.area_treshold;
                    END
                $$ LANGUAGE plpgsql
        ");
    }
}
