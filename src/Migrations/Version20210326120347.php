<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210326120347 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create start_date and end_date column in subscription_packages and a script to fill them out.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE subscription_package ADD start_date timestamp NOT null DEFAULT now()');
        $this->addSql('ALTER TABLE subscription_package ADD end_date timestamp NOT null DEFAULT now()');

        $this->addSql('
            update subscription_package as sp
            set start_date = new_date.start_date::timestamp, end_date = new_date.end_date::timestamp
            from (
            select sp.id as pacakge_id, sp."period" as package_period,
            case 
                when sp.period = 1 then  TO_CHAR(c.start_date, \'YYYY-mm-dd 00:00:00\')
                when dt.is_calendar_period = false and sp.period > 1 then  TO_CHAR((c.start_date + interval \'1 year\'*(sp.period - 1)), \'YYYY-mm-dd 00:00:00\')
                when dt.is_calendar_period = true  and sp.period > 1 then  TO_CHAR(c.start_date + ((sp."period" - 1)||\' \'||dt.slug)::interval, \'YYYY-mm-dd 00:00:00\')
                end start_date,
            case 
                when dt.is_calendar_period = true  and (dt.slug like \'month\' or dt.slug like \'year\') and (sc.duration = sp.period) then  TO_CHAR(c.end_date, \'YYYY-mm-dd 23:59:59\')
                when dt.is_calendar_period = true  then TO_CHAR((c.start_date - interval \'1 day\') + (sp."period"||\' \'||dt.slug)::interval, \'YYYY-mm-dd 23:59:59\')
                when dt.is_calendar_period = false then TO_CHAR((c.start_date - interval \'1 day\') + interval \'1 year\'*sp.period, \'YYYY-mm-dd 23:59:59\')
                end end_date
            from subscription_package sp
            join subscription_contracts sc on sc.id=sp.contract_id
            join duration_type dt on dt.id =sc.duration_type_id 
            join contract c on c.id =sc.id
            ) as new_date
            where new_date.pacakge_id = sp.id;
    ');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE subscription_package DROP start_date');
        $this->addSql('ALTER TABLE subscription_package DROP end_date');
    }
}
