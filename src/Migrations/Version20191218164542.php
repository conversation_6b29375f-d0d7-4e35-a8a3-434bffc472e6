<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191218164542 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SEQUENCE lab_elements_calculations_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE lab_elements_calculations (id INT NOT NULL, element elements_enum, coefficient DOUBLE PRECISION NOT NULL, operation VARCHAR(27) NOT NULL, template_column VARCHAR(27) DEFAULT NULL, PRIMARY KEY(id))');

        // Add lab_elements_calculations data
        $sql = "ALTER TABLE lab_elements_calculations ALTER COLUMN id SET DEFAULT nextval('lab_elements_calculations_id_seq'::regclass)";
        $this->addSql($sql);

        $sql = "INSERT INTO lab_elements_calculations (
            element, coefficient, operation, template_column
        )
        VALUES
            ('pH', 1, '*', 'pH'),
            ('NO3-N', 1, '*', 'NO3'),
            ('NH4-N', 1, '*', 'NH4'),
            ('TMN', 1, '*', 'TMN'),
            ('P2O5', 2.3, '*', 'P'),
            ('K2O', 1.2, '*', 'K'),
            ('Ca', 0.7, '/', 'Ca'),
            ('Mg', 0.6, '/', 'Mg'),
            ('Na', 0.74, '/', 'Na'),
            ('S', 1, '*', 'S'),
            ('Cu', 1, '*', 'Cu'),
            ('Mn', 1, '*', 'Mn'),
            ('Zn', 1, '*', 'Zn'),
            ('B', 1, '*', 'B'),
            ('Ctotal', 1, '*', 'C'),
            ('Corg', 1, '*', 'Corg'),
            ('Cinorg', 1, '*', 'Cinorg'),
            ('Acarbonates', 1, '*', 'Acarbonates'),
            ('EC', 1, '*', 'EC'),
            ('LeafN', 1, '*', 'LeafN'),
            ('LeafP', 10000, '/', 'LeafP'),
            ('LeafK', 10000, '/', 'LeafK'),
            ('LeafCa', 10000, '/', 'LeafCa'),
            ('LeafMg', 10000, '/', 'LeafMg'),
            ('LeafNa', 10000, '/', 'LeafNa'),
            ('LeafS', 10000, '/', 'LeafS'),
            ('LeafFe', 1, '*', 'LeafFe'),
            ('LeafCu', 1, '*', 'LeafCu'),
            ('LeafZn', 1, '*', 'LeafZn'),
            ('LeafMn', 1, '*', 'LeafMn'),
            ('LeafB', 1, '*', 'LeafB')
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SCHEMA public');
        $this->addSql('DROP SEQUENCE lab_elements_calculations_id_seq CASCADE');
        $this->addSql('DROP TABLE lab_elements_calculations');
    }
}
