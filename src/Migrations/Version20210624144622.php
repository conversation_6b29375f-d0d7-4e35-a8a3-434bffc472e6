<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210624144622 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Set column comment_text notnull in table recommendation_element_comments_config. Seed the table (Add Add_Mn, Add_B, Add_Cu, Add_Fe, Add_Zn).';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE recommendation_element_comments_config ALTER COLUMN comment_text DROP NOT NULL');
        $this->addSql("
            INSERT INTO recommendation_element_comments_config (result_element, \"range\", model_id, fertiliser_type, crop_ids, comment_text) VALUES
            ('Add_Mn'::result_element_enum, '(,0]'::numrange, 1, NULL, NULL, 'Преобладаващото съдържание на елемента Mn е ниско. Въпреки това отглежданата култура не е чувствителна към недостиг на този хранителен елемент и е малко вероятно да реагира при допълнителното му осигуряване.'),
            ('Add_B'::result_element_enum, '(,0]'::numrange, 1, NULL, NULL, 'Преобладаващото съдържание на елемента B е ниско. Въпреки това отглежданата култура не е чувствителна към недостиг на този хранителен елемент и е малко вероятно да реагира при допълнителното му осигуряване.'),
            ('Add_Cu'::result_element_enum, '(,0]'::numrange, 1, NULL, NULL, 'Преобладаващото съдържание на елемента Cu е ниско. Въпреки това отглежданата култура не е чувствителна към недостиг на този хранителен елемент и е малко вероятно да реагира при допълнителното му осигуряване.'),
            ('Add_Fe'::result_element_enum, '(,0]'::numrange, 1, NULL, NULL, 'Преобладаващото съдържание на елемента Fe е ниско. Въпреки това отглежданата култура не е чувствителна към недостиг на този хранителен елемент и е малко вероятно да реагира при допълнителното му осигуряване.'),
            ('Add_Zn'::result_element_enum, '(,0]'::numrange, 1, NULL, NULL, 'Преобладаващото съдържание на елемента Zn е ниско. Въпреки това отглежданата култура не е чувствителна към недостиг на този хранителен елемент и е малко вероятно да реагира при допълнителното му осигуряване.'),
            ('Add_Mn'::result_element_enum, '(0,)'::numrange, 1, NULL, NULL, NULL),
            ('Add_B'::result_element_enum, '(0,)'::numrange, 1, NULL, NULL, NULL),
            ('Add_Cu'::result_element_enum, '(0,)'::numrange, 1, NULL, NULL, NULL),
            ('Add_Fe'::result_element_enum, '(0,)'::numrange, 1, NULL, NULL, NULL),
            ('Add_Zn'::result_element_enum, '(0,)'::numrange, 1, NULL, NULL, NULL)
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql("
            DELETE FROM recommendation_element_comments_config
            WHERE result_element in (
                'Add_Mn',
                'Add_B',
                'Add_Cu',
                'Add_Fe',
                'Add_Zn'
            )
        ");
        $this->addSql('ALTER TABLE recommendation_element_comments_config ALTER COLUMN comment_text SET NOT NULL');
    }
}
