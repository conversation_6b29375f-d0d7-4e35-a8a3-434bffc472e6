<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210826091245 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Delete all records from recommendation_element_comments_config table and insert new.';
    }

    public function up(Schema $schema): void
    {
        // Delete all records.
        $this->addSql('DELETE FROM recommendation_element_comments_config');

        // Fetch recommendation models configs
        $modelsConfigs = $this->getRecommendationModelsConfigs();

        $this->addSql("
            INSERT INTO recommendation_element_comments_config (model_id,crop_ids,result_element, \"range\", fertiliser_type, comment_text) VALUES
            ({$modelsConfigs['BG']}, ARRAY[5,6,10,11,12,14,15,18,19], 'Add_N_fall', '(,0]', NULL, 'Съдържанието на минерален азот в почвата е достатъчно, за да осигури добро развитие на културата през есенно-зимния период и не е необходимо да се внасят допълнителни количества. '),
            ({$modelsConfigs['BG']}, ARRAY[5,6,10,11,12,14,15,18,19], 'Add_N_fall', '(0,]', 'N', 'За осигуряване на добро развитие на културата през есенно-зимния период е необходимо част от азотната норма да се внесе предсеитбено/присеитбено.'),
            ({$modelsConfigs['BG']}, ARRAY[5,6,10,11,12,14,15,18,20], 'Add_N_total', '(0,]', 'N', 'Препоръчително е необходимото за пролетно-летния период количество азот да се раздели на две части, с които да се извършат подхранвания. Желателно е първото подхранване да се извърши при възобновяване на вегетацията в края на зимата, а второто – от фаза „вретенене“ до „начало на изкласяване“.'),
            ({$modelsConfigs['BG']}, ARRAY[22], 'Add_N_fall', '(,0]', NULL, 'Съдържанието на минерален азот в почвата е достатъчно, за да осигури добро развитие на културата през есенно-зимния период и не е необходимо да се внасят допълнителни количества.'),
            ({$modelsConfigs['BG']}, ARRAY[22], 'Add_N_fall', '(0,]', 'N', 'За осигуряване на добро развитие на културата през есенно-зимния период е необходимо част от азотната норма да се внесе предсеитбено/присеитбено.'),
            ({$modelsConfigs['BG']}, ARRAY[22], 'Add_N_total', '(0,]', 'N', ' Препоръчително е необходимото за пролетно-летния период количество азот да се раздели на две части, с които да се извършат подхранвания. Желателно е първото подхранване да се извърши при възобновяване на вегетацията в края на зимата, а второто – от фаза „вретенене“ до „начало на изметляване“.'),
            ({$modelsConfigs['BG']}, ARRAY[52,53], 'Add_N_fall', '(,0]', NULL, 'Съдържанието на минерален азот в почвата е достатъчно, за да осигури добро развитие на културата през есенно-зимния период и не е необходимо да се внасят допълнителни количества. Препоръчително е необходимото за пролетно-летния период количество азот да се раздели на две части, с които да се извършат подхранвания. Желателно е първото подхранване да се извърши при възобновяване на вегетацията в края на зимата, а второто във фаза \"зелен бутон\".'),
            ({$modelsConfigs['BG']}, ARRAY[52,53], 'Add_N_fall', '(0,]', 'N', 'За осигуряване на добро развитие на културата през есенно-зимния период е необходимо част от азотната норма да се внесе предсеитбено/присеитбено. '),
            ({$modelsConfigs['BG']}, ARRAY[52,53], 'Add_N_total', '(0,]', 'N', 'Препоръчително е необходимото за пролетно-летния период количество азот да се раздели на две части, с които да се извършат подхранвания. Желателно е първото подхранване да се извърши при възобновяване на вегетацията, в края на зимата, а второто във фаза \"зелен бутон\".'),
            ({$modelsConfigs['BG']}, ARRAY[23,26], 'Add_N_total', '(0,]', 'N', 'Препоръчваме азотната норма да се раздели на две части и едната да се внесе предсеитбено/присеитбено, а с другата да се извърши подхранване от фаза „вретенене“ до „начало на изметляване“.'),
            ({$modelsConfigs['BG']}, ARRAY[24,25], 'Add_N_total', '(0,]', 'N', 'За осигуряване на равномерно хранене на растенията през цялата вегетация и обезпечаването им в критичните фази, препоръчваме азотната норма да се раздели на две части. Желателно е половината да се внесе предсеитбено/присеитбено, а с останалото количество да се извърши подхранване във фаза \"6-ти - 8-ми лист\".'),
            ({$modelsConfigs['BG']}, ARRAY[17,80], 'Add_N_total', '(0,]', 'N', 'За осигуряване на равномерно хранене на растенията през цялата вегетация и обезпечаването им в критичните фази, препоръчваме азотната норма да се раздели на две части. Желателно е половината да се внесе предсеитбено/присеитбено, а с останалото количество да се извърши подхранване във фаза \"6-ти - 8-ми лист\".'),
            ({$modelsConfigs['BG']}, ARRAY[50], 'Add_N_total', '(0,]', 'N', 'За осигуряване на равномерно хранене на растенията през цялата вегетация и обезпечаването им в критичните фази, препоръчваме азотната норма да се раздели на две части. Желателно е половината да се внесе предсеитбено/присеитбено, а с останалото количество да се извърши подхранване възможно по-близо до настъпване на фаза „начало на бутонизация“'),
            ({$modelsConfigs['BG']}, ARRAY[29,30,33,34,35,36], 'Add_N_total', '(0,]', 'N', 'Препоръчително е цялата азотна норма да се внесе предсеитбено, за да бъдат осигурени растенията в началните етапи от развитието си, докато грудковите бактерии все още не са разгърнали целия си азотфиксиращ потенциал.'),
            ({$modelsConfigs['BG']}, ARRAY[56], 'Add_N_total', '(0,]', 'N', 'Препоръчително е азотната норма да се раздели на две части, като едната се внесе предсеитбено, за да бъдат осигурени растенията в началните етапи от развитието си, когато грудковите бактерии все още не са разгърнали целия си азотфиксиращ потенциал. Препоръчително е с останалата част да се извърши подхранване при загърлянето им.'),
            ({$modelsConfigs['BG']}, ARRAY[61], 'Add_N_total', '(0,]', 'N', 'Препоръчително е азотната норма да се раздели на две части, като 1/3 се внесе предсеитбено, а с останалото количество да се извърши подхранване, възможно по-близо до настъпване на фаза \"начало на бутонизация\", когато нуждите на растенията от азот рязко нарастват.'),
            ({$modelsConfigs['BG']}, ARRAY[156], 'Add_N_total', '(0,]', 'N', 'Препоръчително е азотната норма да се раздели на две части, като половината се внесе преди засаждането, а с осталата част се извърши подхранване, заедно с окопаването на културата.'),
            ({$modelsConfigs['BG']}, NULL, 'Add_P_total', '(,0]', NULL, 'Запасите на фосфор са достатъчни за обезпечаване на целевите добиви и не е необходимо да се внасят допълнителни количества.'),
            ({$modelsConfigs['BG']}, NULL, 'Add_P_total', '(0,]', 'P', 'Препоръчително е цялата фосфорна норма да се внесе с основните обработки на почвата или предсеитбено.'),
            ({$modelsConfigs['BG']}, NULL, 'Add_K_total', '(,0]', NULL, 'Запасите на калий са достатъчни за обезпечаване на целевите добиви и не е необходимо да се внасят допълнителни количества.'),
            ({$modelsConfigs['BG']}, NULL, 'Add_K_total', '(0,]', 'K', 'Препоръчително е цялата калиева норма да се внесе с основните обработки на почвата или предсеитбено.'),
            ({$modelsConfigs['BG']}, ARRAY[156], 'Add_K_total', '(0,]', 'K', 'Предвид чувствителността на културата към хлор, за осигуряване на необходимите количества калий не се препоръчва употребата на калиев хлорид. Подходящ източник е калиев сулфат.'),
            ({$modelsConfigs['BG']}, NULL, 'Add_Mn', '(,0]', NULL, 'Преобладаващото съдържание на елемента Mn е ниско. Въпреки това отглежданата култура не е чувствителна към недостиг на този хранителен елемент и е малко вероятно да реагира при допълнителното му осигуряване.'),
            ({$modelsConfigs['BG']}, NULL, 'Add_B', '(,0]', NULL, 'Преобладаващото съдържание на елемента B е ниско. Въпреки това отглежданата култура не е чувствителна към недостиг на този хранителен елемент и е малко вероятно да реагира при допълнителното му осигуряване.'),
            ({$modelsConfigs['BG']}, NULL, 'Add_Cu', '(,0]', NULL, 'Преобладаващото съдържание на елемента Cu е ниско. Въпреки това отглежданата култура не е чувствителна към недостиг на този хранителен елемент и е малко вероятно да реагира при допълнителното му осигуряване.'),
            ({$modelsConfigs['BG']}, NULL, 'Add_Fe', '(,0]', NULL, 'Преобладаващото съдържание на елемента Fe е ниско. Въпреки това отглежданата култура не е чувствителна към недостиг на този хранителен елемент и е малко вероятно да реагира при допълнителното му осигуряване.'),
            ({$modelsConfigs['BG']}, NULL, 'Add_Zn', '(,0]', NULL, 'Преобладаващото съдържание на елемента Zn е ниско. Въпреки това отглежданата култура не е чувствителна към недостиг на този хранителен елемент и е малко вероятно да реагира при допълнителното му осигуряване.'),
            ({$modelsConfigs['BG']}, NULL, 'Add_Mn', '(0,)', NULL, NULL),
            ({$modelsConfigs['BG']}, NULL, 'Add_B', '(0,)', NULL, NULL),
            ({$modelsConfigs['BG']}, NULL, 'Add_Cu', '(0,)', NULL, NULL),
            ({$modelsConfigs['BG']}, NULL, 'Add_Fe', '(0,)', NULL, NULL),
            ({$modelsConfigs['BG']}, NULL, 'Add_Zn', '(0,)', NULL, NULL),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[3,6,5,2], 'Add_N_fall', '(,0]', NULL, 'Визначений рівень азоту в грунті є достатнім для осінньо-зимового періоду та для забезпечення ранньовесняного розвитку. Відтак осіннє внесення азоту не є обовязковим.'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[3,6,5,2], 'Add_N_fall', '(0,]', 'N', 'Для забезпечення азотного живлення в осіннє-зимовий період та при відновленні вегетації необхідно внести азот перед посівом або підчас посіву.'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[3,6,5,2], 'Add_N_total', '(0,]', 'N', 'Весняне підживлення азотом проводити в два прийоми: перше - ближче до відновлення вегетації, друге - у фазу кущення та найпізніше у фазу виходу прапорцевого листка до виходу колоса'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[4], 'Add_N_fall', '(,0]', NULL, 'Визначений рівень мінерального азоту в грунті є достатнім для осінньо-зимового періоду та для забезпечення ранньовесняного розвитку. Відтак осіннє внесення азоту не є обовязковим.'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[4], 'Add_N_fall', '(0,]', 'N', 'Для забезпечення азотного живлення в осіннє-зимовий період та при відновленні вегетації необхідне внести азот перед посівом або підчас посіву.'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[4], 'Add_N_total', '(0,]', 'N', ' Весняне підживлення азотом проводити в два прийоми: перше - ближче до відновлення вегетації, друге - у фазу кущення та найпізніше у фазу виходу прапорцевого листка до виходу колоса'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[12], 'Add_N_fall', '(,0]', NULL, 'Встановлений рівень мінерального азоту в грунті є достатнім для осінньо-зимового періоду та для забезпечення ранньовесняного розвитку. Відтак осіннє внесення азоту не є обовязковим. '),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[12], 'Add_N_fall', '(0,]', 'N', 'Для забезпечення азотного живлення в осіннє-зимовий період та при відновленні вегетації необхідне внести азот перед посівом або підчас посіву.'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[12], 'Add_N_total', '(0,]', 'N', 'Весняне підживлення азотом проводити в два прийоми: перше - ближче до відновлення вегетації, друге - у фазу кущення та найпізніше у фазу виходу прапорцевого листка до виходу колоса'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[9], 'Add_N_total', '(0,]', 'N', 'Азот бажано вносити в декілька прийомів: перше внесення - при посіві, друге - у фазу кущення до появи колоса'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[8], 'Add_N_total', '(0,]', 'N', 'Для забезпечення азотного живлення культури впродовж сезону та в критичні фази розвитку загальну потребу в азоті закривати у два прийоми: перший -перед посівом або підчас посіву, другий - у фазу 6-8 листків'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[7], 'Add_N_total', '(0,]', 'N', 'Для забезпечення азотного живлення культури впродовж сезону та в критичні фази розвитку загальну потребу в азоті закривати у два прийоми: перший -перед посівом або підчас посіву, другий - у фазу 6-8 листків'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[14], 'Add_N_total', '(0,]', 'N', 'Для забезпечення азотного живлення культури впродовж сезону та в критичні фази розвитку потребу в азоті закривати у два прийоми: перший - перед посівом або підчас посіву, другий - перед початком бутонізації'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[15, 16, 17], 'Add_N_total', '(0,]', 'N', 'Азот рекомендовано вносити перед посівом або підчас посіву задля забезпечення азотного живлення на початкових стадіях росту до початку активної роботи азотофіксуючих бактерій.'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[11], 'Add_N_total', '(0,]', 'N', 'Азот бажано вносити в декілька прийомів: перше внесення - при посіві або до посіву для забезпечення азотного живлення на початкових стадіях росту до початку активної роботи азотофіксуючих бактерій. Друге внесення азоту робити перед підсипанням'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[45], 'Add_N_total', '(0,]', 'N', 'Азот бажано вносити в декілька прийомів. Перше внесення - перед висаджуванням або при висаджуванні. Друге - перед або під час підсипання'),
            ({$modelsConfigs['Spectr Аgro']}, NULL, 'Add_P_total', '(,0]', NULL, 'Рівень доступного фосфору відповідно до результатів аналізу є достатнім для отримання запланованої врожайності. Відтак внесення фосфорних добрив не є обовязковим.'),
            ({$modelsConfigs['Spectr Аgro']}, NULL, 'Add_P_total', '(0,]', 'P', 'Фосфор рекомендовано вносити перед або підчас посіву із заробленням в грунт.'),
            ({$modelsConfigs['Spectr Аgro']}, NULL, 'Add_K_total', '(,0]', NULL, 'Рівень обмінного калію відповідно до результатів аналізу є достатнім для отримання запланованої врожайності. Відтак внесення калійних добрив не є обовязковим.'),
            ({$modelsConfigs['Spectr Аgro']}, NULL, 'Add_K_total', '(0,]', 'K', 'Калій рекомендовано вносити перед або підчас посіву із заробленням в грунт.'),
            ({$modelsConfigs['Spectr Аgro']}, ARRAY[45], 'Add_K_total', '(0,]', 'K', 'Зважаючи на чутливість кільтури до рівня хлору в грунті, використання калію хлористого небажане. Використовуйте переважно сульфат калію.'),
            ({$modelsConfigs['Spectr Аgro']}, NULL, 'Add_Mn', '(,0]', NULL, NULL),
            ({$modelsConfigs['Spectr Аgro']}, NULL, 'Add_B', '(,0]', NULL, NULL),
            ({$modelsConfigs['Spectr Аgro']}, NULL, 'Add_Cu', '(,0]', NULL, NULL),
            ({$modelsConfigs['Spectr Аgro']}, NULL, 'Add_Fe', '(,0]', NULL, NULL),
            ({$modelsConfigs['Spectr Аgro']}, NULL, 'Add_Zn', '(,0]', NULL, NULL),
            ({$modelsConfigs['Spectr Аgro']}, NULL, 'Add_Mn', '(0,)', NULL, NULL),
            ({$modelsConfigs['Spectr Аgro']}, NULL, 'Add_B', '(0,)', NULL, NULL),
            ({$modelsConfigs['Spectr Аgro']}, NULL, 'Add_Cu', '(0,)', NULL, NULL),
            ({$modelsConfigs['Spectr Аgro']}, NULL, 'Add_Fe', '(0,)', NULL, NULL),
            ({$modelsConfigs['Spectr Аgro']}, NULL, 'Add_Zn', '(0,)', NULL, NULL),
            ({$modelsConfigs['NIK Italia']}, ARRAY[3,6,5,2], 'Add_N_fall', '(,0]', NULL, 'La quantità misurata di azoto minerale nel suolo è stimata come sufficiente per soddisfare il fabbisogno di azoto delle colture durante il periodo autunno-inverno e per contribuire al buon sviluppo delle prime colture. Per questo motivo non è necessaria una modifica dell''azoto in autunno. '),
            ({$modelsConfigs['NIK Italia']}, ARRAY[3,6,5,2], 'Add_N_fall', '(0,]', 'N', 'Per soddisfare il fabbisogno di azoto delle colture durante il periodo autunno-inverno e per contribuire al buon sviluppo delle prime colture, si raccomanda di applicare una parte del tasso totale di fertilizzante azotato prima o al momento della semina.'),
            ({$modelsConfigs['NIK Italia']}, ARRAY[3,6,5,2], 'Add_N_total', '(0,]', 'N', 'Il tasso di concimazione azotata calcolato per il periodo primavera-estate è raccomandato per essere fornito attraverso due applicazioni di azoto superficiale: la prima - vicino al rinverdimento primaverile e la seconda - durante la giuntura (allungamento dello stelo) e l''ultima all''avvio (prima della comparsa della spiga).'),
            ({$modelsConfigs['NIK Italia']}, ARRAY[4], 'Add_N_fall', '(,0]', NULL, 'La quantità misurata di azoto minerale nel suolo è stimata come sufficiente per soddisfare il fabbisogno di azoto delle colture durante il periodo autunno-inverno e per contribuire al buon sviluppo delle prime colture. Per questo motivo non è necessaria una modifica dell''azoto in autunno.'),
            ({$modelsConfigs['NIK Italia']}, ARRAY[4], 'Add_N_fall', '(0,]', 'N', 'Per soddisfare il fabbisogno di azoto delle colture durante il periodo autunno-inverno e per contribuire al buon sviluppo delle prime colture, si raccomanda di applicare una parte del tasso totale di fertilizzante azotato prima o al momento della semina.'),
            ({$modelsConfigs['NIK Italia']}, ARRAY[4], 'Add_N_total', '(0,]', 'N', ' Il tasso di concimazione azotata calcolato per il periodo primavera-estate è raccomandato per essere fornito attraverso due applicazioni di azoto superficiale: la prima - vicino al verde primaverile e la seconda - durante la giuntura (allungamento del gambo) e più tardi al booting (prima della comparsa del baccello).'),
            ({$modelsConfigs['NIK Italia']}, ARRAY[12], 'Add_N_fall', '(,0]', NULL, 'La quantità misurata di azoto minerale nel suolo è stimata come sufficiente per soddisfare il fabbisogno di azoto delle colture durante il periodo autunno-inverno e per contribuire al buon sviluppo delle prime colture. Per questo motivo non è necessaria una modifica dell''azoto in autunno. '),
            ({$modelsConfigs['NIK Italia']}, ARRAY[12], 'Add_N_fall', '(0,]', 'N', 'Per soddisfare il fabbisogno di azoto delle colture durante il periodo autunno-inverno e per contribuire al buon sviluppo delle prime colture, si raccomanda di applicare una parte del tasso totale di fertilizzante azotato prima o al momento della semina.'),
            ({$modelsConfigs['NIK Italia']}, ARRAY[12], 'Add_N_total', '(0,]', 'N', 'Il tasso di concimazione azotata calcolato per il periodo primavera-estate è raccomandato per essere fornito attraverso due applicazioni di azoto superficiale: la prima - alla fine della dormienza invernale e la seconda - nella fase di pre-bullonatura.'),
            ({$modelsConfigs['NIK Italia']}, ARRAY[9], 'Add_N_total', '(0,]', 'N', 'Si raccomanda di dividere il tasso di concimazione azotata calcolato: la prima applicazione - al momento della semina e la seconda - durante la giuntura (allungamento dello stelo) e l''ultima alla partenza (prima della comparsa delle pannocchie).'),
            ({$modelsConfigs['NIK Italia']}, ARRAY[8], 'Add_N_total', '(0,]', 'N', 'Per fornire un apporto continuo di azoto alla coltura durante la stagione di crescita e per soddisfare le esigenze di azoto delle colture nelle fasi di crescita critica, si raccomanda di dividere il tasso totale di fertilizzante azotato in due applicazioni: la prima metà - prima dell''impianto e la seconda - al 6° - 8° stadio fogliare.'),
            ({$modelsConfigs['NIK Italia']}, ARRAY[7], 'Add_N_total', '(0,]', 'N', 'Per fornire un apporto continuo di azoto alla coltura durante la stagione di crescita e per soddisfare le esigenze di azoto delle colture nelle fasi di crescita critica, si raccomanda di dividere il tasso totale di fertilizzante azotato in due applicazioni: la prima metà - prima dell''impianto e la seconda - al 6° - 8° stadio fogliare.'),
            ({$modelsConfigs['NIK Italia']}, ARRAY[14], 'Add_N_total', '(0,]', 'N', 'Per fornire un apporto continuo di azoto alla coltura durante la stagione di crescita e per soddisfare il fabbisogno di azoto delle colture nelle fasi critiche della crescita, si raccomanda di dividere il tasso totale di fertilizzante azotato in due applicazioni: la prima metà - prima o al momento della piantagione e la seconda - durante la crescita vegetativa, prima dell''incipiente (fase delle gemme).'),
            ({$modelsConfigs['NIK Italia']}, ARRAY[15, 16, 17], 'Add_N_total', '(0,]', 'N', 'Il tasso di fertilizzante azotato totale è raccomandato come applicazione di base, prima o al momento della semina, al fine di soddisfare le esigenze di azoto delle colture durante la prima crescita vegetativa, prima che i batteri azotofissatori associati alle radici abbiano raggiunto il loro pieno potenziale.'),
            ({$modelsConfigs['NIK Italia']}, ARRAY[11], 'Add_N_total', '(0,]', 'N', 'Si raccomanda di dividere il tasso totale di fertilizzante azotato in due applicazioni. La prima - prima o al momento della piantagione, al fine di soddisfare le esigenze di azoto delle colture durante la prima crescita vegetativa, prima che i batteri azotofissatori associati alle radici abbiano raggiunto il loro pieno potenziale. La seconda è raccomandabile da applicare lateralmente appena prima della semina.'),
            ({$modelsConfigs['NIK Italia']}, ARRAY[45], 'Add_N_total', '(0,]', 'N', 'Si raccomanda di dividere il tasso totale di fertilizzante azotato in due applicazioni. La prima metà - prima o al momento della semina. La seconda è raccomandabile per essere applicata lateralmente poco prima o al momento della lavorazione del terreno.'),
            ({$modelsConfigs['NIK Italia']}, NULL, 'Add_P_total', '(,0]', NULL, 'La concentrazione di fosforo prontamente disponibile, misurato dal test del suolo, è stimato come sufficiente a soddisfare i requisiti delle colture per la resa mirata e per questo scopo non è necessaria la correzione del fosforo.'),
            ({$modelsConfigs['NIK Italia']}, NULL, 'Add_P_total', '(0,]', 'P', 'L''applicazione del fosforo è raccomandata come dose basale prima o al momento della semina, con incorporazione nel suolo.'),
            ({$modelsConfigs['NIK Italia']}, NULL, 'Add_K_total', '(,0]', NULL, 'L''applicazione del potassio prontamente disponibile, misurato dal test del suolo, è stimato come sufficiente a soddisfare i requisiti delle colture per la resa prefissata e per questo scopo la modifica del potassio non è necessaria.'),
            ({$modelsConfigs['NIK Italia']}, NULL, 'Add_K_total', '(0,]', 'K', 'L''applicazione di potassio è raccomandata come dose basale prima o al momento della semina, con incorporazione al suolo.'),
            ({$modelsConfigs['NIK Italia']}, ARRAY[45], 'Add_K_total', '(0,]', 'K', 'A causa della sensibilità della coltura all''aumento del contenuto di cloruro del suolo, l''applicazione di cloruro di potassio (MOP) non è raccomandabile. Una fonte favorevole è il solfato di potassio (SOP).'),
            ({$modelsConfigs['NIK Italia']}, NULL, 'Add_Mn', '(,0]', NULL, NULL),
            ({$modelsConfigs['NIK Italia']}, NULL, 'Add_B', '(,0]', NULL, NULL),
            ({$modelsConfigs['NIK Italia']}, NULL, 'Add_Cu', '(,0]', NULL, NULL),
            ({$modelsConfigs['NIK Italia']}, NULL, 'Add_Fe', '(,0]', NULL, NULL),
            ({$modelsConfigs['NIK Italia']}, NULL, 'Add_Zn', '(,0]', NULL, NULL),
            ({$modelsConfigs['NIK Italia']}, NULL, 'Add_Mn', '(0,)', NULL, NULL),
            ({$modelsConfigs['NIK Italia']}, NULL, 'Add_B', '(0,)', NULL, NULL),
            ({$modelsConfigs['NIK Italia']}, NULL, 'Add_Cu', '(0,)', NULL, NULL),
            ({$modelsConfigs['NIK Italia']}, NULL, 'Add_Fe', '(0,)', NULL, NULL),
            ({$modelsConfigs['NIK Italia']}, NULL, 'Add_Zn', '(0,)', NULL, NULL),
            ({$modelsConfigs['Vantage']}, ARRAY[3,6,5,2], 'Add_N_fall', '(,0]', NULL, 'Cantitatea măsurată de azot mineral din sol este estimată ca fiind suficientă pentru a satisface cerințele culturii în perioada de toamnă-iarnă și pentru a contribui la o bună dezvoltare timpurie a culturii. Din acest motiv, nu este necesară aplicarea azotului in toamnă. '),
            ({$modelsConfigs['Vantage']}, ARRAY[3,6,5,2], 'Add_N_fall', '(0,]', 'N', 'Pentru a satisface cerințele de azot ale culturii în perioada de toamnă-iarnă și pentru a contribui la o bună dezvoltare timpurie a culturii, se recomandă aplicarea unei fractii din cantitatea totală de îngrășăminte cu azot înainte de semănat.'),
            ({$modelsConfigs['Vantage']}, ARRAY[3,6,5,2], 'Add_N_total', '(0,]', 'N', 'Se recomandă fertilizarea cu azot în perioada primăvară prin două aplicări: prima - la reintrarea in vegetatie și a doua - în timpul perioadei de (alungirea tulpinii) și cel mai târziu (înainte de apariția urechii).'),
            ({$modelsConfigs['Vantage']}, ARRAY[4], 'Add_N_fall', '(,0]', NULL, 'Cantitatea măsurată de azot mineral din sol este estimată ca fiind suficientă pentru a satisface cerințele culturii în perioada de toamnă-iarnă și pentru a contribui la o bună dezvoltare timpurie a culturii. '),
            ({$modelsConfigs['Vantage']}, ARRAY[4], 'Add_N_fall', '(0,]', 'N', 'Pentru a satisface cerințele de azot ale culturii în perioada de toamnă-iarnă și pentru a contribui la o bună dezvoltare timpurie a culturii, se recomandă aplicarea unei fractii din cantitatea totală de îngrășăminte cu azot înainte de semănat.'),
            ({$modelsConfigs['Vantage']}, ARRAY[4], 'Add_N_total', '(0,]', 'N', 'Se recomandă fertilizarea cu azot în perioada primăvară prin două aplicări: prima - la reintrarea in vegetatie și a doua - în timpul perioadei de (alungirea tulpinii) și cel mai târziu (înainte de apariția burdufului).'),
            ({$modelsConfigs['Vantage']}, ARRAY[12], 'Add_N_fall', '(,0]', NULL, 'Cantitatea măsurată de azot mineral din sol este estimată ca fiind suficientă pentru a satisface cerințele culturii în perioada de toamnă-iarnă și pentru a contribui la o bună dezvoltare timpurie a culturii. Din acest motiv, nu este necesară aplicarea azotului in toamnă. Se recomandă fertilizarea cu azot în perioada primăvară prin două aplicări: prima - la reintrarea in vegetatie și a doua - înainte de apariția burdufului.'),
            ({$modelsConfigs['Vantage']}, ARRAY[12], 'Add_N_fall', '(0,]', 'N', 'Pentru a satisface cerințele de azot ale culturii în perioada de toamnă-iarnă și pentru a contribui la o bună dezvoltare timpurie a culturii, se recomandă aplicarea unei fractii din cantitatea totală de îngrășăminte cu azot înainte de semănat.'),
            ({$modelsConfigs['Vantage']}, ARRAY[12], 'Add_N_total', '(0,]', 'N', 'Se recomandă fertilizarea cu azot în perioada primăvară prin două aplicări: prima - la reintrarea in vegetatie și a doua - înainte de apariția burdufului.'),
            ({$modelsConfigs['Vantage']}, ARRAY[9], 'Add_N_total', '(0,]', 'N', 'Se recomandă împărțirea ratei de fertilizare cu azot: prima aplicare - la momentul însămânțării și a doua - în perioada de (alungirea tulpinii) și cel mai târziu (înainte de apariția paniculului).'),
            ({$modelsConfigs['Vantage']}, ARRAY[8], 'Add_N_total', '(0,]', 'N', 'Pentru a asigura o aprovizionare continuă cu azot a culturii în timpul sezonului de vegetație și pentru a îndeplini cerințele de azot ale culturilor în etapele de creștere critice, se recomandă împărțirea cantității totale de îngrășăminte cu azot în două aplicaări: prima jumătate - înainte sau o dată cu semănatul și a doua - în etapa de dezvoltare de 6-8 frunze.'),
            ({$modelsConfigs['Vantage']}, ARRAY[7], 'Add_N_total', '(0,]', 'N', 'Pentru a asigura o aprovizionare continuă cu azot a culturii în timpul sezonului de vegetație și pentru a îndeplini cerințele de azot ale culturilor în etapele de creștere critice, se recomandă împărțirea cantității totale de îngrășăminte cu azot în două aplicaări: prima jumătate - înainte sau o dată cu semănatul și a doua - în etapa de dezvoltare de 6-8 frunze.'),
            ({$modelsConfigs['Vantage']}, ARRAY[14], 'Add_N_total', '(0,]', 'N', 'Pentru a asigura o aprovizionare continuă cu azot a culturii în timpul sezonului de creștere și pentru a satisface cerințele de azot ale culturilor în etapele critice de creștere, se recomandă împărțirea cantității totale de îngrășăminte cu azot în două aplicări: prima jumătate - înainte sau la momentul semănatului și al doua - în timpul creșterii vegetative, înainte de (stadiul de înmugurire).'),
            ({$modelsConfigs['Vantage']}, ARRAY[15, 16, 17], 'Add_N_total', '(0,]', 'N', 'Doza totală de îngrășăminte cu azot este recomandată ca aplicare de bază, înainte sau la semănat, pentru a satisface cerințele de azot ale culturii în timpul creșterii vegetative timpurii, înainte ca bacteriile fixatoare de azot să-și atingă potențialul maxim.'),
            ({$modelsConfigs['Vantage']}, ARRAY[11], 'Add_N_total', '(0,]', 'N', 'Se recomandă împărțirea cantității totale de îngrășăminte cu azot în două aplicări. Prima - înainte sau în momentul semănatului pentru a satisface cerințele de azot ale culturii în timpul creșterii vegetative timpurii, înainte ca bacteriile fixatoare de azot să-și atingă potențialul maxim. Cea d-a doua este recomandat să fie aplicată pe rând.'),
            ({$modelsConfigs['Vantage']}, ARRAY[45], 'Add_N_total', '(0,]', 'N', 'Se recomandă împărțirea cantității totale de îngrășăminte cu azot în două aplicări. Prima jumătate - înainte sau la momentul plantării. Cea d-a doua este recomandat să fie aplicată pe rând.'),
            ({$modelsConfigs['Vantage']}, NULL, 'Add_P_total', '(,0]', NULL, 'Rezerva de fosfor disponibil, măsurată prin analiza solului, este estimată ca fiind suficientă pentru a îndeplini cerințele culturilor pentru producția propusă și nu este necesară aplicarea acestuia.'),
            ({$modelsConfigs['Vantage']}, NULL, 'Add_P_total', '(0,]', 'P', 'Aplicarea fosforului este recomandat să fie facută înainte sau la momentul însămânțării, încorporat în sol.'),
            ({$modelsConfigs['Vantage']}, NULL, 'Add_K_total', '(,0]', NULL, 'Rezerva de potasiu disponibil, măsurată prin analiza solului, este estimată ca fiind suficientă pentru a îndeplini cerințele culturilor pentru producția propusă și nu este necesară aplicarea acestuia.'),
            ({$modelsConfigs['Vantage']}, NULL, 'Add_K_total', '(0,]', 'K', 'Aplicarea cu potasiu este recomandat să fie facută înainte sau la momentul însămânțării, încorporat în sol.'),
            ({$modelsConfigs['Vantage']}, ARRAY[45], 'Add_K_total', '(0,]', 'K', 'Datorită sensibilității culturii la un conținut crescut de clorură in solul, nu se recomandă aplicarea clorurii de potasiu (MOP). De preferat este sulfatul de potasiu (SOP).'),
            ({$modelsConfigs['Vantage']}, NULL, 'Add_Mn', '(,0]', NULL, NULL),
            ({$modelsConfigs['Vantage']}, NULL, 'Add_B', '(,0]', NULL, NULL),
            ({$modelsConfigs['Vantage']}, NULL, 'Add_Cu', '(,0]', NULL, NULL),
            ({$modelsConfigs['Vantage']}, NULL, 'Add_Fe', '(,0]', NULL, NULL),
            ({$modelsConfigs['Vantage']}, NULL, 'Add_Zn', '(,0]', NULL, NULL),
            ({$modelsConfigs['Vantage']}, NULL, 'Add_Mn', '(0,)', NULL, NULL),
            ({$modelsConfigs['Vantage']}, NULL, 'Add_B', '(0,)', NULL, NULL),
            ({$modelsConfigs['Vantage']}, NULL, 'Add_Cu', '(0,)', NULL, NULL),
            ({$modelsConfigs['Vantage']}, NULL, 'Add_Fe', '(0,)', NULL, NULL),
            ({$modelsConfigs['Vantage']}, NULL, 'Add_Zn', '(0,)', NULL, NULL),
            ({$modelsConfigs['Agricost']}, ARRAY[3,6,5,2], 'Add_N_fall', '(,0]', NULL, 'Cantitatea măsurată de azot mineral din sol este estimată ca fiind suficientă pentru a satisface cerințele culturii în perioada de toamnă-iarnă și pentru a contribui la o bună dezvoltare timpurie a culturii. Din acest motiv, nu este necesară aplicarea azotului in toamnă. '),
            ({$modelsConfigs['Agricost']}, ARRAY[3,6,5,2], 'Add_N_fall', '(0,]', 'N', 'Pentru a satisface cerințele de azot ale culturii în perioada de toamnă-iarnă și pentru a contribui la o bună dezvoltare timpurie a culturii, se recomandă aplicarea unei fractii din cantitatea totală de îngrășăminte cu azot înainte de semănat.'),
            ({$modelsConfigs['Agricost']}, ARRAY[3,6,5,2], 'Add_N_total', '(0,]', 'N', 'Se recomandă fertilizarea cu azot în perioada primăvară prin două aplicări: prima - la reintrarea in vegetatie și a doua - în timpul perioadei de (alungirea tulpinii) și cel mai târziu (înainte de apariția urechii).'),
            ({$modelsConfigs['Agricost']}, ARRAY[4], 'Add_N_fall', '(,0]', NULL, 'Cantitatea măsurată de azot mineral din sol este estimată ca fiind suficientă pentru a satisface cerințele culturii în perioada de toamnă-iarnă și pentru a contribui la o bună dezvoltare timpurie a culturii. '),
            ({$modelsConfigs['Agricost']}, ARRAY[4], 'Add_N_fall', '(0,]', 'N', 'Pentru a satisface cerințele de azot ale culturii în perioada de toamnă-iarnă și pentru a contribui la o bună dezvoltare timpurie a culturii, se recomandă aplicarea unei fractii din cantitatea totală de îngrășăminte cu azot înainte de semănat.'),
            ({$modelsConfigs['Agricost']}, ARRAY[4], 'Add_N_total', '(0,]', 'N', 'Se recomandă fertilizarea cu azot în perioada primăvară prin două aplicări: prima - la reintrarea in vegetatie și a doua - în timpul perioadei de (alungirea tulpinii) și cel mai târziu (înainte de apariția burdufului).'),
            ({$modelsConfigs['Agricost']}, ARRAY[12], 'Add_N_fall', '(,0]', NULL, 'Cantitatea măsurată de azot mineral din sol este estimată ca fiind suficientă pentru a satisface cerințele culturii în perioada de toamnă-iarnă și pentru a contribui la o bună dezvoltare timpurie a culturii. Din acest motiv, nu este necesară aplicarea azotului in toamnă. Se recomandă fertilizarea cu azot în perioada primăvară prin două aplicări: prima - la reintrarea in vegetatie și a doua - înainte de apariția burdufului.'),
            ({$modelsConfigs['Agricost']}, ARRAY[12], 'Add_N_fall', '(0,]', 'N', 'Pentru a satisface cerințele de azot ale culturii în perioada de toamnă-iarnă și pentru a contribui la o bună dezvoltare timpurie a culturii, se recomandă aplicarea unei fractii din cantitatea totală de îngrășăminte cu azot înainte de semănat.'),
            ({$modelsConfigs['Agricost']}, ARRAY[12], 'Add_N_total', '(0,]', 'N', 'Se recomandă fertilizarea cu azot în perioada primăvară prin două aplicări: prima - la reintrarea in vegetatie și a doua - înainte de apariția burdufului.'),
            ({$modelsConfigs['Agricost']}, ARRAY[9], 'Add_N_total', '(0,]', 'N', 'Se recomandă împărțirea ratei de fertilizare cu azot: prima aplicare - la momentul însămânțării și a doua - în perioada de (alungirea tulpinii) și cel mai târziu (înainte de apariția paniculului).'),
            ({$modelsConfigs['Agricost']}, ARRAY[8], 'Add_N_total', '(0,]', 'N', 'Pentru a asigura o aprovizionare continuă cu azot a culturii în timpul sezonului de vegetație și pentru a îndeplini cerințele de azot ale culturilor în etapele de creștere critice, se recomandă împărțirea cantității totale de îngrășăminte cu azot în două aplicaări: prima jumătate - înainte sau o dată cu semănatul și a doua - în etapa de dezvoltare de 6-8 frunze.'),
            ({$modelsConfigs['Agricost']}, ARRAY[7], 'Add_N_total', '(0,]', 'N', 'Pentru a asigura o aprovizionare continuă cu azot a culturii în timpul sezonului de vegetație și pentru a îndeplini cerințele de azot ale culturilor în etapele de creștere critice, se recomandă împărțirea cantității totale de îngrășăminte cu azot în două aplicaări: prima jumătate - înainte sau o dată cu semănatul și a doua - în etapa de dezvoltare de 6-8 frunze.'),
            ({$modelsConfigs['Agricost']}, ARRAY[14], 'Add_N_total', '(0,]', 'N', 'Pentru a asigura o aprovizionare continuă cu azot a culturii în timpul sezonului de creștere și pentru a satisface cerințele de azot ale culturilor în etapele critice de creștere, se recomandă împărțirea cantității totale de îngrășăminte cu azot în două aplicări: prima jumătate - înainte sau la momentul semănatului și al doua - în timpul creșterii vegetative, înainte de (stadiul de înmugurire).'),
            ({$modelsConfigs['Agricost']}, ARRAY[15, 16, 17], 'Add_N_total', '(0,]', 'N', 'Doza totală de îngrășăminte cu azot este recomandată ca aplicare de bază, înainte sau la semănat, pentru a satisface cerințele de azot ale culturii în timpul creșterii vegetative timpurii, înainte ca bacteriile fixatoare de azot să-și atingă potențialul maxim.'),
            ({$modelsConfigs['Agricost']}, ARRAY[11], 'Add_N_total', '(0,]', 'N', 'Se recomandă împărțirea cantității totale de îngrășăminte cu azot în două aplicări. Prima - înainte sau în momentul semănatului pentru a satisface cerințele de azot ale culturii în timpul creșterii vegetative timpurii, înainte ca bacteriile fixatoare de azot să-și atingă potențialul maxim. Cea d-a doua este recomandat să fie aplicată pe rând.'),
            ({$modelsConfigs['Agricost']}, ARRAY[45], 'Add_N_total', '(0,]', 'N', 'Se recomandă împărțirea cantității totale de îngrășăminte cu azot în două aplicări. Prima jumătate - înainte sau la momentul plantării. Cea d-a doua este recomandat să fie aplicată pe rând.'),
            ({$modelsConfigs['Agricost']}, NULL, 'Add_P_total', '(,0]', NULL, 'Rezerva de fosfor disponibil, măsurată prin analiza solului, este estimată ca fiind suficientă pentru a îndeplini cerințele culturilor pentru producția propusă și nu este necesară aplicarea acestuia.'),
            ({$modelsConfigs['Agricost']}, NULL, 'Add_P_total', '(0,]', 'P', 'Aplicarea fosforului este recomandat să fie facută înainte sau la momentul însămânțării, încorporat în sol.'),
            ({$modelsConfigs['Agricost']}, NULL, 'Add_K_total', '(,0]', NULL, 'Rezerva de potasiu disponibil, măsurată prin analiza solului, este estimată ca fiind suficientă pentru a îndeplini cerințele culturilor pentru producția propusă și nu este necesară aplicarea acestuia.'),
            ({$modelsConfigs['Agricost']}, NULL, 'Add_K_total', '(0,]', 'K', 'Aplicarea cu potasiu este recomandat să fie facută înainte sau la momentul însămânțării, încorporat în sol.'),
            ({$modelsConfigs['Agricost']}, ARRAY[45], 'Add_K_total', '(0,]', 'K', 'Datorită sensibilității culturii la un conținut crescut de clorură in solul, nu se recomandă aplicarea clorurii de potasiu (MOP). De preferat este sulfatul de potasiu (SOP).'),
            ({$modelsConfigs['Agricost']}, NULL, 'Add_Mn', '(,0]', NULL, NULL),
            ({$modelsConfigs['Agricost']}, NULL, 'Add_B', '(,0]', NULL, NULL),
            ({$modelsConfigs['Agricost']}, NULL, 'Add_Cu', '(,0]', NULL, NULL),
            ({$modelsConfigs['Agricost']}, NULL, 'Add_Fe', '(,0]', NULL, NULL),
            ({$modelsConfigs['Agricost']}, NULL, 'Add_Zn', '(,0]', NULL, NULL),
            ({$modelsConfigs['Agricost']}, NULL, 'Add_Mn', '(0,)', NULL, NULL),
            ({$modelsConfigs['Agricost']}, NULL, 'Add_B', '(0,)', NULL, NULL),
            ({$modelsConfigs['Agricost']}, NULL, 'Add_Cu', '(0,)', NULL, NULL),
            ({$modelsConfigs['Agricost']}, NULL, 'Add_Fe', '(0,)', NULL, NULL),
            ({$modelsConfigs['Agricost']}, NULL, 'Add_Zn', '(0,)', NULL, NULL)
        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
