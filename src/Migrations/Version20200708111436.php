<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200708111436 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE lab_analysis_group_element ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE lab_elements_calculations ALTER id DROP DEFAULT');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('SELECT setval(\'lab_analysis_group_element_id_seq\', (SELECT MAX(id) FROM lab_analysis_group_element))');
        $this->addSql('ALTER TABLE lab_analysis_group_element ALTER id SET DEFAULT nextval(\'lab_analysis_group_element_id_seq\')');
        $this->addSql('SELECT setval(\'lab_elements_calculations_id_seq\', (SELECT MAX(id) FROM lab_elements_calculations))');
        $this->addSql('ALTER TABLE lab_elements_calculations ALTER id SET DEFAULT nextval(\'lab_elements_calculations_id_seq\')');
    }
}
