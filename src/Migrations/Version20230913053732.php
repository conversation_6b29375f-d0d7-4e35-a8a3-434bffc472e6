<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230913053732 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add column recommendation_id to loggable.recommendation_comments_log';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE loggable.recommendation_comments_log ADD recommendation_id INT DEFAULT NULL');
        $this->addSql('CREATE INDEX recommendation_comments_log_recommendation_id_idx ON loggable.recommendation_comments_log (recommendation_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE loggable.recommendation_comments_log DROP recommendation_id');
        $this->addSql('DROP INDEX IF EXISTS recommendation_comments_log_recommendation_id_idx');
    }
}
