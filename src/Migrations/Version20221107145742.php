<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221107145742 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update db function get_recommendation_crop_model_config_values - join table lab_analysis_package_group to get lab_analysis_group_id';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            DROP FUNCTION IF EXISTS get_recommendation_crop_model_config_values(
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                sampling_type_ids INTEGER[],
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                elements elements_enum[]
            )
        ');

        $this->addSql("
            CREATE FUNCTION get_recommendation_crop_model_config_values(
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                sampling_type_ids INTEGER[],
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                elements elements_enum[]
            )
                RETURNS TABLE (config JSONB, value DOUBLE PRECISION, element_id INTEGER, \"element\" elements_enum)
                AS $$
                    BEGIN
                    RETURN QUERY
                        SELECT
                            jsonb_object_agg(
                                rcmcv.\"parameter\", jsonb_build_object(
                                    'model_id', rcmcv.model_id,
                                    'crop_ids', rcmcv.crop_ids,
                                    'value', rcmcv.value::numeric
                                )
                            ) AS config,
                            ler.value,
                            lage.id AS element_id,
                            lage.\"element\"
                        FROM
                            package_grid_points pgp
                        JOIN lab_elements_results ler 
                            ON ler.package_grid_points_id = pgp.id
                        JOIN lab_element_group leg
                            ON leg.id  = ler.lab_element_group_id
                        JOIN lab_analysis_package_group lapg
                            ON lapg.id = leg.lab_analysis_package_group_id
                        JOIN lab_analysis_group_element lage
                            ON lage.lab_analysis_group_id = lapg.lab_analysis_group_id
                            AND lage.\"element\" = ler.\"element\"
                        JOIN recommendation_models_config rmc
                            ON rmc.id = model
                            AND rmc.service_provider_id = service_provider
                        JOIN recommendation_calc_model_config rcmc
                            ON rcmc.calc_model_id = rmc.calc_model_id 
                            AND rcmc.element_id = lage.id
                        JOIN recommendation_crop_model_config_values rcmcv
                            ON crop = ANY(rcmcv.crop_ids)
                            AND rcmcv.model_id = rmc.id
                            AND (rcmcv.\"parameter\" = ANY(rcmc.params) OR rcmc.params ISNULL)
                        WHERE
                            pgp.plot_uuid = plt_uuid
                            AND pgp.package_id = pckg_id
                            AND pgp.package_type = pckg_type
                            AND ler.\"element\" = ANY(elements)
                            AND (
                                pgp.sampling_type_id = ANY(sampling_type_ids)
                                OR sampling_type_ids ISNULL
                                OR ARRAY_LENGTH(sampling_type_ids, 1) ISNULL
                            )
                        GROUP BY 
                            lage.id,
                            ler.id
                        ORDER BY
                            \"element\";
                    END
                $$ LANGUAGE plpgsql
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DROP FUNCTION IF EXISTS get_recommendation_crop_model_config_values(
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                sampling_type_ids INTEGER[],
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                elements elements_enum[]
            )
        ');

        $this->addSql("
            CREATE FUNCTION get_recommendation_crop_model_config_values(
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                sampling_type_ids INTEGER[],
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                elements elements_enum[]
            )
                RETURNS TABLE (config JSONB, value DOUBLE PRECISION, element_id INTEGER, \"element\" elements_enum)
                AS $$
                    BEGIN
                    RETURN QUERY
                        SELECT
                            jsonb_object_agg(
                                rcmcv.\"parameter\", jsonb_build_object(
                                    'model_id', rcmcv.model_id,
                                    'crop_ids', rcmcv.crop_ids,
                                    'value', rcmcv.value::numeric
                                )
                            ) AS config,
                            ler.value,
                            lage.id AS element_id,
                            lage.\"element\"
                        FROM
                            package_grid_points pgp
                        JOIN lab_elements_results ler 
                            ON ler.package_grid_points_id = pgp.id
                        JOIN lab_element_group leg
                            ON leg.id  = ler.lab_element_group_id
                        JOIN lab_analysis_group_element lage
                            ON lage.lab_analysis_group_id = leg.lab_analysis_group_id
                            AND lage.\"element\" = ler.\"element\"
                        JOIN recommendation_models_config rmc
                            ON rmc.id = model
                            AND rmc.service_provider_id = service_provider
                        JOIN recommendation_calc_model_config rcmc
                            ON rcmc.calc_model_id = rmc.calc_model_id 
                            AND rcmc.element_id = lage.id
                        JOIN recommendation_crop_model_config_values rcmcv
                            ON crop = ANY(rcmcv.crop_ids)
                            AND rcmcv.model_id = rmc.id
                            AND (rcmcv.\"parameter\" = ANY(rcmc.params) OR rcmc.params ISNULL)
                        WHERE
                            pgp.plot_uuid = plt_uuid
                            AND pgp.package_id = pckg_id
                            AND pgp.package_type = pckg_type
                            AND ler.\"element\" = ANY(elements)
                            AND (
                                pgp.sampling_type_id = ANY(sampling_type_ids)
                                OR sampling_type_ids ISNULL
                                OR ARRAY_LENGTH(sampling_type_ids, 1) ISNULL
                            )
                        GROUP BY 
                            lage.id,
                            ler.id
                        ORDER BY
                            \"element\";
                    END
                $$ LANGUAGE plpgsql
        ");
    }
}
