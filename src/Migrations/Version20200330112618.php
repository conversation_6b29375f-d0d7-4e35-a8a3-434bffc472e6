<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200330112618 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE package ALTER slug TYPE VARCHAR(30)');

        $sql = "INSERT INTO package (
            slug,
            service_provider_id,
            is_active,
            contain_fields,
            slug_short,
            is_sampling,
            has_station,
            integration
        )
        VALUES
            ('Irrigation Management', 1, true, false, 'IM', false, false,'FT');
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DELETE FROM package p where p.slug = \'Irrigation Management\';');
        $this->addSql('ALTER TABLE package ALTER slug TYPE VARCHAR(20)');
    }
}
