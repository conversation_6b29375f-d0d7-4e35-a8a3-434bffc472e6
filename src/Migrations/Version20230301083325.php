<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230301083325 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Insert data into label_bg, label_ro, label_ua, label_it';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("UPDATE main_navigation AS mn
            SET label_bg = t.label_bg
            FROM (VALUES
                    ('Land properties', 'Имоти'),
                    ('Irrigations','Системи за напояване'),
                    ('Contracts', 'Договори'),
                    ('Sales contracts', 'Договор за продажби'),
                    ('Mortgages', 'Ипотеки'),
                    ('Annexes', 'Анекси'),
                    ('Agreements', 'Споразомения'),
                    ('Settings', 'Настройки/конфигурации'),
                    ('Payroll', 'Ведомост'),
                    ('Reports', 'Справки'),
                    ('User', 'Информация за потребителя и изход'),
                    ('Leased', 'Преодадени'),
                    ('Warehouse', 'Склад'),
                    ('Owners', 'Собственици'),
                    ('Administrative map', 'Административна карта'),
                    ('Rents', 'Ренти'),
                    ('Weather stations', 'Метеостанции'),
                    ('Takings', 'Вземания'),
                    ('Machines', 'Машини'),
                    ('Fields','Парцели'),
                    ('Ownership', 'Собственост')
                ) AS t (label_en, label_bg)
            WHERE
                mn.label_en = t.label_en
        ");

        $this->addSql("UPDATE main_navigation AS mn
            SET label_ro = t.label_ro
            FROM (VALUES
                    ('Land properties', 'Land properties'),
                    ('Irrigations','Irrigations'),
                    ('Contracts', 'Contracts'),
                    ('Sales contracts', 'Sales contracts'),
                    ('Mortgages', 'Mortgages'),
                    ('Annexes', 'Annexes'),
                    ('Agreements', 'Agreements'),
                    ('Settings', 'Settings'),
                    ('Payroll', 'Payroll'),
                    ('Reports', 'Reports'),
                    ('User', 'User'),
                    ('Leased', 'Leased'),
                    ('Warehouse', 'Warehouse'),
                    ('Owners', 'Owners'),
                    ('Administrative map', 'Administrative map'),
                    ('Rents', 'Rents'),
                    ('Weather stations', 'Weather stations'),
                    ('Takings', 'Takings'),
                    ('Machines', 'Machines'),
                    ('Fields','Fields'),
                    ('Ownership', 'Ownership')
                ) AS t (label_en, label_ro)
            WHERE
                mn.label_en = t.label_en
        ");

        $this->addSql("UPDATE main_navigation AS mn
            SET label_it = t.label_it
            FROM (VALUES
                    ('Land properties', 'Land properties'),
                    ('Irrigations','Irrigations'),
                    ('Contracts', 'Contracts'),
                    ('Sales contracts', 'Sales contracts'),
                    ('Mortgages', 'Mortgages'),
                    ('Annexes', 'Annexes'),
                    ('Agreements', 'Agreements'),
                    ('Settings', 'Settings'),
                    ('Payroll', 'Payroll'),
                    ('Reports', 'Reports'),
                    ('User', 'User'),
                    ('Leased', 'Leased'),
                    ('Warehouse', 'Warehouse'),
                    ('Owners', 'Owners'),
                    ('Administrative map', 'Administrative map'),
                    ('Rents', 'Rents'),
                    ('Weather stations', 'Weather stations'),
                    ('Takings', 'Takings'),
                    ('Machines', 'Machines'),
                    ('Fields','Fields'),
                    ('Ownership', 'Ownership')
                ) AS t (label_en, label_it)
            WHERE
                mn.label_en = t.label_en
        ");

        $this->addSql("UPDATE main_navigation AS mn
            SET label_ua = t.label_ua
            FROM (VALUES
                    ('Land properties', 'Land properties'),
                    ('Irrigations','Irrigations'),
                    ('Contracts', 'Contracts'),
                    ('Sales contracts', 'Sales contracts'),
                    ('Mortgages', 'Mortgages'),
                    ('Annexes', 'Annexes'),
                    ('Agreements', 'Agreements'),
                    ('Settings', 'Settings'),
                    ('Payroll', 'Payroll'),
                    ('Reports', 'Reports'),
                    ('User', 'User'),
                    ('Leased', 'Leased'),
                    ('Warehouse', 'Warehouse'),
                    ('Owners', 'Owners'),
                    ('Administrative map', 'Administrative map'),
                    ('Rents', 'Rents'),
                    ('Weather stations', 'Weather stations'),
                    ('Takings', 'Takings'),
                    ('Machines', 'Machines'),
                    ('Fields','Fields'),
                    ('Ownership', 'Ownership')
                ) AS t (label_en, label_ua)
            WHERE
                mn.label_en = t.label_en
        ");

        $this->addSql('ALTER TABLE main_navigation ALTER label_bg SET NOT NULL');
        $this->addSql('ALTER TABLE main_navigation ALTER label_ro SET NOT NULL');
        $this->addSql('ALTER TABLE main_navigation ALTER label_ro SET NOT NULL');
        $this->addSql('ALTER TABLE main_navigation ALTER label_ua SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE main_navigation ALTER label_bg DROP NOT NULL');
        $this->addSql('ALTER TABLE main_navigation ALTER label_ro DROP NOT NULL');
        $this->addSql('ALTER TABLE main_navigation ALTER label_ro DROP NOT NULL');
        $this->addSql('ALTER TABLE main_navigation ALTER label_ua DROP NOT NULL');

        $this->addSql('UPDATE main_navigation SET label_bg = NULL');
        $this->addSql('UPDATE main_navigation SET label_ro = NULL');
        $this->addSql('UPDATE main_navigation SET label_it = NULL');
        $this->addSql('UPDATE main_navigation SET label_ua = NULL');
    }
}
