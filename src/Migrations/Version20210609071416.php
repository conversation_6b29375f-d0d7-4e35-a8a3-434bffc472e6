<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210609071416 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE TABLE lab_element_interpretations_config (id SERIAL, element_id INT NOT NULL, service_provider_id INT NOT NULL, class_id INT NOT NULL, range numrange NOT NULL, color VARCHAR(10) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_208A18451F1F2A24 ON lab_element_interpretations_config (element_id)');
        $this->addSql('CREATE INDEX IDX_208A1845C6C98E06 ON lab_element_interpretations_config (service_provider_id)');
        $this->addSql('CREATE INDEX IDX_208A1845EA000B10 ON lab_element_interpretations_config (class_id)');
        $this->addSql('COMMENT ON COLUMN lab_element_interpretations_config.range IS \'(DC2Type:numrange)\'');
        $this->addSql('ALTER TABLE lab_element_interpretations_config ADD CONSTRAINT FK_208A18451F1F2A24 FOREIGN KEY (element_id) REFERENCES lab_analysis_group_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE lab_element_interpretations_config ADD CONSTRAINT FK_208A1845C6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE lab_element_interpretations_config ADD CONSTRAINT FK_208A1845EA000B10 FOREIGN KEY (class_id) REFERENCES lab_interpretation_classes_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
        $this->addSql('DROP TABLE lab_element_interpretations_config');
    }
}
