<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191203085641 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SEQUENCE lab_analysis_package_group_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE lab_analysis_package_group (id INT NOT NULL, package_id INT NOT NULL, lab_analysis_group_id INT NOT NULL, package_type VARCHAR(63) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_1117BB95F44CABFF ON lab_analysis_package_group (package_id)');
        $this->addSql('CREATE INDEX IDX_1117BB95FC8E2340 ON lab_analysis_package_group (lab_analysis_group_id)');
        $this->addSql('ALTER TABLE lab_analysis_package_group ADD CONSTRAINT FK_1117BB95F44CABFF FOREIGN KEY (package_id) REFERENCES package (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE lab_analysis_package_group ADD CONSTRAINT FK_1117BB95FC8E2340 FOREIGN KEY (lab_analysis_group_id) REFERENCES lab_analysis_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE lab_analysis_group_element ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE lab_analysis_group_element ALTER element SET NOT NULL');

        // Add lab_analysis_group_element data
        $sql = "ALTER TABLE lab_analysis_package_group ALTER COLUMN id SET DEFAULT nextval('lab_analysis_package_group_id_seq'::regclass)";
        $this->addSql($sql);

        $abVraFullId = $this->getPackageId('AB VRA full');
        $abIsoControlId = $this->getPackageId('AB ISO control');
        $abIsoFullId = $this->getPackageId('AB ISO full');
        $abVraControlId = $this->getPackageId('AB VRA control');
        $abLeafSamplesId = $this->getPackageId('AB Leaf samples');

        $ABSoilpH = $this->getLabAnalysisGroupId('ABSoilpH');
        $ABSoilN = $this->getLabAnalysisGroupId('ABSoilN');
        $ABSoilP = $this->getLabAnalysisGroupId('ABSoilP');
        $ABSoilK = $this->getLabAnalysisGroupId('ABSoilK');
        $ABSoilCaMgNa = $this->getLabAnalysisGroupId('ABSoilCaMgNa');
        $ABSoilS = $this->getLabAnalysisGroupId('ABSoilS');
        $ABSoilCuMnZnB = $this->getLabAnalysisGroupId('ABSoilCuMnZnB');
        $ABSoilCtCoOM = $this->getLabAnalysisGroupId('ABSoilCtCoOM');
        $ABSoilCtCoCiTC = $this->getLabAnalysisGroupId('ABSoilCtCoCiTC');
        $ABSoilAC = $this->getLabAnalysisGroupId('ABSoilAC');
        $ABSoilEC = $this->getLabAnalysisGroupId('ABSoilEC');

        $ABLeafN = $this->getLabAnalysisGroupId('ABLeafN');
        $ABLeafPKCaMgNaS = $this->getLabAnalysisGroupId('ABLeafPKCaMgNaS');
        $ABLeafFeCuZnMnB = $this->getLabAnalysisGroupId('ABLeafFeCuZnMnB');

        $sql = 'INSERT INTO lab_analysis_package_group (
            package_id, package_type, lab_analysis_group_id
        )
        VALUES
            (' . $abVraFullId . ",'subscription', {$ABSoilpH}),
            (" . $abVraFullId . ",'subscription', {$ABSoilN}),
            (" . $abVraFullId . ",'subscription', {$ABSoilP}),
            (" . $abVraFullId . ",'subscription', {$ABSoilK}),
            (" . $abVraFullId . ",'subscription', {$ABSoilCaMgNa}),
            (" . $abVraFullId . ",'subscription', {$ABSoilS}),
            (" . $abVraFullId . ",'subscription', {$ABSoilCuMnZnB}),
            (" . $abVraFullId . ",'subscription', {$ABSoilCtCoOM}),
            (" . $abVraFullId . ",'subscription', {$ABSoilCtCoCiTC}),
            (" . $abVraFullId . ",'subscription', {$ABSoilAC}),
            (" . $abVraFullId . ",'subscription', {$ABSoilEC}),
            
            (" . $abIsoControlId . ",'subscription', {$ABSoilpH}),
            (" . $abIsoControlId . ",'subscription', {$ABSoilN}),
            (" . $abIsoControlId . ",'subscription', {$ABSoilP}),
            (" . $abIsoControlId . ",'subscription', {$ABSoilK}),
            (" . $abIsoControlId . ",'subscription', {$ABSoilCaMgNa}),
            (" . $abIsoControlId . ",'subscription', {$ABSoilS}),
            (" . $abIsoControlId . ",'subscription', {$ABSoilCuMnZnB}),
            (" . $abIsoControlId . ",'subscription', {$ABSoilCtCoOM}),
            (" . $abIsoControlId . ",'subscription', {$ABSoilCtCoCiTC}),
            (" . $abIsoControlId . ",'subscription', {$ABSoilAC}),
            (" . $abIsoControlId . ",'subscription', {$ABSoilEC}),
            
            (" . $abIsoFullId . ",'subscription', {$ABSoilpH}),
            (" . $abIsoFullId . ",'subscription', {$ABSoilN}),
            (" . $abIsoFullId . ",'subscription', {$ABSoilP}),
            (" . $abIsoFullId . ",'subscription', {$ABSoilK}),
            (" . $abIsoFullId . ",'subscription', {$ABSoilCaMgNa}),
            (" . $abIsoFullId . ",'subscription', {$ABSoilS}),
            (" . $abIsoFullId . ",'subscription', {$ABSoilCuMnZnB}),
            (" . $abIsoFullId . ",'subscription', {$ABSoilCtCoOM}),
            (" . $abIsoFullId . ",'subscription', {$ABSoilCtCoCiTC}),
            (" . $abIsoFullId . ",'subscription', {$ABSoilAC}),
            (" . $abIsoFullId . ",'subscription', {$ABSoilEC}),
            
            (" . $abVraControlId . ",'subscription', {$ABSoilpH}),
            (" . $abVraControlId . ",'subscription', {$ABSoilN}),
            (" . $abVraControlId . ",'subscription', {$ABSoilP}),
            (" . $abVraControlId . ",'subscription', {$ABSoilK}),
            (" . $abVraControlId . ",'subscription', {$ABSoilCaMgNa}),
            (" . $abVraControlId . ",'subscription', {$ABSoilS}),
            (" . $abVraControlId . ",'subscription', {$ABSoilCuMnZnB}),
            (" . $abVraControlId . ",'subscription', {$ABSoilCtCoOM}),
            (" . $abVraControlId . ",'subscription', {$ABSoilCtCoCiTC}),
            (" . $abVraControlId . ",'subscription', {$ABSoilAC}),
            (" . $abVraControlId . ",'subscription', {$ABSoilEC}),
            
            (" . $abLeafSamplesId . ",'subscription', {$ABLeafN}),
            (" . $abLeafSamplesId . ",'subscription', {$ABLeafPKCaMgNaS}),
            (" . $abLeafSamplesId . ",'subscription', {$ABLeafFeCuZnMnB})
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP SEQUENCE lab_analysis_package_group_id_seq CASCADE');
        $this->addSql('DROP TABLE lab_analysis_package_group');
        $this->addSql('CREATE SEQUENCE lab_analysis_group_element_id_seq');
        $this->addSql('SELECT setval(\'lab_analysis_group_element_id_seq\', (SELECT MAX(id) FROM lab_analysis_group_element))');
        $this->addSql('ALTER TABLE lab_analysis_group_element ALTER id SET DEFAULT nextval(\'lab_analysis_group_element_id_seq\')');
        $this->addSql('ALTER TABLE lab_analysis_group_element ALTER element DROP NOT NULL');
    }
}
