<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210202113326 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add column service_provider_id to table lab_analysis_uploads';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE lab_analysis_uploads ADD service_provider_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE lab_analysis_uploads ADD CONSTRAINT FK_217872DBC6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_217872DBC6C98E06 ON lab_analysis_uploads (service_provider_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE lab_analysis_uploads DROP CONSTRAINT FK_217872DBC6C98E06');
        $this->addSql('DROP INDEX IDX_217872DBC6C98E06');
        $this->addSql('ALTER TABLE lab_analysis_uploads DROP service_provider_id');
    }
}
