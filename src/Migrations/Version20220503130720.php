<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220503130720 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update pg function get_lab_elements_results_aggregated_classes to order elements by lab_analysis_group_element_visual_order.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP FUNCTION IF EXISTS get_lab_elements_results_aggregated_classes(plot_uuids VARCHAR[], pckg_id INTEGER, pckg_type VARCHAR, service_provider INTEGER)');

        $this->addSql('
        CREATE OR REPLACE FUNCTION public.get_lab_elements_results_aggregated_classes(plot_uuids character varying[], pckg_id integer, pckg_type character varying, service_provider integer)
 RETURNS TABLE(plot_uuid character varying, package_id integer, package_type character varying, element_id integer, element elements_enum, class_ids integer[])
 LANGUAGE plpgsql
AS $function$
        BEGIN
        RETURN QUERY
        WITH element_classes_count AS (
            SELECT
                    lerc.plot_uuid,
                    lerc."element",
                    lerc.class_id,
                    count(lerc.class_id) AS classes_count,
                    leic_ordered.class_position
                FROM 
                    get_lab_elements_results_classes(plot_uuids, pckg_id, pckg_type, service_provider) AS lerc
                JOIN lab_analysis_group_element lage
                    ON lage."element" = lerc."element"
                JOIN (select 
                        row_number () over (partition by leic.service_provider_id, leic.element_id order by "range") class_position, 
                        leic.element_id, 
                        leic.class_id 
                        FROM 
                    lab_element_interpretations_config leic
                    ) leic_ordered
                	on leic_ordered.element_id=lage.id and lerc.class_id=leic_ordered.class_id 
                GROUP BY 
                    lerc.plot_uuid,
                    lerc."element",
                    lerc.class_id,
                    lerc."range",
                    leic_ordered.class_position
                ORDER by 
                    lerc."element"
        ),
        element_classes_sum AS (
            SELECT
                ecc."element",
                sum(ecc.classes_count) AS classes_sum
            FROM
                element_classes_count AS ecc
            GROUP BY
                ecc."element"
        ),
        element_classes_percentage AS (
            SELECT
                ecc.plot_uuid,
                ecc."element",
                ecc.class_position,
                lage.id AS element_id,
                COALESCE((ecc.classes_count / ecs.classes_sum) * 100, 0) AS percentage,
                COALESCE(lag(((ecc.classes_count / ecs.classes_sum) * 100), 1) OVER (PARTITION BY ecc."element" ORDER BY leic."range"), 0) AS previous_percentage,
                ecc.class_id,
                lag(ecc.class_position, 1) OVER (PARTITION BY ecc."element" ORDER BY leic."range") AS previous_class_position,
                lag(ecc.class_id, 1) OVER (PARTITION BY ecc."element" ORDER BY leic."range") AS previous_class_id,
                leaavc.area_treshold,
                leic.range
            FROM
                element_classes_count AS ecc
            JOIN element_classes_sum AS ecs
                ON ecc."element" = ecs."element"
            JOIN lab_element_aggregation_area_value_config leaavc
                ON leaavc.service_provider_id = service_provider
            JOIN lab_analysis_group_element lage
                ON lage."element" = ecc."element"
            JOIN lab_element_interpretations_config leic
                ON leic.element_id = lage.id
                AND leic.service_provider_id = service_provider
                AND leic.class_id = ecc.class_id
            ORDER BY
                ecc.plot_uuid,
                ecc."element",
                ecc.class_id	
        )
        SELECT
            ecp.plot_uuid,
            pckg_id AS package_id,
            pckg_type AS package_type,
            ecp.element_id,
            ecp."element",
            MAX(
                CASE 
                    WHEN 
                        ecp.percentage >= (ecp.area_treshold * 100)
                    THEN 
                        ARRAY[ecp.class_id]
                    WHEN 
                        ecp.percentage < (ecp.area_treshold * 100) 
                        AND ecp.previous_percentage < (ecp.area_treshold * 100) 
                        AND (ecp.percentage + ecp.previous_percentage) >= (ecp.area_treshold * 100)
                        AND ecp.class_position = ecp.previous_class_position + 1
                    THEN 
                        ARRAY[ecp.previous_class_id, ecp.class_id]
                    ELSE NULL
                END
            ) AS class_ids
        FROM
            element_classes_percentage AS ecp
        join lab_analysis_group_element lage
            on lage."element" = ecp."element"
		join lab_analysis_group_element_visual_order evo 
			on evo.lab_analysis_group_element_id = lage.id and evo.service_provider_id = service_provider
        GROUP BY 
            ecp.plot_uuid,
			ecp."element",
			evo.visual_order,
			ecp.element_id
        ORDER BY
            ecp.plot_uuid,
            package_id,
            package_type,
            evo.visual_order,
            class_ids;
        END
        $function$
;
');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP FUNCTION IF EXISTS get_lab_elements_results_aggregated_classes(plot_uuids VARCHAR[], pckg_id INTEGER, pckg_type VARCHAR, service_provider INTEGER)');

        $this->addSql('
        CREATE OR REPLACE FUNCTION public.get_lab_elements_results_aggregated_classes(plot_uuids character varying[], pckg_id integer, pckg_type character varying, service_provider integer)
 RETURNS TABLE(plot_uuid character varying, package_id integer, package_type character varying, element_id integer, element elements_enum, class_ids integer[])
 LANGUAGE plpgsql
AS $function$
        BEGIN
        RETURN QUERY
        WITH element_classes_count AS (
            SELECT
                    lerc.plot_uuid,
                    lerc."element",
                    lerc.class_id,
                    count(lerc.class_id) AS classes_count,
                    leic_ordered.class_position
                FROM 
                    get_lab_elements_results_classes(plot_uuids, pckg_id, pckg_type, service_provider) AS lerc
                JOIN lab_analysis_group_element lage
                    ON lage."element" = lerc."element"
                JOIN (select 
                        row_number () over (partition by leic.service_provider_id, leic.element_id order by "range") class_position, 
                        leic.element_id, 
                        leic.class_id 
                        FROM 
                    lab_element_interpretations_config leic
                    ) leic_ordered
                	on leic_ordered.element_id=lage.id and lerc.class_id=leic_ordered.class_id 
                GROUP BY 
                    lerc.plot_uuid,
                    lerc."element",
                    lerc.class_id,
                    lerc."range",
                    leic_ordered.class_position
                ORDER by 
                    lerc."element"
        ),
        element_classes_sum AS (
            SELECT
                ecc."element",
                sum(ecc.classes_count) AS classes_sum
            FROM
                element_classes_count AS ecc
            GROUP BY
                ecc."element"
        ),
        element_classes_percentage AS (
            SELECT
                ecc.plot_uuid,
                ecc."element",
                ecc.class_position,
                lage.id AS element_id,
                COALESCE((ecc.classes_count / ecs.classes_sum) * 100, 0) AS percentage,
                COALESCE(lag(((ecc.classes_count / ecs.classes_sum) * 100), 1) OVER (PARTITION BY ecc."element" ORDER BY leic."range"), 0) AS previous_percentage,
                ecc.class_id,
                lag(ecc.class_position, 1) OVER (PARTITION BY ecc."element" ORDER BY leic."range") AS previous_class_position,
                lag(ecc.class_id, 1) OVER (PARTITION BY ecc."element" ORDER BY leic."range") AS previous_class_id,
                leaavc.area_treshold,
                leic.range
            FROM
                element_classes_count AS ecc
            JOIN element_classes_sum AS ecs
                ON ecc."element" = ecs."element"
            JOIN lab_element_aggregation_area_value_config leaavc
                ON leaavc.service_provider_id = service_provider
            JOIN lab_analysis_group_element lage
                ON lage."element" = ecc."element"
            JOIN lab_element_interpretations_config leic
                ON leic.element_id = lage.id
                AND leic.service_provider_id = service_provider
                AND leic.class_id = ecc.class_id
            ORDER BY
                ecc.plot_uuid,
                ecc."element",
                ecc.class_id	
        )
        SELECT DISTINCT
            ecp.plot_uuid,
            pckg_id AS package_id,
            pckg_type AS package_type,
            ecp.element_id,
            ecp."element",
            MAX(
                CASE 
                    WHEN 
                        ecp.percentage >= (ecp.area_treshold * 100)
                    THEN 
                        ARRAY[ecp.class_id]
                    WHEN 
                        ecp.percentage < (ecp.area_treshold * 100) 
                        AND ecp.previous_percentage < (ecp.area_treshold * 100) 
                        AND (ecp.percentage + ecp.previous_percentage) >= (ecp.area_treshold * 100)
                        AND ecp.class_position = ecp.previous_class_position + 1
                    THEN 
                        ARRAY[ecp.previous_class_id, ecp.class_id]
                    ELSE NULL
                END
            ) AS class_ids
        FROM
            element_classes_percentage AS ecp
        GROUP BY 
            ecp.plot_uuid,
            ecp."element",
            ecp.element_id
        ORDER BY
            ecp.plot_uuid,
            package_id,
            package_type,
            ecp.element_id,
            ecp."element",
            class_ids;
        END
        $function$
;

        ');
    }
}
