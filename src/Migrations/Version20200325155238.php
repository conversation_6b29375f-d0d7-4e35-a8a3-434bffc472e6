<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200325155238 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE lab_elements_calculations ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE lab_elements_calculations ALTER element SET NOT NULL');
        $this->addSql('ALTER INDEX idx_2e6761b6dedf4c09 RENAME TO IDX_2E6761B62E53E525');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SEQUENCE lab_elements_calculations_id_seq');
        $this->addSql('SELECT setval(\'lab_elements_calculations_id_seq\', (SELECT MAX(id) FROM lab_elements_calculations))');
        $this->addSql('ALTER TABLE lab_elements_calculations ALTER id SET DEFAULT nextval(\'lab_elements_calculations_id_seq\')');
        $this->addSql('ALTER TABLE lab_elements_calculations ALTER element DROP NOT NULL');
        $this->addSql('ALTER INDEX idx_2e6761b62e53e525 RENAME TO idx_2e6761b6dedf4c09');
    }
}
