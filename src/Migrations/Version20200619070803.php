<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200619070803 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $serviceProviderIdId = $this->getServiceProviderId('nikas');

        $sql = "INSERT INTO package (
            slug,
            service_provider_id,
            is_active,
            contain_fields,
            slug_short,
            is_sampling,
            has_station,
            integration
        )
        VALUES
            ('AB Light'," . $serviceProviderIdId . ", true, true, 'Light', true, false, null);
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DELETE FROM package p where p.slug = \'AB Light\';');
    }
}
