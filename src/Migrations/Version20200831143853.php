<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200831143853 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE INDEX lab_elements_results_raw_lab_number_idx ON lab_elements_results_raw (lab_number)');
        $this->addSql('CREATE INDEX package_grid_points_lab_number_idx ON package_grid_points (lab_number)');
        $this->addSql('CREATE INDEX package_grid_points_package_id_idx ON package_grid_points (package_id)');
        $this->addSql('ALTER TABLE lab_elements_results ALTER state SET DEFAULT \'Pending\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP INDEX lab_elements_results_raw_lab_number_idx');
        $this->addSql('DROP INDEX package_grid_points_lab_number_idx');
        $this->addSql('DROP INDEX package_grid_points_package_id_idx');
        $this->addSql('ALTER TABLE lab_elements_results ALTER state DROP DEFAULT');
    }
}
