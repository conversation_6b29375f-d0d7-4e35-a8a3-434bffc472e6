<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210721134627 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Fill the column color with \'#2b386f\' for all the records in table lab_element_interpretations_config.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("UPDATE lab_element_interpretations_config SET color = '#2b386f'");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('UPDATE lab_element_interpretations_config SET color = NULL');
    }
}
