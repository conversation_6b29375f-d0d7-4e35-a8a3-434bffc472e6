<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210723130734 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create postgresql function get_recommendation_crop_model_config_param(). This function is used to get config value by element and parameter.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("
            CREATE FUNCTION get_recommendation_crop_model_config_param(
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                elem elements_enum,
                param_search VARCHAR
            )
            RETURNS TABLE (id INTEGER, model_id integer, crop_ids INTEGER[], \"element\" elements_enum, element_id INTEGER,  \"parameter\" recommendation_crop_model_parameter_enum, value DOUBLE PRECISION)
            AS $$
                BEGIN
                RETURN QUERY	
                    SELECT
                        rcmcv.id,
                        rcmcv.model_id,
                        rcmcv.crop_ids,
                        lage.\"element\",
                        lage.id AS element_id,
                        rcmcv.\"parameter\",
                        rcmcv.value
                    FROM
                        recommendation_models_config rmc
                    JOIN recommendation_calc_model_config rcmc
                        ON rcmc.calc_model_id = rmc.calc_model_id
                    JOIN recommendation_crop_model_config_values rcmcv
                        ON crop = ANY(rcmcv.crop_ids)
                        AND rcmcv.model_id = rmc.id
                        AND (rcmcv.\"parameter\" = ANY(rcmc.params))
                    JOIN lab_analysis_group_element lage
                        ON lage.id = rcmc.element_id
                    WHERE
                        rmc.id = model
                        AND rmc.service_provider_id = service_provider
                        AND (
                            rcmcv.\"parameter\"::TEXT ILIKE '%' || param_search || '%'
                            OR param_search ISNULL
                        )
                        AND (
                            lage.\"element\" = elem
                            OR elem isnull
                        );
                END
            $$ LANGUAGE plpgsql
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DROP FUNCTION IF EXISTS get_recommendation_crop_model_config_param(
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                elem elements_enum,
                param_search varchar
            )
        ');
    }
}
