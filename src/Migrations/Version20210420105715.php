<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 5/17/2021
 * Time: 10:03 AM.
 */
declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

class Version20210420105715 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create sequences for tables';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SEQUENCE IF NOT EXISTS lab_analysis_group_element_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('SELECT setval(\'lab_analysis_group_element_seq\', (SELECT MAX(id) FROM lab_analysis_group_element))');
        $this->addSql('ALTER TABLE lab_analysis_group_element ALTER id SET DEFAULT nextval(\'lab_analysis_group_element_seq\')');

        $this->addSql('CREATE SEQUENCE IF NOT EXISTS lab_elements_calculations_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('SELECT setval(\'lab_elements_calculations_seq\', (SELECT MAX(id) FROM lab_elements_calculations))');
        $this->addSql('ALTER TABLE lab_elements_calculations ALTER id SET DEFAULT nextval(\'lab_elements_calculations_seq\')');

        $this->addSql('CREATE SEQUENCE IF NOT EXISTS lab_analysis_package_group_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('SELECT setval(\'lab_analysis_package_group_seq\', (SELECT MAX(id) FROM lab_analysis_package_group))');
        $this->addSql('ALTER TABLE lab_analysis_package_group ALTER id SET DEFAULT nextval(\'lab_analysis_package_group_seq\')');

        $this->addSql('CREATE SEQUENCE IF NOT EXISTS meta_groups_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('SELECT setval(\'meta_groups_seq\', (SELECT MAX(id) FROM meta_groups))');
        $this->addSql('ALTER TABLE meta_groups ALTER id SET DEFAULT nextval(\'meta_groups_seq\')');

        $this->addSql('CREATE SEQUENCE IF NOT EXISTS meta_elements_groups_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('SELECT setval(\'meta_elements_groups_seq\', (SELECT MAX(id) FROM meta_elements_groups))');
        $this->addSql('ALTER TABLE meta_elements_groups ALTER id SET DEFAULT nextval(\'meta_elements_groups_seq\')');
    }

    public function down(Schema $schema): void {}
}
