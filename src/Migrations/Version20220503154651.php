<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220503154651 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Add missing elements to lab_analysis_group_element_visual_order';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $elementsVisualOrderArr = [
            'Ctotal',
            'Corg',
            'Cinorg',
            'TCarbonates',
            'Acarbonates',
            'EC',
            'CEC',
            'BS',
            'K+',
            'LeafN',
            'LeafP',
            'LeafK',
            'LeafCa',
            'LeafMg',
            'LeafNa',
            'LeafS',
            'LeafFe',
            'LeafCu',
            'LeafZn',
            'LeafMn',
            'LeafB',
            'Ca2+',
            'Mg2+',
            'Na+',
            'H+',
        ];
        $serviceProvidersId = $this->getServiceProvidersId(['vantage_balkans', 'agricost', 'livona', 'spectr_agro', 'nik_italia', 'sumi_agro_tr', 'nikas']);
        $elementIds = $this->getElements();

        foreach ($serviceProvidersId as $serviceProviderId) {
            $cnt = $this->getElementVisualOrder('OM', $serviceProviderId);
            foreach ($elementsVisualOrderArr as $element) {
                ++$cnt;
                $this->addSql("INSERT INTO lab_analysis_group_element_visual_order (lab_analysis_group_element_id, service_provider_id, visual_order) VALUES ({$elementIds[$element]}, {$serviceProviderId}, {$cnt})");
            }
        }
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("
            delete
            from
                lab_analysis_group_element_visual_order evo
            where
                evo.lab_analysis_group_element_id in (
                select
                    lage.id
                from
                    lab_analysis_group_element lage
                where
                    lage.\"element\" in (
                                    'Ctotal',
                                    'Corg',
                                    'Cinorg',
                                    'TCarbonates',
                                    'Acarbonates',
                                    'EC',
                                    'CEC',
                                    'BS',
                                    'K+',
                                    'LeafN',
                                    'LeafP',
                                    'LeafK',
                                    'LeafCa',
                                    'LeafMg',
                                    'LeafNa',
                                    'LeafS',
                                    'LeafFe',
                                    'LeafCu',
                                    'LeafZn',
                                    'LeafMn',
                                    'LeafB',
                                    'Ca2+',
                                    'Mg2+',
                                    'Na+',
                                    'H+'
                    ));
        ");
    }
}
