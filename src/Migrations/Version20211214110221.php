<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20211214110221 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create vra_order_type_enum.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("CREATE TYPE vra_order_type_enum as ENUM ('vra', 'soil_vra')");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TYPE IF EXISTS vra_order_type_enum');
    }
}
