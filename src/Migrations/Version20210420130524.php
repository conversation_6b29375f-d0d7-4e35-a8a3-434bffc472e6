<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210420130524 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Add lab_analysis_package_group for packages AB VRA full OM CEC and  AB ISO full OM CEC';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $abVraFullOmCecId = $this->getPackageId('AB VRA full OM CEC');
        $abIsoFullOmCecId = $this->getPackageId('AB ISO full OM CEC');

        $ABSoilpH = $this->getLabAnalysisGroupId('ABSoilpH');
        $ABSoilN = $this->getLabAnalysisGroupId('ABSoilN');
        $ABSoilP = $this->getLabAnalysisGroupId('ABSoilP');
        $ABSoilK = $this->getLabAnalysisGroupId('ABSoilK');
        $ABSoilCaMgNa = $this->getLabAnalysisGroupId('ABSoilCaMgNa');
        $ABSoilS = $this->getLabAnalysisGroupId('ABSoilS');
        $ABSoilFeCuMnZnB = $this->getLabAnalysisGroupId('ABSoilFeCuMnZnB');
        $ABSoilOM = $this->getLabAnalysisGroupId('ABSoilOM');
        $ABSoilCEC = $this->getLabAnalysisGroupId('ABSoilCEC');

        $sql = "INSERT INTO lab_analysis_package_group (
            package_id, package_type, lab_analysis_group_id
        )
        VALUES
            (:abVraFullOmCecId,'subscription', :ABSoilpH),
            (:abVraFullOmCecId,'subscription', :ABSoilN),
            (:abVraFullOmCecId,'subscription', :ABSoilP),
            (:abVraFullOmCecId,'subscription', :ABSoilK),
            (:abVraFullOmCecId,'subscription', :ABSoilCaMgNa),
            (:abVraFullOmCecId,'subscription', :ABSoilS),
            (:abVraFullOmCecId,'subscription', :ABSoilFeCuMnZnB),
            (:abVraFullOmCecId,'subscription', :ABSoilOM),
            (:abVraFullOmCecId,'subscription', :ABSoilCEC),
            
            (:abIsoFullOmCecId,'subscription', :ABSoilpH),
            (:abIsoFullOmCecId,'subscription', :ABSoilN),
            (:abIsoFullOmCecId,'subscription', :ABSoilP),
            (:abIsoFullOmCecId,'subscription', :ABSoilK),
            (:abIsoFullOmCecId,'subscription', :ABSoilCaMgNa),
            (:abIsoFullOmCecId,'subscription', :ABSoilS),
            (:abIsoFullOmCecId,'subscription', :ABSoilFeCuMnZnB),
            (:abIsoFullOmCecId,'subscription', :ABSoilOM),
            (:abIsoFullOmCecId,'subscription', :ABSoilCEC)
        ";
        $this->addSql($sql, [
            'abVraFullOmCecId' => $abVraFullOmCecId,
            'abIsoFullOmCecId' => $abIsoFullOmCecId,
            'ABSoilpH' => $ABSoilpH,
            'ABSoilN' => $ABSoilN,
            'ABSoilP' => $ABSoilP,
            'ABSoilK' => $ABSoilK,
            'ABSoilCaMgNa' => $ABSoilCaMgNa,
            'ABSoilS' => $ABSoilS,
            'ABSoilFeCuMnZnB' => $ABSoilFeCuMnZnB,
            'ABSoilOM' => $ABSoilOM,
            'ABSoilCEC' => $ABSoilCEC,
        ]);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $abVraFullOmCecId = $this->getPackageId('AB VRA full OM CEC');
        $abIsoFullOmCecId = $this->getPackageId('AB ISO full OM CEC');

        $this->addSql('DELETE FROM lab_analysis_package_group
                            where package_id in (:abVraFullOmCecId, :abIsoFullOmCecId);
                            ', ['abVraFullOmCecId' => $abVraFullOmCecId, 'abIsoFullOmCecId' => $abIsoFullOmCecId]);
    }
}
