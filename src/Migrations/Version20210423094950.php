<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210423094950 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add column "is_satellite" to table "package". Fill the column depending on package\'s slug_short.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE package ADD COLUMN is_satellite BOOLEAN DEFAULT NULL');
        $this->addSql("UPDATE package SET is_satellite = CASE WHEN (slug_short = 'SAT' OR slug_short = 'VRA-N') THEN true ELSE false END");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
        $this->addSql('ALTER TABLE package DROP COLUMN is_satellite');
    }
}
