<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220711113547 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add column sampling_type_id to the package_grid_points table. Seed the column.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE package_grid_points ADD sampling_type_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE package_grid_points ADD CONSTRAINT FK_CA8B52FE45C8B25E FOREIGN KEY (sampling_type_id) REFERENCES sampling_type (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_CA8B52FE45C8B25E ON package_grid_points (sampling_type_id)');
        $this->addSql(
            'UPDATE package_grid_points
            SET sampling_type_id = st.id
            FROM (
                SELECT id
                FROM sampling_type
                WHERE "type" = \'0-30\'
            ) AS st'
        );
        $this->addSql('ALTER TABLE package_grid_points ALTER COLUMN sampling_type_id SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE package_grid_points DROP CONSTRAINT FK_CA8B52FE45C8B25E');
        $this->addSql('DROP INDEX IDX_CA8B52FE45C8B25E');
        $this->addSql('ALTER TABLE package_grid_points DROP sampling_type_id');
    }
}
