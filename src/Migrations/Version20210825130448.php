<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210825130448 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Update color for ph class 19';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $serviceProvidersId = $this->getServiceProvidersId(['vantage_balkans', 'agricost', 'livona', 'spectr_agro', 'nik_italia']);
        $elementIds = $this->getElements();

        foreach ($serviceProvidersId as $serviceProviderId) {
            $this->addSql("
                    UPDATE lab_element_interpretations_config
                    SET color = '#C8C8E1'
                    WHERE service_provider_id = {$serviceProviderId}
                    AND class_id = 19
                    AND element_id = {$elementIds['pH']};
                ");
        }
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
    }
}
