<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200904113042 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("CREATE TYPE recommendation_status_enum AS ENUM (
        'Pending',
        'For approve',
        'Delivered'
        )");

        $this->addSql('ALTER TABLE recommendation_comment ALTER lab_element_group_id DROP NOT NULL');
        $this->addSql('ALTER TABLE recommendations ADD status recommendation_status_enum NOT NULL');
        $this->addSql('ALTER TABLE recommendations ADD due_date DATE NOT NULL');
        $this->addSql('ALTER TABLE recommendation_lab_element_group ADD created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL');
        $this->addSql('ALTER TABLE recommendation_lab_element_group DROP due_date');
        $this->addSql('ALTER TABLE recommendations ALTER status SET DEFAULT \'Pending\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
        $this->addSql('ALTER TABLE recommendation_comment ALTER lab_element_group_id SET NOT NULL');
        $this->addSql('ALTER TABLE recommendations DROP status');
        $this->addSql('ALTER TABLE recommendation_lab_element_group ADD due_date DATE NOT NULL');
        $this->addSql('ALTER TABLE recommendation_lab_element_group DROP created_at');
        $this->addSql('ALTER TABLE recommendations DROP due_date');

        $this->addSql('DROP TYPE IF EXISTS recommendation_status_enum');
    }
}
