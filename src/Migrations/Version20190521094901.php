<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190521094901 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf(
            'postgresql' !== $this->connection->getDatabasePlatform()->getName(),
            'Migration can only be executed safely on \'postgresql\'.'
        );

        $this->addSql('CREATE TABLE service_provider (id SERIAL NOT NULL, name VARCHAR(100) NOT NULL, slug VARCHAR(100) NOT NULL, from_sync BOOLEAN NOT NULL, last_sync TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, country_code VARCHAR(15) DEFAULT \'BG\' NOT NULL, PRIMARY KEY(id))');
        $this->addSql('ALTER TABLE activity ADD service_provider_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE activity ADD CONSTRAINT FK_AC74095AC6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_AC74095AC6C98E06 ON activity (service_provider_id)');
        $this->addSql('ALTER TABLE duration_type ADD service_provider_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE duration_type ADD CONSTRAINT FK_DF8E2F92C6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_DF8E2F92C6C98E06 ON duration_type (service_provider_id)');
        $this->addSql('ALTER TABLE number ADD service_provider_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE number ADD CONSTRAINT FK_96901F54C6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_96901F54C6C98E06 ON number (service_provider_id)');
        $this->addSql('ALTER TABLE price ADD service_provider_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE price ADD CONSTRAINT FK_CAC822D9C6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_CAC822D9C6C98E06 ON price (service_provider_id)');
        $this->addSql('ALTER TABLE contract ADD service_provider_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE contract ADD CONSTRAINT FK_E98F2859C6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_E98F2859C6C98E06 ON contract (service_provider_id)');
        $this->addSql('ALTER TABLE contract_statuses ADD service_provider_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE contract_statuses ADD CONSTRAINT FK_DE4705EEC6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_DE4705EEC6C98E06 ON contract_statuses (service_provider_id)');
        $this->addSql('ALTER TABLE subscription_package ADD service_provider_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE subscription_package ADD CONSTRAINT FK_AD7D870EC6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_AD7D870EC6C98E06 ON subscription_package (service_provider_id)');
        $this->addSql('ALTER TABLE currency ADD service_provider_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE currency ADD CONSTRAINT FK_6956883FC6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_6956883FC6C98E06 ON currency (service_provider_id)');
        $this->addSql('ALTER TABLE package_statuses ADD service_provider_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE package_statuses ADD CONSTRAINT FK_AF718A7FC6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_AF718A7FC6C98E06 ON package_statuses (service_provider_id)');
        $this->addSql('ALTER TABLE package ADD service_provider_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE package ADD CONSTRAINT FK_DE686795C6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_DE686795C6C98E06 ON package (service_provider_id)');
        $this->addSql('ALTER TABLE service_statuses ADD service_provider_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE service_statuses ADD CONSTRAINT FK_B286452CC6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_B286452CC6C98E06 ON service_statuses (service_provider_id)');
        $this->addSql('ALTER TABLE service ADD service_provider_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE service ADD CONSTRAINT FK_E19D9AD2C6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_E19D9AD2C6C98E06 ON service (service_provider_id)');

        $sql = "INSERT INTO service_provider (
            name,
            slug,
            from_sync,
            country_code
        )
        VALUES
            ('NIK AgroService', 'nikas', true, 'BG'),
            ('VantageBalkans', 'vantage_balkans', true, 'RO'),
            ('Agricost', 'agricost', true, 'RO');
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf(
            'postgresql' !== $this->connection->getDatabasePlatform()->getName(),
            'Migration can only be executed safely on \'postgresql\'.'
        );

        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE activity DROP CONSTRAINT FK_AC74095AC6C98E06');
        $this->addSql('ALTER TABLE duration_type DROP CONSTRAINT FK_DF8E2F92C6C98E06');
        $this->addSql('ALTER TABLE number DROP CONSTRAINT FK_96901F54C6C98E06');
        $this->addSql('ALTER TABLE price DROP CONSTRAINT FK_CAC822D9C6C98E06');
        $this->addSql('ALTER TABLE contract DROP CONSTRAINT FK_E98F2859C6C98E06');
        $this->addSql('ALTER TABLE contract_statuses DROP CONSTRAINT FK_DE4705EEC6C98E06');
        $this->addSql('ALTER TABLE subscription_package DROP CONSTRAINT FK_AD7D870EC6C98E06');
        $this->addSql('ALTER TABLE currency DROP CONSTRAINT FK_6956883FC6C98E06');
        $this->addSql('ALTER TABLE package_statuses DROP CONSTRAINT FK_AF718A7FC6C98E06');
        $this->addSql('ALTER TABLE package DROP CONSTRAINT FK_DE686795C6C98E06');
        $this->addSql('ALTER TABLE service_statuses DROP CONSTRAINT FK_B286452CC6C98E06');
        $this->addSql('ALTER TABLE service DROP CONSTRAINT FK_E19D9AD2C6C98E06');
        $this->addSql('DROP SEQUENCE service_provider_id_seq CASCADE');
        $this->addSql('DROP TABLE service_provider');
        $this->addSql('DROP INDEX IDX_AF718A7FC6C98E06');
        $this->addSql('ALTER TABLE package_statuses DROP service_provider_id');
        $this->addSql('DROP INDEX IDX_B286452CC6C98E06');
        $this->addSql('ALTER TABLE service_statuses DROP service_provider_id');
        $this->addSql('DROP INDEX IDX_CAC822D9C6C98E06');
        $this->addSql('ALTER TABLE price DROP service_provider_id');
        $this->addSql('DROP INDEX IDX_DE4705EEC6C98E06');
        $this->addSql('ALTER TABLE contract_statuses DROP service_provider_id');
        $this->addSql('DROP INDEX IDX_6956883FC6C98E06');
        $this->addSql('ALTER TABLE currency DROP service_provider_id');
        $this->addSql('DROP INDEX IDX_E98F2859C6C98E06');
        $this->addSql('ALTER TABLE contract DROP service_provider_id');
        $this->addSql('DROP INDEX IDX_DF8E2F92C6C98E06');
        $this->addSql('ALTER TABLE duration_type DROP service_provider_id');
        $this->addSql('DROP INDEX IDX_AC74095AC6C98E06');
        $this->addSql('ALTER TABLE activity DROP service_provider_id');
        $this->addSql('DROP INDEX IDX_E19D9AD2C6C98E06');
        $this->addSql('ALTER TABLE service DROP service_provider_id');
        $this->addSql('DROP INDEX IDX_DE686795C6C98E06');
        $this->addSql('ALTER TABLE package DROP service_provider_id');
        $this->addSql('DROP INDEX IDX_AD7D870EC6C98E06');
        $this->addSql('ALTER TABLE subscription_package DROP service_provider_id');
        $this->addSql('DROP INDEX IDX_96901F54C6C98E06');
        $this->addSql('ALTER TABLE number DROP service_provider_id');
    }
}
