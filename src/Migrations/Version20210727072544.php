<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210727072544 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Drop table recommendation_leaf_fertiliser_comments_ph_config and create recommendation_leaf_fertiliser_comments_config.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP SEQUENCE recommendation_leaf_fertiliser_comments_ph_config_id_seq CASCADE');
        $this->addSql('DROP TABLE recommendation_leaf_fertiliser_comments_ph_config');

        $this->addSql('CREATE TABLE recommendation_leaf_fertiliser_comments_config (id SERIAL NOT NULL, model_id INT NOT NULL, crop_ids integer[] DEFAULT NULL, result_elements result_element_enum[], range numrange NOT NULL, comment_text TEXT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_28736A8A7975B7E7 ON recommendation_leaf_fertiliser_comments_config (model_id)');
        $this->addSql('COMMENT ON COLUMN recommendation_leaf_fertiliser_comments_config.crop_ids IS \'(DC2Type:integer[])\'');
        $this->addSql('COMMENT ON COLUMN recommendation_leaf_fertiliser_comments_config.range IS \'(DC2Type:numrange)\'');
        $this->addSql('ALTER TABLE recommendation_leaf_fertiliser_comments_config ADD CONSTRAINT FK_28736A8A7975B7E7 FOREIGN KEY (model_id) REFERENCES recommendation_models_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP SEQUENCE recommendation_leaf_fertiliser_comments_config_id_seq CASCADE');
        $this->addSql('DROP TABLE recommendation_leaf_fertiliser_comments_config');

        $this->addSql('CREATE TABLE recommendation_leaf_fertiliser_comments_ph_config (id SERIAL NOT NULL, model_id INT NOT NULL, fertiliser_type VARCHAR(255) DEFAULT NULL, result_element result_element_enum NOT NULL, range numrange NOT NULL, comment_text TEXT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_5d5785927975b7e7 ON recommendation_leaf_fertiliser_comments_ph_config (model_id)');
        $this->addSql('COMMENT ON COLUMN recommendation_leaf_fertiliser_comments_ph_config.range IS \'(DC2Type:numrange)\'');
        $this->addSql('ALTER TABLE recommendation_leaf_fertiliser_comments_ph_config ADD CONSTRAINT fk_5d5785927975b7e7 FOREIGN KEY (model_id) REFERENCES recommendation_models_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }
}
