<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231102132133 extends AbstractBaseMigration
{
    public const GS_DB_NAME = 'susi_main_v5';

    public function getDescription(): string
    {
        return '
            NOTE: This migration will fill the uuid column only for farms which have a gs_organization_id different than null in su_users_farming (tf DB);
            
            Fill the uuid column in su_users (laravel project) and su_users_farming (tf project)
        ';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');

        $gsDBConfig = $this->getGSDbConfig(self::GS_DB_NAME);
        $tfDBConfig = $this->getTFDbConfig();

        $farmsDataSql = 'SELECT
                tf_farms.farm_id AS tf_farm_id,
                gs_farms.farm_id AS gs_farm_id,
                gs_farms.farm_name,
                gs_farms.organization_id,
                uuid_generate_v4() AS uuid
            FROM
                dblink(:gsDBConf, $$
                    SELECT
                        id,
                        "name",
                        organization_id
                    FROM
                        su_farms
                $$) AS gs_farms(farm_id INT, farm_name TEXT, organization_id INT)
            FULL JOIN dblink(:tfDBConf, $$
                    SELECT
                        suf.id,
                        suf."name",
                        su.gs_organization_id
                    FROM
                        su_users_farming suf
                    JOIN su_users su
                        ON suf.user_id = su.id
                $$) AS tf_farms(farm_id INT, farm_name TEXT, organization_id INT)
            ON
                gs_farms.farm_name = tf_farms.farm_name
                AND gs_farms.organization_id = tf_farms.organization_id
        ';

        $farmsDataStmt = $this->connection->executeQuery(
            $farmsDataSql,
            [
                'gsDBConf' => $gsDBConfig,
                'tfDBConf' => $tfDBConfig,
            ],
            [
                'gsDBConf' => ParameterType::STRING,
                'tfDBConf' => ParameterType::STRING,
            ],
        );

        $farmsData = $farmsDataStmt->fetchAllAssociative();

        $gsValuesStr = '';
        $tfValuesStr = '';

        foreach ($farmsData as $farm) {
            if (isset($farm['gs_farm_id'])) {
                $gsValuesStr .= "({$farm['gs_farm_id']}, '{$farm['uuid']}'::UUID),";
            }

            if (isset($farm['tf_farm_id'])) {
                $tfValuesStr .= "({$farm['tf_farm_id']}, '{$farm['uuid']}'::UUID),";
            }
        }

        // remove the last ','
        $gsValuesStr = substr_replace($gsValuesStr, '', -1);
        $tfValuesStr = substr_replace($tfValuesStr, '', -1);

        $tfFarmsUpdateSql = "UPDATE
                su_users_farming
            SET
                uuid = farm_data.uuid
            FROM (
                VALUES
                {$tfValuesStr}
            ) AS farm_data(id, uuid)
            WHERE
	            su_users_farming.id = farm_data.id
        ";

        $gsFarmsUpdateSql = "UPDATE
                su_farms
            SET
                uuid = farm_data.uuid
            FROM (
                VALUES
                {$gsValuesStr}
            ) AS farm_data(id, uuid)
            WHERE
	            su_farms.id = farm_data.id
        ";

        $this->addSql(
            'SELECT dblink(:tfDBConf, :tfFarmsUpdateSql)',
            [
                'tfDBConf' => $tfDBConfig,
                'tfFarmsUpdateSql' => $tfFarmsUpdateSql,
            ],
            [
                'tfDBConf' => ParameterType::STRING,
                'tfFarmsUpdateSql' => ParameterType::STRING,
            ]
        );

        $this->addSql(
            'SELECT dblink(:gsDBConf, :gsFarmsUpdateSql)',
            [
                'gsDBConf' => $gsDBConfig,
                'gsFarmsUpdateSql' => $gsFarmsUpdateSql,
            ],
            [
                'gsDBConf' => ParameterType::STRING,
                'gsFarmsUpdateSql' => ParameterType::STRING,
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $gsDBConfig = $this->getGSDbConfig(self::GS_DB_NAME);
        $tfDBConfig = $this->getTFDbConfig();

        $tfFarmsUpdateSql = 'UPDATE
                su_users_farming
            SET
                uuid = NULL
        ';

        $gsFarmsUpdateSql = 'UPDATE
                su_farms
            SET
                uuid = NULL       
        ';

        $this->addSql(
            'SELECT dblink(:tfDBConf, :tfFarmsUpdateSql)',
            [
                'tfDBConf' => $tfDBConfig,
                'tfFarmsUpdateSql' => $tfFarmsUpdateSql,
            ],
            [
                'tfDBConf' => ParameterType::STRING,
                'tfFarmsUpdateSql' => ParameterType::STRING,
            ]
        );

        $this->addSql(
            'SELECT dblink(:gsDBConf, :gsFarmsUpdateSql)',
            [
                'gsDBConf' => $gsDBConfig,
                'gsFarmsUpdateSql' => $gsFarmsUpdateSql,
            ],
            [
                'gsDBConf' => ParameterType::STRING,
                'gsFarmsUpdateSql' => ParameterType::STRING,
            ]
        );
        // this down() migration is auto-generated, please modify it to your needs
    }
}
