<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190508124756 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf(
            'postgresql' !== $this->connection->getDatabasePlatform()->getName(),
            'Migration can only be executed safely on \'postgresql\'.'
        );

        $this->addSql('CREATE SEQUENCE activity_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE duration_type_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE price_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE contract_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE contract_statuses_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE currency_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE package_statuses_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE package_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE service_statuses_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE service_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE activity (id INT NOT NULL, slug VARCHAR(20) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE duration_type (id INT NOT NULL, slug VARCHAR(20) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE price (id INT NOT NULL, subscription_id INT NOT NULL, period INT NOT NULL, price NUMERIC(10, 4) DEFAULT NULL, minimum_amount NUMERIC(10, 4) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_CAC822D99A1887DC ON price (subscription_id)');
        $this->addSql('CREATE TABLE contract (id INT NOT NULL, status_id INT NOT NULL, currency_id INT NOT NULL, parent_id INT DEFAULT NULL, contract_date DATE NOT NULL, start_date DATE NOT NULL, end_date DATE NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, is_annex BOOLEAN NOT NULL, type VARCHAR(255) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_E98F28596BF700BD ON contract (status_id)');
        $this->addSql('CREATE INDEX IDX_E98F285938248176 ON contract (currency_id)');
        $this->addSql('CREATE INDEX IDX_E98F2859727ACA70 ON contract (parent_id)');
        $this->addSql('CREATE TABLE service_contracts (id INT NOT NULL, amount NUMERIC(10, 4) NOT NULL, price NUMERIC(10, 4) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE contract_statuses (id INT NOT NULL, slug VARCHAR(20) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE subscription_contracts (id INT NOT NULL, duration_type_id INT NOT NULL, duration INT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_3BEF6BCF80CA3F3B ON subscription_contracts (duration_type_id)');
        $this->addSql('CREATE TABLE currency (id INT NOT NULL, slug VARCHAR(255) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE package_statuses (id INT NOT NULL, slug VARCHAR(20) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE package (id INT NOT NULL, slug VARCHAR(20) UNIQUE NOT NULL , PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE service_statuses (id INT NOT NULL, slug VARCHAR(20) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE service (id INT NOT NULL, slug VARCHAR(20) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE service_activity (service_id INT NOT NULL, activity_id INT NOT NULL, PRIMARY KEY(service_id, activity_id))');
        $this->addSql('CREATE INDEX IDX_55025267ED5CA9E6 ON service_activity (service_id)');
        $this->addSql('CREATE INDEX IDX_5502526781C06096 ON service_activity (activity_id)');
        $this->addSql('CREATE TABLE service_package (service_id INT NOT NULL, package_id INT NOT NULL, PRIMARY KEY(service_id, package_id))');
        $this->addSql('CREATE INDEX IDX_11EC3509ED5CA9E6 ON service_package (service_id)');
        $this->addSql('CREATE INDEX IDX_11EC3509F44CABFF ON service_package (package_id)');
        $this->addSql('ALTER TABLE price ADD CONSTRAINT FK_CAC822D99A1887DC FOREIGN KEY (subscription_id) REFERENCES subscription_contracts (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE contract ADD CONSTRAINT FK_E98F28596BF700BD FOREIGN KEY (status_id) REFERENCES contract_statuses (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE contract ADD CONSTRAINT FK_E98F285938248176 FOREIGN KEY (currency_id) REFERENCES currency (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE contract ADD CONSTRAINT FK_E98F2859727ACA70 FOREIGN KEY (parent_id) REFERENCES contract (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE service_contracts ADD CONSTRAINT FK_51C19339BF396750 FOREIGN KEY (id) REFERENCES contract (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE subscription_contracts ADD CONSTRAINT FK_3BEF6BCF80CA3F3B FOREIGN KEY (duration_type_id) REFERENCES duration_type (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE subscription_contracts ADD CONSTRAINT FK_3BEF6BCFBF396750 FOREIGN KEY (id) REFERENCES contract (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE service_activity ADD CONSTRAINT FK_55025267ED5CA9E6 FOREIGN KEY (service_id) REFERENCES service (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE service_activity ADD CONSTRAINT FK_5502526781C06096 FOREIGN KEY (activity_id) REFERENCES activity (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE service_package ADD CONSTRAINT FK_11EC3509ED5CA9E6 FOREIGN KEY (service_id) REFERENCES service (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE service_package ADD CONSTRAINT FK_11EC3509F44CABFF FOREIGN KEY (package_id) REFERENCES package (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf(
            'postgresql' !== $this->connection->getDatabasePlatform()->getName(),
            'Migration can only be executed safely on \'postgresql\'.'
        );

        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE service_activity DROP CONSTRAINT FK_5502526781C06096');
        $this->addSql('ALTER TABLE subscription_contracts DROP CONSTRAINT FK_3BEF6BCF80CA3F3B');
        $this->addSql('ALTER TABLE contract DROP CONSTRAINT FK_E98F2859727ACA70');
        $this->addSql('ALTER TABLE service_contracts DROP CONSTRAINT FK_51C19339BF396750');
        $this->addSql('ALTER TABLE subscription_contracts DROP CONSTRAINT FK_3BEF6BCFBF396750');
        $this->addSql('ALTER TABLE contract DROP CONSTRAINT FK_E98F28596BF700BD');
        $this->addSql('ALTER TABLE price DROP CONSTRAINT FK_CAC822D99A1887DC');
        $this->addSql('ALTER TABLE contract DROP CONSTRAINT FK_E98F285938248176');
        $this->addSql('ALTER TABLE service_package DROP CONSTRAINT FK_11EC3509F44CABFF');
        $this->addSql('ALTER TABLE service_activity DROP CONSTRAINT FK_55025267ED5CA9E6');
        $this->addSql('ALTER TABLE service_package DROP CONSTRAINT FK_11EC3509ED5CA9E6');
        $this->addSql('DROP SEQUENCE activity_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE duration_type_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE price_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE contract_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE contract_statuses_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE currency_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE package_statuses_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE package_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE service_statuses_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE service_id_seq CASCADE');
        $this->addSql('DROP TABLE activity');
        $this->addSql('DROP TABLE duration_type');
        $this->addSql('DROP TABLE price');
        $this->addSql('DROP TABLE contract');
        $this->addSql('DROP TABLE service_contracts');
        $this->addSql('DROP TABLE contract_statuses');
        $this->addSql('DROP TABLE subscription_contracts');
        $this->addSql('DROP TABLE currency');
        $this->addSql('DROP TABLE package_statuses');
        $this->addSql('DROP TABLE package');
        $this->addSql('DROP TABLE service_statuses');
        $this->addSql('DROP TABLE service');
        $this->addSql('DROP TABLE service_activity');
        $this->addSql('DROP TABLE service_package');
    }
}
