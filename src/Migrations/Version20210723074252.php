<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210723074252 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create table recommendations_vra_orders';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE TABLE recommendations_vra_orders (id SERIAL, order_uuid VARCHAR(255) NOT NULL, recommendation_id INT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_73904ED72576E0AA ON recommendations_vra_orders (order_uuid)');
        $this->addSql('CREATE INDEX IDX_73904ED7F44CABBB ON recommendations_vra_orders (recommendation_id)');
        $this->addSql('ALTER TABLE recommendations_vra_orders ADD CONSTRAINT FK_73904ED72576A2DD FOREIGN KEY (recommendation_id) REFERENCES recommendations (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE recommendations_vra_orders DROP CONSTRAINT FK_73904ED72576A2DD');
        $this->addSql('DROP TABLE recommendations_vra_orders');
    }
}
