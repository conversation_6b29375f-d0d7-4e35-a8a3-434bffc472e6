<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210420133414 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Add lab_analysis_package_group for packages AB VRA full OM and  AB ISO full OM';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $abVraFullOmId = $this->getPackageId('AB VRA full OM');
        $abIsoFullOmId = $this->getPackageId('AB ISO full OM');

        $ABSoilpH = $this->getLabAnalysisGroupId('ABSoilpH');
        $ABSoilN = $this->getLabAnalysisGroupId('ABSoilN');
        $ABSoilP = $this->getLabAnalysisGroupId('ABSoilP');
        $ABSoilK = $this->getLabAnalysisGroupId('ABSoilK');
        $ABSoilCaMgNa = $this->getLabAnalysisGroupId('ABSoilCaMgNa');
        $ABSoilS = $this->getLabAnalysisGroupId('ABSoilS');
        $ABSoilFeCuMnZnB = $this->getLabAnalysisGroupId('ABSoilFeCuMnZnB');
        $ABSoilOM = $this->getLabAnalysisGroupId('ABSoilOM');

        $sql = "INSERT INTO lab_analysis_package_group (
            package_id, package_type, lab_analysis_group_id
        )
        VALUES
            (:abVraFullOmId,'subscription', :ABSoilpH),
            (:abVraFullOmId,'subscription', :ABSoilN),
            (:abVraFullOmId,'subscription', :ABSoilP),
            (:abVraFullOmId,'subscription', :ABSoilK),
            (:abVraFullOmId,'subscription', :ABSoilCaMgNa),
            (:abVraFullOmId,'subscription', :ABSoilS),
            (:abVraFullOmId,'subscription', :ABSoilFeCuMnZnB),
            (:abVraFullOmId,'subscription', :ABSoilOM),
            
            (:abIsoFullOmId,'subscription', :ABSoilpH),
            (:abIsoFullOmId,'subscription', :ABSoilN),
            (:abIsoFullOmId,'subscription', :ABSoilP),
            (:abIsoFullOmId,'subscription', :ABSoilK),
            (:abIsoFullOmId,'subscription', :ABSoilCaMgNa),
            (:abIsoFullOmId,'subscription', :ABSoilS),
            (:abIsoFullOmId,'subscription', :ABSoilFeCuMnZnB),
            (:abIsoFullOmId,'subscription', :ABSoilOM)
        ";
        $this->addSql($sql, [
            'abVraFullOmId' => $abVraFullOmId,
            'abIsoFullOmId' => $abIsoFullOmId,
            'ABSoilpH' => $ABSoilpH,
            'ABSoilN' => $ABSoilN,
            'ABSoilP' => $ABSoilP,
            'ABSoilK' => $ABSoilK,
            'ABSoilCaMgNa' => $ABSoilCaMgNa,
            'ABSoilS' => $ABSoilS,
            'ABSoilFeCuMnZnB' => $ABSoilFeCuMnZnB,
            'ABSoilOM' => $ABSoilOM,
        ]);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $abVraFullOmId = $this->getPackageId('AB VRA full OM');
        $abIsoFullOmId = $this->getPackageId('AB ISO full OM');

        $this->addSql('DELETE FROM lab_analysis_package_group
                            where package_id in (:abVraFullOmId, :abIsoFullOmId);
                            ', ['abVraFullOmId' => $abVraFullOmId, 'abIsoFullOmId' => $abIsoFullOmId]);
    }
}
