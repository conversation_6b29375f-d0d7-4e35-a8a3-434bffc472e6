<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210610124712 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create table recommendation_crop_model_config_values.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE TABLE recommendation_crop_model_config_values (id SERIAL, model_id INT NOT NULL, crop_ids integer[] NOT NULL, parameter recommendation_crop_model_parameter_enum NOT NULL, value DOUBLE PRECISION DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('COMMENT ON COLUMN recommendation_crop_model_config_values.crop_ids IS \'(DC2Type:integer[])\'');
        $this->addSql('ALTER TABLE recommendation_crop_model_config_values ADD CONSTRAINT FK_F3C737397975B7E7 FOREIGN KEY (model_id) REFERENCES recommendation_models_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_F3C737397975B7E7 ON recommendation_crop_model_config_values (model_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP TABLE recommendation_crop_model_config_values');
    }
}
