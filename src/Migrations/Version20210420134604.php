<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210420134604 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Inert groups CaOrganic matter and Cation exchange capacity and elements in meta_elements_groups';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $metaGroups = $this->getMetaGroups();

        $this->addSql("DELETE FROM meta_elements_groups
                            where group_id = :carbonatesId
                            and element = 'OM';
                            ", [
            'carbonatesId' => $metaGroups['Carbonates'],
        ]);

        $this->addSql('INSERT INTO meta_elements_groups (group_id, element) VALUES
            (:organicMatterId, \'OM\'),
            (:cationExchangeCapacityId, \'CEC\'),
            (:cationExchangeCapacityId, \'BS\'),
            (:cationExchangeCapacityId, \'K+\'),
            (:cationExchangeCapacityId, \'Ca2+\'),
            (:cationExchangeCapacityId, \'Mg2+\'),
            (:cationExchangeCapacityId, \'Na+\'),
            (:cationExchangeCapacityId, \'H+\')
        ', [
            'organicMatterId' => $metaGroups['Organic matter'],
            'cationExchangeCapacityId' => $metaGroups['Cation exchange capacity'],
        ]);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $metaGroups = $this->getMetaGroups();
        $this->addSql('DELETE FROM meta_elements_groups
                            where group_id in (:organicMatterId, :cationExchangeCapacityId);
                            ', [
            'organicMatterId' => $metaGroups['Organic matter'],
            'cationExchangeCapacityId' => $metaGroups['Cation exchange capacity'],
        ]);

        $this->addSql('INSERT INTO meta_elements_groups (group_id, element) VALUES
            (:carbonatesId, \'OM\')
        ', [
            'carbonatesId' => $metaGroups['Carbonates'],
        ]);
    }
}
