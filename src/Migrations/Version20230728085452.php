<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230728085452 extends AbstractMigration
{
    public function isTransactional(): bool
    {
        return false;
    }

    public function getDescription(): string
    {
        return 'Add values: \'Total Carbon\', \'OM (LOI)\', \'Total carbonates\', \'Active carbonates\', \'OM (Dumas)\', \'Active carbon\'  to elements_enum.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS 'Total Carbon'");
        $this->addSql("ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS 'OM (LOI)'");
        $this->addSql("ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS 'Total carbonates'");
        $this->addSql("ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS 'Active carbonates'");
        $this->addSql("ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS 'OM (Dumas)'");
        $this->addSql("ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS 'Active carbon'");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }
}
