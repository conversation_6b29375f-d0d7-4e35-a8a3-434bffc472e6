<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191204170716 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("CREATE TYPE point_states_enum AS ENUM ('Pending', 'Sampling', 'Sampled', 'ReceivedInLab')");

        $this->addSql('CREATE SEQUENCE package_grid_points_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE package_grid_points (id INT NOT NULL, point_gid INT NOT NULL, sample_id INT NOT NULL, plot_id INT NOT NULL, package_id INT NOT NULL, package_type VARCHAR(63) NOT NULL, grid_type VARCHAR(63) DEFAULT NULL, barcode VARCHAR(127) DEFAULT NULL, lab_number VARCHAR(63) DEFAULT NULL, state point_states_enum, state_updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('ALTER TABLE lab_elements_results ADD package_grid_points_id INT NOT NULL');
        $this->addSql('ALTER TABLE lab_elements_results ADD CONSTRAINT FK_2E6761B62BFCFE5E FOREIGN KEY (package_grid_points_id) REFERENCES package_grid_points (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_2E6761B62BFCFE5E ON lab_elements_results (package_grid_points_id)');
        $this->addSql('ALTER TABLE package_grid_points ALTER state SET DEFAULT \'Pending\'');
        $this->addSql('ALTER TABLE package_grid_points ALTER state SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE lab_elements_results DROP CONSTRAINT FK_2E6761B62BFCFE5E');
        $this->addSql('DROP SEQUENCE package_grid_points_id_seq CASCADE');
        $this->addSql('DROP TABLE package_grid_points');
        $this->addSql('DROP INDEX IDX_2E6761B62BFCFE5E');
        $this->addSql('ALTER TABLE lab_elements_results DROP package_grid_points_id');
    }
}
