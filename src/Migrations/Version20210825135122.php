<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210825135122 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Seed recommendation_models_config table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("
            INSERT INTO recommendation_models_config (service_provider_id, name, calc_model_id) VALUES
                (2,'Vantage',1),
                (3,'Agricost',1),
                (5,'Spectr Аgro',1),
                (6,'NIK Italia',1)
        ");

        /*
         *  The column calc_model_id from table recommendation_models_config will be removed
         *  and the column calc_model_id from table recommenda_calc_model_config must be renamed to 'model_id'
         *  and set as foreign key to recommendation_models_config (id).
         *
         *  At this time set the calc_model_id = id.
         */
        $this->addSql("
            UPDATE recommendation_models_config SET calc_model_id = id
            WHERE
                service_provider_id IN (2, 3, 5, 6) 
                AND name IN ('Vantage ','Agricost', 'Spectr Аgro', 'NIK Italia')
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql("
            DELETE FROM recommendation_models_config 
            WHERE
                service_provider_id IN (2, 3, 5, 6) 
                AND name IN ('Vantage ','Agricost', 'Spectr Аgro', 'NIK Italia'
        )");
    }
}
