<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210805062335 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add columns \'unit\' and \'method\' to table lab_analysis_group_element.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE lab_analysis_group_element
            ADD COLUMN unit VARCHAR(50) DEFAULT NULL,
            ADD COLUMN method VARCHAR(50) DEFAULT NULL
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE lab_analysis_group_element
            DROP COLUMN unit,
            DROP COLUMN method
        ');
    }
}
