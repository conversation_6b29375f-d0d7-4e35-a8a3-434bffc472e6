<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220429091501 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create table lab_analysis_group_element_visual_order';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE TABLE lab_analysis_group_element_visual_order (id SERIAL NOT NULL, lab_analysis_group_element_id INT NOT NULL, service_provider_id INT NOT NULL, visual_order INT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_814F8C9CCB7C4B31 ON lab_analysis_group_element_visual_order (lab_analysis_group_element_id)');
        $this->addSql('ALTER TABLE lab_analysis_group_element_visual_order ADD CONSTRAINT FK_814F8C9CCB7C4B31 FOREIGN KEY (lab_analysis_group_element_id) REFERENCES lab_analysis_group_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP TABLE lab_analysis_group_element_visual_order');
    }
}
