<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210823082348 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Insert lab_element_interpretations_config for element OM fro nikas service provider';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $serviceProviderId = $this->getServiceProviderId('nikas');

        $elementIds = $this->getElements();

        $this->addSql("
            INSERT INTO lab_element_interpretations_config (element_id, service_provider_id, class_id, range, color)
            VALUES           
                ({$elementIds['OM']}, {$serviceProviderId}, 1, '(,1]'::numrange, '#E0CEC2'),
                ({$elementIds['OM']}, {$serviceProviderId}, 2, '(1,2]'::numrange, '#CBAD99'),
                ({$elementIds['OM']}, {$serviceProviderId}, 3, '(2,3]'::numrange, '#B79075'),
                ({$elementIds['OM']}, {$serviceProviderId}, 7, '(3,4]'::numrange, '#815C43'),
                ({$elementIds['OM']}, {$serviceProviderId}, 4, '(4,5]'::numrange, '#5B412F'),
                ({$elementIds['OM']}, {$serviceProviderId}, 5, '(5,)'::numrange, '#3D2B1F')
        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $serviceProviderId = $this->getServiceProviderId('nikas');
        $elementIds = $this->getElements();

        $sql = 'DELETE FROM lab_element_interpretations_config WHERE service_provider_id  = :serviceProviderId and element_id = :elementId';
        $this->addSql($sql, ['serviceProviderId' => $serviceProviderId, 'elementId' => $elementIds['OM']]);
    }
}
