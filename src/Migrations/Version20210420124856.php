<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210420124856 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Create packages AB VRA full OM CEC and AB ISO full OM CEC ';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $serviceProviderIdId = $this->getServiceProviderId('spectr_agro');
        $sql = "INSERT INTO package (
            slug,
            service_provider_id,
            is_active,
            contain_fields,
            slug_short,
            is_sampling,
            has_station,
            integration,
            is_vra,
            is_full_sampling
        )
        VALUES
            ('AB VRA full OM CEC', :serviceProviderIdId , true, true, 'VRA+', true, null, null, true, true),
            ('AB ISO full OM CEC', :serviceProviderIdId , true, true, 'ISO+', true, null, null, false, true);
        ";

        $this->addSql($sql, ['serviceProviderIdId' => $serviceProviderIdId]);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $serviceProviderIdId = $this->getServiceProviderId('spectr_agro');
        $this->addSql("DELETE FROM package 
                            where slug in ('AB VRA full OM CEC', 'AB ISO full OM CEC')
                            and service_provider_id = :serviceProviderIdId
                            ", ['serviceProviderIdId' => $serviceProviderIdId]);
    }
}
