<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210909054850 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Seed column visual_order in table recommendation_calc_model_config for all service providers';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("UPDATE recommendation_calc_model_config AS rcmc
            SET visual_order = t.visual_order
            FROM (VALUES
                    ('Add_N_fall',      1),
                    ('Add_N_total',     2),
                    ('Add_P_total',     3),
                    ('Add_K_total',     4),
                    ('Add_S_total',     5),
                    ('Add_Ca',          6),
                    ('Add_Mg',          7),
                    ('Add_Zn',          8),
                    ('Add_Mn',          9),
                    ('Add_Cu',          10),
                    ('Add_Fe',          11),
                    ('Add_B',           12),
                    ('Add_Mo',          13)
                ) AS t (result_element, visual_order)
            WHERE
                rcmc.result_element = t.result_element::result_element_enum
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('UPDATE recommendation_calc_model_config AS rcmc SET visual_order = NULL');
    }
}
