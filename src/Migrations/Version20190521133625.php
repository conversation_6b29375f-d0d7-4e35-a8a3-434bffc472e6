<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190521133625 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf(
            'postgresql' !== $this->connection->getDatabasePlatform()->getName(),
            'Migration can only be executed safely on \'postgresql\'.'
        );

        $this->addSql('ALTER TABLE duration_type ADD start_day INT DEFAULT NULL');
        $this->addSql('ALTER TABLE duration_type ADD end_day INT DEFAULT NULL');
        $this->addSql('ALTER TABLE duration_type DROP start_date');
        $this->addSql('ALTER TABLE duration_type DROP end_date');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf(
            'postgresql' !== $this->connection->getDatabasePlatform()->getName(),
            'Migration can only be executed safely on \'postgresql\'.'
        );

        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE duration_type ADD start_date DATE DEFAULT NULL');
        $this->addSql('ALTER TABLE duration_type ADD end_date DATE DEFAULT NULL');
        $this->addSql('ALTER TABLE duration_type DROP start_day');
        $this->addSql('ALTER TABLE duration_type DROP end_day');
    }
}
