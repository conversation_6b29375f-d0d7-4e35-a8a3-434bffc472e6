<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210622122337 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create postgresql function add_n_total.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("
            CREATE FUNCTION add_n_total (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_n NUMERIC,
                vol_density NUMERIC,
                soil_abs_n NUMERIC,
                organic_n NUMERIC,
                add_organic_n NUMERIC,
                fall_n_part NUMERIC,
                fertiliser_abs_n NUMERIC,
                add_n_reduction_sampling NUMERIC,
                humus NUMERIC,
                sampling_date DATE
            )
                RETURNS NUMERIC
                AS $$
                    DECLARE
                        result NUMERIC;
                    BEGIN
                        WITH calculations AS (
                            SELECT
                                (
                                    CASE WHEN fall_n_part > 0 THEN
                                        (yield * uptake_n / 100) - (1 * organic_n * add_organic_n) - (avg_element_result * vol_density * soil_abs_n)
                                    ELSE 
                                        (yield * uptake_n / 100)
                                        - (
                                            CASE WHEN sampling_date <=  now() - '2 months'::INTERVAL THEN
                                                (avg_element_result * add_n_reduction_sampling * vol_density * soil_abs_n)
                                            ELSE
                                                (avg_element_result * vol_density * soil_abs_n)
                                            END
                                        )
                                        - (humus * organic_n * add_organic_n)
                                    END
                                ) / fertiliser_abs_n  AS value
                        )
                        SELECT
                            (calculations.value - add_n_fall(yield, avg_element_result, uptake_n, vol_density, soil_abs_n, organic_n, add_organic_n, fall_n_part, fertiliser_abs_n, add_n_reduction_sampling, humus, sampling_date))::NUMERIC
                            INTO result
                        FROM
                            calculations;
                        
                        RETURN result;
                    END
                $$ LANGUAGE plpgsql
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DROP FUNCTION IF EXISTS add_n_total (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_n NUMERIC,
                vol_density NUMERIC,
                soil_abs_n NUMERIC,
                organic_n NUMERIC,
                add_organic_n NUMERIC,
                fall_n_part NUMERIC,
                fertiliser_abs_n NUMERIC,
                add_n_reduction_sampling NUMERIC,
                humus NUMERIC,
                sampling_date DATE
            )
        ');
    }
}
