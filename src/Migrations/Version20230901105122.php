<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230901105122 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create table recommendation_comments_log in schema \'loggable\'';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE loggable.recommendation_comments_log (id SERIAL, action VARCHAR(8) NOT NULL, logged_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, object_id VARCHAR(64) DEFAULT NULL, object_class VARCHAR(191) NOT NULL, version INT NOT NULL, data TEXT DEFAULT NULL, username VARCHAR(191) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX recommendation_comments_log_class_lookup_idx ON loggable.recommendation_comments_log (object_class)');
        $this->addSql('CREATE INDEX recommendation_comments_log_date_lookup_idx ON loggable.recommendation_comments_log (logged_at)');
        $this->addSql('CREATE INDEX recommendation_comments_log_user_lookup_idx ON loggable.recommendation_comments_log (username)');
        $this->addSql('CREATE INDEX recommendation_comments_log_version_lookup_idx ON loggable.recommendation_comments_log (object_id, object_class, version)');
        $this->addSql('COMMENT ON COLUMN loggable.recommendation_comments_log.data IS \'(DC2Type:array)\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP SEQUENCE loggable.recommendation_comments_log_id_seq CASCADE');
        $this->addSql('DROP TABLE loggable.recommendation_comments_log');
    }
}
