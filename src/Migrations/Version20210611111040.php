<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210611111040 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Seed table recommendation_comment. ';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("INSERT INTO recommendation_comment (recommendation_id, comment_type, param, value) VALUES
            (1, 'element', 'Add_N_total', 'За осигуряване на добро развитие на културата през есенно-зимния период е необходимо част от азотната норма да се внесе предсеитбено/присеитбено.Препоръчително е необходимото за пролетно-летния период количество азот да се раздели на две части, с които да се извършат подхранвания. Желателно е първото подхранване да се извърши при възобновяване на вегетацията в края на зимата, а второто – от фаза „вретенене“ до „начало на изкласяване“.'),
            (1, 'element', 'Add_P_total', NULL),
            (1, 'element', 'Add_K_total', 'custom'),
            (1, 'element', 'Add_S_total', NULL),
            (1, 'element', 'Add_N_fall', NULL),
            (1, 'custom', NULL, NULL)
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM recommendation_comment WHERE recommendation_id = 1');
    }
}
