<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230822102248 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add column valid_from to the recommendations table. Seed the column.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE recommendations ADD COLUMN valid_from DATE');
        $this->addSql('UPDATE recommendations SET valid_from = created_at::DATE');
        $this->addSql('ALTER TABLE recommendations ALTER COLUMN valid_from SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE recommendations DROP COLUMN valid_from');
    }
}
