<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210625051517 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove the old records and seed table recommendation_calc_model_config including columns  element_id and params.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DELETE FROM recommendation_calc_model_config');
        $this->addSql("
            INSERT INTO recommendation_calc_model_config (calc_model_id, result_element, element_id, params)
            SELECT
                rcmc.calc_model_id,
                rcmc.result_element::result_element_enum,
                CASE 
                    WHEN rcmc.result_element = 'Need_N_total' OR rcmc.result_element = 'Add_N_total' OR rcmc.result_element = 'Add_N_fall'
                        THEN max(lage.id) FILTER (WHERE lage.\"element\" = 'TMN')
                    WHEN rcmc.result_element = 'Need_P_total' OR rcmc.result_element = 'Add_P_total'
                        THEN max(lage.id) FILTER (WHERE lage.\"element\" = 'P2O5')
                    WHEN rcmc.result_element = 'Need_K_total' OR rcmc.result_element = 'Add_K_total'
                        THEN max(lage.id) FILTER (WHERE lage.\"element\" = 'K2O')
                    WHEN rcmc.result_element = 'Need_S_total' OR rcmc.result_element = 'Add_S_total'
                        THEN max(lage.id) FILTER (WHERE lage.\"element\" = 'S')
                    WHEN rcmc.result_element = 'pH_predominant'
                        THEN max(lage.id) FILTER (WHERE lage.\"element\" = 'pH')
                    WHEN rcmc.result_element = 'Add_Cu'
                        THEN max(lage.id) FILTER (WHERE lage.\"element\" = 'Cu')
                    WHEN rcmc.result_element = 'Add_Mn'
                        THEN max(lage.id) FILTER (WHERE lage.\"element\" = 'Mn')
                    WHEN rcmc.result_element = 'Add_Mo'
                        THEN max(lage.id) FILTER (WHERE lage.\"element\" = 'Mo')
                    WHEN rcmc.result_element = 'Add_B'
                        THEN max(lage.id) FILTER (WHERE lage.\"element\" = 'B')
                    WHEN rcmc.result_element = 'Add_Fe'
                        THEN max(lage.id) FILTER (WHERE lage.\"element\" = 'Fe')
                    WHEN rcmc.result_element = 'Add_Zn'
                        THEN max(lage.id) FILTER (WHERE lage.\"element\" = 'Zn')
                END AS element_id,
                CASE
                    WHEN rcmc.result_element = 'Add_P_total'
                        THEN ARRAY['Uptake P','Soil abs P','Fertiliser abs P', 'Vol density']::recommendation_crop_model_parameter_enum[]
                    WHEN rcmc.result_element = 'Add_K_total'
                        THEN ARRAY['Uptake K', 'Soil abs K', 'Fertiliser abs K', 'Vol density']::recommendation_crop_model_parameter_enum[]
                    WHEN rcmc.result_element = 'Add_S_total'
                        THEN ARRAY['Uptake S', 'Soil abs S', 'Fertiliser abs S', 'Vol density']::recommendation_crop_model_parameter_enum[]
                    WHEN rcmc.result_element = 'Add_N_fall'
                        THEN ARRAY['Uptake N', 'Soil abs N', 'Fertiliser abs N', 'Vol density', 'Organic N', 'Add organic N', 'Fall N part']::recommendation_crop_model_parameter_enum[]
                    WHEN rcmc.result_element = 'Add_N_total'
                        THEN ARRAY['Uptake N', 'Soil abs N', 'Fertiliser abs N', 'Vol density', 'Organic N', 'Add organic N', 'Fall N part', 'Add N reduction sampling']::recommendation_crop_model_parameter_enum[]
                    WHEN rcmc.result_element = 'Add_Cu'
                        THEN ARRAY['Susces Cu']::recommendation_crop_model_parameter_enum[]
                    WHEN rcmc.result_element = 'Add_Mn'
                        THEN ARRAY['Susces Mn']::recommendation_crop_model_parameter_enum[]
                    WHEN rcmc.result_element = 'Add_Mo'
                        THEN ARRAY['Susces Mo']::recommendation_crop_model_parameter_enum[]
                    WHEN rcmc.result_element = 'Add_Fe'
                        THEN ARRAY['Susces Fe']::recommendation_crop_model_parameter_enum[]
                    WHEN rcmc.result_element = 'Add_B'
                        THEN ARRAY['Susces B']::recommendation_crop_model_parameter_enum[]
                    WHEN rcmc.result_element = 'Add_Zn'
                        THEN ARRAY['Susces Zn']::recommendation_crop_model_parameter_enum[]
                END AS params
            FROM ( 
                VALUES 
                        (1, 'Need_N_total'),
                        (1, 'Need_P_total'),
                        (1, 'Need_K_total'),
                        (1, 'Need_S_total'),
                        (1, 'Add_N_total'),
                        (1, 'Add_P_total'),
                        (1, 'Add_K_total'),
                        (1, 'Add_S_total'),
                        (1, 'Add_N_fall'),
                        (1, 'pH_predominant'),
                        (1, 'Add_B'),
                        (1, 'Add_Mn'),
                        (1, 'Add_Cu'),
                        (1, 'Add_Zn'),
                        (1, 'Add_Mo'),
                        (1, 'Add_Fe')
            ) AS rcmc (calc_model_id, result_element)
            CROSS JOIN lab_analysis_group_element lage
            GROUP BY 
                rcmc.calc_model_id,
                rcmc.result_element
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM recommendation_calc_model_config');
    }
}
