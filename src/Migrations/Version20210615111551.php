<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210615111551 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Drop tables recommendation_crops, recommendation_crop_orders, recommendation_lab_element_group (not used in the new db structure)';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE IF EXISTS recommendation_crops CASCADE');
        $this->addSql('DROP TABLE IF EXISTS recommendation_crop_order CASCADE');
        $this->addSql('DROP TABLE IF EXISTS recommendation_lab_element_group CASCADE');
        $this->addSql('DROP SEQUENCE recommendation_lab_element_group_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE recommendation_crops_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE recommendation_crop_order_id_seq CASCADE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
