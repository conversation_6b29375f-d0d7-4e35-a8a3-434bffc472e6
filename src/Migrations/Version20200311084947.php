<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200311084947 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TYPE field_states_enum ADD VALUE \'New\'');
        $this->addSql('ALTER TYPE field_states_enum ADD VALUE \'Active\'');
        $this->addSql('ALTER TYPE field_states_enum ADD VALUE \'Expired\'');

        $this->addSql('ALTER TABLE subscription_package_field ALTER field_state SET DEFAULT \'New\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE subscription_package_field DROP CONSTRAINT FK_A0F4124436A9EB9A');
        $this->addSql('DROP INDEX "primary"');
        $this->addSql('ALTER TABLE subscription_package_field ALTER field_state SET DEFAULT \'Gridded\'');
    }
}
