<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210826102424 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Seed table lab_element_aggregation_area_value_config.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO lab_element_aggregation_area_value_config (service_provider_id, area_treshold) VALUES
            (2, 0.7),
            (3, 0.7),
            (5, 0.7),
            (6, 0.7)
        ');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
