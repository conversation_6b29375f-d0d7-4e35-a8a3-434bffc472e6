<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210622103601 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create postgresql function add_p_total.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
        CREATE FUNCTION add_p_total (
            yield NUMERIC,
            avg_element_result NUMERIC,
            uptake_p NUMERIC,
            vol_density NUMERIC,
            soil_abs_p NUMERIC,
            fertiliser_abs_p NUMERIC
        )
            RETURNS NUMERIC
            AS $$
                BEGIN
                RETURN (
                    ((yield * uptake_p / 100) - (avg_element_result * vol_density * soil_abs_p)) / fertiliser_abs_p
                )::NUMERIC;
                END
            $$ LANGUAGE plpgsql
       ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DROP FUNCTION IF EXISTS add_p_total (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_p NUMERIC,
                vol_density NUMERIC,
                soil_abs_p NUMERIC,
                fertiliser_abs_p NUMERIC
            )
       ');
    }
}
