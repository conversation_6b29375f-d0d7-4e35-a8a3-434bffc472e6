<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210422074734 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Set template name to have regex patern when is necessary to avoid taking items that are similar';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("UPDATE lab_elements_calculations SET template_column = 'K\+' WHERE template_column = 'K+'");
        $this->addSql("UPDATE lab_elements_calculations SET template_column = 'Ca2\+' WHERE template_column = 'Ca2+'");
        $this->addSql("UPDATE lab_elements_calculations SET template_column = 'Mg2\+' WHERE template_column = 'Mg2+'");
        $this->addSql("UPDATE lab_elements_calculations SET template_column = 'Na\+' WHERE template_column = 'Na+'");
        $this->addSql("UPDATE lab_elements_calculations SET template_column = 'H\+' WHERE template_column = 'H+'");
        $this->addSql("UPDATE lab_elements_calculations SET template_column = 'K(?!\+)' WHERE template_column = 'K'");
        $this->addSql("UPDATE lab_elements_calculations SET template_column = 'Na(?!\+)' WHERE template_column = 'Na'");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("UPDATE lab_elements_calculations SET template_column = 'K+' WHERE template_column = 'K\+'");
        $this->addSql("UPDATE lab_elements_calculations SET template_column = 'Ca2+' WHERE template_column = 'Ca2\+'");
        $this->addSql("UPDATE lab_elements_calculations SET template_column = 'Mg2+' WHERE template_column = 'Mg2\+'");
        $this->addSql("UPDATE lab_elements_calculations SET template_column = 'Na+' WHERE template_column = 'Na\+'");
        $this->addSql("UPDATE lab_elements_calculations SET template_column = 'H+' WHERE template_column = 'H\+'");
        $this->addSql("UPDATE lab_elements_calculations SET template_column = 'K' WHERE template_column = 'K(?!\+)'");
        $this->addSql("UPDATE lab_elements_calculations SET template_column = 'Na' WHERE template_column = 'Na(?!\+)'");
    }
}
