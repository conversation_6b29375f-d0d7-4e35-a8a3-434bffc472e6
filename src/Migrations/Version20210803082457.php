<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210803082457 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove attrubute \'For final approve\' from element_group_states_enum';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE lab_element_group ALTER state DROP DEFAULT');
        $this->addSql('UPDATE lab_element_group SET state = \'For recommendation\' WHERE state = \'For final approve\'');
        $this->addSql('CREATE TYPE element_group_states_enum_new AS ENUM (
        \'Pending\', \'In progress\', \'For reanalysis\', \'For approve\', \'For recommendation\', \'Delivered\'
        )');
        $this->addSql('ALTER TABLE lab_element_group ALTER COLUMN state TYPE element_group_states_enum_new USING (state::text::element_group_states_enum_new)');
        $this->addSql('DROP TYPE element_group_states_enum');
        $this->addSql('ALTER TYPE element_group_states_enum_new RENAME TO element_group_states_enum');
        $this->addSql('ALTER TABLE lab_element_group ALTER state SET DEFAULT \'Pending\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE lab_element_group ALTER state DROP DEFAULT');
        $this->addSql('CREATE TYPE element_group_states_enum_new AS ENUM (
        \'Pending\', \'In progress\', \'For reanalysis\', \'For approve\', \'For recommendation\', \'Delivered\', \'For final approve\'
        )');
        $this->addSql('ALTER TABLE lab_element_group ALTER COLUMN state TYPE element_group_states_enum_new USING (state::text::element_group_states_enum_new)');
        $this->addSql('DROP TYPE element_group_states_enum');
        $this->addSql('ALTER TYPE element_group_states_enum_new RENAME TO element_group_states_enum');
        $this->addSql('ALTER TABLE lab_element_group ALTER state SET DEFAULT \'Pending\'');
    }
}
