<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210611100324 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Recreate enum recommendation_comment_type_enum';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP TYPE IF EXISTS recommendation_comment_type_enum');
        $this->addSql("CREATE TYPE recommendation_comment_type_enum AS ENUM ('element', 'custom')");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TYPE IF EXISTS recommendation_comment_type_enum');
    }
}
