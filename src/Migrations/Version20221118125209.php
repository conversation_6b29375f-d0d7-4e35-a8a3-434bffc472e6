<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221118125209 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create table main_navigation.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE main_navigation (id SERIAL NOT NULL, "path" ltree, label VARCHAR(255) NOT NULL, url VARCHAR(255), no_data_url VARCHAR(255), icon XML, PRIMARY KEY(id))');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE main_navigation');
    }
}
