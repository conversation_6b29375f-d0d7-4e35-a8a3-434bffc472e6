<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191106165826 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("CREATE TYPE package_statuses_enum AS ENUM ('New', 'Active', 'Expired')");
        $this->addSql("CREATE TYPE package_states_enum AS ENUM ('Waiting for plots', 'Approve plots', 'In progress', 'Done', 'Expired', 'Activate contract', 'Add stations', 'Add devices')");

        $this->addSql('ALTER TABLE subscription_package DROP CONSTRAINT fk_ad7d870e6bf700bd');
        $this->addSql('ALTER TABLE service_contract_packages DROP CONSTRAINT fk_6415be8d6bf700bd');
        $this->addSql('ALTER TABLE subscription_package DROP CONSTRAINT fk_ad7d870e5d83cc1');
        $this->addSql('ALTER TABLE service_contract_packages DROP CONSTRAINT fk_6415be8d5d83cc1');
        $this->addSql('DROP SEQUENCE package_statuses_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE package_states_id_seq CASCADE');
        $this->addSql('DROP TABLE package_statuses');
        $this->addSql('DROP TABLE package_states');
        $this->addSql('DROP INDEX idx_6415be8d6bf700bd');
        $this->addSql('DROP INDEX idx_6415be8d5d83cc1');
        $this->addSql('ALTER TABLE service_contract_packages ADD status package_statuses_enum DEFAULT \'New\'');
        $this->addSql('ALTER TABLE service_contract_packages ADD state package_states_enum DEFAULT \'Waiting for plots\'');
        $this->addSql('ALTER TABLE service_contract_packages DROP status_id');
        $this->addSql('ALTER TABLE service_contract_packages DROP state_id');
        $this->addSql('DROP INDEX idx_ad7d870e5d83cc1');
        $this->addSql('DROP INDEX idx_ad7d870e6bf700bd');
        $this->addSql('ALTER TABLE subscription_package ADD status package_statuses_enum DEFAULT \'New\'');
        $this->addSql('ALTER TABLE subscription_package ADD state package_states_enum DEFAULT \'Waiting for plots\'');
        $this->addSql('ALTER TABLE subscription_package DROP status_id');
        $this->addSql('ALTER TABLE subscription_package DROP state_id');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SEQUENCE package_statuses_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE package_states_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE package_statuses (id INT NOT NULL, service_provider_id INT DEFAULT NULL, slug VARCHAR(20) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_af718a7fc6c98e06 ON package_statuses (service_provider_id)');
        $this->addSql('CREATE TABLE package_states (id INT NOT NULL, service_provider_id INT DEFAULT NULL, slug VARCHAR(31) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_f390491c6c98e06 ON package_states (service_provider_id)');

        // package_statuses
        $sql = "ALTER TABLE package_statuses ALTER COLUMN id SET DEFAULT nextval('package_statuses_id_seq'::regclass)";
        $this->addSql($sql);

        $sql = "INSERT INTO package_statuses (
            slug,
            service_provider_id
        )
        VALUES
            ('New', 1),
            ('Active', 1),
            ('Expired', 1)
        ";
        $this->addSql($sql);

        // package_states
        $sql = "ALTER TABLE package_states ALTER COLUMN id SET DEFAULT nextval('package_states_id_seq'::regclass)";
        $this->addSql($sql);

        $sql = "INSERT INTO package_states (
            slug,
            service_provider_id
        )
        VALUES
            ('Waiting for plots', 1),
            ('Approve plots', 1),
            ('In progress', 1),
            ('Done', 1),
            ('Expired', 1),
            ('Activate contract', 1),
            ('Add stations', 1),
            ('Add devices', 1)
        ";
        $this->addSql($sql);

        $this->addSql('ALTER TABLE package_statuses ADD CONSTRAINT fk_af718a7fc6c98e06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE package_states ADD CONSTRAINT fk_f390491c6c98e06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE subscription_package ADD status_id INT DEFAULT \'1\'');
        $this->addSql('ALTER TABLE subscription_package ADD state_id INT DEFAULT \'1\'');
        $this->addSql('ALTER TABLE subscription_package DROP status');
        $this->addSql('ALTER TABLE subscription_package DROP state');
        $this->addSql('ALTER TABLE subscription_package ADD CONSTRAINT fk_ad7d870e6bf700bd FOREIGN KEY (status_id) REFERENCES package_statuses (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE subscription_package ADD CONSTRAINT fk_ad7d870e5d83cc1 FOREIGN KEY (state_id) REFERENCES package_states (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_ad7d870e5d83cc1 ON subscription_package (state_id)');
        $this->addSql('CREATE INDEX idx_ad7d870e6bf700bd ON subscription_package (status_id)');
        $this->addSql('ALTER TABLE service_contract_packages ADD status_id INT DEFAULT \'1\'');
        $this->addSql('ALTER TABLE service_contract_packages ADD state_id INT DEFAULT \'1\'');
        $this->addSql('ALTER TABLE service_contract_packages DROP status');
        $this->addSql('ALTER TABLE service_contract_packages DROP state');
        $this->addSql('ALTER TABLE service_contract_packages ADD CONSTRAINT fk_6415be8d6bf700bd FOREIGN KEY (status_id) REFERENCES package_statuses (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE service_contract_packages ADD CONSTRAINT fk_6415be8d5d83cc1 FOREIGN KEY (state_id) REFERENCES package_states (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_6415be8d6bf700bd ON service_contract_packages (status_id)');
        $this->addSql('CREATE INDEX idx_6415be8d5d83cc1 ON service_contract_packages (state_id)');

        $this->addSql('DROP TYPE IF EXISTS package_statuses_enum');
        $this->addSql('DROP TYPE IF EXISTS package_states_enum');
    }
}
