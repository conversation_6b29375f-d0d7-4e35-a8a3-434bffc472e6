<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210826090048 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create table recommendation_elements_susces.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE TABLE recommendation_elements_susces (id SERIAL, recommendation_id INT NOT NULL, element elements_enum NOT NULL, value BOOLEAN NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_F32E2DF1D284951A ON recommendation_elements_susces (recommendation_id)');
        $this->addSql('ALTER TABLE recommendation_elements_susces ADD CONSTRAINT FK_F33A1DF1D182840B FOREIGN KEY (recommendation_id) REFERENCES recommendations (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE recommendation_elements_susces DROP CONSTRAINT FK_F33A1DF1D182840B');
        $this->addSql('DROP INDEX IDX_F32E2DF1D284951A');
        $this->addSql('DROP TABLE recommendation_elements_susces');
    }
}
