<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210420120106 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Add elements yo ABSoilCE lab_analysis_group ';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $ABSoilCEC = $this->getLabAnalysisGroupId('ABSoilCEC');

        $this->addSql(
            "INSERT INTO lab_analysis_group_element (lab_analysis_group_id, element) VALUES
            (:ABSoilCEC,'CEC'),
            (:ABSoilCEC,'BS'),
            (:ABSoilCEC,'K+'),
            (:ABSoilCEC,'Ca2+'),
            (:ABSoilCEC,'Mg2+'),
            (:ABSoilCEC,'Na+'),
            (:ABSoilCEC,'H+')
        ",
            ['ABSoilCEC' => (int)$ABSoilCEC]
        );
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $ABSoilCEC = $this->getLabAnalysisGroupId('ABSoilCEC');
        $this->addSql('DELETE FROM lab_analysis_group_element lage where lage.lab_analysis_group_id = :ABSoilCEC', ['ABSoilCEC' => $ABSoilCEC]);
    }
}
