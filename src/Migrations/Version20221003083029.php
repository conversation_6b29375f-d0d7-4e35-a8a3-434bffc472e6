<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221003083029 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add column parent_id to table \'subscrtiption_package_fields\'.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE subscription_package_field ADD parent_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE subscription_package_field ADD CONSTRAINT FK_A0F41244727ACA70 FOREIGN KEY (parent_id) REFERENCES subscription_package_field (id) ON DELETE SET NULL NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_A0F41244727ACA70 ON subscription_package_field (parent_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE subscription_package_field DROP CONSTRAINT FK_A0F41244727ACA70');
        $this->addSql('DROP INDEX IDX_A0F41244727ACA70');
        $this->addSql('ALTER TABLE subscription_package_field DROP parent_id');
    }
}
