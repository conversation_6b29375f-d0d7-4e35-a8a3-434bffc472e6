<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;
use PDO;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210826073340 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Seed recommendation_calc_model_config.';
    }

    public function up(Schema $schema): void
    {
        // Fetch recommendation models configs
        $modelsConfigs = $this->getRecommendationModelsConfigs();

        // Seed recommendation_calc_model_config
        $this->addSql("
            INSERT INTO recommendation_calc_model_config (calc_model_id, result_element, element_id, params) VALUES
                ({$modelsConfigs['BG']},'Add_Ca',7,NULL),
                ({$modelsConfigs['BG']},'Add_Mg',8,NULL),
                ({$modelsConfigs['BG']},'Add_Mo',44,'{Suscess Mo}'),
                ({$modelsConfigs['Vantage']},'pH_predominant',1,NULL),
                ({$modelsConfigs['Vantage']},'Add_Mo',44,'{Susces Mo}'),
                ({$modelsConfigs['Vantage']},'Add_Fe',36,'{Susces Fe}'),
                ({$modelsConfigs['Vantage']},'Need_N_total',4,NULL),
                ({$modelsConfigs['Vantage']},'Add_N_fall',4,'{Uptake N,Soil abs N,Fertiliser abs N,Vol density,Organic N,Add organic N,Fall N part}'),
                ({$modelsConfigs['Vantage']},'Need_P_total',5,NULL),
                ({$modelsConfigs['Vantage']},'Add_N_total',4,'{Uptake N,Soil abs N,Fertiliser abs N,Vol density,Organic N,Add organic N,Fall N part,Add N reduction sampling}'),
                ({$modelsConfigs['Vantage']},'Add_Mn',12,'{Susces Mn}'),
                ({$modelsConfigs['Vantage']},'Add_K_total',6,'{Uptake K,Soil abs K,Fertiliser abs K,Vol density}'),
                ({$modelsConfigs['Vantage']},'Add_B',14,'{Susces B}'),
                ({$modelsConfigs['Vantage']},'Add_S_total',10,'{Uptake S,Soil abs S,Fertiliser abs S,Vol density}'),
                ({$modelsConfigs['Vantage']},'Need_S_total',10,NULL),
                ({$modelsConfigs['Vantage']},'Need_K_total',6,NULL),
                ({$modelsConfigs['Vantage']},'Add_Cu',11,'{Susces Cu}'),
                ({$modelsConfigs['Vantage']},'Add_P_total',5,'{Uptake P,Soil abs P,Fertiliser abs P,Vol density}'),
                ({$modelsConfigs['Vantage']},'Add_Zn',13,'{Susces Zn}'),
                ({$modelsConfigs['Vantage']},'Add_Ca',7,NULL),
                ({$modelsConfigs['Vantage']},'Add_Mg',8,NULL),
                ({$modelsConfigs['Vantage']},'Add_Mo',44,'{Suscess Mo}'),
                ({$modelsConfigs['Agricost']},'pH_predominant',1,NULL),
                ({$modelsConfigs['Agricost']},'Add_Mo',44,'{Susces Mo}'),
                ({$modelsConfigs['Agricost']},'Add_Fe',36,'{Susces Fe}'),
                ({$modelsConfigs['Agricost']},'Need_N_total',4,NULL),
                ({$modelsConfigs['Agricost']},'Add_N_fall',4,'{Uptake N,Soil abs N,Fertiliser abs N,Vol density,Organic N,Add organic N,Fall N part}'),
                ({$modelsConfigs['Agricost']},'Need_P_total',5,NULL),
                ({$modelsConfigs['Agricost']},'Add_N_total',4,'{Uptake N,Soil abs N,Fertiliser abs N,Vol density,Organic N,Add organic N,Fall N part,Add N reduction sampling}'),
                ({$modelsConfigs['Agricost']},'Add_Mn',12,'{Susces Mn}'),
                ({$modelsConfigs['Agricost']},'Add_K_total',6,'{Uptake K,Soil abs K,Fertiliser abs K,Vol density}'),
                ({$modelsConfigs['Agricost']},'Add_B',14,'{Susces B}'),
                ({$modelsConfigs['Agricost']},'Add_S_total',10,'{Uptake S,Soil abs S,Fertiliser abs S,Vol density}'),
                ({$modelsConfigs['Agricost']},'Need_S_total',10,NULL),
                ({$modelsConfigs['Agricost']},'Need_K_total',6,NULL),
                ({$modelsConfigs['Agricost']},'Add_Cu',11,'{Susces Cu}'),
                ({$modelsConfigs['Agricost']},'Add_P_total',5,'{Uptake P,Soil abs P,Fertiliser abs P,Vol density}'),
                ({$modelsConfigs['Agricost']},'Add_Zn',13,'{Susces Zn}'),
                ({$modelsConfigs['Agricost']},'Add_Ca',7,NULL),
                ({$modelsConfigs['Agricost']},'Add_Mg',8,NULL),
                ({$modelsConfigs['Agricost']},'Add_Mo',44,'{Suscess Mo}'),
                ({$modelsConfigs['Spectr Аgro']},'pH_predominant',1,NULL),
                ({$modelsConfigs['Spectr Аgro']},'Add_Mo',44,'{Susces Mo}'),
                ({$modelsConfigs['Spectr Аgro']},'Add_Fe',36,'{Susces Fe}'),
                ({$modelsConfigs['Spectr Аgro']},'Need_N_total',4,NULL),
                ({$modelsConfigs['Spectr Аgro']},'Add_N_fall',4,'{Uptake N,Soil abs N,Fertiliser abs N,Vol density,Organic N,Add organic N,Fall N part}'),
                ({$modelsConfigs['Spectr Аgro']},'Need_P_total',5,NULL),
                ({$modelsConfigs['Spectr Аgro']},'Add_N_total',4,'{Uptake N,Soil abs N,Fertiliser abs N,Vol density,Organic N,Add organic N,Fall N part,Add N reduction sampling}'),
                ({$modelsConfigs['Spectr Аgro']},'Add_Mn',12,'{Susces Mn}'),
                ({$modelsConfigs['Spectr Аgro']},'Add_K_total',6,'{Uptake K,Soil abs K,Fertiliser abs K,Vol density}'),
                ({$modelsConfigs['Spectr Аgro']},'Add_B',14,'{Susces B}'),
                ({$modelsConfigs['Spectr Аgro']},'Add_S_total',10,'{Uptake S,Soil abs S,Fertiliser abs S,Vol density}'),
                ({$modelsConfigs['Spectr Аgro']},'Need_S_total',10,NULL),
                ({$modelsConfigs['Spectr Аgro']},'Need_K_total',6,NULL),
                ({$modelsConfigs['Spectr Аgro']},'Add_Cu',11,'{Susces Cu}'),
                ({$modelsConfigs['Spectr Аgro']},'Add_P_total',5,'{Uptake P,Soil abs P,Fertiliser abs P,Vol density}'),
                ({$modelsConfigs['Spectr Аgro']},'Add_Zn',13,'{Susces Zn}'),
                ({$modelsConfigs['Spectr Аgro']},'Add_Ca',7,NULL),
                ({$modelsConfigs['Spectr Аgro']},'Add_Mg',8,NULL),
                ({$modelsConfigs['Spectr Аgro']},'Add_Mo',44,'{Suscess Mo}'),
                ({$modelsConfigs['NIK Italia']},'pH_predominant',1,NULL),
                ({$modelsConfigs['NIK Italia']},'Add_Mo',44,'{Susces Mo}'),
                ({$modelsConfigs['NIK Italia']},'Add_Fe',36,'{Susces Fe}'),
                ({$modelsConfigs['NIK Italia']},'Need_N_total',4,NULL),
                ({$modelsConfigs['NIK Italia']},'Add_N_fall',4,'{Uptake N,Soil abs N,Fertiliser abs N,Vol density,Organic N,Add organic N,Fall N part}'),
                ({$modelsConfigs['NIK Italia']},'Need_P_total',5,NULL),
                ({$modelsConfigs['NIK Italia']},'Add_N_total',4,'{Uptake N,Soil abs N,Fertiliser abs N,Vol density,Organic N,Add organic N,Fall N part,Add N reduction sampling}'),
                ({$modelsConfigs['NIK Italia']},'Add_Mn',12,'{Susces Mn}'),
                ({$modelsConfigs['NIK Italia']},'Add_K_total',6,'{Uptake K,Soil abs K,Fertiliser abs K,Vol density}'),
                ({$modelsConfigs['NIK Italia']},'Add_B',14,'{Susces B}'),
                ({$modelsConfigs['NIK Italia']},'Add_S_total',10,'{Uptake S,Soil abs S,Fertiliser abs S,Vol density}'),
                ({$modelsConfigs['NIK Italia']},'Need_S_total',10,NULL),
                ({$modelsConfigs['NIK Italia']},'Need_K_total',6,NULL),
                ({$modelsConfigs['NIK Italia']},'Add_Cu',11,'{Susces Cu}'),
                ({$modelsConfigs['NIK Italia']},'Add_P_total',5,'{Uptake P,Soil abs P,Fertiliser abs P,Vol density}'),
                ({$modelsConfigs['NIK Italia']},'Add_Zn',13,'{Susces Zn}'),
                ({$modelsConfigs['NIK Italia']},'Add_Ca',7,NULL),
                ({$modelsConfigs['NIK Italia']},'Add_Mg',8,NULL),
                ({$modelsConfigs['NIK Italia']},'Add_Mo',44,'{Suscess Mo}')
        ");
    }

    public function down(Schema $schema): void
    {
        $sql = "SELECT id FROM recommendation_models_config WHERE name IN ('Vantage', 'Agricost', 'Spectr Аgro', 'NIK Italia')";
        $stmt = $this->connection->prepare($sql);
        $stmt->execute();
        $modelsConfigIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $modelsConfigIdsStr = implode(', ', $modelsConfigIds);

        $this->connection->exec("
            DELETE FROM recommendation_calc_model_config 
            WHERE calc_model_id IN ({$modelsConfigIdsStr})
            OR (
                calc_model_id = 1
                AND result_element IN ('Add_Ca', 'Add_Mg', 'Add_Mo')
            )
        ");
    }
}
