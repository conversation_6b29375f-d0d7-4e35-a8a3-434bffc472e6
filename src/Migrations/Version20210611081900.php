<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210611081900 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create table recommendation_elements_results.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE TABLE recommendation_elements_results (id SERIAL, recommendation_id INT NOT NULL, result_element result_element_enum NOT NULL, value DOUBLE PRECISION DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_F44E2DF1D173940B ON recommendation_elements_results (recommendation_id)');
        $this->addSql('ALTER TABLE recommendation_elements_results ADD CONSTRAINT FK_F44E2DF1D173940B FOREIGN KEY (recommendation_id) REFERENCES recommendations (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP TABLE recommendation_elements_results');
    }
}
