<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20211104074843 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE package ALTER contain_fields SET DEFAULT \'false\'');
        $this->addSql('ALTER TABLE package ALTER is_sampling SET DEFAULT \'false\'');
        $this->addSql('ALTER TABLE package ALTER has_station SET DEFAULT \'false\'');
        $this->addSql('ALTER TABLE package ALTER is_full_sampling SET DEFAULT \'false\'');
        $this->addSql('ALTER TABLE package ALTER is_satellite SET DEFAULT \'false\'');

        $this->addSql('
        update package 
        set is_satellite = case when is_satellite isnull then false when is_satellite is true then true  else false end,
            is_sampling  = case when is_sampling isnull then false when is_sampling is true then true else false end,
            has_station = case when has_station isnull then false when has_station is true then true else false end,
            is_full_sampling = case when is_full_sampling isnull then false when is_full_sampling is true then true else false end;
        ');
    }

    public function down(Schema $schema): void {}
}
