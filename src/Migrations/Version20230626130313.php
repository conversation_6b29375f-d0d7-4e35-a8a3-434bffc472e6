<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230626130313 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create enum link_target_enum.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("CREATE TYPE link_target_enum AS ENUM ('_self', '_blank', '_parent', '_top')");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TYPE IF EXISTS link_target_enum');
    }
}
