<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221118140425 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create table package_main_navigation.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE package_main_navigation (id SERIAL NOT NULL, package_id INT NOT NULL, main_navigation_id INT NOT NULL, visual_order INT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_F9279DFCF44CABFF ON package_main_navigation (package_id)');
        $this->addSql('CREATE INDEX IDX_F9279DFC8B532EF0 ON package_main_navigation (main_navigation_id)');
        $this->addSql('ALTER TABLE package_main_navigation ADD CONSTRAINT FK_F9279DFCF44CABFF FOREIGN KEY (package_id) REFERENCES package (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE package_main_navigation ADD CONSTRAINT FK_F9279DFC8B532EF0 FOREIGN KEY (main_navigation_id) REFERENCES main_navigation (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE package_main_navigation DROP CONSTRAINT FK_F9279DFCF44CABFF');
        $this->addSql('ALTER TABLE package_main_navigation DROP CONSTRAINT FK_F9279DFC8B532EF0');
        $this->addSql('DROP INDEX IDX_F9279DFCF44CABFF');
        $this->addSql('DROP INDEX IDX_F9279DFC8B532EF0');
        $this->addSql('DROP TABLE package_main_navigation');
    }
}
