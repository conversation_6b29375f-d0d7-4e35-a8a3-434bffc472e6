<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210826072949 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update enums result_element_enum, recommendation_crop_model_parameter_enum.';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        $this->addSql("ALTER TYPE result_element_enum ADD VALUE IF NOT EXISTS 'Add_Ca'");
        $this->addSql("ALTER TYPE result_element_enum ADD VALUE IF NOT EXISTS 'Add_Mg'");
        $this->addSql("ALTER TYPE result_element_enum ADD VALUE IF NOT EXISTS 'Add_Mo'");

        $this->addSql("ALTER TYPE recommendation_crop_model_parameter_enum ADD VALUE IF NOT EXISTS 'Suscess Mo'");
    }

    public function down(Schema $schema): void {}
}
