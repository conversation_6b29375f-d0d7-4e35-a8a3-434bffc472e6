<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190808154011 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SEQUENCE package_states_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE package_states (id INT NOT NULL, service_provider_id INT DEFAULT NULL, slug VARCHAR(31) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_F390491C6C98E06 ON package_states (service_provider_id)');
        $this->addSql('ALTER TABLE package_states ADD CONSTRAINT FK_F390491C6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE service_contract_packages ADD state_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE service_contract_packages ADD CONSTRAINT FK_6415BE8D5D83CC1 FOREIGN KEY (state_id) REFERENCES package_states (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_6415BE8D5D83CC1 ON service_contract_packages (state_id)');
        $this->addSql('ALTER TABLE contract_statuses ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE subscription_package ADD state_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE subscription_package ADD CONSTRAINT FK_AD7D870E5D83CC1 FOREIGN KEY (state_id) REFERENCES package_states (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_AD7D870E5D83CC1 ON subscription_package (state_id)');
        $this->addSql('ALTER TABLE package_statuses ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE package ALTER id DROP DEFAULT');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE service_contract_packages DROP CONSTRAINT FK_6415BE8D5D83CC1');
        $this->addSql('ALTER TABLE subscription_package DROP CONSTRAINT FK_AD7D870E5D83CC1');
        $this->addSql('DROP SEQUENCE package_states_id_seq CASCADE');
        $this->addSql('DROP TABLE package_states');
        $this->addSql('CREATE SEQUENCE package_statuses_id_seq');
        $this->addSql('SELECT setval(\'package_statuses_id_seq\', (SELECT MAX(id) FROM package_statuses))');
        $this->addSql('ALTER TABLE package_statuses ALTER id SET DEFAULT nextval(\'package_statuses_id_seq\')');
        $this->addSql('CREATE SEQUENCE contract_statuses_id_seq');
        $this->addSql('SELECT setval(\'contract_statuses_id_seq\', (SELECT MAX(id) FROM contract_statuses))');
        $this->addSql('ALTER TABLE contract_statuses ALTER id SET DEFAULT nextval(\'contract_statuses_id_seq\')');
        $this->addSql('DROP INDEX IDX_AD7D870E5D83CC1');
        $this->addSql('ALTER TABLE subscription_package DROP state_id');
        $this->addSql('CREATE SEQUENCE package_id_seq');
        $this->addSql('SELECT setval(\'package_id_seq\', (SELECT MAX(id) FROM package))');
        $this->addSql('ALTER TABLE package ALTER id SET DEFAULT nextval(\'package_id_seq\')');
        $this->addSql('DROP INDEX IDX_6415BE8D5D83CC1');
        $this->addSql('ALTER TABLE service_contract_packages DROP state_id');
    }
}
