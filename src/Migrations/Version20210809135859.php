<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210809135859 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Seed the columns \'soil_map\'';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('UPDATE lab_analysis_group_element 
                            SET has_soil_map = true 
                            WHERE element in(\'pH\',\'TMN\',\'P2O5\',\'K2O\',\'S\',\'Cu\',\'Mn\',\'Zn\',\'B\',\'CaO\',\'MgO\',\'Fe\',\'Mo\',\'Hu<PERSON>\')');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
