<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191203123645 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("CREATE TYPE element_result_states_enum AS ENUM ('Pending', 'For Analysis', 'Analysed')");

        $this->addSql('CREATE SEQUENCE lab_elements_results_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE lab_elements_results (id INT NOT NULL, lab_element_group_id INT NOT NULL, element elements_enum, value DOUBLE PRECISION DEFAULT NULL, state element_result_states_enum, state_updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_2E6761B65F1DCC2F ON lab_elements_results (lab_element_group_id)');
        $this->addSql('ALTER TABLE lab_elements_results ADD CONSTRAINT FK_2E6761B65F1DCC2F FOREIGN KEY (lab_element_group_id) REFERENCES lab_element_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE lab_element_group ALTER state SET DEFAULT \'Pending\'');
        $this->addSql('ALTER TABLE lab_element_group ALTER state SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP SEQUENCE lab_elements_results_id_seq CASCADE');
        $this->addSql('DROP TABLE lab_elements_results');
        $this->addSql('ALTER TABLE lab_element_group ALTER state DROP DEFAULT');
        $this->addSql('ALTER TABLE lab_element_group ALTER state DROP NOT NULL');
    }
}
