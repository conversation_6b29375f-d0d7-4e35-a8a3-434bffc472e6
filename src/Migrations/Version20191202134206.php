<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191202134206 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SEQUENCE lab_analysis_group_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE lab_analysis_group (id INT NOT NULL, name VARCHAR(63) NOT NULL, PRIMARY KEY(id))');

        // Add lab_analysis_group data
        $sql = "ALTER TABLE lab_analysis_group ALTER COLUMN id SET DEFAULT nextval('lab_analysis_group_id_seq'::regclass)";
        $this->addSql($sql);

        $sql = "INSERT INTO lab_analysis_group (
            name
        )
        VALUES
            ('ABSoilpH'),
            ('ABSoilN'),
            ('ABSoilP'),
            ('ABSoilK'),
            ('ABSoilCaMgNa'),
            ('ABSoilS'),
            ('ABSoilCuMnZnB'),
            ('ABSoilCtCoOM'),
            ('ABSoilCtCoCiTC'),
            ('ABSoilAC'),
            ('ABSoilEC'),
            ('ABLeafN'),
            ('ABLeafPKCaMgNaS'),
            ('ABLeafFeCuZnMnB')
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP SEQUENCE lab_analysis_group_id_seq CASCADE');
        $this->addSql('DROP TABLE lab_analysis_group');
    }
}
