<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210706112512 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create postgresql function get_holydays(search_year INT); TODO: !!! Update the function to work for all service providers and all years.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("
            CREATE FUNCTION get_holydays(search_year INT)
            -- !!! Warning: This pg function returns the national holydays only for BG and for years 2020, 2021, 2022.
            -- TODO: Remove the hardcoded dates. Calculate the National holidays by service provider.
            RETURNS TABLE (\"yr\" INT, \"dts\" DATE[])
            AS $$
                BEGIN
                RETURN QUERY
                WITH wk AS (
                    SELECT
                        EXTRACT (YEAR FROM d::date) AS \"year\",
                        array_agg(d::date) AS \"dates\"
                    FROM
                        generate_series('2020-01-01', '2022-12-31', '1 day'::interval) d
                    WHERE
                        EXTRACT(isodow FROM d::date) in (6, 7)
                    GROUP BY 
                        \"year\"
                ), -- Saturdays and Sundays
                hld AS (
                    SELECT
                        \"year\",
                        \"dates\"
                    FROM
                        (VALUES
                        (
                            ARRAY [
                            '2020-01-01',
                            '2020-03-03',
                            '2020-04-17',
                            '2020-04-20',
                            '2020-05-01',
                            '2020-05-06',
                            '2020-05-25', 
                            '2020-09-07',
                            '2020-09-22', 
                            '2020-12-24',
                            '2020-12-25',
                            '2020-12-28'
                            ]::date[],
                            2020
                        ),
                        (
                            ARRAY [
                            '2021-01-01',
                            '2021-03-03',
                            '2021-04-30',
                            '2021-05-03',
                            '2021-05-04',
                            '2021-05-06',
                            '2021-05-24',
                            '2021-09-06',
                            '2021-09-22',
                            '2021-12-24',
                            '2021-12-27',
                            '2021-12-28'
                            ]::date[],
                            2021
                        ),
                        (
                            ARRAY [
                            '2022-01-03',
                            '2022-03-03',
                            '2022-04-22',
                            '2022-04-25',
                            '2022-05-02',
                            '2022-05-06',
                            '2022-05-24',
                            '2022-09-06',
                            '2022-09-22',
                            '2022-12-26',
                            '2022-12-27',
                            '2022-12-28'
                            ]::date[],
                            2022
                        )
                    ) AS holydays(\"dates\", \"year\")	
                ) -- National holidays
                SELECT
                    wk.\"year\"::int AS yr,
                    wk.\"dates\" || hld.\"dates\" AS dts
                from
                    wk
                JOIN hld
                    ON hld.\"year\" = wk.\"year\"
                WHERE
                    wk.\"year\" = search_year
                ORDER BY 
                    wk.\"year\";
                END
            $$ LANGUAGE plpgsql
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP FUNCTION IF EXISTS get_holydays(search_year INT)');
    }
}
