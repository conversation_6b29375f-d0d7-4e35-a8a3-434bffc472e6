<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200205091151 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs

        $this->addSql('ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS \'CaO\'');
        $this->addSql('ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS \'MgO\'');
        $this->addSql('ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS \'Na2O\'');
        $this->addSql('ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS \'Cl\'');
        $this->addSql('ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS \'Fe\'');
        $this->addSql('ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS \'Mo\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
