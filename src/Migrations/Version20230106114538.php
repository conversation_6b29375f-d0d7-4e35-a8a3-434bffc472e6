<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230106114538 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add elements \'Total Nitrogen\' and \'Bulk Density\' elements to lab_elements_calculations.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("INSERT INTO lab_elements_calculations (element, coefficient, operation, template_column) VALUES 
            ('Total Nitrogen'::elements_enum, 1, '*', 'Total Nitrogen'),
            ('Bulk Density'::elements_enum, 1, '*', 'Bulk Density')
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql("DELETE 
            FROM lab_elements_calculations
            WHERE element in ('Total Nitrogen'::elements_enum, 'Bulk Density'::elements_enum)
        ");
    }
}
