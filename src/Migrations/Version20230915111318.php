<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230915111318 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update db functions add_n_fall and add_n_total - add parameter valid_from and update calculation logic';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP FUNCTION add_n_fall (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_n NUMERIC,
                vol_density NUMERIC,
                soil_abs_n NUMERIC,
                organic_n NUMERIC,
                add_organic_n NUMERIC,
                fall_n_part NUMERIC,
                fertiliser_abs_n NUMERIC,
                add_n_reduction_sampling NUMERIC,
                humus NUMERIC,
                sampling_date DATE
            )
        ');

        $this->addSql("CREATE FUNCTION add_n_fall (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_n NUMERIC,
                vol_density NUMERIC,
                soil_abs_n NUMERIC,
                organic_n NUMERIC,
                add_organic_n NUMERIC,
                fall_n_part NUMERIC,
                fertiliser_abs_n NUMERIC,
                add_n_reduction_sampling NUMERIC,
                humus NUMERIC,
                sampling_date DATE,
                valid_from DATE
            )
                RETURNS NUMERIC
                AS $$
                    DECLARE 
                        result NUMERIC;
                    BEGIN
                        WITH calculations AS (
                            SELECT
                                (
                                    ((yield * uptake_n / 100) 
                                    - (
                                        CASE WHEN sampling_date <=  valid_from - '2 months'::INTERVAL THEN
                                            (avg_element_result * add_n_reduction_sampling * vol_density * soil_abs_n)
                                        ELSE
                                            (avg_element_result * vol_density * soil_abs_n)
                                        END
                                    ) 
                                    - (humus * organic_n * add_organic_n)) / fertiliser_abs_n
                                ) * fall_n_part AS value,
                                (avg_element_result * vol_density * soil_abs_n) AS value_limit
                        )
                        SELECT
                            (CASE WHEN calculations.value <= calculations.value_limit THEN 0 ELSE calculations.value - calculations.value_limit END)::NUMERIC
                            INTO result
                        FROM
                            calculations;

                        RETURN result;
                    END
                $$ LANGUAGE plpgsql
        ");

        $this->addSql('DROP FUNCTION add_n_total (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_n NUMERIC,
                vol_density NUMERIC,
                soil_abs_n NUMERIC,
                organic_n NUMERIC,
                add_organic_n NUMERIC,
                fall_n_part NUMERIC,
                fertiliser_abs_n NUMERIC,
                add_n_reduction_sampling NUMERIC,
                humus NUMERIC,
                sampling_date DATE
            )
        ');

        $this->addSql("CREATE FUNCTION add_n_total (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_n NUMERIC,
                vol_density NUMERIC,
                soil_abs_n NUMERIC,
                organic_n NUMERIC,
                add_organic_n NUMERIC,
                fall_n_part NUMERIC,
                fertiliser_abs_n NUMERIC,
                add_n_reduction_sampling NUMERIC,
                humus NUMERIC,
                sampling_date DATE,
                valid_from DATE
            )
                RETURNS NUMERIC
                AS $$
                    DECLARE
                        result NUMERIC;
                    BEGIN
                        WITH calculations AS (
                            SELECT
                                (
                                    CASE WHEN fall_n_part > 0 THEN
                                        (yield * uptake_n / 100) * (1 - fall_n_part) - (humus * organic_n * add_organic_n)
                                    ELSE 
                                        (yield * uptake_n / 100)
                                        - (
                                            CASE WHEN sampling_date <=  valid_from - '2 months'::INTERVAL THEN
                                                (avg_element_result * add_n_reduction_sampling * vol_density * soil_abs_n)
                                            ELSE
                                                (avg_element_result * vol_density * soil_abs_n)
                                            END
                                        )
                                        - (humus * organic_n * add_organic_n)
                                    END
                                ) / fertiliser_abs_n  AS value
                        )
                        SELECT
                            (calculations.value)::NUMERIC
                            INTO result
                        FROM
                            calculations;
                        
                        RETURN result;
                    END
                $$ LANGUAGE plpgsql
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP FUNCTION add_n_fall (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_n NUMERIC,
                vol_density NUMERIC,
                soil_abs_n NUMERIC,
                organic_n NUMERIC,
                add_organic_n NUMERIC,
                fall_n_part NUMERIC,
                fertiliser_abs_n NUMERIC,
                add_n_reduction_sampling NUMERIC,
                humus NUMERIC,
                sampling_date DATE,
                valid_from DATE
            )
        ');

        $this->addSql("CREATE FUNCTION add_n_fall (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_n NUMERIC,
                vol_density NUMERIC,
                soil_abs_n NUMERIC,
                organic_n NUMERIC,
                add_organic_n NUMERIC,
                fall_n_part NUMERIC,
                fertiliser_abs_n NUMERIC,
                add_n_reduction_sampling NUMERIC,
                humus NUMERIC,
                sampling_date DATE
            )
                RETURNS NUMERIC
                AS $$
                    DECLARE 
                        result NUMERIC;
                    BEGIN
                        WITH calculations AS (
                            SELECT
                                (
                                    ((yield * uptake_n / 100) 
                                    - (
                                        CASE WHEN sampling_date <=  now() - '2 months'::INTERVAL THEN
                                            (avg_element_result * add_n_reduction_sampling * vol_density * soil_abs_n)
                                        ELSE
                                            (avg_element_result * vol_density * soil_abs_n)
                                        END
                                    ) 
                                    - (humus * organic_n * add_organic_n)) / fertiliser_abs_n
                                ) * fall_n_part AS value,
                                (avg_element_result * vol_density * soil_abs_n) AS value_limit
                        )
                        SELECT
                            (CASE WHEN calculations.value <= calculations.value_limit THEN 0 ELSE calculations.value - calculations.value_limit END)::NUMERIC
                            INTO result
                        FROM
                            calculations;

                        RETURN result;
                    END
                $$ LANGUAGE plpgsql
        ");

        $this->addSql('DROP FUNCTION add_n_total (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_n NUMERIC,
                vol_density NUMERIC,
                soil_abs_n NUMERIC,
                organic_n NUMERIC,
                add_organic_n NUMERIC,
                fall_n_part NUMERIC,
                fertiliser_abs_n NUMERIC,
                add_n_reduction_sampling NUMERIC,
                humus NUMERIC,
                sampling_date DATE,
                valid_from DATE
            )
        ');

        $this->addSql("
            CREATE OR REPLACE FUNCTION add_n_total (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_n NUMERIC,
                vol_density NUMERIC,
                soil_abs_n NUMERIC,
                organic_n NUMERIC,
                add_organic_n NUMERIC,
                fall_n_part NUMERIC,
                fertiliser_abs_n NUMERIC,
                add_n_reduction_sampling NUMERIC,
                humus NUMERIC,
                sampling_date DATE
            )
                RETURNS NUMERIC
                AS $$
                    DECLARE
                        result NUMERIC;
                    BEGIN
                        WITH calculations AS (
                            SELECT
                                (
                                    CASE WHEN fall_n_part > 0 THEN
                                        (yield * uptake_n / 100) * (1 - fall_n_part) - (humus * organic_n * add_organic_n)
                                    ELSE 
                                        (yield * uptake_n / 100)
                                        - (
                                            CASE WHEN sampling_date <=  now() - '2 months'::INTERVAL THEN
                                                (avg_element_result * add_n_reduction_sampling * vol_density * soil_abs_n)
                                            ELSE
                                                (avg_element_result * vol_density * soil_abs_n)
                                            END
                                        )
                                        - (humus * organic_n * add_organic_n)
                                    END
                                ) / fertiliser_abs_n  AS value
                        )
                        SELECT
                            (calculations.value)::NUMERIC
                            INTO result
                        FROM
                            calculations;
                        
                        RETURN result;
                    END
                $$ LANGUAGE plpgsql
        ");
    }
}
