<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200205165809 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE lab_element_group DROP plot_id');
        $this->addSql('ALTER TABLE package_grid_points DROP point_gid');
        $this->addSql('ALTER TABLE package_grid_points DROP plot_id');
        $this->addSql('ALTER TABLE subscription_package_field DROP field_id');
        $this->addSql('ALTER TABLE subscription_package_field DROP order_id');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE package_grid_points ADD point_gid INT NOT NULL');
        $this->addSql('ALTER TABLE package_grid_points ADD plot_id INT NOT NULL');
        $this->addSql('ALTER TABLE subscription_package_field ADD field_id INT NOT NULL');
        $this->addSql('ALTER TABLE subscription_package_field ADD order_id INT NOT NULL');
        $this->addSql('ALTER TABLE lab_element_group ADD plot_id INT NOT NULL');
    }
}
