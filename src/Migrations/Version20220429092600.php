<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220429092600 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Fill lab_analysis_group_element_visual_order';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $elementsVisualOrderArr = ['pH', 'NO3-N', 'NH4-N', 'TMN', 'P2O5', 'K2O', 'CaO', 'MgO', 'Na2O', 'S', 'B', 'Cu', 'Fe', 'Mn', 'Zn', 'Mo'];
        $serviceProvidersId = $this->getServiceProvidersId(['vantage_balkans', 'agricost', 'livona', 'spectr_agro', 'nik_italia', 'sumi_agro_tr', 'nikas']);
        $elementIds = $this->getElements();

        foreach ($serviceProvidersId as $serviceProviderId) {
            foreach ($elementsVisualOrderArr as $key => $element) {
                ++$key;
                $this->addSql("INSERT INTO lab_analysis_group_element_visual_order (lab_analysis_group_element_id, service_provider_id, visual_order) VALUES ({$elementIds[$element]}, {$serviceProviderId}, {$key})");
            }
        }
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
        $this->addSql('TRUNCATE lab_analysis_group_element_visual_order ');
    }
}
