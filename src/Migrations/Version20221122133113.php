<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221122133113 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create db function get_package_main_navigation_tree() that returns a jsonb containing the children of the specified main_navigation path for given package.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("CREATE OR REPLACE
            FUNCTION get_package_main_navigation_tree(_package_id INT, _path LTREE)
                RETURNS JSONB
                LANGUAGE plpgsql AS
            $$
                DECLARE
                    _result JSONB;
                BEGIN
                    SELECT JSONB_AGG(sub) INTO _result
                    FROM (
                        SELECT
                            mn.*,
                            NLEVEL(mn.\"path\") AS level,
                            pmn.visual_order,
                            COALESCE(get_package_main_navigation_tree(_package_id, mn.\"path\"), '[]'::JSONB) AS children
                        FROM
                            package_main_navigation pmn
                        JOIN main_navigation AS mn
                            ON mn.id = pmn.main_navigation_id
                        WHERE
                            pmn.package_id = _package_id
                            AND _path @> mn.\"path\"
                            AND NLEVEL(mn.\"path\") = NLEVEL(_path) + 1
                        GROUP BY
                            mn.id,
                            pmn.visual_order
                        ORDER BY
                            pmn.visual_order
                    ) sub;
                    RETURN _result;
                END;
            $$
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP FUNCTION IF EXISTS get_package_main_navigation_tree(_package_id INT, _path LTREE)');
    }
}
