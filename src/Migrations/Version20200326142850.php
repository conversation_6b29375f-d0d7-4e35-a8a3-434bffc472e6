<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200326142850 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
        $this->addSql('SELECT setval(\'package_id_seq\', (SELECT MAX(id) FROM package))');
        $this->addSql('ALTER TABLE package ALTER id SET DEFAULT nextval(\'package_id_seq\')');
        $this->addSql("CREATE TYPE integration_type_enum AS ENUM ('FT')");
        $this->addSql('ALTER TABLE package ADD integration integration_type_enum DEFAULT NULL');

        $sql = "INSERT INTO package (
            slug,
            service_provider_id,
            is_active,
            contain_fields,
            slug_short,
            is_sampling,
            has_station,
            integration
        )
        VALUES
            ('Farm Track', 1, true, false, 'FT', false, false,'FT');
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE package DROP integration');
        $this->addSql('DELETE FROM package p where p.slug = \'Farm Track\';');
        $this->addSql('DROP TYPE integration_type_enum;');
        $this->addSql('ALTER TABLE package ALTER id DROP DEFAULT');
    }
}
