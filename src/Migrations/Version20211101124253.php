<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20211101124253 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Drop get_recommendation_results (recommendation INTEGER,service_provider INTEGER)';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            '
            DROP FUNCTION IF EXISTS get_recommendation_results (
                recommendation INTEGER,
                service_provider INTEGER
            )'
        );
    }

    public function down(Schema $schema): void {}
}
