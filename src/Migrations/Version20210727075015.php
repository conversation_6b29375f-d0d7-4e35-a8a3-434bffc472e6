<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210727075015 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Seed table recommendation_leaf_fertiliser_comments_config.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("INSERT INTO recommendation_leaf_fertiliser_comments_config (model_id, crop_ids, result_elements, range, comment_text) values
            (1, NULL, ARRAY['Add_B', 'Add_Mn', 'Add_Cu', 'Add_Zn', 'Add_Mo', 'Add_Fe']::result_element_enum[], '[0,)'::numrange, 'Препоръчваме през периода на интензивно вегетативно развитие, но най-късно до фаза „начало на цъфтеж“ да се извършат 1-2 листни третирания на растенията с торови продукти, които съдържат в по-висока концентрация посочените хранителни елементи. Препоръчително е да се следват указанията за дози и фази на приложение, дадени от производителя на избрания продукт.')
        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DELETE FROM recommendation_leaf_fertiliser_comments_config WHERE model_id = 1');
    }
}
