<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210611110654 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Recreate table recommendation_comment.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE TABLE recommendation_comment (id SERIAL, recommendation_id INT NOT NULL, comment_type recommendation_comment_type_enum NOT NULL, param result_element_enum DEFAULT NULL, value TEXT DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_80A31AE0D173940B ON recommendation_comment (recommendation_id)');
        $this->addSql('ALTER TABLE recommendation_comment ADD CONSTRAINT FK_80A31AE0D173940B FOREIGN KEY (recommendation_id) REFERENCES recommendations (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP TABLE IF EXISTS recommendation_comment CASCADE');
    }
}
