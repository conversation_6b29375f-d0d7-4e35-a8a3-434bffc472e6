<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20211214110821 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update table recommendations_vra_orders. Rename column order_soil_vra_id TO vra_order_id. Add column vra_order_type.';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE recommendations_vra_orders ADD vra_order_type vra_order_type_enum NOT NULL DEFAULT \'soil_vra\'');
        $this->addSql('ALTER TABLE recommendations_vra_orders RENAME COLUMN order_soil_vra_id TO vra_order_id');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE recommendations_vra_orders DROP vra_order_type');
        $this->addSql('ALTER TABLE recommendations_vra_orders RENAME COLUMN vra_order_id TO order_soil_vra_id');
    }
}
