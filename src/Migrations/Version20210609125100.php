<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210609125100 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create table lab_element_aggregation_area_value_config';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE TABLE lab_element_aggregation_area_value_config (id SERIAL, service_provider_id INT NOT NULL, area_treshold DOUBLE PRECISION NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_CC2ED0D5C6C98E06 ON lab_element_aggregation_area_value_config (service_provider_id)');
        $this->addSql('ALTER TABLE lab_element_aggregation_area_value_config ADD CONSTRAINT FK_CC2ED0D5C6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP TABLE lab_element_aggregation_area_value_config');
    }
}
