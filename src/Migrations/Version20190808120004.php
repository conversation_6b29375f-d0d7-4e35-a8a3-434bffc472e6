<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190808120004 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // contract_statuses
        $sql = "ALTER TABLE contract_statuses ALTER COLUMN id SET DEFAULT nextval('contract_statuses_id_seq'::regclass)";
        $this->addSql($sql);

        $sql = "INSERT INTO contract_statuses (
            slug,
            service_provider_id
        )
        VALUES
            ('New', 1),
            ('Active', 1),
            ('Expired', 1)
        ";
        $this->addSql($sql);

        // package_statuses
        $sql = "ALTER TABLE package_statuses ALTER COLUMN id SET DEFAULT nextval('package_statuses_id_seq'::regclass)";
        $this->addSql($sql);

        $sql = "INSERT INTO package_statuses (
            slug,
            service_provider_id
        )
        VALUES
            ('New', 1),
            ('Active', 1),
            ('Expired', 1)
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
