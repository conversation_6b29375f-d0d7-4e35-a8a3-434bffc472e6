<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210624084816 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add column content_class to table lab_interpretation_classes_config. Seed the column.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE lab_interpretation_classes_config ADD COLUMN content_class content_class_enum DEFAULT NULL');
        $this->addSql("
            UPDATE lab_interpretation_classes_config SET
            content_class = CASE 
                WHEN slug in ('VL', 'L') THEN 'Low'::content_class_enum
                WHEN slug in ('M', 'S', 'G') THEN 'Medium'::content_class_enum
                WHEN slug in ('H', 'VH') THEN 'High'::content_class_enum
            END
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE lab_interpretation_classes_config DROP COLUMN content_class');
    }
}
