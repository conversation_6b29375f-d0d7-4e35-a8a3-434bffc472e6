<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20211112084931 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update db function get_recommendation_calculations() - distinct the calculations array.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE OR REPLACE FUNCTION json_object_values_array(obj json)
            RETURNS json
            AS $$
                DECLARE
                    res json;
                BEGIN
                    WITH json_values AS (
                        SELECT DISTINCT ON (elm_key)
                            ROW_NUMBER() OVER () AS elm_order,
                            elm_value
                        FROM
                            json_each(obj) AS element(elm_key, elm_value)
                    )
                    SELECT 
                        json_agg(elm_value ORDER BY elm_order) INTO res
                    FROM 
                        json_values;
                    RETURN res;
                END
            $$ LANGUAGE plpgsql
        ');
    }

    public function down(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP FUNCTION json_object_values_array(json)');
    }
}
