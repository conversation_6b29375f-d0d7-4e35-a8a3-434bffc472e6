<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191106160457 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('service_contracts.id', 'service_contracts.amount', 'contract.service_provider_id')
            ->from('service_contracts', 'service_contracts')
            ->innerJoin('service_contracts', 'contract', 'contract', 'contract.id = service_contracts.id');

        $rows = $queryBuilder->execute();

        foreach ($rows as $row) {
            $id = $row['id'] ?? null;
            $amount = $row['amount'] ?? null;
            $serviceProviderId = $row['service_provider_id'] ?? null;
            $this->connection->insert('price', ['contract_id' => $id, 'amount' => $amount, 'service_provider_id' => $serviceProviderId, 'status' => 'None']);
        }
    }

    public function down(Schema $schema): void {}
}
