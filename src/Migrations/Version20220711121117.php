<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220711121117 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Set default value for column id of table sampling_type.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("SELECT setval('sampling_type_id_seq', (SELECT MAX(id) FROM sampling_type));");
        $this->addSql("ALTER TABLE sampling_type ALTER COLUMN id SET DEFAULT nextval('sampling_type_id_seq'::regclass);");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE sampling_type ALTER COLUMN id DROP DEFAULT;');
    }
}
