<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230913104518 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add column recommendation_id to tables loggable.recommendation_elements_results_log, loggable.recommendation_elements_susces_log, loggable.recommendations_vra_orders_log ';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE loggable.recommendation_elements_results_log ADD recommendation_id INT DEFAULT NULL');
        $this->addSql('CREATE INDEX recommendation_elements_results_log_recommendation_id_idx ON loggable.recommendation_elements_results_log (recommendation_id)');

        $this->addSql('ALTER TABLE loggable.recommendation_elements_susces_log ADD recommendation_id INT DEFAULT NULL');
        $this->addSql('CREATE INDEX recommendation_elements_susces_log_recommendation_id_idx ON loggable.recommendation_elements_susces_log (recommendation_id)');

        $this->addSql('ALTER TABLE loggable.recommendations_vra_orders_log ADD recommendation_id INT DEFAULT NULL');
        $this->addSql('CREATE INDEX recommendations_vra_orders_log_recommendation_id_idx ON loggable.recommendations_vra_orders_log (recommendation_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX IF EXISTS recommendation_elements_results_log_recommendation_id_idx');
        $this->addSql('ALTER TABLE loggable.recommendation_elements_results_log DROP recommendation_id');

        $this->addSql('DROP INDEX IF EXISTS recommendation_elements_susces_log_recommendation_id_idx');
        $this->addSql('ALTER TABLE loggable.recommendation_elements_susces_log DROP recommendation_id');

        $this->addSql('DROP INDEX IF EXISTS recommendations_vra_orders_log_recommendation_id_idx');
        $this->addSql('ALTER TABLE loggable.recommendations_vra_orders_log DROP recommendation_id');
    }
}
