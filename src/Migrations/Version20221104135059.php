<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221104135059 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add column lab_analysis_package_group_id to lab_element_group table and drop column lab_analysis_group_id. Seed the new column. Update lab_element_group_id column in table lab_element_results for the results with sampling_type_id <> 0';
    }

    public function up(Schema $schema): void
    {
        // Set seq as default value for lab_element_group.id
        $this->addSql('CREATE SEQUENCE IF NOT EXISTS lab_element_group_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('SELECT setval(\'lab_element_group_id_seq\', (SELECT MAX(id) FROM lab_element_group))');
        $this->addSql('ALTER TABLE lab_element_group ALTER id SET DEFAULT nextval(\'lab_element_group_id_seq\')');

        // Add column lab_analysis_package_group_id to table lab_element_group
        $this->addSql('ALTER TABLE lab_element_group ADD lab_analysis_package_group_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE lab_element_group ADD CONSTRAINT FK_858A2F33197B4D8E FOREIGN KEY (lab_analysis_package_group_id) REFERENCES lab_analysis_package_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_858A2F33197B4D8E ON lab_element_group (lab_analysis_package_group_id)');

        // Fill column lab_element_group.lab_analysis_package_group_id for already existing records(sampling_type_id = 0) and insert new records (for sampling_type_id <> 0)
        $this->addSql("WITH leg_lapg AS
        (
                -- fetch lab element group data
                SELECT distinct
                    leg.id,
                    leg.package_id,
                    leg.package_type,
                    leg.state,
                    leg.state_updated_at,
                    leg.plot_uuid,
                    lapg.id AS lapg_id,
                    lapg.sampling_type_id
                FROM 
                    lab_element_group leg
                LEFT JOIN subscription_package sp 
                    ON sp.id = leg.package_id
                    AND leg.package_type = 'subscription'
                LEFT JOIN service_contract_packages scp 
                    ON scp.id = leg.package_id
                    AND leg.package_type = 'service'
                LEFT JOIN package p
                    ON p.id = sp.package_id
                    OR p.id = scp.package_id
                LEFT JOIN lab_analysis_package_group lapg        
                    ON lapg.lab_analysis_group_id = leg.lab_analysis_group_id
                    AND lapg.package_type = leg.package_type
                    AND lapg.package_id = p.id	
            ),
            update_leg AS (
                -- fill lab_analysis_package_group_id for the already existing lab_element_group (lapg.sampling_type_id = 0)
                UPDATE lab_element_group
                SET lab_analysis_package_group_id = leg_lapg.lapg_id
                FROM
                    leg_lapg
                WHERE
                    leg_lapg.id = lab_element_group.id
                    AND leg_lapg.sampling_type_id = 0
            )
            -- insert the new lab element groups (lapg.sampling_type <> 0)
            INSERT INTO lab_element_group (package_id, package_type, state, state_updated_at, plot_uuid, lab_analysis_package_group_id)
            SELECT
                package_id,
                package_type,
                state,
                state_updated_at,
                plot_uuid,
                lapg_id
            FROM
                leg_lapg
            WHERE
                leg_lapg.sampling_type_id <> 0
        ");

        // Update lab_element_results.lab_element_group_id with the id of the newly inserted lab_element_groups
        // (for results that have package_grid_points with sampling_type_id <> 0)
        $this->addSql('WITH ler_data AS 
            (
                SELECT distinct
                    ler.id AS ler_id,
                    new_leg.id AS new_leg_id
                FROM
                    lab_elements_results ler 
                JOIN package_grid_points pgp
                    ON pgp.id = ler.package_grid_points_id
                    AND pgp.sampling_type_id <> 0
                JOIN lab_element_group leg
                    ON leg.id = ler.lab_element_group_id
                JOIN lab_analysis_package_group lapg
                    ON lapg.id = leg.lab_analysis_package_group_id
                LEFT JOIN lab_analysis_package_group new_lapg
                    ON new_lapg.package_id = lapg.package_id
                    AND new_lapg.package_type = lapg.package_type
                    AND new_lapg.lab_analysis_group_id = lapg.lab_analysis_group_id
                    AND new_lapg.sampling_type_id = pgp.sampling_type_id
                LEFT JOIN lab_element_group new_leg
                    ON new_leg.package_id = pgp.package_id
                    AND new_leg.package_type = pgp.package_type
                    AND new_leg.plot_uuid = pgp.plot_uuid
                    AND new_leg.lab_analysis_package_group_id = new_lapg.id	
            )
            UPDATE lab_elements_results
            SET lab_element_group_id = ler_data.new_leg_id
            FROM
                ler_data
            WHERE
                ler_data.ler_id = lab_elements_results.id
        ');

        // Drop column lab_element_group, fk and index
        $this->addSql('ALTER TABLE lab_element_group DROP CONSTRAINT FK_858A2F33FC8E2340');
        $this->addSql('DROP INDEX IDX_858A2F33FC8E2340');
        $this->addSql('ALTER TABLE lab_element_group DROP COLUMN lab_analysis_group_id');
    }

    public function down(Schema $schema): void
    {
        // Set the old lab_element_group_id for lab_element results that have package grid points
        // with sampling_type_id <> 0
        $this->addSql('WITH ler_data AS
            (
                SELECT distinct
                    ler.id AS ler_id,
                    old_leg.id AS old_leg_id
                FROM
                    lab_elements_results ler 
                JOIN package_grid_points pgp
                    ON pgp.id = ler.package_grid_points_id
                    AND pgp.sampling_type_id <> 0
                JOIN lab_element_group leg
                    ON leg.id = ler.lab_element_group_id
                JOIN lab_analysis_package_group lapg
                    ON lapg.id = leg.lab_analysis_package_group_id
                LEFT JOIN lab_analysis_package_group old_lapg
                    ON old_lapg.package_id = lapg.package_id
                    AND old_lapg.package_type = lapg.package_type
                    AND old_lapg.lab_analysis_group_id = lapg.lab_analysis_group_id
                    AND old_lapg.sampling_type_id = 0
                LEFT JOIN lab_element_group old_leg
                    ON old_leg.package_id = pgp.package_id
                    AND old_leg.package_type = pgp.package_type
                    AND old_leg.plot_uuid = pgp.plot_uuid
                    AND old_leg.lab_analysis_package_group_id = old_lapg.id		
            )
            UPDATE lab_elements_results
            SET lab_element_group_id = ler_data.old_leg_id
            FROM
                ler_data
            WHERE
                ler_data.ler_id = lab_elements_results.id
        ');

        // Remove the new lab_element_groups (with sampling_type_id <> 0)
        $this->addSql('DELETE FROM lab_element_group
            USING lab_analysis_package_group
            WHERE
                lab_element_group.lab_analysis_package_group_id = lab_analysis_package_group.id
                AND lab_analysis_package_group.sampling_type_id <> 0
        ');

        // Add column lab_element_group, fk and index
        $this->addSql('ALTER TABLE lab_element_group ADD COLUMN lab_analysis_group_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE lab_element_group ADD CONSTRAINT FK_858A2F33FC8E2340 FOREIGN KEY (lab_analysis_group_id) REFERENCES lab_analysis_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_858A2F33FC8E2340 ON lab_element_group (lab_analysis_group_id)');

        // Fill column lab_element_group
        $this->addSql('WITH leg_lag AS
            (
                SELECT
                    leg.id AS leg_id,
                    lapg.lab_analysis_group_id
                FROM 
                    lab_element_group leg
                JOIN lab_analysis_package_group lapg
                    ON lapg.id = leg.lab_analysis_package_group_id
            )
            UPDATE lab_element_group
            SET 
                lab_analysis_group_id = leg_lag.lab_analysis_group_id
            FROM
                leg_lag
            WHERE
                lab_element_group.id = leg_lag.leg_id
        ');

        // Drop column lab_analysis_package_group_id, index and fk
        $this->addSql('ALTER TABLE lab_element_group DROP CONSTRAINT FK_858A2F33197B4D8E');
        $this->addSql('DROP INDEX IDX_858A2F33197B4D8E');
        $this->addSql('ALTER TABLE lab_element_group DROP COLUMN lab_analysis_package_group_id');
    }
}
