<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210809135058 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("CREATE OR REPLACE FUNCTION get_recommendation_calculations (
            plt_uuid VARCHAR,
            pckg_id INTEGER,
            pckg_type VARCHAR,
            service_provider INTEGER,
            model INTEGER,
            crop INTEGER,
            humus NUMERIC,
            yield NUMERIC
        )
            RETURNS TABLE (\"comments\" JSONB, calculations JSONB, susces JSONB)
            AS $$
            BEGIN
                RETURN QUERY
                WITH result_elements AS (
                    SELECT
                        DISTINCT ON (lerac.\"element\", rer.result_element)
                        lerac.element_id,
                        lerac.\"element\",
                        CASE WHEN licc.content_class = 'Low'::content_class_enum AND (elem_susces.value)::integer = 1
                            THEN
                                true
                            ELSE
                                false
                        END AS susces,
                        case when licc.content_class = 'Low'::content_class_enum and (elem_susces.value)::integer = 0
                            then 0
                        end as content_class,
                        rer.result_element,
                        rcmc.result_element AS result_element_all,
                        rer.value AS result_element_value,
                        sampling_date
                    FROM
                        get_recommendation_sampling_date(plt_uuid, pckg_id, pckg_type) AS sampling_date,
                        get_lab_elements_results_aggregated_classes(ARRAY[plt_uuid], pckg_id, pckg_type, service_provider) AS lerac
                    LEFT JOIN lab_interpretation_classes_config licc
                        ON licc.id = ANY(lerac.class_ids)
                    JOIN recommendation_calc_model_config rcmc
                        ON rcmc.element_id = lerac.element_id
                    LEFT JOIN get_recommendation_crop_model_config_param(service_provider, model, crop, lerac.\"element\", 'Susces') AS elem_susces
                        ON lerac.\"element\" = elem_susces.\"element\"
                    LEFT JOIN get_recommendation_elements_results(plt_uuid, pckg_id, pckg_type, service_provider, model, crop, humus, yield, sampling_date) rer
                        ON rer.result_element = rcmc.result_element
                    GROUP BY
                        lerac.element_id,
                        lerac.\"element\",
                        rer.result_element,
                        licc.content_class,
                        elem_susces.value,
                        rer.value,
                        rcmc.result_element,
                        sampling_date
                    ORDER BY
                        lerac.\"element\",
                        rer.result_element
                )
                SELECT
                    JSONB_AGG(DISTINCT
                        JSONB_BUILD_OBJECT(
                            'result_element', re.result_element,
                            'comment_text', COALESCE(recc.comment_text, rlfcc.comment_text, rfcp.comment_text, recc2.comment_text)
                        )
                    ) FILTER (WHERE COALESCE(recc.comment_text, rfcp.comment_text, rlfcc.comment_text, recc2.comment_text) NOTNULL)  AS \"comments\",
                    JSONB_AGG(
                        JSONB_BUILD_OBJECT(
                            'element', re.\"element\",
                            'result_element', re.result_element,
                            'result_element_value', round(re.result_element_value::NUMERIC, 3)
                        )
                    ) FILTER (WHERE result_element_value NOTNULL) AS calculations,
                    JSONB_AGG(DISTINCT
                        JSONB_BUILD_OBJECT(
                            'element', re.\"element\",
                            'value', re.susces
                        )
                    ) AS susces
                FROM
                    result_elements AS re
                CROSS JOIN LATERAL (
                    SELECT
                        COUNT(re.\"element\")
                    FROM
                        result_elements re
                    WHERE
                        re.susces = TRUE
                ) susces_elements_cnt
                LEFT JOIN recommendation_element_comments_config recc
                        ON (crop = ANY(recc.crop_ids) OR recc.crop_ids ISNULL)
                        AND recc.result_element = re.result_element
                        AND re.result_element_value::numeric <@ recc.\"range\"
                LEFT JOIN recommendation_leaf_fertiliser_comments_config rlfcc
                    ON rlfcc.model_id = model
                    AND (crop = ANY(rlfcc.crop_ids) OR rlfcc.crop_ids ISNULL)
                    and re.result_element_all = ANY(rlfcc.result_elements)
                    and susces_elements_cnt.\"count\" > 0
                LEFT JOIN get_recommendation_fertiliser_comments_ph(plt_uuid, pckg_id, pckg_type, service_provider, model, crop, humus, yield, re.sampling_date) rfcp
                    ON re.\"element\" = 'pH'::elements_enum
                left join recommendation_element_comments_config recc2 
                    on (crop = ANY(recc.crop_ids) OR recc.crop_ids ISNULL)
                        AND recc2.result_element = re.result_element_all
                        AND content_class::numeric <@ recc2.\"range\"
                        and content_class notnull;
                END
            $$ LANGUAGE plpgsql");
    }

    public function down(Schema $schema): void
    {
        $this->addSql("CREATE OR REPLACE FUNCTION get_recommendation_calculations (
            plt_uuid VARCHAR,
            pckg_id INTEGER,
            pckg_type VARCHAR,
            service_provider INTEGER,
            model INTEGER,
            crop INTEGER,
            humus NUMERIC,
            yield NUMERIC
        )
            RETURNS TABLE (\"comments\" JSONB, calculations JSONB, susces JSONB)
            AS $$
                BEGIN
                RETURN QUERY
                WITH result_elements AS (
                    SELECT
                        DISTINCT ON (lerac.\"element\", rer.result_element)
                        lerac.element_id,
                        lerac.\"element\",
                        CASE WHEN licc.content_class = 'Low'::content_class_enum AND (elem_susces.value)::integer = 1
                            THEN
                                true
                            ELSE
                                false
                        END AS susces,
                        rer.result_element,
                        rcmc.result_element AS result_element_all,
                        rer.value AS result_element_value,
                        sampling_date
                    FROM
                        get_recommendation_sampling_date(plt_uuid, pckg_id, pckg_type) AS sampling_date,
                        get_lab_elements_results_aggregated_classes(ARRAY[plt_uuid], pckg_id, pckg_type, service_provider) AS lerac
                    JOIN lab_interpretation_classes_config licc
                        ON licc.id = ANY(lerac.class_ids)
                    JOIN recommendation_calc_model_config rcmc
                        ON rcmc.element_id = lerac.element_id
                    LEFT JOIN get_recommendation_crop_model_config_param(service_provider, model, crop, lerac.\"element\", 'Susces') AS elem_susces
                        ON lerac.\"element\" = elem_susces.\"element\"
                    LEFT JOIN get_recommendation_elements_results(plt_uuid, pckg_id, pckg_type, service_provider, model, crop, humus, yield, sampling_date) rer
                        ON rer.result_element = rcmc.result_element
                    GROUP BY
                        lerac.element_id,
                        lerac.\"element\",
                        rer.result_element,
                        licc.content_class,
                        elem_susces.value,
                        rer.value,
                        rcmc.result_element,
                        sampling_date
                    ORDER BY
                        lerac.\"element\",
                        rer.result_element
                )
                SELECT
                    JSONB_AGG(DISTINCT
                        JSONB_BUILD_OBJECT(
                            'result_element', re.result_element,
                            'comment_text', COALESCE(recc.comment_text, rlfcc.comment_text, rfcp.comment_text)
                        )
                    ) FILTER (WHERE COALESCE(recc.comment_text, rfcp.comment_text, rlfcc.comment_text) NOTNULL)  AS \"comments\",
                    JSONB_AGG(
                        JSONB_BUILD_OBJECT(
                            'element', re.\"element\",
                            'result_element', re.result_element,
                            'result_element_value', round(re.result_element_value::NUMERIC, 3)
                        )
                    ) FILTER (WHERE result_element_value NOTNULL) AS calculations,
                    JSONB_AGG(DISTINCT
                        JSONB_BUILD_OBJECT(
                            'element', re.\"element\",
                            'value', re.susces
                        )
                    ) AS susces
                FROM
                    result_elements AS re
                CROSS JOIN LATERAL (
                    SELECT
                        COUNT(re.\"element\")
                    FROM
                        result_elements re
                    WHERE
                        re.susces = TRUE
                ) susces_elements_cnt
                LEFT JOIN recommendation_element_comments_config recc
                        ON (crop = ANY(recc.crop_ids) OR recc.crop_ids ISNULL)
                        AND recc.result_element = re.result_element
                        AND re.result_element_value::numeric <@ recc.\"range\"
                LEFT JOIN recommendation_leaf_fertiliser_comments_config rlfcc
                    ON rlfcc.model_id = model
                    AND (crop = ANY(rlfcc.crop_ids) OR rlfcc.crop_ids ISNULL)
                    and re.result_element_all = ANY(rlfcc.result_elements)
                    and susces_elements_cnt.\"count\" > 0
                LEFT JOIN get_recommendation_fertiliser_comments_ph(plt_uuid, pckg_id, pckg_type, service_provider, model, crop, humus, yield, re.sampling_date) rfcp
                    ON re.\"element\" = 'pH'::elements_enum;
            END
        $$ LANGUAGE plpgsql
    ");
    }
}
