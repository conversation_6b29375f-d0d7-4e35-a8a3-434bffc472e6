<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230105111250 extends AbstractMigration
{
    public function isTransactional(): bool
    {
        return false;
    }

    public function getDescription(): string
    {
        return 'Add values: \'Total Nitrogen\', \'Bulk Density\', \'Dumas\', \'Organic Carbon\', \'Inorganic Carbon\', \'Active Carbonates\', \'EC\', \'Active Carbon\' to elements_enum.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS 'Total Nitrogen'");
        $this->addSql("ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS 'Bulk Density'");
        $this->addSql("ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS 'Dumas'");
        $this->addSql("ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS 'Organic Carbon'");
        $this->addSql("ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS 'Inorganic Carbon'");
        $this->addSql("ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS 'Active Carbonates'");
        $this->addSql("ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS 'EC'");
        $this->addSql("ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS 'Active Carbon'");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
