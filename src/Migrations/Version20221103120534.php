<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221103120534 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update subscription package filed state were they dont have element group in state For recommendation';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('
        update subscription_package_field
            set field_state  = \'In progress\'
            from (select
                    subscription_package_field.id
            from
                    subscription_package_field
            join lab_element_group leg on
                    leg.package_id = subscription_package_field.subscription_package_id
                and leg.plot_uuid = subscription_package_field.plot_uuid
            where
                    subscription_package_field.field_state = \'For recommendation\'
            group by
                    subscription_package_field.id
            having
                    not \'For recommendation\' = any (array_agg(distinct leg.state))
                and not \'Delivered\' = any (array_agg(distinct leg.state))) as fields_with_wrong_state
            where subscription_package_field.id = fields_with_wrong_state.id;
                    ');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
    }
}
