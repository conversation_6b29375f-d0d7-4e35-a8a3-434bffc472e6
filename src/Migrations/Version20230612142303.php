<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230612142303 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add new column instance and enum main_navigation_instance_enum';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("CREATE TYPE main_navigation_instance_enum AS ENUM ('web', 'mobile')");
        $this->addSql('ALTER TABLE main_navigation ADD instance main_navigation_instance_enum DEFAULT \'web\' NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE main_navigation DROP instance');
        $this->addSql('DROP TYPE IF EXISTS main_navigation_instance_enum');
    }
}
