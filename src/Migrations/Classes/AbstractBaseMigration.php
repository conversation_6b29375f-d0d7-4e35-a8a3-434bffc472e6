<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 6/19/2020
 * Time: 11:46 AM.
 */

namespace App\Migrations\Classes;

use Doctrine\DBAL\DBALException;
use Doctrine\DBAL\ParameterType;
use Doctrine\Migrations\AbstractMigration;
use Exception;
use PDO;

abstract class AbstractBaseMigration extends AbstractMigration
{
    /**
     * @throws DBALException
     *
     * @return false|mixed
     */
    protected function getServiceProviderId(string $slug)
    {
        $sql = 'SELECT id FROM service_provider WHERE slug = :slug';

        return $this->exequteSql($sql, 'slug', $slug);
    }

    /**
     * @throws DBALException
     */
    protected function getServiceProvidersId(array $slugs): array
    {
        $sql = 'SELECT id FROM service_provider';
        $bindingData = str_repeat('?,', count($slugs) - 1) . '?';
        $sql .= ' WHERE slug in (' . $bindingData . ')';
        $stmt = $this->connection->prepare($sql);
        $stmt->execute($slugs);

        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    /**
     * Return elements array [element_name] => element_id.
     *
     * @deprecated this method is deprecated because it may return wrong ids
     * if there are multiple records with the same "element" (for different service provider).
     * Use methdo getElementsByServiceProvider instead.
     */
    protected function getElements(): array
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('id', 'element')
            ->from('lab_analysis_group_element');
        $rows = $queryBuilder->execute();
        $elementIds = [];
        foreach ($rows as $row) {
            $elementIds[$row['element']] = $row['id'];
        }

        return $elementIds;
    }

    /**
     * Return elements array [element_name] => element_id by service provider.
     */
    protected function getElementsByServiceProvider(int $serviceProviderId): array
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select(
                'lage.id',
                'lage.element'
            )
            ->from('lab_analysis_group_element', 'lage')
            ->join(
                'lage',
                'lab_analysis_group_element_visual_order',
                'lagevo',
                'lagevo.lab_analysis_group_element_id = lage.id'
            )
            ->where('lagevo.service_provider_id = :serviceProviderId')
            ->setParameter('serviceProviderId', $serviceProviderId, ParameterType::INTEGER);

        $rows = $queryBuilder->execute();
        $elementIds = [];
        foreach ($rows as $row) {
            $elementIds[$row['element']] = $row['id'];
        }

        return $elementIds;
    }

    /**
     * @throws DBALException
     *
     * @return false|mixed
     */
    protected function getPackageId(string $slug)
    {
        $sql = 'SELECT id FROM package WHERE slug = :slug';

        return $this->exequteSql($sql, 'slug', $slug);
    }

    /**
     * @throws DBALException
     */
    protected function getId(string $sql, array $bindData): int
    {
        return $this->exequteSqlWithMultiBindData($sql, $bindData);
    }

    /**
     * @throws DBALException
     *
     * @return false|mixed
     */
    protected function getLabAnalysisGroupId(string $name)
    {
        $sql = 'SELECT id FROM lab_analysis_group WHERE name = :name';

        return $this->exequteSql($sql, 'name', $name);
    }

    protected function addNewPackageSamplingType(string $packageName, string $samplingType, array $serviceProviders): void
    {
        $bindData = ['type' => $samplingType];
        $sql = 'SELECT id FROM sampling_type WHERE type = :type';
        $samplingTypeId = $this->getId($sql, $bindData);

        foreach ($serviceProviders as $serviceProviderId) {
            $bindData = ['slug' => $packageName, 'service_provider_id' => $serviceProviderId];
            $sql = 'SELECT id FROM package WHERE slug = :slug AND service_provider_id = :service_provider_id';
            $abLightId = $this->getId($sql, $bindData);

            // Insert if not exists yet
            $sql = 'INSERT INTO package_sampling_type (package_id, sampling_type_id)
            SELECT ' . $abLightId . ' AS package_id, ' . $samplingTypeId . ' AS sampling_type_id FROM package_sampling_type
            WHERE NOT EXISTS(
                SELECT package_id FROM package_sampling_type WHERE package_id = ' . $abLightId . '
            )
            LIMIT 1';

            $this->addSql($sql);
        }
    }

    /**
     * @throws DBALException
     *
     * @return array
     */
    protected function getMetaGroups()
    {
        $getMetaGroupsSql = 'SELECT name, id FROM meta_groups';
        $stmt = $this->connection->prepare($getMetaGroupsSql);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    }

    protected function getRecommendationModelsConfigs()
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('id', 'name')
            ->from('recommendation_models_config');

        $rows = $queryBuilder->execute();

        $modelsConfigs = [];
        foreach ($rows as $row) {
            $modelsConfigs[$row['name']] = $row['id'];
        }

        return $modelsConfigs;
    }

    protected function getElementVisualOrder(string $element, int $serviceProvider)
    {
        $sql = '
        select evo.visual_order from lab_analysis_group_element_visual_order evo
        join lab_analysis_group_element lage on lage.id = evo.lab_analysis_group_element_id
        where lage."element"  = :element and service_provider_id = :service_provider_id
        ';
        $stmt = $this->connection->prepare($sql);
        $stmt->bindValue('element', $element);
        $stmt->bindValue('service_provider_id', $serviceProvider);
        $stmt->execute();

        return $stmt->fetch()['visual_order'];
    }

    protected function getGSDbConfig($dbName)
    {
        $env = getenv();

        return 'host=' . $env['SUSI_MAIN_DB_HOST']
            . ' port=' . $env['SUSI_MAIN_DB_PORT']
            . ' dbname=' . $dbName
            . ' user=' . $env['SUSI_MAIN_DB_USER']
            . ' password=' . $env['SUSI_MAIN_DB_PASS'];
    }

    protected function getTFDbConfig()
    {
        $env = getenv();

        return 'host=' . $env['TF_MAIN_DB_HOST']
            . ' port=' . $env['TF_MAIN_DB_PORT']
            . ' dbname=' . $env['TF_MAIN_DB_NAME']
            . ' user=' . $env['TF_MAIN_DB_USER']
            . ' password=' . $env['TF_MAIN_DB_PASS'];
    }

    /**
     * @throws DBALException
     *
     * @return false|mixed
     */
    private function exequteSql(string $sql, string $columnName, $columnValue)
    {
        $stmt = $this->connection->prepare($sql);
        $stmt->bindValue($columnName, $columnValue);

        $stmt->execute();
        $arrResult = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (!$arrResult) {
            throw new Exception('result with ' . $columnName . ': ' . $columnValue . ' not found!');
        }

        if (count($arrResult) > 1) {
            throw new Exception('Too many results with name: ' . $columnValue . '!');
        }

        return reset($arrResult);
    }

    /**
     * @throws DBALException
     */
    private function exequteSqlWithMultiBindData(string $sql, array $bindData): int
    {
        $stmt = $this->connection->prepare($sql);
        foreach ($bindData as $columnName => $columnvalue) {
            $stmt->bindValue($columnName, $columnvalue);
        }
        $stmt->execute();
        $arrResult = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (!$arrResult) {
            throw new Exception('result not found!');
        }

        if (count($arrResult) > 1) {
            throw new Exception('Too many results!');
        }

        return reset($arrResult);
    }
}
