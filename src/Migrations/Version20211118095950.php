<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20211118095950 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create index contract_customer_identification_idx.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX contract_customer_identification_idx ON contract USING btree(customer_identification)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX IF EXISTS contract_customer_identification_idx');
    }
}
