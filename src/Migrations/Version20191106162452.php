<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191106162452 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("CREATE TYPE field_states_enum AS ENUM ('Not approved', 'For grid', 'Gridding', 'Gridded', 'For sampling', 'Sampling', 'Sampled', 'Analised', 'Recomendation', 'For approve', 'Approved')");

        $this->addSql('ALTER TABLE subscription_package_field DROP CONSTRAINT fk_a0f41244d3e0bb0c');
        $this->addSql('DROP SEQUENCE field_states_id_seq CASCADE');
        $this->addSql('DROP TABLE field_states');
        $this->addSql('DROP INDEX idx_a0f41244d3e0bb0c');
        $this->addSql('ALTER TABLE subscription_package_field ADD field_state field_states_enum DEFAULT \'Not approved\'');
        $this->addSql('ALTER TABLE subscription_package_field DROP field_state_id');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SEQUENCE field_states_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE field_states (id SERIAL NOT NULL, service_provider_id INT DEFAULT NULL, slug VARCHAR(31) NOT NULL, PRIMARY KEY(id))');

        // Add field_states data
        $sql = "ALTER TABLE field_states ALTER COLUMN id SET DEFAULT nextval('field_states_id_seq'::regclass)";
        $this->addSql($sql);

        $sql = "INSERT INTO field_states (
            slug,
            service_provider_id
        )
        VALUES
            ('Not approved', 1),
            ('For grid', 1),
            ('Gridding', 1),
            ('Gridded', 1),
            ('For sampling', 1),
            ('Sampling', 1),
            ('Sampled', 1),
            ('Analised', 1),
            ('Recomendation', 1),
            ('For approve', 1),
            ('Approved', 1)
        ";
        $this->addSql($sql);

        $this->addSql('CREATE INDEX idx_1b86fb4fc6c98e06 ON field_states (service_provider_id)');
        $this->addSql('ALTER TABLE field_states ADD CONSTRAINT fk_1b86fb4fc6c98e06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE subscription_package_field ADD field_state_id INT DEFAULT \'1\'');
        $this->addSql('ALTER TABLE subscription_package_field DROP field_state');
        $this->addSql('ALTER TABLE subscription_package_field ADD CONSTRAINT fk_a0f41244d3e0bb0c FOREIGN KEY (field_state_id) REFERENCES field_states (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_a0f41244d3e0bb0c ON subscription_package_field (field_state_id)');

        $this->addSql('DROP TYPE IF EXISTS field_states_enum');
    }
}
