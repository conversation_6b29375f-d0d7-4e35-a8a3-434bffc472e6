<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230329104806 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add new column farm_id in subscription_package_field table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE subscription_package_field ADD farm_id INT DEFAULT 0 NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE subscription_package_field DROP farm_id');
    }
}
