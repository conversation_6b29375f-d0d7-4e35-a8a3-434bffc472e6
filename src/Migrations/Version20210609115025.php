<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210609115025 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Seed table lab_element_interpretations_config.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $serviceProviderId = $this->getServiceProviderId('nikas');

        $elementIds = $this->getElements();

        $this->addSql("
            INSERT INTO lab_element_interpretations_config (element_id, service_provider_id, class_id, range)
            VALUES
                ({$elementIds['pH']}, {$serviceProviderId}, 13, '(,3.5]'::numrange),
                ({$elementIds['pH']}, {$serviceProviderId}, 14, '(3.5,4.5]'::numrange),
                ({$elementIds['pH']}, {$serviceProviderId}, 15, '(4.5,5]'::numrange),
                ({$elementIds['pH']}, {$serviceProviderId}, 16, '(5,5.5]'::numrange),
                ({$elementIds['pH']}, {$serviceProviderId}, 17, '(5.5,6]'::numrange),
                ({$elementIds['pH']}, {$serviceProviderId}, 18, '(6,6.5]'::numrange),
                ({$elementIds['pH']}, {$serviceProviderId}, 19, '(6.5,7.3]'::numrange),
                ({$elementIds['pH']}, {$serviceProviderId}, 20, '(7.3,7.8]'::numrange),
                ({$elementIds['pH']}, {$serviceProviderId}, 21, '(7.8,8.4]'::numrange),
                ({$elementIds['pH']}, {$serviceProviderId}, 22, '(8.4,9]'::numrange),
                ({$elementIds['pH']}, {$serviceProviderId}, 23, '(9,)'::numrange),

                ({$elementIds['TMN']}, {$serviceProviderId}, 1, '(,20]'::numrange),
                ({$elementIds['TMN']}, {$serviceProviderId}, 2, '(20,40]'::numrange),
                ({$elementIds['TMN']}, {$serviceProviderId}, 3, '(40,60]'::numrange),
                ({$elementIds['TMN']}, {$serviceProviderId}, 4, '(60,80]'::numrange),
                ({$elementIds['TMN']}, {$serviceProviderId}, 5, '(80,)'::numrange),

                ({$elementIds['P2O5']}, {$serviceProviderId}, 2, '(,40]'::numrange),
                ({$elementIds['P2O5']}, {$serviceProviderId}, 6, '(40,80]'::numrange),
                ({$elementIds['P2O5']}, {$serviceProviderId}, 7, '(80,150]'::numrange),
                ({$elementIds['P2O5']}, {$serviceProviderId}, 4, '(150,220]'::numrange),
                ({$elementIds['P2O5']}, {$serviceProviderId}, 5, '(220,)'::numrange),

                ({$elementIds['K2O']}, {$serviceProviderId}, 2, '(,85]'::numrange),
                ({$elementIds['K2O']}, {$serviceProviderId}, 6, '(85,170]'::numrange),
                ({$elementIds['K2O']}, {$serviceProviderId}, 7, '(170,220]'::numrange),
                ({$elementIds['K2O']}, {$serviceProviderId}, 4, '(220,500]'::numrange),
                ({$elementIds['K2O']}, {$serviceProviderId}, 5, '(500,)'::numrange),

                ({$elementIds['CaO']}, {$serviceProviderId}, 2, '(,1500]'::numrange),
                ({$elementIds['CaO']}, {$serviceProviderId}, 6, '(1500,2800]'::numrange),
                ({$elementIds['CaO']}, {$serviceProviderId}, 4, '(2800,)'::numrange),

                ({$elementIds['MgO']}, {$serviceProviderId}, 2, '(,100]'::numrange),
                ({$elementIds['MgO']}, {$serviceProviderId}, 6, '(100,200]'::numrange),
                ({$elementIds['MgO']}, {$serviceProviderId}, 4, '(200,)'::numrange),

                ({$elementIds['S']}, {$serviceProviderId}, 2, '(,5]'::numrange),
                ({$elementIds['S']}, {$serviceProviderId}, 6, '(5,10]'::numrange),
                ({$elementIds['S']}, {$serviceProviderId}, 4, '(10,20]'::numrange),
                ({$elementIds['S']}, {$serviceProviderId}, 5, '(20,)'::numrange),

                ({$elementIds['B']}, {$serviceProviderId}, 2, '(,0.5]'::numrange),
                ({$elementIds['B']}, {$serviceProviderId}, 6, '(0.5,2]'::numrange),
                ({$elementIds['B']}, {$serviceProviderId}, 4, '(2,)'::numrange),

                ({$elementIds['Cu']}, {$serviceProviderId}, 2, '(,0.6]'::numrange),
                ({$elementIds['Cu']}, {$serviceProviderId}, 6, '(0.6,2]'::numrange),
                ({$elementIds['Cu']}, {$serviceProviderId}, 4, '(2,)'::numrange),

                ({$elementIds['Fe']}, {$serviceProviderId}, 2, '(,2.5]'::numrange),
                ({$elementIds['Fe']}, {$serviceProviderId}, 6, '(2.5,5]'::numrange),
                ({$elementIds['Fe']}, {$serviceProviderId}, 4, '(5,)'::numrange),

                ({$elementIds['Mn']}, {$serviceProviderId}, 2, '(,5]'::numrange),
                ({$elementIds['Mn']}, {$serviceProviderId}, 6, '(5,10]'::numrange),
                ({$elementIds['Mn']}, {$serviceProviderId}, 4, '(10,)'::numrange),

                ({$elementIds['Zn']}, {$serviceProviderId}, 2, '(,1]'::numrange),
                ({$elementIds['Zn']}, {$serviceProviderId}, 6, '(1,1.5]'::numrange),
                ({$elementIds['Zn']}, {$serviceProviderId}, 4, '(1.5,)'::numrange),

                ({$elementIds['Mo']}, {$serviceProviderId}, 2, '(,0.1]'::numrange),
                ({$elementIds['Mo']}, {$serviceProviderId}, 6, '(0.1,0.3]'::numrange),
                ({$elementIds['Mo']}, {$serviceProviderId}, 4, '(0.3,1]'::numrange),
                ({$elementIds['Mo']}, {$serviceProviderId}, 5, '(1,)'::numrange),

                ({$elementIds['Humus']}, {$serviceProviderId}, 1, '(,1]'::numrange),
                ({$elementIds['Humus']}, {$serviceProviderId}, 2, '(1,2]'::numrange),
                ({$elementIds['Humus']}, {$serviceProviderId}, 3, '(2,3]'::numrange),
                ({$elementIds['Humus']}, {$serviceProviderId}, 7, '(3,4]'::numrange),
                ({$elementIds['Humus']}, {$serviceProviderId}, 4, '(4,5]'::numrange),
                ({$elementIds['Humus']}, {$serviceProviderId}, 5, '(5,)'::numrange)
        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DELETE FROM lab_element_interpretations_config');
    }
}
