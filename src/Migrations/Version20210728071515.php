<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210728071515 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Drop function postgresql get_recommendation_element_comments_config()';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            DROP FUNCTION IF EXISTS get_recommendation_element_comments_config (
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                elements elements_enum[]
            )
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            CREATE FUNCTION get_recommendation_element_comments_config (
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                elements elements_enum[]
            )
                RETURNS TABLE (
                    "element" elements_enum,
                    params recommendation_crop_model_parameter_enum[],
                    result_element result_element_enum,
                    value NUMERIC,
                    renge NUMRANGE,
                    class_ids INTEGER[],
                    content_class content_class_enum,
                    comment_text TEXT
                )
                AS $$
                    BEGIN
                    RETURN QUERY
                        SELECT
                            lerc."element",
                            rcmc.params,
                            recc.result_element,
                            rcmcv.value::numeric,
                            recc."range",
                            lerc.class_ids,
                            licc.content_class,
                            recc.comment_text
                        FROM 
                            get_lab_elements_results_aggregated_classes(ARRAY[plt_uuid], pckg_id, pckg_type, service_provider) AS lerc	
                        JOIN lab_interpretation_classes_config licc
                            ON licc.id = ANY(lerc.class_ids)
                            AND licc.content_class NOTNULL
                        JOIN recommendation_calc_model_config rcmc
                            ON lerc.element_id = rcmc.element_id
                        JOIN recommendation_crop_model_config_values rcmcv
                            ON rcmcv.model_id = model
                            AND crop = ANY(rcmcv.crop_ids)
                            AND (rcmcv."parameter" = any(rcmc.params) OR rcmc.params ISNULL)
                        JOIN recommendation_element_comments_config recc
                            ON (crop = ANY(recc.crop_ids) OR recc.crop_ids ISNULL)
                            AND recc.result_element = rcmc.result_element
                            AND rcmcv.value::numeric <@ recc."range"
                        WHERE
                            lerc."element" = ANY (elements);
                    END;
                $$ LANGUAGE plpgsql
        ');
    }
}
