<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210803121159 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create postgresql function get_recommendation_results()';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("
            CREATE FUNCTION get_recommendation_results (
                recommendation INTEGER,
                service_provider INTEGER
            )
                RETURNS TABLE (\"comments\" JSONB, results JSONB, susces JSONB)
                AS $$
                    BEGIN
                    RETURN QUERY
                    WITH recommendation_elements AS (
                        SELECT DISTINCT
                            r.id AS recommendation_id,
                            lerac.element_id,
                            lerac.\"element\",
                            CASE WHEN licc.content_class = 'Low'::content_class_enum AND (elem_susces.value)::integer = 1
                                THEN
                                    true
                                ELSE
                                    false
                            END AS susces
                        FROM
                            recommendations AS r
                        CROSS JOIN get_lab_elements_results_aggregated_classes(array[r.plot_uuid], r.package_id, r.package_type, service_provider) as lerac
                        JOIN lab_interpretation_classes_config licc
                            ON licc.id = ANY(lerac.class_ids)
                        LEFT JOIN get_recommendation_crop_model_config_param(service_provider, r.model_id, r.crop_id, lerac.\"element\", 'Susces') AS elem_susces
                            ON lerac.\"element\" = elem_susces.\"element\"
                        WHERE r.id = recommendation
                    )
                    SELECT
                    JSONB_AGG(DISTINCT
                        JSONB_BUILD_OBJECT(
                            'id', rc.id,
                            'result_element', rc.param,
                            'comment_text', rc.value
                        )
                    ) FILTER (WHERE rc.value NOTNULL)  AS \"comments\",
                    JSONB_AGG(DISTINCT
                        JSONB_BUILD_OBJECT(
                            'id', rer.id,
                            'element', re.\"element\",
                            'result_element', rer.result_element,
                            'result_element_value', round(rer.value::NUMERIC, 3)
                        )
                    ) FILTER (WHERE rer.value NOTNULL AND rcmc.result_element = rer.result_element) AS results,
                    JSONB_AGG(DISTINCT
                        JSONB_BUILD_OBJECT(
                            'element', re.\"element\",
                            'value', re.susces
                        )
                    ) AS susces
                    FROM
                        recommendations r
                    JOIN recommendation_comment rc
                        ON rc.recommendation_id = r.id
                    JOIN recommendation_elements_results rer
                        ON rer.recommendation_id = r.id
                    JOIN recommendation_elements re
                        ON re.recommendation_id = r.id
                    JOIN recommendation_calc_model_config rcmc
                        ON rcmc.element_id = re.element_id;
                END
            $$ LANGUAGE plpgsql
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            '
            DROP FUNCTION IF EXISTS get_recommendation_results (
                recommendation INTEGER,
                service_provider INTEGER
            )'
        );
    }
}
