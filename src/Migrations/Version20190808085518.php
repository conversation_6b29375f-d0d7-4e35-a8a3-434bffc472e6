<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190808085518 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $sql = "ALTER TABLE package ALTER COLUMN id SET DEFAULT nextval('package_id_seq'::regclass)";
        $this->addSql($sql);

        $sql = "INSERT INTO package (
            slug,
            service_provider_id,
            is_active,
            contain_fields
        )
        VALUES
            ('AB ISO full', 1, true, true),
            ('AB VRA full', 1, true, true),
            ('AB ISO control', 1, true, true),
            ('AB VRA control', 1, true, true),
            ('AB Leaf samples', 1, true, true),
            ('AB Humus', 1, true, true),
            ('Satellite imagery', 1, true, true),
            ('Weather data', 1, true, true),
            ('Weather stations', 1, true, false),
            ('Index VRA-N', 1, true, false),
            ('3D mapping', 1, true, false),
            ('TF connect', 1, true, false)
         ";

        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
