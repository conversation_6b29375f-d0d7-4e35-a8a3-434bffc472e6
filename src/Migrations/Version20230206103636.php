<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230206103636 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add pacakge attributes column in package table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE package ADD attributes jsonb DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE package DROP attributes');
    }
}
