<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210624052600 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Drop NOT NULL constraint for column crop_ids in table recommendation_element_comments_config. Set the crop_ids to null where crop_ids = [0]';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE recommendation_element_comments_config ALTER COLUMN crop_ids DROP NOT NULL;
        ');

        $this->addSql('
            UPDATE recommendation_element_comments_config SET crop_ids = NULL WHERE 0 = ANY (crop_ids);
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            UPDATE recommendation_element_comments_config SET crop_ids = ARRAY[0] WHERE crop_ids ISNULL;
        ');

        $this->addSql('
            ALTER TABLE recommendation_element_comments_config ALTER COLUMN crop_ids SET NOT NULL;
        ');
    }
}
