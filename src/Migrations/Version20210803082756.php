<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210803082756 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add status \'For recommendation\' to subscription_package_field.';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TYPE field_states_enum ADD VALUE \'For recommendation\'');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE subscription_package_field ALTER field_states_enum DROP DEFAULT');
        $this->addSql('CREATE TYPE field_states_enum_new AS ENUM (
        \'New\', \'Active\', \'Gridded\', \'For sampling\', \'Sampling\', \'Sampled\', \'In progress\', \'Delivered\', \'Expired\'
        )');
        $this->addSql('ALTER TABLE subscription_package_field ALTER COLUMN status TYPE field_states_enum_new USING (status::text::recommendation_status_enum_new)');
        $this->addSql('DROP TYPE field_states_enum');
        $this->addSql('ALTER TYPE field_states_enum_new RENAME TO field_states_enum');
        $this->addSql('ALTER TABLE subscription_package_field ALTER field_states_enum SET DEFAULT \'New\'');
    }
}
