<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210609071931 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Add element \'Mo\' to elements_enum type. Add constraint lage_unique_fields UNIQUE (lab_analysis_group_id, element) to table lab_analysis_group_element. Add \'Mo\' to lab_analysis_group_element (AbSoilCEC).';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TYPE elements_enum ADD VALUE IF NOT EXISTS \'Mo\'');
        $this->addSql('ALTER TABLE lab_analysis_group_element ADD CONSTRAINT lage_unique_fields UNIQUE (lab_analysis_group_id, element)');

        $this->addSql('INSERT INTO lab_analysis_group_element (lab_analysis_group_id, element) VALUES (
            (SELECT id FROM lab_analysis_group WHERE name = \'ABSoilCEC\'), \'Mo\'
        ) ON CONFLICT DO NOTHING');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE lab_analysis_group_element DROP CONSTRAINT lage_unique_fields');
        $this->addSql('DELETE FROM lab_analysis_group_element
            USING lab_analysis_group
            WHERE
                lab_analysis_group.id = lab_analysis_group_element.lab_analysis_group_id
                and lab_analysis_group.name = \'ABSoilCEC\'
                AND lab_analysis_group_element.element = \'Mo\'
        ');
    }
}
