<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200406070245 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '
            Replace elements (Ca -> CaO, Mg -> MgO, Na -> Na2O) in tables 
            lab_elements_calculations, lab_analysis_group_element, lab_elements_results, lab_elements_results_raw. 
        ';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE lab_elements_calculations SET element = \'CaO\' WHERE element = \'Ca\'');
        $this->addSql('UPDATE lab_elements_calculations SET element = \'MgO\' WHERE element = \'Mg\'');
        $this->addSql('UPDATE lab_elements_calculations SET element = \'Na2O\' WHERE element = \'Na\'');

        $this->addSql('UPDATE lab_analysis_group_element SET element = \'CaO\' WHERE element = \'Ca\'');
        $this->addSql('UPDATE lab_analysis_group_element SET element = \'MgO\' WHERE element = \'Mg\'');
        $this->addSql('UPDATE lab_analysis_group_element SET element = \'Na2O\' WHERE element = \'Na\'');

        $this->addSql('UPDATE lab_elements_results SET element = \'CaO\' WHERE element = \'Ca\'');
        $this->addSql('UPDATE lab_elements_results SET element = \'MgO\' WHERE element = \'Mg\'');
        $this->addSql('UPDATE lab_elements_results SET element = \'Na2O\' WHERE element = \'Na\'');

        $this->addSql('UPDATE lab_elements_results SET element = \'CaO\' WHERE element = \'Ca\'');
        $this->addSql('UPDATE lab_elements_results SET element = \'MgO\' WHERE element = \'Mg\'');
        $this->addSql('UPDATE lab_elements_results SET element = \'Na2O\' WHERE element = \'Na\'');

        $this->addSql('UPDATE lab_elements_results_raw SET element = \'CaO\' WHERE element = \'Ca\'');
        $this->addSql('UPDATE lab_elements_results_raw SET element = \'MgO\' WHERE element = \'Mg\'');
        $this->addSql('UPDATE lab_elements_results_raw SET element = \'Na2O\' WHERE element = \'Na\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('UPDATE lab_elements_calculations SET element = \'Ca\' WHERE element = \'CaO\'');
        $this->addSql('UPDATE lab_elements_calculations SET element = \'Mg\' WHERE element = \'MgO\'');
        $this->addSql('UPDATE lab_elements_calculations SET element = \'Na\' WHERE element = \'Na2O\'');

        $this->addSql('UPDATE lab_analysis_group_element SET element = \'Ca\' WHERE element = \'CaO\'');
        $this->addSql('UPDATE lab_analysis_group_element SET element = \'Mg\' WHERE element = \'MgO\'');
        $this->addSql('UPDATE lab_analysis_group_element SET element = \'Na\' WHERE element = \'Na2O\'');

        $this->addSql('UPDATE lab_elements_results SET element = \'Ca\' WHERE element = \'CaO\'');
        $this->addSql('UPDATE lab_elements_results SET element = \'Mg\' WHERE element = \'MgO\'');
        $this->addSql('UPDATE lab_elements_results SET element = \'Na\' WHERE element = \'Na2O\'');

        $this->addSql('UPDATE lab_elements_results_raw SET element = \'Ca\' WHERE element = \'CaO\'');
        $this->addSql('UPDATE lab_elements_results_raw SET element = \'Mg\' WHERE element = \'MgO\'');
        $this->addSql('UPDATE lab_elements_results_raw SET element = \'Na\' WHERE element = \'Na2O\'');
    }
}
