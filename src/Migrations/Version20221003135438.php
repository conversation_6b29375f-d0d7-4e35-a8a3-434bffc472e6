<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Exception;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Output\ConsoleOutput;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221003135438 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Seed column parent_id in table \'subscription_package_field\'.';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        $env = getenv();
        $dbConnStr = 'host=' . $env['SUSI_MAIN_DB_HOST']
            . ' port=' . $env['SUSI_MAIN_DB_PORT']
            . ' user=' . $env['SUSI_MAIN_DB_USER']
            . ' password=' . $env['SUSI_MAIN_DB_PASS'];

        $dbNameByServiceProvider = [
            1 => 'susi_main_v5',
            2 => 'susi_main_ro_v5',
            3 => 'susi_main_ro_v5',
            4 => 'susi_main_rs_v5',
            5 => 'susi_main_ua',
            6 => 'susi_main_it_v5',
            9 => 'susi_main_v5',
            7 => 'susi_main_tr',
        ];

        $output = new ConsoleOutput();
        $progressBar = new ProgressBar($output, count($dbNameByServiceProvider));
        $progressBar->start();

        $params = [];
        $paramTypes = [];
        foreach ($dbNameByServiceProvider as $serviceProviderId => $dbName) {
            $params['dbConnStr'] = $dbConnStr . ' dbname=' . $dbName;
            $paramTypes['dbConnStr'] = ParameterType::STRING;

            $params['serviceProviderId'] = $serviceProviderId;
            $paramTypes['serviceProviderId'] = ParameterType::INTEGER;

            $sql = 'WITH contract_packages_data AS (
                    SELECT
                        sp.contract_id,
                        spf.subscription_package_id,
                        sp."period" AS package_period,
                        p.is_sampling,
                        p.is_full_sampling,
                        p.is_vra,
                        p.id AS package_id,
                        p.parent_id AS package_parent_id,
                        spf.plot_uuid,
                        spf.order_uuid,
                        spf.id AS subscription_package_field_id
                    FROM 
                        subscription_package_field spf
                    JOIN subscription_package sp
                        ON sp.id = spf.subscription_package_id
                    JOIN package p
                        ON p.id = sp.package_id
                    JOIN contract c
                        ON c.id = sp.contract_id
                    WHERE
                        c.service_provider_id = :serviceProviderId
                        AND p.is_sampling = TRUE
                ),
                contract_orders_plots AS (
                    SELECT
                        contract_id,
                        array_agg(DISTINCT plot_uuid) FILTER (WHERE is_full_sampling = TRUE) AS full_sampling_package_plot_uuids,
                        array_agg(DISTINCT order_uuid) FILTER (WHERE is_full_sampling = TRUE) AS full_sampling_package_order_uuids,
                        array_agg(DISTINCT plot_uuid) AS all_packages_plot_uuids,
                        array_agg(DISTINCT order_uuid) AS all_packages_order_uuids
                    FROM
                        contract_packages_data
                    GROUP BY 
                        contract_id
                )
                UPDATE subscription_package_field SET
                    parent_id = full_sampling_package_fields.subscription_package_field_id
                FROM
                    contract_orders_plots AS cop,
                    dblink(
                        :dbConnStr,
                        format(
                            $$
                            WITH cms_plots_with_geom AS (
                                SELECT
                                    sopr.plot_uuid,
                                    sopr.order_uuid,
                                    sp.geom
                                FROM
                                    su_satellite_orders_plots_rel sopr
                                JOIN su_satellite_plots AS sp
                                    ON sp.uuid = sopr.plot_uuid
                                WHERE
                                    sopr.plot_uuid = ANY(%L)
                                    AND sopr.order_uuid = ANY(%L)
                            )
                            SELECT
                                cpwg.plot_uuid AS full_sampling_package_plot_uuid,
                                cpwg.order_uuid AS full_sampling_package_order_uuid,
                                cpwg2.plot_uuid AS other_package_plot_uuid,
                                cpwg2.order_uuid AS other_package_order_uuid
                            FROM
                                cms_plots_with_geom AS cpwg
                            LEFT JOIN cms_plots_with_geom cpwg2
                                ON ST_Area(ST_INTERSECTION(cpwg.geom, cpwg2.geom)) / st_area(cpwg.geom) > 0.5
                                AND cpwg.order_uuid <> cpwg2.order_uuid
                            WHERE
                                cpwg.plot_uuid = ANY(%L)
                                AND cpwg.order_uuid = ANY(%L)
                            $$,
                            cop.all_packages_plot_uuids, cop.all_packages_order_uuids, cop.full_sampling_package_plot_uuids, cop.full_sampling_package_order_uuids
                        )
                    ) AS laravel_data_diff (
                        full_sampling_package_plot_uuid uuid,
                        full_sampling_package_order_uuid uuid,
                        other_package_plot_uuid uuid,
                        other_package_order_uuid uuid
                    )
                LEFT JOIN contract_packages_data AS full_sampling_package_fields
                    ON full_sampling_package_fields.plot_uuid::uuid = laravel_data_diff.full_sampling_package_plot_uuid
                    AND full_sampling_package_fields.order_uuid::uuid = laravel_data_diff.full_sampling_package_order_uuid
                    AND full_sampling_package_fields.is_sampling = TRUE
                    AND full_sampling_package_fields.is_full_sampling = TRUE
                LEFT JOIN contract_packages_data AS not_full_sampling_package_fields
                    ON not_full_sampling_package_fields.plot_uuid::uuid = laravel_data_diff.other_package_plot_uuid
                    AND not_full_sampling_package_fields.order_uuid::uuid = laravel_data_diff.other_package_order_uuid
                    AND not_full_sampling_package_fields.is_sampling = TRUE
                    AND not_full_sampling_package_fields.is_full_sampling = FALSE
                    AND not_full_sampling_package_fields.package_parent_id = full_sampling_package_fields.package_id
                WHERE
                    not_full_sampling_package_fields.subscription_package_field_id notnull
                    AND id = not_full_sampling_package_fields.subscription_package_field_id
            ';

            try {
                $this->connection->executeQuery($sql, $params, $paramTypes);
            } catch (Exception $e) {
                echo "\nCould not connect to laravel db '{$dbName}' for service provider {$serviceProviderId}. Continuing with the other service providers.";

                continue;
            }

            $progressBar->advance();
        }
        $progressBar->finish();
    }

    public function down(Schema $schema): void
    {
        $this->addSql('UPDATE subscription_package_field SET parent_id = NULL');
    }
}
