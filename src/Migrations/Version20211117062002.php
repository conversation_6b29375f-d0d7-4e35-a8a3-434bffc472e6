<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20211117062002 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add new states for point_states_enum and field_states_enum';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TYPE point_states_enum ADD VALUE IF NOT EXISTS \'For sampling\' BEFORE \'Sampling\'');
        $this->addSql('ALTER TYPE field_states_enum ADD VALUE IF NOT EXISTS \'Cells selected\' BEFORE \'For sampling\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
    }
}
