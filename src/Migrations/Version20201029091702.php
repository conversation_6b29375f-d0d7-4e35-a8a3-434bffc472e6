<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20201029091702 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $serviceProviderIdId = $this->getServiceProviderId('nikas');
        $arrServiceProviderIds = [$serviceProviderIdId];

        $this->addNewPackageSamplingType('AB Light', '0-30', $arrServiceProviderIds);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $abLightId = $this->getPackageId('AB Light');
        $sql = 'DELETE FROM package_sampling_type WHERE package_id = ' . $abLightId . '';
        $this->addSql($sql);
    }
}
