<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210608085245 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Seed table lab_interpretation_classes_config';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
        $this->addSql('
            INSERT INTO lab_interpretation_classes_config (slug, description)
            VALUES
                (\'VL\', \'very low\'),
                (\'L\', \'low\'),
                (\'M\', \'medium\'),
                (\'H\', \'high\'),
                (\'VH\', \'very high\'),
                (\'S\', \'sufficient\'),
                (\'G\', \'good\'),
                (\'NS\', \'non saline\'),
                (\'SLS\', \'slightly saline\'),
                (\'MS\', \'moderately saline\'),
                (\'STS\', \'strongly saline\'),
                (\'VSS\', \'very strongly saline\'),
                (\'UA\', \'ultra acidic\'),
                (\'ЕA\', \'extremely acidic\'),
                (\'VSC\', \'very strongly acidic\'),
                (\'SAC\', \'strongly acidic\'),
                (\'MAC\', \'moderately acidic\'),
                (\'SLA\', \'slightly acidic\'),
                (\'NEU\', \'neutral\'),
                (\'SAL\', \'slightly alkaline\'),
                (\'MAL\', \'moderately alkaline\'),
                (\'STA\', \'strongly alkaline\'),
                (\'VSA\', \'very strongly alkaline\'),
                (\'AC\', \'acidic\'),
                (\'NE\', \'neutral\'),
                (\'AL\', \'alcaline\')
        ');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
        $this->addSql('DELETE FROM lab_interpretation_classes_config');
    }
}
