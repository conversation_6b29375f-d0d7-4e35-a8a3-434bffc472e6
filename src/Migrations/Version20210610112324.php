<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210610112324 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Seed table recommendation_calc_model_config';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("INSERT INTO recommendation_calc_model_config (calc_model_id, result_element) VALUES
            (1, 'Need_N_total'),
            (1, 'Need_P_total'),
            (1, 'Need_K_total'),
            (1, 'Need_S_total'),
            (1, 'Add_N_total'),
            (1, 'Add_P_total'),
            (1, 'Add_K_total'),
            (1, 'Add_S_total'),
            (1, 'Add_N_fall'),
            (1, 'pH_predominant'),
            (1, 'Add_B'),
            (1, 'Add_Mn'),
            (1, 'Add_Cu'),
            (1, 'Add_Zn'),
            (1, 'Add_Mo'),
            (1, 'Add_Fe')
        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DELETE FROM recommendation_calc_model_config');
    }
}
