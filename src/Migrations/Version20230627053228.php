<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230627053228 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add \'target\' property to the result of function get_package_main_navigation_tree';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP FUNCTION IF exists get_package_main_navigation_tree (_package_id INT, _path LTREE)');
        $this->addSql('CREATE OR REPLACE
        FUNCTION get_package_main_navigation_tree(_lang VARCHAR, _package_id INT, _path LTREE)
                RETURNS JSONB
                LANGUAGE plpgsql AS
            $$
                DECLARE
                    _result JSONB;
                BEGIN
                    EXECUTE format(
                       $query$
                        SELECT JSONB_AGG(sub)
                        FROM (
                            SELECT
                                mn.id,
                                mn.label_%1$s as label,
                                mn.url,
                                mn.no_data_url,
                                mn.path,
                                mn.icon,
                                NLEVEL(mn."path") AS level,
                                pmn.visual_order,
                                pmn.target,
                                COALESCE(get_package_main_navigation_tree(\'%1$s\', %2$s, mn."path"), \'[]\'::JSONB) AS children
                            FROM
                                package_main_navigation pmn
                            JOIN main_navigation AS mn
                                ON mn.id = pmn.main_navigation_id
                            WHERE
                                pmn.package_id = %2$s
                                AND \'%3$s\'::LTREE @> mn."path"
                                AND NLEVEL(mn."path") = NLEVEL(\'%3$s\'::LTREE) + 1
                            GROUP BY
                                mn.id,
                                pmn.visual_order,
                                pmn.target
                            ORDER BY
                                pmn.visual_order
                        ) sub;
                    $query$,
                    _lang, _package_id, _path) INTO _result;
                    RETURN _result;
                END;
            $$;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP FUNCTION IF exists get_package_main_navigation_tree (_package_id INT, _path LTREE)');
        $this->addSql('CREATE OR REPLACE
        FUNCTION get_package_main_navigation_tree(_lang VARCHAR, _package_id INT, _path LTREE)
                RETURNS JSONB
                LANGUAGE plpgsql AS
            $$
                DECLARE
                    _result JSONB;
                BEGIN
                    EXECUTE format(
                       $query$
                        SELECT JSONB_AGG(sub)
                        FROM (
                            SELECT
                                mn.id,
                                mn.label_%1$s as label,
                                mn.url,
                                mn.no_data_url,
                                mn.path,
                                mn.icon,
                                NLEVEL(mn."path") AS level,
                                pmn.visual_order,
                                COALESCE(get_package_main_navigation_tree(\'%1$s\', %2$s, mn."path"), \'[]\'::JSONB) AS children
                            FROM
                                package_main_navigation pmn
                            JOIN main_navigation AS mn
                                ON mn.id = pmn.main_navigation_id
                            WHERE
                                pmn.package_id = %2$s
                                AND \'%3$s\'::LTREE @> mn."path"
                                AND NLEVEL(mn."path") = NLEVEL(\'%3$s\'::LTREE) + 1
                            GROUP BY
                                mn.id,
                                pmn.visual_order
                            ORDER BY
                                pmn.visual_order
                        ) sub;
                    $query$,
                    _lang, _package_id, _path) INTO _result;
                    RETURN _result;
                END;
            $$;
        ');
    }
}
