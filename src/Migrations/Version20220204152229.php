<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220204152229 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create function get_farming_year to return farming year by giving date.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql(
            'create or replace
                            function get_farming_year(date DATE)
                            returns int
                            as 
                            $$
                            begin
                            if to_char(date, \'mm\')::int < 10 then 
                                return to_char(date, \'YYYY\')::int;
                            else
                                return to_char(date, \'YYYY\')::int + 1;
                            end if;
                            end
                            $$ language plpgsql;'
        );
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
        $this->addSql('DROP FUNCTION IF EXISTS get_farming_year(DATE);');
    }
}
