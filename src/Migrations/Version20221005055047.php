<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221005055047 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create function witch return farming years by given organization';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
        $this->addSql('DROP FUNCTION IF exists farm_years_by_organization (organizationId INTEGER)');
        $this->addSql("
        create function farm_years_by_organization (organizationiId INTEGER)
             returns table (farming_years JSONB)
             as $$
                begin
                return QUERY
                    with min_and_max_dates_for_contracts_by_organization as(
                    select
                        min(c.start_date) as start_date,
                        max(c.end_date) as end_date,
                        dt.start_month,
                        dt.start_day,
                        dt.end_month,
                        dt.end_day
                    from
                        contract c
                    join duration_type as dt on
                        c.service_provider_id = dt.service_provider_id
                    where
                        c.organization_id = organizationiId
                        and dt.slug = 'farming_year'
                    group by
                        dt.start_month,
                        dt.start_day,
                        dt.end_month,
                        dt.end_day
                    ),
                    check_farming_year_rules as (
                    select 
                        case 
                            when to_char(mamdfcbo.start_date::timestamp , 'mm')::int < mamdfcbo.start_month 
                                then TO_CHAR(mamdfcbo.start_date, concat('YYYY-', mamdfcbo.start_month, '-', mamdfcbo.start_day))::timestamp - interval '1 year'
                            else TO_CHAR(mamdfcbo.start_date, concat('YYYY-', mamdfcbo.start_month, '-', mamdfcbo.start_day))::timestamp
                        end start_period_date,
                        case
                            when to_char(mamdfcbo.end_date::timestamp , 'mm')::int >= mamdfcbo.start_month 
                            then TO_CHAR(mamdfcbo.end_date, concat('YYYY-', mamdfcbo.end_month, '-', mamdfcbo.end_day))::timestamp + interval '1 year'
                            else TO_CHAR(mamdfcbo.end_date, concat('YYYY-', mamdfcbo.end_month, '-', mamdfcbo.end_day))::timestamp
                        end end_period_date,
                        mamdfcbo.start_month,
                        mamdfcbo.start_day,
                        mamdfcbo.end_month,
                        mamdfcbo.end_day
                    from
                        min_and_max_dates_for_contracts_by_organization as mamdfcbo
                    ),
                    create_periods as (
                    select
                        TO_CHAR(generate_series(cfyr.start_period_date , cfyr.end_period_date , '1 year'::interval), 'YYYY-mm-dd')::timestamp as start_period_date,
                        TO_CHAR(generate_series(cfyr.start_period_date + interval '1 year', cfyr.end_period_date + interval '1 year', '1 year'::interval), concat('YYYY-', cfyr.end_month, '-', cfyr.end_day, ' 23:59:59') )::timestamp as end_period_date
                    from
                        check_farming_year_rules as cfyr
                    ),
                    select_periods_for_organization as(
                    select
                        distinct
                        cp.start_period_date,
                        cp.end_period_date
                    from
                        contract as c
                    cross join create_periods as cp
                    where
                        (
                         c.start_date between cp.start_period_date and cp.end_period_date
                            or (c.end_date between cp.start_period_date and cp.end_period_date)
                                or (c.start_date <= cp.start_period_date
                                    and c.end_date >= cp.end_period_date)
                        )
                        and c.organization_id = organizationiId
                    )
                    select
                        jsonb_agg( jsonb_build_object(
                        'title', TO_CHAR(spfo.end_period_date, 'YYYY')::integer,
                        'id', TO_CHAR(spfo.end_period_date, 'YYYY')::integer,
                        'year', TO_CHAR(spfo.end_period_date, 'YYYY')::integer,
                        'default', case when TO_CHAR(spfo.end_period_date, 'YYYY')::integer = date_part('year', now())::integer then true else false end,
                        'from_date', spfo.start_period_date::date,
                        'to_date', spfo.end_period_date::date,
                        'farming_year', concat(TO_CHAR(spfo.start_period_date, 'YYYY'), '/', TO_CHAR(spfo.end_period_date, 'YYYY')) 
                    ) order by
                        spfo.start_period_date ) as farming_years
                    from
                        select_periods_for_organization as spfo;
                end
            $$ language plpgsql
        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP FUNCTION IF exists farm_years_by_organization (organizationId INTEGER)');
    }
}
