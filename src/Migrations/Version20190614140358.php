<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190614140358 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP SEQUENCE package_field_id_seq CASCADE');
        $this->addSql('CREATE SEQUENCE service_contract_packages_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE service_package_field_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE subscription_package_field_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE service_contract_packages (id INT NOT NULL, contract_id INT NOT NULL, package_id INT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_6415BE8D2576E0FD ON service_contract_packages (contract_id)');
        $this->addSql('CREATE INDEX IDX_6415BE8DF44CABFF ON service_contract_packages (package_id)');
        $this->addSql('CREATE TABLE service_package_field (id INT NOT NULL, service_package_id INT NOT NULL, field_id INT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_1E4EF83D621D924B ON service_package_field (service_package_id)');
        $this->addSql('CREATE TABLE subscription_package_field (id INT NOT NULL, subscription_package_id INT NOT NULL, field_id INT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_A0F4124436A9EB9A ON subscription_package_field (subscription_package_id)');
        $this->addSql('ALTER TABLE service_contract_packages ADD CONSTRAINT FK_6415BE8D2576E0FD FOREIGN KEY (contract_id) REFERENCES service_contracts (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE service_contract_packages ADD CONSTRAINT FK_6415BE8DF44CABFF FOREIGN KEY (package_id) REFERENCES package (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE service_package_field ADD CONSTRAINT FK_1E4EF83D621D924B FOREIGN KEY (service_package_id) REFERENCES service_contract_packages (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE subscription_package_field ADD CONSTRAINT FK_A0F4124436A9EB9A FOREIGN KEY (subscription_package_id) REFERENCES subscription_package (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('DROP TABLE service_contracts_packages');
        $this->addSql('DROP TABLE package_field');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE service_package_field DROP CONSTRAINT FK_1E4EF83D621D924B');
        $this->addSql('DROP SEQUENCE service_contract_packages_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE service_package_field_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE subscription_package_field_id_seq CASCADE');
        $this->addSql('CREATE SEQUENCE package_field_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE service_contracts_packages (contract_id INT NOT NULL, package_id INT NOT NULL, PRIMARY KEY(contract_id, package_id))');
        $this->addSql('CREATE INDEX idx_3b500f31f44cabff ON service_contracts_packages (package_id)');
        $this->addSql('CREATE INDEX idx_3b500f312576e0fd ON service_contracts_packages (contract_id)');
        $this->addSql('CREATE TABLE package_field (id INT NOT NULL, package_id INT NOT NULL, field_id INT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_cef142bff44cabff ON package_field (package_id)');
        $this->addSql('ALTER TABLE service_contracts_packages ADD CONSTRAINT fk_3b500f312576e0fd FOREIGN KEY (contract_id) REFERENCES service_contracts (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE service_contracts_packages ADD CONSTRAINT fk_3b500f31f44cabff FOREIGN KEY (package_id) REFERENCES package (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE package_field ADD CONSTRAINT fk_cef142bff44cabff FOREIGN KEY (package_id) REFERENCES package (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('DROP TABLE service_contract_packages');
        $this->addSql('DROP TABLE service_package_field');
        $this->addSql('DROP TABLE subscription_package_field');
    }
}
