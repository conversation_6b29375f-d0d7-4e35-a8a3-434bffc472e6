<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240206123227 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Makes main navigation URLs relative.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Payments.Home',no_data_url='client/no-subscriptions' WHERE id=16;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Agreements.Home',no_data_url='client/no-subscriptions' WHERE id=14;");
        $this->addSql("UPDATE public.main_navigation SET url='client/fields',no_data_url='client/no-subscriptions' WHERE id=1;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Reports.Home',no_data_url='client/no-subscriptions' WHERE id=20;");
        $this->addSql("UPDATE public.main_navigation SET url='client/administrative-map',no_data_url='client/no-subscriptions' WHERE id=2;");
        $this->addSql("UPDATE public.main_navigation SET url='client/machines',no_data_url='client/no-subscriptions' WHERE id=3;");
        $this->addSql("UPDATE public.main_navigation SET url='client/irrigation-monitoring',no_data_url='client/no-subscriptions' WHERE id=4;");
        $this->addSql("UPDATE public.main_navigation SET url='client/weather-stations/stations',no_data_url='client/no-subscriptions' WHERE id=5;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Owners.Home',no_data_url='client/no-subscriptions' WHERE id=21;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Plots.Home',no_data_url='client/no-subscriptions' WHERE id=7;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=SalesContracts.Home',no_data_url='client/no-subscriptions' WHERE id=9;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Hypothecs.Home',no_data_url='client/no-subscriptions' WHERE id=10;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Contracts.Home',no_data_url='client/no-subscriptions' WHERE id=11;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Annexes.Home',no_data_url='client/no-subscriptions' WHERE id=13;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=OwnerPayments.Home',no_data_url='client/no-subscriptions' WHERE id=17;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Payroll.Home',no_data_url='client/no-subscriptions' WHERE id=18;");
        $this->addSql("UPDATE public.main_navigation SET url='client/reports',no_data_url='client/no-subscriptions' WHERE id=22;");
        $this->addSql("UPDATE public.main_navigation SET url='admin/tasks',no_data_url='client/no-subscriptions' WHERE id=23;");
        $this->addSql("UPDATE public.main_navigation SET no_data_url='client/no-subscriptions' WHERE id=24;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Coverage.Home',no_data_url='client/no-subscriptions' WHERE id=29;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Warehouse.Home',no_data_url='client/no-subscriptions' WHERE id=44;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=HomeTrack',no_data_url='client/no-subscriptions' WHERE id=31;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Navigation.Home',no_data_url='client/no-subscriptions' WHERE id=32;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Cooperators.Home',no_data_url='client/no-subscriptions' WHERE id=34;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Dividends.Home',no_data_url='client/no-subscriptions' WHERE id=35;");
        $this->addSql("UPDATE public.main_navigation SET no_data_url='client/no-subscriptions' WHERE id=36;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=WarehouseAddTransaction.Home',no_data_url='client/no-subscriptions' WHERE id=46;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=WarehouseAddProductionTransaction.Home',no_data_url='client/no-subscriptions' WHERE id=47;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=WarehouseReturnTransaction.Home',no_data_url='client/no-subscriptions' WHERE id=48;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=WarehouseSubContragentsTransaction.Home',no_data_url='client/no-subscriptions' WHERE id=50;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=WarehouseSubPlotsTransaction.Home',no_data_url='client/no-subscriptions' WHERE id=51;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=WarehouseSubMachinesTransaction.Home',no_data_url='client/no-subscriptions' WHERE id=52;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Farming.Home',no_data_url='client/no-subscriptions' WHERE id=55;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=WarehouseReports.Home',no_data_url='client/no-subscriptions' WHERE id=54;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=WarehouseTransferTransaction.Home',no_data_url='client/no-subscriptions' WHERE id=53;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Subleases.Home',no_data_url='client/no-subscriptions' WHERE id=12;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Collections.Home',no_data_url='client/no-subscriptions' WHERE id=19;");
        $this->addSql("UPDATE public.main_navigation SET url='tf/index.php?page=Diary.Home',no_data_url='client/no-subscriptions' WHERE id=30;");
    }

    public function down(Schema $schema): void
    {
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Payments.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=16;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Agreements.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=14;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/client/fields',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=1;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Reports.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=20;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/client/administrative-map',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=2;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/client/machines',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=3;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/client/irrigation-monitoring',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=4;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/client/weather-stations/stations',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=5;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Owners.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=21;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Plots.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=7;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=SalesContracts.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=9;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Hypothecs.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=10;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Contracts.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=11;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Annexes.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=13;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=OwnerPayments.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=17;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Payroll.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=18;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/client/reports',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=22;");
        $this->addSql("UPDATE public.main_navigation SET url='admin/tasks',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=23;");
        $this->addSql("UPDATE public.main_navigation SET no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=24;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Coverage.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=29;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Warehouse.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=44;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=HomeTrack',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=31;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Navigation.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=32;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Cooperators.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=34;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Dividends.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=35;");
        $this->addSql("UPDATE public.main_navigation SET no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=36;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=WarehouseAddTransaction.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=46;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=WarehouseAddProductionTransaction.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=47;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=WarehouseReturnTransaction.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=48;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=WarehouseSubContragentsTransaction.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=50;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=WarehouseSubPlotsTransaction.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=51;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=WarehouseSubMachinesTransaction.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=52;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Farming.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=55;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=WarehouseReports.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=54;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=WarehouseTransferTransaction.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=53;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Subleases.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=12;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Collections.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=19;");
        $this->addSql("UPDATE public.main_navigation SET url='https://app.geoscan.info/tf/index.php?page=Diary.Home',no_data_url='https://app.geoscan.info/client/no-subscriptions' WHERE id=30;");
    }
}
