<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191129165315 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("CREATE TYPE elements_enum AS ENUM (
        'pH',
        'NO3-N',
        'NH4-N',
        'TMN',
        'P2O5',
        'K2O',
        'Ca',
        'Mg',
        'Na',
        'S',
        'Cu',
        'Mn',
        'Zn',
        'B',
        'Ctotal',
        'Corg',
        'OM',
        'C<PERSON>rg',
        'TCarbonates',
        'Acarbonates',
        'EC',
        'LeafN',
        'LeafP',
        'LeafK',
        'LeafCa',
        'LeafMg',
        'LeafNa',
        'LeafS',
        'LeafFe',
        'LeafCu',
        'LeafZn',
        'LeafMn',
        'LeafB'
        )");

        $this->addSql('CREATE SEQUENCE lab_elements_results_raw_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE lab_elements_results_raw (id INT NOT NULL, lab_analisys_uploads_id INT NOT NULL, lab_number VARCHAR(27) NOT NULL, element elements_enum, value DOUBLE PRECISION NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_D0BE8251542FC42F ON lab_elements_results_raw (lab_analisys_uploads_id)');
        $this->addSql('ALTER TABLE lab_elements_results_raw ADD CONSTRAINT FK_D0BE8251542FC42F FOREIGN KEY (lab_analisys_uploads_id) REFERENCES lab_analysis_uploads (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE lab_analysis_uploads ALTER state SET DEFAULT \'Success\'');
        $this->addSql('ALTER TABLE lab_analysis_uploads ALTER state SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP SEQUENCE lab_elements_results_raw_id_seq CASCADE');
        $this->addSql('DROP TABLE lab_elements_results_raw');
        $this->addSql('ALTER TABLE lab_analysis_uploads ALTER state DROP DEFAULT');
        $this->addSql('ALTER TABLE lab_analysis_uploads ALTER state DROP NOT NULL');
    }
}
