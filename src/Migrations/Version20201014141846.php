<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20201014141846 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function isTransactional(): bool
    {
        return false;
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("ALTER TYPE element_group_states_enum ADD VALUE 'For final approve'");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("UPDATE lab_element_group SET state = 'Delivered' WHERE state = 'For final approve'");
        $this->addSql('ALTER TYPE element_group_states_enum RENAME TO element_group_states_enum_old;');
        $this->addSql("CREATE TYPE element_group_states_enum AS ENUM ('Pending', 'In progress', 'For reanalysis', 'For approve', 'For recommendation', 'Delivered')");
        $this->addSql('ALTER TABLE lab_element_group ALTER COLUMN state TYPE element_group_states_enum USING status::text::element_group_states_enum');
        $this->addSql('DROP TYPE element_group_states_enum_old');
    }
}
