<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210706113301 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create postgresql function calculate_recommendation_due_date(received_in_lab_date DATE, due_date_period INT)';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("
            CREATE FUNCTION calculate_recommendation_due_date(plt_uuid VARCHAR, pckg_id INT, pckg_type VARCHAR, due_date_period INT)
            RETURNS DATE
            AS $$
                BEGIN
                    RETURN (
                        WITH pgp_data AS (
                            SELECT
                                max(pgp.state_updated_at) AS received_in_lab_date,
                                array_agg(pgp.state)::point_states_enum[] AS states
                            FROM
                                package_grid_points AS pgp
                            WHERE
                                pgp.package_id = pckg_id
                                AND pgp.package_type = pckg_type
                                AND pgp.plot_uuid = plt_uuid
                        ),
                        holydays_in_period AS (
                            SELECT
                                due_date_days.\"date\",
                                pgp.received_in_lab_date,
                                pgp.states
                            FROM 
                                pgp_data AS pgp,
                                generate_series(pgp.received_in_lab_date, pgp.received_in_lab_date + (due_date_period || ' days')::interval, '1 day'::interval) AS due_date_days(\"date\"),
                                get_holydays(EXTRACT (YEAR FROM pgp.received_in_lab_date)::int) AS holydays
                            WHERE due_date_days.\"date\"::date = ANY(holydays.dts) -- check which days of the period are holydays
                        )
                        SELECT
                            CASE WHEN 'ReceivedInLab'::point_states_enum <> ANY(holydays_in_period.states)
                                THEN NULL
                                ELSE holydays_in_period.received_in_lab_date + ((due_date_period + count(holydays_in_period.\"date\"))::text || ' days')::interval
                            END AS \"date\"
                        FROM holydays_in_period
                        GROUP BY
                            holydays_in_period.received_in_lab_date,
                            holydays_in_period.states
                    );
                END
            $$ LANGUAGE plpgsql
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP FUNCTION IF EXISTS calculate_recommendation_due_date(plt_uuid VARCHAR, pckg_id INT, pckg_type VARCHAR, due_date_period INT)');
    }
}
