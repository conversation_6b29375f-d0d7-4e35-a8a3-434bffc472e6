<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221110132806 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add column \'style\' to table package';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE package ADD style jsonb DEFAULT \'{"color": "#000000", "backgroundColor": "#fafafa"}\' NOT NULL');
        $this->addSql('COMMENT ON COLUMN package.style IS \'(DC2Type:jsonb)\'');

        $this->addSql('WITH package_style AS
            (
                SELECT
                    slug_short,
                    "style"::jsonb
                FROM
                    (
                    VALUES
                    (
                        \'ISO\',
                        \'{"color": "#ffffff", "backgroundColor": "#0f572f96"}\'
                    ),
                    (
                        \'ISO-C\',
                        \'{"color": "#ffffff", "backgroundColor": "#0f572f96"}\'
                    ),
                    (
                        \'VRA\',
                        \'{"color": "#ffffff", "backgroundColor": "#f1b73a"}\'
                    ),
                    (
                        \'VRA-C\',
                        \'{"color": "#ffffff", "backgroundColor": "#f1b73a"}\'
                    ),
                    (
                        \'VRA-N\',
                        \'{"color": "#ffffff", "backgroundColor": "#008a5d"}\'
                    ),
                    (
                        \'SAT\',
                        \'{"color": "#ffffff", "backgroundColor": "#0083cb"}\'
                    ),
                    (
                        \'WD\',
                        \'{"color": "#ffffff", "backgroundColor": "#008080"}\'
                    ),
                    (
                        \'LS\',
                        \'{"color": "#ffffff", "backgroundColor": "#005400"}\'
                    ),
                    (
                        \'OM\',
                        \'{"color": "#ffffff", "backgroundColor": "#976f48"}\'
                    ),
                    (
                        \'TFC\',
                        \'{"color": "#ffffff", "backgroundColor": "#a0acbd"}\'
                    ),
                    (
                        \'WS\',
                        \'{"color": "#ffffff", "backgroundColor": "#0f56ee"}\'
                    ),
                    (
                        \'FT\',
                        \'{"color": "#ffffff", "backgroundColor": "#b62822"}\'
                    ),
                    (
                        \'IM\',
                        \'{"color": "#ffffff", "backgroundColor": "#00b6cb"}\'
                    ),
                    (
                        \'Light\',
                        \'{"color": "#ffffff", "backgroundColor": "#7f7b82"}\'
                    )
                    ) AS style_map (
                        slug_short,
                        "style"
                    )
            )
            UPDATE
                package
            SET
                "style" = package_style."style"
            FROM
                package_style
            WHERE
                package.slug_short = package_style.slug_short
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE package DROP style');
    }
}
