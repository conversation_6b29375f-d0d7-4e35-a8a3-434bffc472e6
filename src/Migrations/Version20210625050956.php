<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210625050956 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove column calc_model_entity_class from table recommendation_calc_model_config and add columns element_id, params.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE recommendation_calc_model_config ADD element_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE recommendation_calc_model_config ADD params recommendation_crop_model_parameter_enum[]');
        $this->addSql('ALTER TABLE recommendation_calc_model_config DROP calc_model_entity_class');
        $this->addSql('COMMENT ON COLUMN recommendation_calc_model_config.params IS \'(DC2Type:string[])\'');
        $this->addSql('ALTER TABLE recommendation_calc_model_config ADD CONSTRAINT FK_282DFB371F1F2A24 FOREIGN KEY (element_id) REFERENCES lab_analysis_group_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_282DFB371F1F2A24 ON recommendation_calc_model_config (element_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE recommendation_calc_model_config DROP CONSTRAINT FK_282DFB371F1F2A24');
        $this->addSql('DROP INDEX IDX_282DFB371F1F2A24');
        $this->addSql('ALTER TABLE recommendation_calc_model_config ADD calc_model_entity_class VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE recommendation_calc_model_config DROP element_id');
        $this->addSql('ALTER TABLE recommendation_calc_model_config DROP params');
    }
}
