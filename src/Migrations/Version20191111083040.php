<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191111083040 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE subscription_package ALTER status SET NOT NULL');
        $this->addSql('ALTER TABLE subscription_package ALTER state SET NOT NULL');
        $this->addSql('ALTER TABLE contract ALTER status SET NOT NULL');
        $this->addSql('ALTER TABLE service_contract_packages ALTER status SET NOT NULL');
        $this->addSql('ALTER TABLE service_contract_packages ALTER state SET NOT NULL');
        $this->addSql('ALTER TABLE subscription_package_field ALTER field_state SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE contract ALTER status DROP NOT NULL');
        $this->addSql('ALTER TABLE service_contract_packages ALTER status DROP NOT NULL');
        $this->addSql('ALTER TABLE service_contract_packages ALTER state DROP NOT NULL');
        $this->addSql('ALTER TABLE subscription_package ALTER status DROP NOT NULL');
        $this->addSql('ALTER TABLE subscription_package ALTER state DROP NOT NULL');
        $this->addSql('ALTER TABLE subscription_package_field ALTER field_state DROP NOT NULL');
    }
}
