<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200821115849 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
        $this->addSql("CREATE TYPE recommendation_comment_type_enum AS ENUM (
        'lab-results',
        'recommendation-data'
        )");

        $this->addSql("CREATE TYPE recommendation_data_result_type_enum AS ENUM (
        'fertilizer-rate',
        'leaf-application'
        )");

        $this->addSql('CREATE SEQUENCE recommendation_lab_element_group_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE recommendation_comment_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE recommendations_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE recommendation_crops_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE recommendation_lab_element_group (id INT NOT NULL, recommendation_id INT NOT NULL, lab_element_group_id INT NOT NULL, due_date DATE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_862B5C18D173940B ON recommendation_lab_element_group (recommendation_id)');
        $this->addSql('CREATE INDEX IDX_862B5C185F1DCC2F ON recommendation_lab_element_group (lab_element_group_id)');
        $this->addSql('CREATE TABLE recommendation_comment (id INT NOT NULL, recommendation_id INT NOT NULL, lab_element_group_id INT NOT NULL, type recommendation_comment_type_enum, content TEXT DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_C9175BC0D173940B ON recommendation_comment (recommendation_id)');
        $this->addSql('CREATE INDEX IDX_C9175BC05F1DCC2F ON recommendation_comment (lab_element_group_id)');
        $this->addSql('CREATE TABLE recommendations (id INT NOT NULL, contract_id INT NOT NULL, package_id INT NOT NULL, plot_name VARCHAR(255) NOT NULL, humus DOUBLE PRECISION DEFAULT NULL, density DOUBLE PRECISION DEFAULT NULL, absorption DOUBLE PRECISION DEFAULT NULL, date_created DATE NOT NULL, package_type VARCHAR(63) NOT NULL, farming_year VARCHAR(63) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_73904ED72576E0FD ON recommendations (contract_id)');
        $this->addSql('CREATE INDEX IDX_73904ED7F44CABFF ON recommendations (package_id)');
        $this->addSql('CREATE TABLE recommendation_crops (id INT NOT NULL, recommendation_id INT NOT NULL, crop_id INT NOT NULL, yield DOUBLE PRECISION NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_B54F9C35D173940B ON recommendation_crops (recommendation_id)');
        $this->addSql('ALTER TABLE recommendation_lab_element_group ADD CONSTRAINT FK_862B5C18D173940B FOREIGN KEY (recommendation_id) REFERENCES recommendations (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE recommendation_lab_element_group ADD CONSTRAINT FK_862B5C185F1DCC2F FOREIGN KEY (lab_element_group_id) REFERENCES lab_element_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE recommendation_comment ADD CONSTRAINT FK_C9175BC0D173940B FOREIGN KEY (recommendation_id) REFERENCES recommendations (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE recommendation_comment ADD CONSTRAINT FK_C9175BC05F1DCC2F FOREIGN KEY (lab_element_group_id) REFERENCES lab_element_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE recommendations ADD CONSTRAINT FK_73904ED72576E0FD FOREIGN KEY (contract_id) REFERENCES contract (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE recommendations ADD CONSTRAINT FK_73904ED7F44CABFF FOREIGN KEY (package_id) REFERENCES subscription_package (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE recommendation_crops ADD CONSTRAINT FK_B54F9C35D173940B FOREIGN KEY (recommendation_id) REFERENCES recommendations (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE recommendation_lab_element_group DROP CONSTRAINT FK_862B5C18D173940B');
        $this->addSql('ALTER TABLE recommendation_comment DROP CONSTRAINT FK_C9175BC0D173940B');
        $this->addSql('ALTER TABLE recommendation_crops DROP CONSTRAINT FK_B54F9C35D173940B');
        $this->addSql('DROP SEQUENCE recommendation_lab_element_group_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE recommendation_comment_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE recommendations_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE recommendation_crops_id_seq CASCADE');
        $this->addSql('DROP TABLE recommendation_lab_element_group');
        $this->addSql('DROP TABLE recommendation_comment');
        $this->addSql('DROP TABLE recommendations');
        $this->addSql('DROP TABLE recommendation_crops');

        $this->addSql('DROP TYPE IF EXISTS recommendation_comment_type_enum');
        $this->addSql('DROP TYPE IF EXISTS recommendation_data_result_type_enum');
    }
}
