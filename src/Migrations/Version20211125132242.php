<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20211125132242 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Set package slug_short for AB ISO control and AB VRA control packages.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("UPDATE package SET slug_short = 'ISO-C' WHERE slug = 'AB ISO control'");
        $this->addSql("UPDATE package SET slug_short = 'VRA-C' WHERE slug = 'AB VRA control'");
    }

    public function down(Schema $schema): void
    {
        $this->addSql("UPDATE package SET slug_short = NULL WHERE slug = 'AB ISO control'");
        $this->addSql("UPDATE package SET slug_short = NULL WHERE slug = 'AB VRA control'");
    }
}
