<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230830090227 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add column decline_reason to recommendations table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE recommendations ADD COLUMN decline_reason TEXT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE recommendations DROP COLUMN decline_reason;');
    }
}
