<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210609143217 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create table lab_aggregated_element_interpetations_config';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE TABLE lab_aggregated_element_interpetations_config (id SERIAL, element_id INT NOT NULL, service_provider_id INT NOT NULL, class_ids integer[] DEFAULT NULL, comment_text TEXT DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_6E577C171F1F2A24 ON lab_aggregated_element_interpetations_config (element_id)');
        $this->addSql('CREATE INDEX IDX_6E577C17C6C98E06 ON lab_aggregated_element_interpetations_config (service_provider_id)');
        $this->addSql('COMMENT ON COLUMN lab_aggregated_element_interpetations_config.class_ids IS \'(DC2Type:integer[])\'');
        $this->addSql('ALTER TABLE lab_aggregated_element_interpetations_config ADD CONSTRAINT FK_6E577C171F1F2A24 FOREIGN KEY (element_id) REFERENCES lab_analysis_group_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE lab_aggregated_element_interpetations_config ADD CONSTRAINT FK_6E577C17C6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP TABLE lab_aggregated_element_interpetations_config');
    }
}
