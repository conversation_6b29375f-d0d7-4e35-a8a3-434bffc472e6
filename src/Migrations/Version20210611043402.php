<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210611043402 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create table recommendation_fertiliser_comments_ph_config.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE TABLE recommendation_fertiliser_comments_ph_config (id SERIAL, model_id INT NOT NULL, fertiliser_type VARCHAR(255) DEFAULT NULL, result_element result_element_enum NOT NULL, range numrange NOT NULL, comment_text TEXT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_99262DF57975B7E7 ON recommendation_fertiliser_comments_ph_config (model_id)');
        $this->addSql('COMMENT ON COLUMN recommendation_fertiliser_comments_ph_config.range IS \'(DC2Type:numrange)\'');
        $this->addSql('ALTER TABLE recommendation_fertiliser_comments_ph_config ADD CONSTRAINT FK_99262DF57975B7E7 FOREIGN KEY (model_id) REFERENCES recommendation_models_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP TABLE recommendation_fertiliser_comments_ph_config');
    }
}
