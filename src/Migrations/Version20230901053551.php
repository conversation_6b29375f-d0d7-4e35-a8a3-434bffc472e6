<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230901053551 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create schema \'loggable\'';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE SCHEMA loggable');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP SCHEMA loggable');
    }
}
