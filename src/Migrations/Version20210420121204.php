<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210420121204 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Rename ABSoilCtCoOM lab_analysis_group to ABSoilOM and clear elements Ctotal and Corg';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('UPDATE lab_analysis_group SET name = \'ABSoilOM\' WHERE name = \'ABSoilCtCoOM\'');

        $ABSoilCtCoOM = $this->getLabAnalysisGroupId('ABSoilCtCoOM');
        $this->addSql("DELETE FROM lab_analysis_group_element 
                            where lab_analysis_group_id = :ABSoilCtCoOM 
                            and (element = 'Ctotal' or element = 'Corg')
                            ", ['ABSoilCtCoOM' => $ABSoilCtCoOM]);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('UPDATE lab_analysis_group SET name = \'ABSoilCtCoOM\' WHERE name = \'ABSoilOM\'');

        $ABSoilOM = $this->getLabAnalysisGroupId('ABSoilOM');
        $this->addSql(
            "INSERT INTO lab_analysis_group_element (lab_analysis_group_id, element) VALUES
            (:ABSoilOM,'Ctotal'),
            (:ABSoilOM,'Corg')
        ",
            ['ABSoilOM' => (int)$ABSoilOM]
        );
    }
}
