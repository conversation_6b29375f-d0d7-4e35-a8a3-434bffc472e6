<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190523170533 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf(
            'postgresql' !== $this->connection->getDatabasePlatform()->getName(),
            'Migration can only be executed safely on \'postgresql\'.'
        );

        $this->addSql('CREATE TABLE service_contracts_services (contract_id INT NOT NULL, service_id INT NOT NULL, PRIMARY KEY(contract_id, service_id))');
        $this->addSql('CREATE INDEX IDX_D3D72EFF2576E0FD ON service_contracts_services (contract_id)');
        $this->addSql('CREATE INDEX IDX_D3D72EFFED5CA9E6 ON service_contracts_services (service_id)');
        $this->addSql('ALTER TABLE service_contracts_services ADD CONSTRAINT FK_D3D72EFF2576E0FD FOREIGN KEY (contract_id) REFERENCES service_contracts (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE service_contracts_services ADD CONSTRAINT FK_D3D72EFFED5CA9E6 FOREIGN KEY (service_id) REFERENCES service (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf(
            'postgresql' !== $this->connection->getDatabasePlatform()->getName(),
            'Migration can only be executed safely on \'postgresql\'.'
        );

        $this->addSql('CREATE SCHEMA public');
        $this->addSql('DROP TABLE service_contracts_services');
    }
}
