<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210610110733 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create enum result_element_enum';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("CREATE TYPE result_element_enum AS ENUM (
            'Need_N_total',
            'Need_P_total',
            'Need_K_total',
            'Need_S_total',
            'Add_N_total',
            'Add_P_total',
            'Add_K_total',
            'Add_S_total',
            'Add_N_fall',
            'pH_predominant',
            'Add_B',
            'Add_Mn',
            'Add_Cu',
            'Add_Zn',
            'Add_Mo',
            'Add_Fe'
        )");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TYPE IF EXISTS result_element_enum');
    }
}
