<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230818130336 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update db function get_recommendation_results - join element groups by service_provider ';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            DROP FUNCTION get_recommendation_results (
                recommendation INTEGER
            )
        ');

        $this->addSql("
            CREATE OR REPLACE FUNCTION get_recommendation_results (
                recommendation INTEGER,
                service_provider INTEGER
            )
                RETURNS TABLE (\"comments\" JSONB, results JSONB, susces JSONB)
                AS $$
                    BEGIN
                    RETURN QUERY
                    SELECT
                    JSONB_AGG(DISTINCT
                        JSONB_BUILD_OBJECT(
                            'id', rc.id,
                            'result_element', rc.param,
                            'comment_text', rc.value
                        )
                    ) FILTER (WHERE rc.value NOTNULL)  AS \"comments\",
                    JSONB_AGG(DISTINCT
                        JSONB_BUILD_OBJECT(
                            'id', rer.id,
                            'element', res.\"element\",
                            'result_element', rer.result_element,
                            'result_element_value', round(rer.value::NUMERIC, 3)
                        )
                    ) FILTER (WHERE rer.value NOTNULL AND rcmc.result_element = rer.result_element) AS results,
                    JSONB_AGG(DISTINCT
                        JSONB_BUILD_OBJECT(
                            'element', res.\"element\",
                            'value', res.value
                        )
                    ) AS susces
                    FROM
                        recommendations r
                    JOIN recommendation_comment rc
                        ON rc.recommendation_id = r.id
                    JOIN recommendation_elements_results rer
                        ON rer.recommendation_id = r.id
                    JOIN recommendation_elements_susces res
                        ON res.recommendation_id = r.id
                    JOIN lab_analysis_group_element lage
                        ON lage.\"element\" = res.\"element\"
                    JOIN lab_analysis_group_element_visual_order lagevo
                        ON lagevo.lab_analysis_group_element_id = lage.id
                        AND lagevo.service_provider_id = service_provider
                    JOIN recommendation_calc_model_config rcmc
                        ON rcmc.element_id = lage.id
                    WHERE r.id = recommendation;
                END
            $$ LANGUAGE plpgsql
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DROP FUNCTION get_recommendation_results (
                recommendation INTEGER,
                service_provider INTEGER
            )
        ');

        $this->addSql("
            CREATE OR REPLACE FUNCTION get_recommendation_results (
                recommendation INTEGER
            )
                RETURNS TABLE (\"comments\" JSONB, results JSONB, susces JSONB)
                AS $$
                    BEGIN
                    RETURN QUERY
                    SELECT
                    JSONB_AGG(DISTINCT
                        JSONB_BUILD_OBJECT(
                            'id', rc.id,
                            'result_element', rc.param,
                            'comment_text', rc.value
                        )
                    ) FILTER (WHERE rc.value NOTNULL)  AS \"comments\",
                    JSONB_AGG(DISTINCT
                        JSONB_BUILD_OBJECT(
                            'id', rer.id,
                            'element', res.\"element\",
                            'result_element', rer.result_element,
                            'result_element_value', round(rer.value::NUMERIC, 3)
                        )
                    ) FILTER (WHERE rer.value NOTNULL AND rcmc.result_element = rer.result_element) AS results,
                    JSONB_AGG(DISTINCT
                        JSONB_BUILD_OBJECT(
                            'element', res.\"element\",
                            'value', res.value
                        )
                    ) AS susces
                    FROM
                        recommendations r
                    JOIN recommendation_comment rc
                        ON rc.recommendation_id = r.id
                    JOIN recommendation_elements_results rer
                        ON rer.recommendation_id = r.id
                    JOIN recommendation_elements_susces res
                        ON res.recommendation_id = r.id
                    JOIN lab_analysis_group_element lage
                        ON lage.\"element\" = res.\"element\"
                    JOIN recommendation_calc_model_config rcmc
                        ON rcmc.element_id = lage.id
                    WHERE r.id = recommendation;
                END
            $$ LANGUAGE plpgsql
        ");
    }
}
