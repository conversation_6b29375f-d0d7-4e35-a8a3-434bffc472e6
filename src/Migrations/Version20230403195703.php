<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230403195703 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Drop default value for farm_id';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE subscription_package_field ALTER farm_id DROP DEFAULT');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE subscription_package_field ALTER farm_id SET DEFAULT 0');
    }
}
