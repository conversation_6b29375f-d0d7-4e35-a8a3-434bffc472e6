<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190809080548 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SEQUENCE field_states_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE field_states (id INT NOT NULL, service_provider_id INT DEFAULT NULL, slug VARCHAR(31) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_1B86FB4FC6C98E06 ON field_states (service_provider_id)');
        $this->addSql('ALTER TABLE field_states ADD CONSTRAINT FK_1B86FB4FC6C98E06 FOREIGN KEY (service_provider_id) REFERENCES service_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE subscription_package_field ADD field_state_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE subscription_package_field ADD CONSTRAINT FK_A0F41244D3E0BB0C FOREIGN KEY (field_state_id) REFERENCES field_states (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_A0F41244D3E0BB0C ON subscription_package_field (field_state_id)');
        $this->addSql('ALTER TABLE package_states ALTER id DROP DEFAULT');

        // Add field_states data
        $sql = "ALTER TABLE field_states ALTER COLUMN id SET DEFAULT nextval('field_states_id_seq'::regclass)";
        $this->addSql($sql);

        $sql = "INSERT INTO field_states (
            slug,
            service_provider_id
        )
        VALUES
            ('Not approved', 1),
            ('For grid', 1),
            ('Gridding', 1),
            ('Gridded', 1),
            ('For sampling', 1),
            ('Sampling', 1),
            ('Sampled', 1),
            ('Analised', 1),
            ('Recomendation', 1),
            ('For approve', 1),
            ('Approved', 1)
        ";
        $this->addSql($sql);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE subscription_package_field DROP CONSTRAINT FK_A0F41244D3E0BB0C');
        $this->addSql('DROP SEQUENCE field_states_id_seq CASCADE');
        $this->addSql('DROP TABLE field_states');
        $this->addSql('DROP INDEX IDX_A0F41244D3E0BB0C');
        $this->addSql('ALTER TABLE subscription_package_field DROP field_state_id');
        $this->addSql('CREATE SEQUENCE package_states_id_seq');
        $this->addSql('SELECT setval(\'package_states_id_seq\', (SELECT MAX(id) FROM package_states))');
        $this->addSql('ALTER TABLE package_states ALTER id SET DEFAULT nextval(\'package_states_id_seq\')');
    }
}
