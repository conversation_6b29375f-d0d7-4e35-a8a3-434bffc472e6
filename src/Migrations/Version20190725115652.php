<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190725115652 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE service_contract_packages ADD status_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE service_contract_packages ADD CONSTRAINT FK_6415BE8D6BF700BD FOREIGN KEY (status_id) REFERENCES package_statuses (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_6415BE8D6BF700BD ON service_contract_packages (status_id)');
        $this->addSql('ALTER TABLE subscription_package ADD status_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE subscription_package ADD CONSTRAINT FK_AD7D870E6BF700BD FOREIGN KEY (status_id) REFERENCES package_statuses (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_AD7D870E6BF700BD ON subscription_package (status_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE subscription_package DROP CONSTRAINT FK_AD7D870E6BF700BD');
        $this->addSql('DROP INDEX IDX_AD7D870E6BF700BD');
        $this->addSql('ALTER TABLE subscription_package DROP status_id');
        $this->addSql('ALTER TABLE service_contract_packages DROP CONSTRAINT FK_6415BE8D6BF700BD');
        $this->addSql('DROP INDEX IDX_6415BE8D6BF700BD');
        $this->addSql('ALTER TABLE service_contract_packages DROP status_id');
    }
}
