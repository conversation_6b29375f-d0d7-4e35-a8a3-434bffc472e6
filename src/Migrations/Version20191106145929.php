<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20191106145929 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("CREATE TYPE price_status_enum AS ENUM ('None', 'Partial', 'Paid')");

        $this->addSql('ALTER TABLE price ADD status price_status_enum DEFAULT \'None\'');
        $this->addSql('ALTER TABLE price RENAME COLUMN subscription_id TO contract_id');
        $this->addSql('ALTER TABLE price RENAME COLUMN minimum_amount TO amount');
        $this->addSql('ALTER TABLE price ADD CONSTRAINT FK_CAC822D92576E0FD FOREIGN KEY (contract_id) REFERENCES contract (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_CAC822D92576E0FD ON price (contract_id)');

        $this->addSql('ALTER TABLE price ALTER COLUMN period DROP NOT NULL');
        $this->addSql('ALTER TABLE price ALTER COLUMN id SET DEFAULT nextval(\'price_id_seq\'::regclass)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE price RENAME COLUMN contract_id TO subscription_id');
        $this->addSql('ALTER TABLE price RENAME COLUMN amount TO minimum_amount');
    }
}
