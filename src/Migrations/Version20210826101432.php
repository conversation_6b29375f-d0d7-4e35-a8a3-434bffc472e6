<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210826101432 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Delete all records from table recommendation_leaf_fertiliser_comments_config and insert new.';
    }

    public function up(Schema $schema): void
    {
        // Fetch recommendation models configs
        $modelsConfigs = $this->getRecommendationModelsConfigs();

        // Delete all records
        $this->addSql('DELETE FROM recommendation_leaf_fertiliser_comments_config');

        // Insert the new records
        $this->addSql("
            INSERT INTO recommendation_leaf_fertiliser_comments_config (model_id, crop_ids, result_elements, range, comment_text) VALUES
            ({$modelsConfigs['BG']},NULL,ARRAY['Add_B','Add_Mn','Add_Cu','Add_Zn','Add_Mo','Add_Fe', 'Add_N_total', 'Add_P_total', 'Add_K_total', 'Add_S_total', 'Add_N_fall']::result_element_enum[],'[0,)'::NUMRANGE,'Препоръчваме през периода на интензивно вегетативно развитие, но най-късно до фаза „начало на цъфтеж“ да се извършат 1-2 листни третирания на растенията с торови продукти, които съдържат в по-висока концентрация посочените хранителни елементи. Препоръчително е да се следват указанията за дози и фази на приложение, дадени от производителя на избрания продукт.'),
            ({$modelsConfigs['Vantage']},NULL,ARRAY['Add_B','Add_Mn','Add_Cu','Add_Zn','Add_Mo','Add_Fe', 'Add_N_total', 'Add_P_total', 'Add_K_total', 'Add_S_total', 'Add_N_fall']::result_element_enum[],'[0,)'::NUMRANGE,'Se recomandă 1-2 aplicări foliare în timpul creșterii vegetative intensive a culturilor, cel mai târziu înainte de apariția inflorescenței. Este recomandat să utilizați produse cu o concentrație mai mare de nutrienți, așa cum este indicat în planul de fertilizare. Se recomandă respectarea instrucțiunilor de aplicare date de producătorul produsului.'),
            ({$modelsConfigs['Agricost']},NULL,ARRAY['Add_B','Add_Mn','Add_Cu','Add_Zn','Add_Mo','Add_Fe', 'Add_N_total', 'Add_P_total', 'Add_K_total', 'Add_S_total', 'Add_N_fall']::result_element_enum[],'[0,)'::NUMRANGE,'Se recomandă 1-2 aplicări foliare în timpul creșterii vegetative intensive a culturilor, cel mai târziu înainte de apariția inflorescenței. Este recomandat să utilizați produse cu o concentrație mai mare de nutrienți, așa cum este indicat în planul de fertilizare. Se recomandă respectarea instrucțiunilor de aplicare date de producătorul produsului.'),
            ({$modelsConfigs['Spectr Аgro']},NULL,ARRAY['Add_B','Add_Mn','Add_Cu','Add_Zn','Add_Mo','Add_Fe', 'Add_N_total', 'Add_P_total', 'Add_K_total', 'Add_S_total', 'Add_N_fall']::result_element_enum[],'[0,)'::NUMRANGE,'1-2 позакореневих підживлення рекомендовані впродовж інтенсивної вегетації, найпізніше - перед початком цвітіння. Рекомендовано використовувати препарати із високим вмістом поживних елементів, які були вказані в рекомендаціях. При використанні препаратів дотримуйтесь інструкції виробника.'),
            ({$modelsConfigs['NIK Italia']},NULL,ARRAY['Add_B','Add_Mn','Add_Cu','Add_Zn','Add_Mo','Add_Fe', 'Add_N_total', 'Add_P_total', 'Add_K_total', 'Add_S_total', 'Add_N_fall']::result_element_enum[],'[0,)'::NUMRANGE,'1-2 applicazioni fogliari sono raccomandabili durante la fase intensiva di crescita vegetativa delle colture, con l''ultima applicata prima dell''inizio della fioritura. È suggerito l''utilizzo di prodotti con un’alta concentrazione dei nutrienti indicati nel piano di concimazione. Si raccomanda di seguire le istruzioni per l''applicazione fornite nell’etichetta del prodotto.')
        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
