<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220414075019 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Fix problem with fields wrong state "For sampling" and "Sampling" where package grid point state is "ReceivedInLab"';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql("
            update
                subscription_package_field
            set
                field_state = 'Sampled'
            from
                (
                select
                    distinct spf.id
                from
                    subscription_package_field spf
                join package_grid_points pgp on
                    pgp.package_id = spf.subscription_package_id
                    and pgp.plot_uuid = spf.plot_uuid
                where
                    pgp.state = 'ReceivedInLab'
                    and spf.field_state in ('For sampling', 'Sampling')) as sffs
            where
                subscription_package_field.id = sffs.id
        ");
    }

    public function down(Schema $schema): void
    {
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
    }
}
