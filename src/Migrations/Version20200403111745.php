<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200403111745 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Seed table meta_elements_groups';
    }

    public function up(Schema $schema): void
    {
        $this->connection->query('INSERT INTO meta_groups (name) VALUES (\'Carbonates\'), (\'EC\'), (\'Plant tissue\')');

        $metaGroups = $this->getMetaGroups();

        $this->addSql('DELETE FROM meta_elements_groups');

        $this->addSql('INSERT INTO meta_elements_groups (group_id, element) VALUES
            (:macId, \'pH\'),
            (:macId, \'NO3-N\'),
            (:macId, \'NH4-N\'),
            (:macId, \'TMN\'),
            (:macId, \'P2O5\'),
            (:macId, \'K2O\'),
            (:macId, \'CaO\'),
            (:macId, \'MgO\'),
            (:macId, \'Na2O\'),
            (:macId, \'S\'),
            (:micId, \'Cu\'),
            (:micId, \'Mn\'),
            (:micId, \'Zn\'),
            (:micId, \'B\'),
            (:cbId, \'Ctotal\'),
            (:cbId, \'Corg\'),
            (:cbId, \'OM\'),
            (:cbId, \'Cinorg\'),
            (:cbId, \'TCarbonates\'),
            (:cbId, \'Acarbonates\'),
            (:ecId, \'EC\'),
            (:ptId, \'LeafN\'),
            (:ptId, \'LeafP\'),
            (:ptId, \'LeafK\'),
            (:ptId, \'LeafCa\'),
            (:ptId, \'LeafMg\'),
            (:ptId, \'LeafNa\'),
            (:ptId, \'LeafS\'),
            (:ptId, \'LeafFe\'),
            (:ptId, \'LeafCu\'),
            (:ptId, \'LeafZn\'),
            (:ptId, \'LeafMn\'),
            (:ptId, \'LeafB\')
        ', [
            'macId' => $metaGroups['Macro-elements'],
            'micId' => $metaGroups['Micro-elements'],
            'cbId' => $metaGroups['Carbonates'],
            'ecId' => $metaGroups['EC'],
            'ptId' => $metaGroups['Plant tissue'],
        ]);
    }

    public function down(Schema $schema): void
    {
        $this->connection->query('DELETE FROM meta_elements_groups');
        $this->connection->query('DELETE FROM meta_groups');
        $this->connection->query('INSERT INTO meta_groups (name) VALUES (\'Macro-elements\'), (\'Micro-elements\')');

        $metaGroups = $this->getMetaGroups();

        $this->addSql('INSERT INTO meta_elements_groups (group_id, element) VALUES
            (:macId, \'pH\'),
            (:macId, \'NO3-N\'),
            (:macId, \'NH4-N\'),
            (:macId, \'P2O5\'),
            (:macId, \'K2O\'),
            (:macId, \'S\'),
            (:macId, \'CaO\'),
            (:macId, \'MgO\'),
            (:macId, \'Na2O\'),
            (:micId, \'Cl\'),
            (:micId, \'B\'),
            (:micId, \'Cu\'),
            (:micId, \'Fe\'),
            (:micId, \'Mn\'),
            (:micId, \'Zn\'),
            (:micId, \'Mo\')
            
        ', [
            'macId' => $metaGroups['Macro-elements'],
            'micId' => $metaGroups['Micro-elements'],
        ]);
    }
}
