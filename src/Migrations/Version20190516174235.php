<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190516174235 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf(
            'postgresql' !== $this->connection->getDatabasePlatform()->getName(),
            'Migration can only be executed safely on \'postgresql\'.'
        );

        $this->addSql('CREATE SEQUENCE subscription_package_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE subscription_package (id INT NOT NULL, contract_id INT NOT NULL, package_id INT NOT NULL, period INT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_AD7D870E2576E0FD ON subscription_package (contract_id)');
        $this->addSql('CREATE INDEX IDX_AD7D870EF44CABFF ON subscription_package (package_id)');
        $this->addSql('ALTER TABLE subscription_package ADD CONSTRAINT FK_AD7D870E2576E0FD FOREIGN KEY (contract_id) REFERENCES subscription_contracts (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE subscription_package ADD CONSTRAINT FK_AD7D870EF44CABFF FOREIGN KEY (package_id) REFERENCES package (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf(
            'postgresql' !== $this->connection->getDatabasePlatform()->getName(),
            'Migration can only be executed safely on \'postgresql\'.'
        );

        $this->addSql('CREATE SCHEMA public');
        $this->addSql('DROP SEQUENCE subscription_package_id_seq CASCADE');
        $this->addSql('DROP TABLE subscription_package');
    }
}
