<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210622104552 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create postgresql function add_k_total.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE FUNCTION add_k_total (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_k NUMERIC,
                vol_density NUMERIC,
                soil_abs_k NUMERIC,
                fertiliser_abs_k NUMERIC
            )
                RETURNS NUMERIC
                AS $$
                    BEGIN
                    RETURN (
                        ((yield * uptake_k / 100) - (avg_element_result * vol_density * soil_abs_k)) / fertiliser_abs_k
                    )::NUMERIC;
                    END
                $$ LANGUAGE plpgsql
       ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DROP FUNCTION IF EXISTS add_k_total (
                yield NUMERIC,
                avg_element_result NUMERIC,
                uptake_k NUMERIC,
                vol_density NUMERIC,
                soil_abs_k NUMERIC,
                fertiliser_abs_k NUMERIC
            )
        ');
    }
}
