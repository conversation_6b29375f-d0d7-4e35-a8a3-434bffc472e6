<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210610121456 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Seed table recommendation_models_config.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $serviceProviderId = $this->getServiceProviderId('nikas');
        $this->addSql("INSERT INTO recommendation_models_config (name, service_provider_id, calc_model_id) VALUES ('BG', {$serviceProviderId}, 1)");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $serviceProviderId = $this->getServiceProviderId('nikas');
        $this->addSql("DELETE FROM recommendation_models_config WHERE service_provider_id = {$serviceProviderId}");
    }
}
