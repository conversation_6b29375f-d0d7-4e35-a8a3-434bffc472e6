<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230823094853 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update db function get_recommendation_calculations() - use column recommendation.valid_from instad of sampling_date';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP FUNCTION IF EXISTS
            get_recommendation_calculations (
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                sampling_type_ids INTEGER[],
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                humus NUMERIC,
                yield NUMERIC
            )
        ');

        $this->addSql("CREATE FUNCTION
            get_recommendation_calculations (
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                sampling_type_ids INTEGER[],
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                humus NUMERIC,
                yield NUMERIC,
                valid_from DATE
            )
                RETURNS TABLE (\"comments\" JSONB, calculations JSONB, susces JSONB)
                AS $$
                BEGIN
                    RETURN QUERY
                    WITH result_elements AS (
                            SELECT
                                DISTINCT ON (lerac.\"element\", rer.result_element)
                                lerac.element_id,
                                lerac.\"element\",
                                CASE WHEN (licc.content_class = 'Low'::content_class_enum AND (elem_susces.value)::integer = 1) OR rer.value > 0
                                    THEN
                                        true
                                    ELSE
                                        false
                                END AS susces,
                                case when licc.content_class = 'Low'::content_class_enum and (elem_susces.value)::integer = 0
                                    then 0
                                end as content_class,
                                rer.result_element,
                                rcmc.result_element AS result_element_all,
                                rcmc.visual_order,
                                rer.value AS result_element_value,
                                valid_from
                            FROM
                                get_lab_elements_results_aggregated_classes(ARRAY[plt_uuid], pckg_id, pckg_type, service_provider) AS lerac
                            JOIN recommendation_models_config rmc
                                ON rmc.service_provider_id = service_provider
                                AND rmc.id = model
                            LEFT JOIN lab_interpretation_classes_config licc
                                ON licc.id = ANY(lerac.class_ids)
                            JOIN recommendation_calc_model_config rcmc
                                ON rcmc.element_id = lerac.element_id
                                AND rcmc.calc_model_id = rmc.calc_model_id
                            LEFT JOIN get_recommendation_crop_model_config_param(service_provider, model, crop, lerac.\"element\", 'Susces') AS elem_susces
                                ON lerac.\"element\" = elem_susces.\"element\"
                            LEFT JOIN get_recommendation_elements_results(plt_uuid, pckg_id, pckg_type, sampling_type_ids, service_provider, model, crop, humus, yield, valid_from) rer
                                ON rer.result_element = rcmc.result_element
                            WHERE 
                                lerac.sampling_type_id  = ANY(sampling_type_ids)
                                OR COALESCE(sampling_type_ids) ISNULL
                                OR ARRAY_LENGTH(sampling_type_ids, 1) ISNULL
                            GROUP BY
                                lerac.element_id,
                                lerac.\"element\",
                                rer.result_element,
                                licc.content_class,
                                elem_susces.value,
                                rer.value,
                                rcmc.result_element,
                                rcmc.visual_order,
                                valid_from
                        )
                        SELECT 
                            JSONB_AGG(DISTINCT
                                JSONB_BUILD_OBJECT(
                                    'result_element',
                                    CASE WHEN COALESCE(recc2.comment_text, recc.comment_text, rlfcc.comment_text, rfcp.comment_text) = rlfcc.comment_text 
                                        THEN NULL
                                        ELSE re.result_element
                                    END,
                                    'comment_text',
                                    COALESCE(recc2.comment_text, recc.comment_text, rlfcc.comment_text, rfcp.comment_text)
                                )
                            )  FILTER (WHERE COALESCE(recc2.comment_text, recc.comment_text, rfcp.comment_text, rlfcc.comment_text ) notnull) AS \"comments\",
                            json_object_values_array(
                                JSON_OBJECT_AGG(
                                    re.result_element,
                                    JSON_BUILD_OBJECT(
                                        'element', re.\"element\",
                                        'result_element', re.result_element,
                                        'result_element_value', round(re.result_element_value::NUMERIC, 3)
                                    )
                                    ORDER BY re.visual_order ASC
                                ) FILTER (WHERE result_element_value NOTNULL)
                            )::JSONB AS calculations,
                            JSONB_AGG(DISTINCT
                                JSONB_BUILD_OBJECT(
                                    'element',
                                    re.\"element\",
                                    'value',
                                    CASE WHEN re.\"element\" = 'TMN'::elements_enum 
                                        THEN susces_elements.cnt_tmn > 0 
                                        ELSE re.susces
                                    END
                                )
                            ) FILTER (WHERE re.susces=true or re.result_element_all = any (rlfcc2.result_elements))  susces
                        FROM
                            result_elements AS re
                        JOIN recommendation_models_config rmc
                            ON rmc.id = model
                            AND rmc.service_provider_id = service_provider
                        CROSS JOIN LATERAL (
                            SELECT
                                COUNT(re.\"element\") FILTER (WHERE  re.susces = TRUE) AS cnt_all,
                                COUNT(re.\"element\") FILTER (WHERE re.\"element\" = 'TMN'::elements_enum AND re.result_element_value > 0) AS cnt_tmn
                            FROM
                                result_elements re
                        ) susces_elements
                        LEFT JOIN recommendation_element_comments_config recc
                            ON (crop = ANY(recc.crop_ids) OR recc.crop_ids ISNULL)
                            AND recc.result_element = re.result_element
                            AND re.result_element_value::numeric <@ recc.\"range\"
                            and recc.model_id = model
                        LEFT JOIN recommendation_leaf_fertiliser_comments_config rlfcc
                            ON rlfcc.model_id = model
                            AND (crop = ANY(rlfcc.crop_ids) OR rlfcc.crop_ids ISNULL)
                            and re.result_element_all = ANY(rlfcc.result_elements)
                            and susces_elements.cnt_all > 0
                        LEFT JOIN get_recommendation_fertiliser_comments_ph(plt_uuid, pckg_id, pckg_type, sampling_type_ids, service_provider, model, crop, humus, yield, re.valid_from) rfcp
                            ON re.\"element\" = 'pH'::elements_enum
                        LEFT JOIN recommendation_element_comments_config recc2 
                            ON (crop = ANY(recc2.crop_ids) OR recc2.crop_ids ISNULL)
                            AND recc2.result_element = re.result_element_all
                            AND content_class::numeric <@ recc2.\"range\"
                            AND recc2.model_id = model
                            AND content_class notnull
                        LEFT JOIN recommendation_leaf_fertiliser_comments_config rlfcc2
                            ON rlfcc2.model_id = model
                            AND (crop = ANY(rlfcc2.crop_ids) OR rlfcc2.crop_ids ISNULL)
                            AND re.result_element_all = ANY(rlfcc2.result_elements);
                    END
                $$ LANGUAGE plpgsql
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP FUNCTION IF EXISTS
            get_recommendation_calculations (
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                sampling_type_ids INTEGER[],
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                humus NUMERIC,
                yield NUMERIC,
                valid_from DATE
            )
        ');

        $this->addSql("CREATE FUNCTION
            get_recommendation_calculations (
                plt_uuid VARCHAR,
                pckg_id INTEGER,
                pckg_type VARCHAR,
                sampling_type_ids INTEGER[],
                service_provider INTEGER,
                model INTEGER,
                crop INTEGER,
                humus NUMERIC,
                yield NUMERIC
            )
                RETURNS TABLE (\"comments\" JSONB, calculations JSONB, susces JSONB)
                AS $$
                BEGIN
                    RETURN QUERY
                    WITH result_elements AS (
                            SELECT
                                DISTINCT ON (lerac.\"element\", rer.result_element)
                                lerac.element_id,
                                lerac.\"element\",
                                CASE WHEN (licc.content_class = 'Low'::content_class_enum AND (elem_susces.value)::integer = 1) OR rer.value > 0
                                    THEN
                                        true
                                    ELSE
                                        false
                                END AS susces,
                                case when licc.content_class = 'Low'::content_class_enum and (elem_susces.value)::integer = 0
                                    then 0
                                end as content_class,
                                rer.result_element,
                                rcmc.result_element AS result_element_all,
                                rcmc.visual_order,
                                rer.value AS result_element_value,
                                sampling_date
                            FROM
                                get_recommendation_sampling_date(plt_uuid, pckg_id, pckg_type) AS sampling_date,
                                get_lab_elements_results_aggregated_classes(ARRAY[plt_uuid], pckg_id, pckg_type, service_provider) AS lerac
                            JOIN recommendation_models_config rmc
                                ON rmc.service_provider_id = service_provider
                                AND rmc.id = model
                            LEFT JOIN lab_interpretation_classes_config licc
                                ON licc.id = ANY(lerac.class_ids)
                            JOIN recommendation_calc_model_config rcmc
                                ON rcmc.element_id = lerac.element_id
                                AND rcmc.calc_model_id = rmc.calc_model_id
                            LEFT JOIN get_recommendation_crop_model_config_param(service_provider, model, crop, lerac.\"element\", 'Susces') AS elem_susces
                                ON lerac.\"element\" = elem_susces.\"element\"
                            LEFT JOIN get_recommendation_elements_results(plt_uuid, pckg_id, pckg_type, sampling_type_ids, service_provider, model, crop, humus, yield, sampling_date) rer
                                ON rer.result_element = rcmc.result_element
                            WHERE 
                                lerac.sampling_type_id  = ANY(sampling_type_ids)
                                OR COALESCE(sampling_type_ids) ISNULL
                                OR ARRAY_LENGTH(sampling_type_ids, 1) ISNULL
                            GROUP BY
                                lerac.element_id,
                                lerac.\"element\",
                                rer.result_element,
                                licc.content_class,
                                elem_susces.value,
                                rer.value,
                                rcmc.result_element,
                                rcmc.visual_order,
                                sampling_date
                        )
                        SELECT 
                            JSONB_AGG(DISTINCT
                                JSONB_BUILD_OBJECT(
                                    'result_element',
                                    CASE WHEN COALESCE(recc2.comment_text, recc.comment_text, rlfcc.comment_text, rfcp.comment_text) = rlfcc.comment_text 
                                        THEN NULL
                                        ELSE re.result_element
                                    END,
                                    'comment_text',
                                    COALESCE(recc2.comment_text, recc.comment_text, rlfcc.comment_text, rfcp.comment_text)
                                )
                            )  FILTER (WHERE COALESCE(recc2.comment_text, recc.comment_text, rfcp.comment_text, rlfcc.comment_text ) notnull) AS \"comments\",
                            json_object_values_array(
                                JSON_OBJECT_AGG(
                                    re.result_element,
                                    JSON_BUILD_OBJECT(
                                        'element', re.\"element\",
                                        'result_element', re.result_element,
                                        'result_element_value', round(re.result_element_value::NUMERIC, 3)
                                    )
                                    ORDER BY re.visual_order ASC
                                ) FILTER (WHERE result_element_value NOTNULL)
                            )::JSONB AS calculations,
                            JSONB_AGG(DISTINCT
                                JSONB_BUILD_OBJECT(
                                    'element',
                                    re.\"element\",
                                    'value',
                                    CASE WHEN re.\"element\" = 'TMN'::elements_enum 
                                        THEN susces_elements.cnt_tmn > 0 
                                        ELSE re.susces
                                    END
                                )
                            ) FILTER (WHERE re.susces=true or re.result_element_all = any (rlfcc2.result_elements))  susces
                        FROM
                            result_elements AS re
                        JOIN recommendation_models_config rmc
                            ON rmc.id = model
                            AND rmc.service_provider_id = service_provider
                        CROSS JOIN LATERAL (
                            SELECT
                                COUNT(re.\"element\") FILTER (WHERE  re.susces = TRUE) AS cnt_all,
                                COUNT(re.\"element\") FILTER (WHERE re.\"element\" = 'TMN'::elements_enum AND re.result_element_value > 0) AS cnt_tmn
                            FROM
                                result_elements re
                        ) susces_elements
                        LEFT JOIN recommendation_element_comments_config recc
                            ON (crop = ANY(recc.crop_ids) OR recc.crop_ids ISNULL)
                            AND recc.result_element = re.result_element
                            AND re.result_element_value::numeric <@ recc.\"range\"
                            and recc.model_id = model
                        LEFT JOIN recommendation_leaf_fertiliser_comments_config rlfcc
                            ON rlfcc.model_id = model
                            AND (crop = ANY(rlfcc.crop_ids) OR rlfcc.crop_ids ISNULL)
                            and re.result_element_all = ANY(rlfcc.result_elements)
                            and susces_elements.cnt_all > 0
                        LEFT JOIN get_recommendation_fertiliser_comments_ph(plt_uuid, pckg_id, pckg_type, sampling_type_ids, service_provider, model, crop, humus, yield, re.sampling_date) rfcp
                            ON re.\"element\" = 'pH'::elements_enum
                        LEFT JOIN recommendation_element_comments_config recc2 
                            ON (crop = ANY(recc2.crop_ids) OR recc2.crop_ids ISNULL)
                            AND recc2.result_element = re.result_element_all
                            AND content_class::numeric <@ recc2.\"range\"
                            AND recc2.model_id = model
                            AND content_class notnull
                        LEFT JOIN recommendation_leaf_fertiliser_comments_config rlfcc2
                            ON rlfcc2.model_id = model
                            AND (crop = ANY(rlfcc2.crop_ids) OR rlfcc2.crop_ids ISNULL)
                            AND re.result_element_all = ANY(rlfcc2.result_elements);
                    END
                $$ LANGUAGE plpgsql
        ");
    }
}
