<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220720104901 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add column sampling_type_ids to table recommendations. Seed the column.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE recommendations ADD sampling_type_ids integer[]');
        $this->addSql('COMMENT ON COLUMN recommendations.sampling_type_ids IS \'(DC2Type:integer[])\'');

        $this->addSql('UPDATE recommendations SET sampling_type_ids = ARRAY[0]::integer[]');
        $this->addSql('ALTER TABLE recommendations ALTER COLUMN sampling_type_ids SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE recommendations DROP COLUMN sampling_type_ids');
    }
}
