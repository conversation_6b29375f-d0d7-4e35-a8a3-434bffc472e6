<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210823104208 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Update colors in lab_element_interpretations_config for service provdier nikas';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $elementRanges = [
            'pH' => [
                ['range' => '(,3.5]', 'color' => '#F32D2D'],
                ['range' => '(3.5,4.5]', 'color' => '#F44646'],
                ['range' => '(4.5,5]', 'color' => '#F55858'],
                ['range' => '(5,5.5]', 'color' => '#E68282'],
                ['range' => '(5.5,6]', 'color' => '#E6AAAA'],
                ['range' => '(6,6.5]', 'color' => '#E1C8C8'],
                ['range' => '(6.5,7.3]', 'color' => '#C8C8E1'],
                ['range' => '(7.3,7.8]', 'color' => '#AAAAEB'],
                ['range' => '(7.8,8.4]', 'color' => '#8282F0'],
                ['range' => '(8.4,9]', 'color' => '#5B5BF0'],
                ['range' => '(9,)', 'color' => '#3C3CF0'],
            ],
            'TMN' => [
                ['range' => '(,20]', 'color' => '#EED3F2'],
                ['range' => '(20,40]', 'color' => '#DDA7D5'],
                ['range' => '(40,60]', 'color' => '#D785D1'],
                ['range' => '(60,80]', 'color' => '#B553B6'],
                ['range' => '(80,)', 'color' => '#912499'],
            ],
            'P2O5' => [
                ['range' => '(,40]', 'color' => '#D6D6F2'],
                ['range' => '(40,80]', 'color' => '#A6A3E2'],
                ['range' => '(80,150]', 'color' => '#6767BF'],
                ['range' => '(150,220]', 'color' => '#34348C'],
                ['range' => '(220,)', 'color' => '#00026F'],
            ],
            'K2O' => [
                ['range' => '(,85]', 'color' => '#BE9F9D'],
                ['range' => '(85,170]', 'color' => '#AD918E'],
                ['range' => '(170,220]', 'color' => '#A08081'],
                ['range' => '(220,500]', 'color' => '#886261'],
                ['range' => '(500,)', 'color' => '#683E40'],
            ],
            'CaO' => [
                ['range' => '(,1500]', 'color' => '#ECE1BA'],
                ['range' => '(1500,2800]', 'color' => '#E0CF90'],
                ['range' => '(2800,)', 'color' => '#C6AF3E'],
            ],
            'MgO' => [
                ['range' => '(,100]', 'color' => '#DFE3ED'],
                ['range' => '(100,200]', 'color' => '#C4CBDE'],
                ['range' => '(200,)', 'color' => '#9FAAC9'],
            ],
            'S' => [
                ['range' => '(,5]', 'color' => '#FFFFB3'],
                ['range' => '(5,10]', 'color' => '#FFFF81'],
                ['range' => '(10,20]', 'color' => '#FFFF37'],
                ['range' => '(20,)', 'color' => '#F4EE00'],
            ],
            'B' => [
                ['range' => '(,0.5]', 'color' => '#CCFF66'],
                ['range' => '(0.5,2]', 'color' => '#A5DD27'],
                ['range' => '(2,)', 'color' => '#6AA818'],
            ],
            'Cu' => [
                ['range' => '(,0.6]', 'color' => '#CD8637'],
                ['range' => '(0.6,2]', 'color' => '#C06F16'],
                ['range' => '(2,)', 'color' => '#7E4B00'],
            ],
            'Fe' => [
                ['range' => '(,2.5]', 'color' => '#F8A990'],
                ['range' => '(2.5,5]', 'color' => '#F47C56'],
                ['range' => '(5,)', 'color' => '#F04E1C'],
            ],
            'Mn' => [
                ['range' => '(,5]', 'color' => '#9C968C'],
                ['range' => '(5,10]', 'color' => '#69635B'],
                ['range' => '(10,)', 'color' => '#3F3C37'],
            ],
            'Zn' => [
                ['range' => '(,1]', 'color' => '#E7E8E9'],
                ['range' => '(1,1.5]', 'color' => '#CFD1D3'],
                ['range' => '(1.5,)', 'color' => '#A5A9AD'],
            ],
            'Mo' => [
                ['range' => '(,0.1]', 'color' => '#E8AABC'],
                ['range' => '(0.1,0.3]', 'color' => '#DB7B96'],
                ['range' => '(0.3,1]', 'color' => '#D25679'],
                ['range' => '(1,)', 'color' => '#A62C4F'],
            ],
            'Humus' => [
                ['range' => '(,1]', 'color' => '#E0CEC2'],
                ['range' => '(1,2]', 'color' => '#CBAD99'],
                ['range' => '(2,3]', 'color' => '#B79075'],
                ['range' => '(3,4]', 'color' => '#815C43'],
                ['range' => '(4,5]', 'color' => '#5B412F'],
                ['range' => '(5,)', 'color' => '#3D2B1F'],
            ],
            'OM' => [
                ['range' => '(,1]', 'color' => '#E0CEC2'],
                ['range' => '(1,2]', 'color' => '#CBAD99'],
                ['range' => '(2,3]', 'color' => '#B79075'],
                ['range' => '(3,4]', 'color' => '#815C43'],
                ['range' => '(4,5]', 'color' => '#5B412F'],
                ['range' => '(5,)', 'color' => '#3D2B1F'],
            ],
        ];

        $serviceProviderId = $this->getServiceProviderId('nikas');
        $elementIds = $this->getElements();

        foreach ($elementRanges as $elementName => $ranges) {
            foreach ($ranges as $rangeDAta) {
                $this->addSql("
                    UPDATE lab_element_interpretations_config
                    SET color = '{$rangeDAta['color']}'
                    WHERE service_provider_id = '{$serviceProviderId}'
                    AND element_id = '{$elementIds[$elementName]}'
                    AND range = '{$rangeDAta['range']}'
                ");
            }
        }
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');
    }
}
