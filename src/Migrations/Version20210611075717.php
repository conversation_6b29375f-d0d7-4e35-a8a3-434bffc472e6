<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210611075717 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Recreate table recommendations.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP SEQUENCE recommendations_id_seq');
        $this->addSql('DROP TABLE IF EXISTS recommendations CASCADE');
        $this->addSql('CREATE TABLE recommendations (id SERIAL, package_id INT NOT NULL, package_type VARCHAR(63) NOT NULL, model_id INT DEFAULT NULL, plot_uuid VARCHAR(63) NOT NULL, plot_name VARCHAR(255) NOT NULL, crop_id INT DEFAULT NULL, status recommendation_status_enum DEFAULT \'Pending\' NOT NULL, target_yield DOUBLE PRECISION DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_5F42D6C9F44CABFF ON recommendations (package_id)');
        $this->addSql('CREATE INDEX IDX_5F42D6C97975B7E7 ON recommendations (model_id)');
        $this->addSql('ALTER TABLE recommendations ADD CONSTRAINT FK_5F42D6C97975B7E7 FOREIGN KEY (model_id) REFERENCES recommendation_models_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf('postgresql' !== $this->connection->getDatabasePlatform()->getName(), 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('DROP TABLE IF EXISTS recommendations CASCADE');
    }
}
