<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Migrations\Classes\AbstractBaseMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230329105711 extends AbstractBaseMigration
{
    public function getDescription(): string
    {
        return 'Update column farm_id by actual data from susi_main database for used service provider in CMS';
    }

    public function up(Schema $schema): void
    {
        $env = getenv();
        $databases = ['susi_main_v5', 'susi_main_ro_v5', 'susi_main_ua', 'susi_main_it_v5', 'susi_main_tr'];

        foreach ($databases as $susiMainDBName) {
            $susiMainDBConnStr = 'host=' . $env['SUSI_MAIN_DB_HOST']
                . ' port=' . $env['SUSI_MAIN_DB_PORT']
                . ' dbname=' . $susiMainDBName
                . ' user=' . $env['SUSI_MAIN_DB_USER']
                . ' password=' . $env['SUSI_MAIN_DB_PASS'];

            $this->addSql("
                update subscription_package_field spf
                set farm_id = farm.id
                from dblink(
                    '{$susiMainDBConnStr}',
                    $$  select sf.id as farm_id, ssp.uuid as plot_uuid
                        from su_farms sf
                        join su_farms_users sfu on sfu.farm_id = sf.id
                        join su_satellite_plots ssp on sf.id = ssp.farm_id 
                        group by sf.id, ssp.uuid
                    $$
                    ) as farm(id int, plot_uuid varchar)
                where farm.plot_uuid = spf.plot_uuid;
            ");
        }
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
                update subscription_package_field spf
                set farm_id = 0
            ');
    }
}
