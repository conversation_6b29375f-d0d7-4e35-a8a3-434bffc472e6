<?php

namespace App\Model\Farm;

use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Serializer\Annotation\SerializedName;

class BankInfo
{
    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     */
    private $name;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     */
    private $iban;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     */
    private $bic;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("bank_branch")
     */
    private $bankBranch;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("bank_branch_address")
     */
    private $bankBranchAddress;

    /**
     * @return int
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getIban(): ?string
    {
        return $this->iban;
    }

    public function setIban(?string $iban): self
    {
        $this->iban = $iban;

        return $this;
    }

    public function getBic(): ?string
    {
        return $this->bic;
    }

    public function setBic(?string $bic): self
    {
        $this->bic = $bic;

        return $this;
    }

    public function getBankBranch(): ?string
    {
        return $this->bankBranch;
    }

    public function setBankBranch(?string $bankBranch): self
    {
        $this->bankBranch = $bankBranch;

        return $this;
    }

    public function getBankBranchAddress(): ?string
    {
        return $this->bankBranchAddress;
    }

    public function setBankBranchAddress(?string $bankBranchAddress): self
    {
        $this->bankBranchAddress = $bankBranchAddress;

        return $this;
    }
}
