<?php

namespace App\Model\Farm;

use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Serializer\Annotation\SerializedName;

class Farm
{
    /**
     * @var int
     *
     * @Groups({"api", "user", "tf", "gs"})
     */
    private $id;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf", "gs"})
     */
    private $name;

    /**
     * @var null|bool
     *
     * @Groups({"api", "user", "gs"})
     */
    private $isVisible;

    /**
     * @var null|bool
     *
     * @Groups({"api", "user", "gs"})
     */
    private $isAttached;

    /**
     * @var null|int
     *
     * @Groups({"api", "user", "tf", "gs"})
     */
    private $organizationId;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf", "gs"})
     */
    private $uuid;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     */
    private $address;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     */
    private $company;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("company_ekatte");
     */
    private $companyEkatte;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     */
    private $bulstat;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("company_address");
     */
    private $companyAddress;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     */
    private $mol;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("mol_egn");
     */
    private $molEgn;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("farming_mol_phone");
     */
    private $farmingMolPhone;

    /**
     * @var string[]
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("iban_arr");
     */
    private $ibanArr;

    /**
     * @var PostPaymentFields
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("post_payment_fields");
     */
    private $postPaymentFields;

    /**
     * @var int
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("number_of_contracts");
     */
    private $numberOfContracts;

    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName()
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getIsVisible(): ?bool
    {
        return $this->isVisible;
    }

    public function setIsVisible(?bool $isVisible): self
    {
        $this->isVisible = $isVisible;

        return $this;
    }

    public function getOrganizationId(): ?int
    {
        return $this->organizationId;
    }

    public function setOrganizationId(?int $organizationId): self
    {
        $this->organizationId = $organizationId;

        return $this;
    }

    public function getIsAttached(): ?bool
    {
        return $this->isAttached;
    }

    public function setIsAttached(?bool $isAttached): self
    {
        $this->isAttached = $isAttached;

        return $this;
    }

    public function getUuid(): ?string
    {
        return $this->uuid;
    }

    public function setUuid(?string $uuid): self
    {
        $this->uuid = $uuid;

        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(?string $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getCompany(): ?string
    {
        return $this->company;
    }

    public function setCompany(?string $company): self
    {
        $this->company = $company;

        return $this;
    }

    public function getCompanyEkatte(): ?string
    {
        return $this->companyEkatte;
    }

    public function setCompanyEkatte(?string $companyEkatte): self
    {
        $this->companyEkatte = $companyEkatte;

        return $this;
    }

    public function getBulstat(): ?string
    {
        return $this->bulstat;
    }

    public function setBulstat(?string $bulstat): self
    {
        $this->bulstat = $bulstat;

        return $this;
    }

    public function getCompanyAddress(): ?string
    {
        return $this->companyAddress;
    }

    public function setCompanyAddress(?string $companyAddress): self
    {
        $this->companyAddress = $companyAddress;

        return $this;
    }

    public function getMol(): ?string
    {
        return $this->mol;
    }

    public function setMol(?string $mol): self
    {
        $this->mol = $mol;

        return $this;
    }

    public function getMolEgn(): ?string
    {
        return $this->molEgn;
    }

    public function setMolEgn(?string $molEgn): self
    {
        $this->molEgn = $molEgn;

        return $this;
    }

    public function getFarmingMolPhone(): ?string
    {
        return $this->farmingMolPhone;
    }

    public function setFarmingMolPhone(?string $farmingMolPhone): self
    {
        $this->farmingMolPhone = $farmingMolPhone;

        return $this;
    }

    public function getIbanArr(): ?array
    {
        return $this->ibanArr;
    }

    public function setIbanArr(?array $ibanArr): self
    {
        $this->ibanArr = $ibanArr;

        return $this;
    }

    public function getPostPaymentFields(): ?PostPaymentFields
    {
        return $this->postPaymentFields;
    }

    public function setPostPaymentFields(?PostPaymentFields $postPaymentFields): self
    {
        $this->postPaymentFields = $postPaymentFields;

        return $this;
    }

    public function getNumberOfContracts(): ?int
    {
        return $this->numberOfContracts;
    }

    public function setNumberOfContracts(?int $numberOfContracts): self
    {
        $this->numberOfContracts = $numberOfContracts;

        return $this;
    }
}
