<?php

namespace App\Model\Farm;

use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Serializer\Annotation\SerializedName;

class PostPaymentFields
{
    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("sender-city")
     */
    private $senderCity;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("sender-region")
     */
    private $senderRegion;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("sender-street")
     */
    private $senderStreet;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("sender-building")
     */
    private $senderBuilding;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("sender-entrance")
     */
    private $senderEntrance;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("sender-floor")
     */
    private $senderFloor;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("sender-appartment")
     */
    private $senderAppartment;

    /**
     * @var string
     *
     * @Groups({"api", "user", "tf"})
     *
     * @SerializedName("sender-post-code")
     */
    private $senderPostCode;

    public function getSenderCity(): ?string
    {
        return $this->senderCity;
    }

    public function setSenderCity(string $senderCity): self
    {
        $this->senderCity = $senderCity;

        return $this;
    }

    /**
     * @return string
     */
    public function getSenderRegion(): ?string
    {
        return $this->senderRegion;
    }

    public function setSenderRegion(string $senderRegion): self
    {
        $this->senderRegion = $senderRegion;

        return $this;
    }

    public function getSenderStreet(): ?string
    {
        return $this->senderStreet;
    }

    public function setSenderStreet(string $senderStreet): self
    {
        $this->senderStreet = $senderStreet;

        return $this;
    }

    public function getSenderBuilding(): ?string
    {
        return $this->senderBuilding;
    }

    public function setSenderBuilding(string $senderBuilding): self
    {
        $this->senderBuilding = $senderBuilding;

        return $this;
    }

    public function getSenderEntrance(): ?string
    {
        return $this->senderEntrance;
    }

    public function setSenderEntrance(string $senderEntrance): self
    {
        $this->senderEntrance = $senderEntrance;

        return $this;
    }

    public function getSenderFloor(): ?string
    {
        return $this->senderFloor;
    }

    public function setSenderFloor(string $senderFloor): self
    {
        $this->senderFloor = $senderFloor;

        return $this;
    }

    public function getSenderAppartment(): ?string
    {
        return $this->senderAppartment;
    }

    public function setSenderAppartment(string $senderAppartment): self
    {
        $this->senderAppartment = $senderAppartment;

        return $this;
    }

    public function getSenderPostCode(): ?string
    {
        return $this->senderPostCode;
    }

    public function setSenderPostCode(string $senderPostCode): self
    {
        $this->senderPostCode = $senderPostCode;

        return $this;
    }
}
