<?php

namespace App\Model\Types;

/**
 * Custom range type.
 */
class RangeType
{
    private $min;
    private $max;
    private $includeMin;
    private $includeMax;

    public function __construct(?float $min, ?float $max, bool $includeMin = false, bool $includeMax = false)
    {
        $this->min = $min;
        $this->max = $max;
        $this->includeMin = $includeMin;
        $this->includeMax = $includeMax;
    }

    public function getMin(): ?float
    {
        return $this->min;
    }

    public function setMin(?float $min): void
    {
        $this->min = $min;
    }

    public function getMax(): ?float
    {
        return $this->max;
    }

    public function setMax(?float $max): void
    {
        $this->max = $max;
    }

    public function includeMin(): bool
    {
        return $this->includeMin;
    }

    public function setIncludeMin(bool $includeMin): void
    {
        $this->includeMin = $includeMin;
    }

    public function includeMax(): bool
    {
        return $this->includeMax;
    }

    public function setIncludeMax(bool $includeMax): void
    {
        $this->includeMax = $includeMax;
    }
}
