<?php

namespace App\Model\GeoSCAN;

use Symfony\Component\Serializer\Annotation\Groups;

class Permission
{
    /**
     * @var string
     *
     * @Groups({"user"})
     */
    public $name;

    /**
     * @var int
     *
     * @Groups({"user"})
     */
    public $value;

    /**
     * @var string
     *
     * @Groups({"user"})
     */
    public $checked;

    /**
     * @var string
     *
     * @Groups({"user"})
     */
    public $label;

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): Permission
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return string
     */
    public function getValue(): ?int
    {
        return $this->value;
    }

    /**
     * @param string $title
     * @param ?int $value
     */
    public function setValue(?int $value): Permission
    {
        $this->value = $value;

        return $this;
    }

    /**
     * @return string
     */
    public function getChecked(): ?string
    {
        return $this->checked;
    }

    /**
     * @param string $title
     * @param ?string $value
     */
    public function setChecked(?string $value): Permission
    {
        $this->checked = $value;

        return $this;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    /**
     * @param $name
     * @param ?string $label
     */
    public function setLabel(?string $label): Permission
    {
        $this->label = $label;

        return $this;
    }
}
