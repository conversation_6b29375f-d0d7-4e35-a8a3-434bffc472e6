<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>non<PERSON>
 * Date: 7/29/2019
 * Time: 2:47 PM.
 */

namespace App\Model\GeoSCAN;

use Symfony\Component\Serializer\Annotation\Groups;

class Ability
{
    /**
     * @var int
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $id;

    /**
     * @var string
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $name;

    /**
     * @var string
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $title;

    /**
     * @return int
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): void
    {
        $this->title = $title;
    }
}
