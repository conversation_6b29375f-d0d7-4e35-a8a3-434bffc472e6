<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>non<PERSON>
 * Date: 7/30/2019
 * Time: 10:23 AM.
 */

namespace App\Model\GeoSCAN;

use Symfony\Component\Serializer\Annotation\Groups;

class Role
{
    /**
     * @var string
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $name;
    /**
     * @var string
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $title;

    /**
     * @var [int]
     */
    private $level;

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): Role
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return string
     */
    public function getTitle(): ?string
    {
        return $this->title;
    }

    /**
     * @param string $title
     */
    public function setTitle(?string $title): Role
    {
        $this->title = $title;

        return $this;
    }

    /**
     * @param int $level
     */
    public function setLevel(?int $level): void
    {
        $this->level = $level;
    }

    public function getLevel(): ?int
    {
        return $this->level;
    }
}
