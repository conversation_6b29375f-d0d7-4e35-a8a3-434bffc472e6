<?php

namespace App\Model\GeoSCAN;

use Symfony\Component\Serializer\Annotation\Groups;

class UserPermissions
{
    /**
     * @var int
     *
     * @Groups({"user"})
     */
    public $organizationId;

    /**
     * @var string
     *
     * @Groups({"user"})
     */
    public $organizationIdentityNumber;

    /**
     * @var Permission[]
     *
     * @Groups({"user"})
     */
    public $userPermissions;

    public function getOrganizationId(): ?int
    {
        return $this->organizationId;
    }

    public function setOrganizationId(?int $id): UserPermissions
    {
        $this->organizationId = $id;

        return $this;
    }

    public function getOrganizationIdentityNumber(): ?string
    {
        return $this->organizationIdentityNumber;
    }

    public function setOrganizationIdentityNumber(?string $value): UserPermissions
    {
        $this->organizationIdentityNumber = $value;

        return $this;
    }

    public function getUserPermissions(): array
    {
        return $this->userPermissions;
    }

    /**
     * @param string $title
     * @param ?array $value
     */
    public function setUserPermissions(?array $value): UserPermissions
    {
        $this->userPermissions = $value;

        return $this;
    }
}
