<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Model\GeoSCAN;

use App\Model\GeoSCAN\Organization\Address;
use App\Model\GeoSCAN\Organization\ContactPerson;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Serializer\Annotation\MaxDepth;

class Organization
{
    /**
     * @var int
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $id;
    /**
     * @var string
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $name;
    /**
     * @var string
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $identityNumber;
    /**
     * @var null|string
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $vatNumber;
    /**
     * @var null|User[]
     *
     * @Groups({"api"})
     *
     * @MaxDepth(2)
     */
    private $usersAssigned;
    /**
     * @var Farm[]
     *
     * @Groups({"api"})
     */
    private $farms = [];
    /**
     * @var null|Address[]
     *
     * @Groups({"api"})
     */
    private $addresses;
    /**
     * @var null|ContactPerson[]
     *
     * @Groups({"api"})
     */
    private $contactPersons;

    /**
     * @var null|array
     *
     * @Groups({"api"})
     *
     * @MaxDepth(2)
     */
    private $permissions;

    public function setPermissions($value)
    {
        $this->permissions = $value;

        return $this;
    }

    public function getPermissions()
    {
        return $this->permissions;
    }

    /**
     * @return int
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): Organization
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return string
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): Organization
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return string
     */
    public function getIdentityNumber(): ?string
    {
        return $this->identityNumber;
    }

    /**
     * @param string $identityNumber
     */
    public function setIdentityNumber(?string $identityNumber): Organization
    {
        $this->identityNumber = $identityNumber;

        return $this;
    }

    public function getVatNumber(): ?string
    {
        return $this->vatNumber;
    }

    public function setVatNumber(?string $vatNumber): Organization
    {
        $this->vatNumber = $vatNumber;

        return $this;
    }

    /**
     * @return null|User[]
     */
    public function getUsersAssigned(): ?array
    {
        return $this->usersAssigned;
    }

    /**
     * @param null|User[] $usersAssigned
     */
    public function setUsersAssigned(?array $usersAssigned): Organization
    {
        $this->usersAssigned = $usersAssigned;

        return $this;
    }

    /**
     * @return Farm[]
     */
    public function getFarms(): array
    {
        return $this->farms;
    }

    /**
     * @param null|Farm[] $farms
     */
    public function setFarms(?array $farms): Organization
    {
        $this->farms = $farms;

        return $this;
    }

    /**
     * @return null|Address[]
     */
    public function getAddresses(): ?array
    {
        return $this->addresses;
    }

    /**
     * @param null|Address[] $addresses
     */
    public function setAddresses(?array $addresses): Organization
    {
        $this->addresses = $addresses;

        return $this;
    }

    /**
     * @return null|ContactPerson[]
     */
    public function getContactPersons(): ?array
    {
        return $this->contactPersons;
    }

    /**
     * @param null|ContactPerson[] $contactPersons
     */
    public function setContactPersons(?array $contactPersons): Organization
    {
        $this->contactPersons = $contactPersons;

        return $this;
    }
}
