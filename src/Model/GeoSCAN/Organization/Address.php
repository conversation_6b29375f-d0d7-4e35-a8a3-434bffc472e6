<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Model\GeoSCAN\Organization;

use Symfony\Component\Serializer\Annotation\Groups;

class Address
{
    /**
     * @var int
     *
     * @Groups({"api"})
     */
    private $id;
    /**
     * @var string
     *
     * @Groups({"api"})
     */
    private $address;
    /**
     * @var string
     *
     * @Groups({"api"})
     */
    private $city;
    /**
     * @var string
     *
     * @Groups({"api"})
     */
    private $country;
    /**
     * @var null|string
     *
     * @Groups({"api"})
     */
    private $note;
    /**
     * @var null|bool
     *
     * @Groups({"api"})
     */
    private $isMain;

    /**
     * @return int
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): Address
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return string
     */
    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(string $address): Address
    {
        $this->address = $address;

        return $this;
    }

    /**
     * @return string
     */
    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(string $city): Address
    {
        $this->city = $city;

        return $this;
    }

    /**
     * @return string
     */
    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(string $country): Address
    {
        $this->country = $country;

        return $this;
    }

    public function getNote(): ?string
    {
        return $this->note;
    }

    public function setNote(?string $note): Address
    {
        $this->note = $note;

        return $this;
    }

    public function getIsMain(): ?bool
    {
        return $this->isMain;
    }

    public function setIsMain(?bool $isMain): Address
    {
        $this->isMain = $isMain;

        return $this;
    }
}
