<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Model\GeoSCAN\Organization;

use Symfony\Component\Serializer\Annotation\Groups;

class ContactPerson
{
    /**
     * @var int
     *
     * @Groups({"api"})
     */
    private $id;
    /**
     * @var string
     *
     * @Groups({"api"})
     */
    private $name;
    /**
     * @var null|string
     *
     * @Groups({"api"})
     */
    private $email;
    /**
     * @var null|string
     *
     * @Groups({"api"})
     */
    private $phone;
    /**
     * @var null|string
     *
     * @Groups({"api"})
     */
    private $position;
    /**
     * @var null|string
     *
     * @Groups({"api"})
     */
    private $note;
    /**
     * @var null|bool
     *
     * @Groups({"api"})
     */
    private $isRepresentative;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id): ContactPerson
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    public function setName(string $name): ContactPerson
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    public function setEmail(string $email): ContactPerson
    {
        $this->email = $email;

        return $this;
    }

    /**
     * @return string
     */
    public function getPhone()
    {
        return $this->phone;
    }

    public function setPhone(string $phone): ContactPerson
    {
        $this->phone = $phone;

        return $this;
    }

    /**
     * @return string
     */
    public function getPosition()
    {
        return $this->position;
    }

    /**
     * @param string $position
     */
    public function setPosition(?string $position): ContactPerson
    {
        $this->position = $position;

        return $this;
    }

    /**
     * @return string
     */
    public function getNote()
    {
        return $this->note;
    }

    /**
     * @param string $note
     */
    public function setNote(?string $note): ContactPerson
    {
        $this->note = $note;

        return $this;
    }

    /**
     * @return bool
     */
    public function getIsRepresentative()
    {
        return $this->isRepresentative;
    }

    public function setIsRepresentative(bool $isRepresentative): ContactPerson
    {
        $this->isRepresentative = $isRepresentative;

        return $this;
    }
}
