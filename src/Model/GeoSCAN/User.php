<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Model\GeoSCAN;

use Symfony\Component\Serializer\Annotation\Groups;

class User
{
    public const ROLE_SERVICE = 'SERVICE';
    public const ROLE_SAMPLER = 'SAMPLER';
    public const ROLES = [
        self::ROLE_SERVICE,
        self::ROLE_SAMPLER,
    ];

    /**
     * @var int
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $id;

    /**
     * @var int
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $groupId;
    /**
     * @var int
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $oldId;
    /**
     * @var string
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $username;
    /**
     * @var string
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $name;
    /**
     * @var null|Role
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $role;
    /**
     * @var null|string
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $password;
    /**
     * @var null|string
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $passwordReType;
    /**
     * @var null|Farm[]
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $farms;

    /**
     * @var null|Organization[]
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $organizations;

    /**
     * @var string
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $email;

    /**
     * @var string
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $phone;

    /**
     * @var bool
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $active;

    /**
     * @var null|Ability[]
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $abilities;

    /**
     * @var null|string
     *
     * @Groups({"api"})
     * @Groups({"user"})
     */
    private $profileImage;

    /**
     * @var null|Permissions[]
     *
     * @Groups({"user"})
     */
    private $permissions = [];

    /**
     * @var string
     *
     * @Groups({"user"})
     */
    private $keycloakUid;

    /**
     * @var int
     *
     * @Groups({"user"})
     */
    private $technofarmUserId;

    /**
     * @return int
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): User
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return int
     */
    public function getGroupId(): ?int
    {
        return $this->groupId;
    }

    public function setGroupId(int $id): User
    {
        $this->groupId = $id;

        return $this;
    }

    /**
     * @return int
     */
    public function getOldId(): ?int
    {
        return $this->oldId;
    }

    public function setOldId(int $id): User
    {
        $this->oldId = $id;

        return $this;
    }

    /**
     * @return string
     */
    public function getUsername(): ?string
    {
        return $this->username;
    }

    public function setUsername(string $username): User
    {
        $this->username = $username;

        return $this;
    }

    /**
     * @return string
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): User
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return null|Role
     */
    public function getRole()
    {
        return $this->role;
    }

    public function setRole(?Object $role): User
    {
        $this->role = $role;

        return $this;
    }

    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setPassword(?string $password): User
    {
        $this->password = $password;

        return $this;
    }

    public function getPasswordReType(): ?string
    {
        return $this->passwordReType;
    }

    public function setPasswordReType(?string $passwordReType): void
    {
        $this->passwordReType = $passwordReType;
    }

    /**
     * @return null|Farm[]
     */
    public function getFarms(): ?array
    {
        return $this->farms;
    }

    /**
     * @param null|Farm[] $farms
     */
    public function setFarms(?array $farms): User
    {
        $this->farms = $farms;

        return $this;
    }

    /**
     * @return string
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): User
    {
        $this->email = $email;

        return $this;
    }

    /**
     * @return null|Organization[]
     */
    public function getOrganizations(): ?array
    {
        return $this->organizations;
    }

    /**
     * @param null|Organization[] $organizations
     */
    public function setOrganizations(?array $organizations): User
    {
        $this->organizations = $organizations;

        return $this;
    }

    /**
     * @return bool
     */
    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): User
    {
        $this->active = (bool)$active;

        return $this;
    }

    /**
     * @return null|Ability[]
     */
    public function getAbilities()
    {
        return $this->abilities;
    }

    public function setAbilities($abilities): User
    {
        $this->abilities = $abilities;

        return $this;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): User
    {
        $this->phone = $phone;

        return $this;
    }

    public function getProfileImage(): ?string
    {
        return $this->profileImage;
    }

    public function setProfileImage($profileImage): User
    {
        $this->profileImage = $profileImage;

        return $this;
    }

    public function setPermissions($permissions): User
    {
        $this->permissions = $permissions;

        return $this;
    }

    public function getPermissions(): iterable
    {
        return $this->permissions;
    }

    /**
     * @return int
     */
    public function getTechnofarmUserId(): ?int
    {
        return $this->technofarmUserId;
    }

    /**
     * @param int $id
     *
     * @ Not all user providers and user roles are technofarm integrated.
     */
    public function setTechnofarmUserId(?int $id): User
    {
        $this->technofarmUserId = $id;

        return $this;
    }

    /**
     * @return string
     */
    public function getKeycloakUid(): ?string
    {
        return $this->keycloakUid;
    }

    /**
     * @param string $id
     */
    public function setKeycloakUid($uid): User
    {
        $this->keycloakUid = $uid;

        return $this;
    }
}
