<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Model\GeoSCAN;

use Symfony\Component\Serializer\Annotation\Groups;

class History
{
    /**
     * @var string
     *
     * @Groups({"api"})
     */
    private $updatedAt;

    /**
     * @var array
     *
     * @Groups({"api"})
     */
    private $previous;

    /**
     * @var array
     *
     * @Groups({"api"})
     */
    private $current;

    /**
     * @var string
     *
     * @Groups({"api"})
     */
    private $editorName;

    /**
     * @Groups({"api"})
     */
    public function getUpdatedAt(): string
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(string $updatedAt): History
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    public function getPrevious(): array
    {
        return $this->previous;
    }

    public function setPrevious(array $previous): History
    {
        $this->previous = $previous;

        return $this;
    }

    public function getCurrent(): array
    {
        return $this->current;
    }

    public function setCurrent(array $current): History
    {
        $this->current = $current;

        return $this;
    }

    public function getEditorName(): string
    {
        return $this->editorName;
    }

    public function setEditorName(string $name): History
    {
        $this->editorName = $name;

        return $this;
    }
}
