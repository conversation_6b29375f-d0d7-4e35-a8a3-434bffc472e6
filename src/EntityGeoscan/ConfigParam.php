<?php

namespace App\EntityGeoscan;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\ConfigParamRepository")
 *
 * @ORM\Table(name="config_params")
 */
class ConfigParam
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=63)
     */
    private $domain;

    /**
     * @ORM\OneToMany(targetEntity="App\EntityGeoscan\ConfigParamValue", mappedBy="configParamId")
     */
    private $configParamValues;

    public function __construct()
    {
        $this->configParamValues = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDomain(): ?string
    {
        return $this->domain;
    }

    public function setDomain(string $domain): self
    {
        $this->domain = $domain;

        return $this;
    }

    /**
     * @return Collection|ConfigParamValue[]
     */
    public function getConfigParamValues(): Collection
    {
        return $this->configParamValues;
    }

    public function addConfigParamValue(ConfigParamValue $configParamValue): self
    {
        if (!$this->configParamValues->contains($configParamValue)) {
            $this->configParamValues[] = $configParamValue;
            $configParamValue->setConfigParamId($this);
        }

        return $this;
    }
}
