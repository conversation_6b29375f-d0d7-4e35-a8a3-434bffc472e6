<?php

namespace App\EntityGeoscan;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\ServiceProviderGeoScanRepository")
 *
 * @ORM\Table(name="service_providers")
 */
class ServiceProviderGeoScan
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=100)
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=20)
     */
    private $slug;

    /**
     * @ORM\ManyToOne(targetEntity="App\EntityGeoscan\Country")
     *
     * @ORM\JoinColumn(name="country_id", nullable=false)
     */
    private $country;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $logo;

    /**
     * @ORM\Column(type="jsonb", nullable=true)
     */
    private $companyInfo;

    /**
     * @ORM\Column(type="jsonb", nullable=false)
     */
    private $couchdbConfig;

    public function __construct() {}

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function setSlug(string $slug): self
    {
        $this->slug = $slug;

        return $this;
    }

    public function getCountry(): ?Country
    {
        return $this->country;
    }

    public function setCountry(Country $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getLogo(): ?string
    {
        return $this->logo;
    }

    public function setLogo(?string $logo): self
    {
        $this->logo = $logo;

        return $this;
    }

    public function getCompanyInfo(): ?array
    {
        return $this->companyInfo;
    }

    public function setCompanyInfo(?array $companyInfo): self
    {
        $this->companyInfo = $companyInfo;

        return $this;
    }

    public function getCouchDBConfig(): array
    {
        $envPasswordName = $this->couchdbConfig['password'];
        $this->couchdbConfig['password'] = getenv($envPasswordName);

        return $this->couchdbConfig;
    }
}
