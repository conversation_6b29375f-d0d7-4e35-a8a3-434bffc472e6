<?php

namespace App\EntityGeoscan;

use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\CountryRepository")
 *
 * @ORM\Table(name="countries")
 */
class Country
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="text")
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=2, name="iso_alpha_2_code")
     */
    private $isoAlpha2Code;

    /**
     * @ORM\Column(type="string", length=3, name="iso_alpha_3_code")
     */
    private $isoAlpha3Code;
    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $active;

    /**
     * @ORM\OneToMany(targetEntity="App\EntityGeoscan\ConfigParamValue", mappedBy="countryId")
     */
    private $configParamValues;

    /**
     * @ORM\Column(type="string", length=2, name="database_name")
     */
    private $databaseName;

    public function __construct() {}

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getIsoAlpha2Code(): ?string
    {
        return $this->isoAlpha2Code;
    }

    public function setIsoAlpha2Code(string $isoAlpha2Code): self
    {
        $this->isoAlpha2Code = $isoAlpha2Code;

        return $this;
    }

    public function getIsoAlpha3Code(): ?string
    {
        return $this->isoAlpha3Code;
    }

    public function setIsoAlpha3Code(string $isoAlpha3Code): self
    {
        $this->isoAlpha3Code = $isoAlpha3Code;

        return $this;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(?bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    /**
     * @return Collection|ConfigParamValue[]
     */
    public function getConfigParamValues(): Collection
    {
        return $this->configParamValues;
    }

    public function addConfigParamValue(ConfigParamValue $configParamValue): self
    {
        if (!$this->configParamValues->contains($configParamValue)) {
            $this->configParamValues[] = $configParamValue;
            $configParamValue->setCountryId($this);
        }

        return $this;
    }

    public function getDatabaseName()
    {
        return $this->databaseName;
    }

    public function setDatabaseName($databaseName): void
    {
        $this->databaseName = $databaseName;
    }
}
