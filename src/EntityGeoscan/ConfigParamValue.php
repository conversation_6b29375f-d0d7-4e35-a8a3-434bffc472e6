<?php

namespace App\EntityGeoscan;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\ConfigParamValueRepository")
 *
 * @ORM\Table(name="config_params_values")
 */
class ConfigParamValue
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $value;

    /**
     * @ORM\ManyToOne(targetEntity="App\EntityGeoscan\ConfigParam", inversedBy="configParamValues")
     *
     * @ORM\JoinColumn(nullable=false, name="config_param_id")
     */
    private $configParamId;

    /**
     * @ORM\ManyToOne(targetEntity="App\EntityGeoscan\Country", inversedBy="configParamValues")
     *
     * @ORM\JoinColumn(nullable=false, name="country_id")
     */
    private $countryId;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(string $value): self
    {
        $this->value = $value;

        return $this;
    }

    public function getConfigParamId(): ?ConfigParam
    {
        return $this->configParamId;
    }

    public function setConfigParamId(?ConfigParam $configParamId): self
    {
        $this->configParamId = $configParamId;

        return $this;
    }

    public function getCountryId(): ?Country
    {
        return $this->countryId;
    }

    public function setCountryId(?Country $countryId): self
    {
        $this->countryId = $countryId;

        return $this;
    }
}
