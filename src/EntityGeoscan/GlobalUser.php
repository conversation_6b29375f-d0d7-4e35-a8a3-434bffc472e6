<?php

namespace App\EntityGeoscan;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * @ORM\Entity(repositoryClass="App\Repository\GlobalUserRepository")
 *
 * @ORM\Table(name="su_users")
 */
class GlobalUser implements UserInterface
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\EntityGeoscan\ServiceProviderGeoScan")
     *
     * @ORM\JoinColumn(name="service_provider_id", referencedColumnName="id", name="service_provider_id")
     */
    private $serviceProvider;

    /**
     * @ORM\ManyToOne(targetEntity="App\EntityGeoscan\Country")
     *
     * @ORM\JoinColumn(name="country", referencedColumnName="id")
     */
    private $country;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $username;

    /**
     * @ORM\ManyToMany(targetEntity="App\EntityGeoscan\Role", inversedBy="users")
     *
     * @ORM\JoinTable(
     *     name="assigned_roles",
     *     joinColumns={
     *
     *         @ORM\JoinColumn(name="entity_id", referencedColumnName="id")
     *     },
     *     inverseJoinColumns={
     *         @ORM\JoinColumn(name="role_id", referencedColumnName="id")
     *     }
     * )
     */
    private $roles;

    /**
     * @ORM\Column(type="integer")
     */
    private $old_id;

    /**
     * @ORM\Column(type="text", unique=true)
     */
    private $keycloakUid;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $email;

    private $password;

    private $accessToken;

    public function __construct()
    {
        $this->roles = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getOldId(): ?int
    {
        return $this->old_id;
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getServiceProvider(): ?ServiceProviderGeoScan
    {
        return $this->serviceProvider;
    }

    public function setServiceProvider(?ServiceProviderGeoScan $serviceProvider): self
    {
        $this->serviceProvider = $serviceProvider;

        return $this;
    }

    public function getCountry(): ?Country
    {
        return $this->country;
    }

    public function setCountry(?Country $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function setAccessToken(string $token)
    {
        $this->accessToken = $token;
    }

    public function getAccessToken()
    {
        return $this->accessToken;
    }

    public function setKeycloakUid(string $uuid)
    {
        $this->keycloakUid = $uuid;
    }

    public function getKeycloakUid()
    {
        return $this->keycloakUid;
    }

    public function getRoles(): array
    {
        return $this->roles->map(function (Role $role) {
            return "ROLE_{$role->getName()}";
        })->toArray();
    }

    public function addRole(Role $role): self
    {
        if (!$this->roles->contains($role)) {
            $this->roles[] = $role;
            $role->addUser($this);
        }

        return $this;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function setPassword(string $password): self
    {
        $this->password = $password;

        return $this;
    }

    /**
     * Returning a salt is only needed, if you are not using a modern
     * hashing algorithm (e.g. bcrypt or sodium) in your security.yaml.
     *
     * @see UserInterface
     */
    public function getSalt(): ?string
    {
        return null;
    }

    /**
     * The public representation of the user (e.g. a username, an email address, etc.).
     *
     * @see UserInterface
     */
    public function getUserIdentifier(): string
    {
        return (string) $this->username;
    }

    public function eraseCredentials() {}
}
