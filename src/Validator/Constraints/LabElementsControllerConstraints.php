<?php

namespace App\Validator\Constraints;

use Symfony\Component\Validator\Constraints as Assert;

class LabElementsControllerConstraints extends Constraints
{
    public static function getElementsResultsForApprove(bool $allowExtraFields = true): Assert\Collection
    {
        $constraints = new Assert\Collection([
            'contract_id' => [new Assert\Required(), new Assert\Type('numeric')],
            'subscription_package_id' => [new Assert\Required(), new Assert\Type('numeric')],
            'plot_uuids' => [new Assert\Required()],
        ]);

        return $allowExtraFields ? self::allowExtraFields($constraints) : $constraints;
    }
}
