<?php

namespace App\Validator\Constraints;

use App\EntityGeoscan\Role as EntityGeoscanRole;
use App\Model\GeoSCAN\Role;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

class RoleValidator extends ConstraintValidator
{
    private $doctrine;

    public function __construct(ManagerRegistry $doctrine)
    {
        $this->doctrine = $doctrine;
    }

    public function validate($role, Constraint $constraint)
    {
        // @var $constraint \App\Validator\RoleConstraint
        if (!$role instanceof Role) {
            return;
        }

        // Check if role with submitted name already exists in the database
        $existingRole = $this->doctrine->getRepository(EntityGeoscanRole::class, 'geoscan')->findOneBy(['name' => $role->getName()]);

        if (null === $existingRole) {
            $this->context->buildViolation('There is no role with the name "{{ value }}".')
                ->setParameter('{{ value }}', (string) $role->getName())
                ->addViolation();
        }
    }
}
