<?php

namespace App\Validator\Constraints;

use Symfony\Component\Validator\Constraints as Assert;

class RecommendationControllerConstraints extends Constraints
{
    public static function store(bool $allowExtraFields = true)
    {
        $constraints = new Assert\Collection([
            'subscription_package_field_id' => [new Assert\NotBlank(), new Assert\Type('integer')],
            'plot_name' => [new Assert\NotBlank(), new Assert\Type('string')],
            'crop_id' => [new Assert\NotBlank(), new Assert\Type('integer')],
            'model_id' => [new Assert\NotBlank(), new Assert\Type('integer')],
            'humus' => [new Assert\NotBlank(), new Assert\Type(['float', 'integer'])],
            'yield' => [new Assert\NotBlank(), new Assert\Type(['float', 'integer'])],
            'valid_from' => [new Assert\NotBlank(), new Assert\Type('string')],
            'results' => new Assert\All(
                new Assert\Collection([
                    'result_element' => [new Assert\NotBlank(), new Assert\Type('string')],
                    'result_element_value' => [new Assert\NotBlank(), new Assert\Type(['float', 'integer'])],
                ])
            ),
            'comments' => new Assert\All(
                new Assert\Collection([
                    'result_element' => [new Assert\NotBlank(['allowNull' => true]), new Assert\Type('string')],
                    'comment_text' => [new Assert\NotBlank(), new Assert\Type('string')],
                ])
            ),
            'susces' => new Assert\All(
                new Assert\Collection([
                    'element' => [new Assert\NotBlank(), new Assert\Type('string')],
                    'value' => [new Assert\NotNull(), new Assert\Type('bool')],
                ])
            ),
            'vra_orders' => new Assert\Optional(new Assert\All(
                new Assert\Collection([
                    'id' => [new Assert\NotBlank(), new Assert\Type('integer')],
                    'type' => [new Assert\NotNull(), new Assert\Type('string')],
                ])
            )),
            'sampling_type_ids' => [new Assert\NotBlank(), new Assert\Type('array')],
        ]);

        return $allowExtraFields ? self::allowExtraFields($constraints) : $constraints;
    }

    public static function getRecommendationCalculations(bool $allowExtraFields = true)
    {
        $constraints = new Assert\Collection([
            'crop_id' => [new Assert\NotBlank(), new Assert\Type('numeric')],
            'model_id' => [new Assert\NotBlank(), new Assert\Type('numeric')],
            'humus' => [new Assert\NotBlank(), new Assert\Type('numeric')],
            'valid_from' => [new Assert\NotBlank(), new Assert\Type('string')],
            'yield' => [new Assert\NotBlank(), new Assert\Type('numeric')],
            'sampling_type_ids' => [new Assert\Optional([
                new Assert\Count(['min' => 1]),
                new Assert\NotBlank(),
                new Assert\Type('array')])], // json strin/Applications/Visual Studio Code.app/Contents/Resources/app/out/vs/code/electron-sandbox/workbench/workbench.htmlg array
        ]);

        return $allowExtraFields ? self::allowExtraFields($constraints) : $constraints;
    }

    public static function updateStatus(bool $allowExtraFields = true): Assert\Collection
    {
        $constraints = new Assert\Collection([
            'status' => [new Assert\NotBlank(), new Assert\Type('string')],
            'decline_reason' => [new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string')])],
        ]);

        return $allowExtraFields ? self::allowExtraFields($constraints) : $constraints;
    }

    public static function updateRecommendation(bool $allowExtraFields = true)
    {
        $constraints = new Assert\Collection([
            'crop_id' => new Assert\Optional([new Assert\Type('integer'), new Assert\NotBlank()]),
            'model_id' => new Assert\Optional([new Assert\Type('integer'), new Assert\NotBlank()]),
            'yield' => new Assert\Optional([new Assert\Type(['float', 'integer']), new Assert\NotBlank()]),
            'valid_from' => new Assert\Optional([new Assert\Type(['string']), new Assert\NotBlank()]),
            'humus' => new Assert\Optional([new Assert\Type(['float', 'integer']), new Assert\NotBlank()]),
            'sampling_type_ids' => new Assert\Optional([new Assert\Type('array'), new Assert\NotBlank(), new Assert\Count(['min' => 1])]),
            'results' => new Assert\Optional(new Assert\All(
                new Assert\Collection([
                    'result_element' => [new Assert\NotBlank(), new Assert\Type('string')],
                    'result_element_value' => [new Assert\NotBlank(), new Assert\Type(['float', 'integer'])],
                ])
            )),
            'comments' => new Assert\Optional(new Assert\All(
                new Assert\Collection([
                    'result_element' => [new Assert\NotBlank(['allowNull' => true]), new Assert\Type('string')],
                    'comment_text' => [new Assert\NotBlank(), new Assert\Type('string')],
                ])
            )),
            'susces' => new Assert\Optional(new Assert\All(
                new Assert\Collection([
                    'element' => [new Assert\NotBlank(), new Assert\Type('string')],
                    'value' => [new Assert\NotNull(), new Assert\Type('bool')],
                ])
            )),
            'vra_orders' => new Assert\Optional(new Assert\All(
                new Assert\Collection([
                    'id' => [new Assert\NotBlank(), new Assert\Type('integer')],
                    'type' => [new Assert\NotNull(), new Assert\Type('string')],
                ])
            )),
        ]);

        return $allowExtraFields ? self::allowExtraFields($constraints) : $constraints;
    }

    public static function listRecommendationByPlot(bool $allowExtraFields = true): Assert\Collection
    {
        $constraints = new Assert\Collection([
            'from_date' => [new Assert\NotBlank(), new Assert\Type('string')],
            'to_date' => [new Assert\NotBlank(), new Assert\Type('string')],
            'status' => [new Assert\NotBlank(), new Assert\Type('string')],
        ]);

        return $allowExtraFields ? self::allowExtraFields($constraints) : $constraints;
    }

    public static function getRecommendationReport(bool $allowExtraFields = true): Assert\Collection
    {
        $constraints = new Assert\Collection([
            'group_by' => [new Assert\NotBlank(), new Assert\Type('string')],
            'offset' => [new Assert\NotBlank(), new Assert\Type('numeric')],
            'limit' => [new Assert\NotBlank(), new Assert\Type('numeric')],
            'order_by' => [new Assert\NotBlank(), new Assert\Type('array')],
            'filter' => new Assert\All(
                new Assert\Collection([
                    'customer_identification' => [new Assert\Required(), new Assert\Type('string')],
                    'crop_id' => [new Assert\Optional(), new Assert\Type('numeric')],
                    'plot_uuid' => [new Assert\Optional(), new Assert\Type('string')],
                    'start_data' => [new Assert\Required(), new Assert\Type('numeric')],
                    'end_date' => [new Assert\Required(), new Assert\Type('numeric')],
                ])
            ),
        ]);

        return $allowExtraFields ? self::allowExtraFields($constraints) : $constraints;
    }

    public static function getRecommendationLabResults(bool $allowExtraFields = true): Assert\Collection
    {
        $constraints = new Assert\Collection([
            'sampling_type_ids' => [new Assert\NotBlank(),  new Assert\Type('array')],
        ]);

        return $allowExtraFields ? self::allowExtraFields($constraints) : $constraints;
    }
}
