<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

/**
 * Class SubscriptionPackageAreaValidator.
 */
class SubscriptionPackageAreaValidator extends ConstraintValidator
{
    public function validate($object, Constraint $constraint)
    {
        if (!$constraint instanceof SubscriptionPackageArea) {
            throw new UnexpectedTypeException($constraint, SubscriptionPackageArea::class);
        }

        if (null === $object) {
            return;
        }

        $contractArea = $object->getContract()->getArea();
        $totalArea = 0;
        foreach ($object->getSubscriptionPackageFields() as $item) {
            $totalArea += $item->getArea();
        }
        if ($totalArea > $contractArea) {
            $this->context->buildViolation($constraint->message)
                ->addViolation();
        }
    }
}
