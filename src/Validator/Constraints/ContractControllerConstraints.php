<?php

namespace App\Validator\Constraints;

use Symfony\Component\Validator\Constraints as Assert;

class ContractControllerConstraints extends Constraints
{
    public static function removeFieldsFromContract(bool $allowExtraFields = true)
    {
        $constraints = new Assert\Collection([
            'period' => [new Assert\Optional([new Assert\NotBlank(), new Assert\Type('integer')])],
            'plotUuids' => [new Assert\NotBlank(), new Assert\Type('array')],
        ]);

        return $allowExtraFields ? self::allowExtraFields($constraints) : $constraints;
    }

    public static function getContractFields(array $contractTypes, bool $allowExtraFields = true)
    {
        $constraints = new Assert\Collection([
            'contractType' => [new Assert\Required(), new Assert\NotBlank(), new Assert\Choice($contractTypes)],
            'packageId' => [new Assert\Required(), new Assert\NotBlank(), new Assert\Type('integer')],
        ]);

        return $allowExtraFields ? self::allowExtraFields($constraints) : $constraints;
    }

    public static function contractsIntegrationCardList(bool $allowExtraFields = true)
    {
        $constraints = new Assert\Collection([
            'userId' => [new Assert\Required(), new Assert\NotBlank(), new Assert\Type('numeric')],
            'packageSlug' => [new Assert\Required(), new Assert\NotBlank(), new Assert\Type('string')],
            'customerIdentification' => [new Assert\Type('string')],
            'limit' => [new Assert\Required(), new Assert\NotBlank(), new Assert\Type('numeric')],
            'offset' => [new Assert\Required(), new Assert\NotBlank(), new Assert\Type('numeric')],
        ]);

        return $allowExtraFields ? self::allowExtraFields($constraints) : $constraints;
    }

    public static function contractsGetCardsData(bool $allowExtraFields = true)
    {
        $constraints = new Assert\Collection([
            'userId' => [new Assert\Required(), new Assert\NotBlank(), new Assert\Type('numeric')],
        ]);

        return $allowExtraFields ? self::allowExtraFields($constraints) : $constraints;
    }
}
