<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Validator\Constraints;

use Symfony\Component\Validator\Constraint;

/**
 * Class SubscriptionPackageArea.
 *
 * @Annotation
 */
class SubscriptionPackageArea extends Constraint
{
    public $message = 'Sum of fields areas should not be greater than the contract area.';

    public function getTargets()
    {
        return self::CLASS_CONSTRAINT;
    }
}
