<?php

namespace App\Validator\Constraints;

use Symfony\Component\Validator\Constraints as Assert;

class SamplingTypeConstraints extends Constraints
{
    public static function getSamplingTypes(bool $allowExtraFields = true): Assert\Collection
    {
        $constraints = new Assert\Collection([
            'filter' => new Assert\All(
                new Assert\Collection([
                    'package_id' => [new Assert\Optional(), new Assert\Type('numeric')],
                    'plot_uuid' => [new Assert\Optional(), new Assert\Type('string')],
                    'start_data' => [new Assert\Required(), new Assert\Type('string')],
                    'end_date' => [new Assert\Required(), new Assert\Type('string')],
                ])
            ),
        ]);

        return $allowExtraFields ? self::allowExtraFields($constraints) : $constraints;
    }
}
