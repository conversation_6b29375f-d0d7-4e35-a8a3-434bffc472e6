<?php

namespace App\Validator\Constraints;

use Symfony\Component\Validator\Constraint;

/**
 * @Annotation
 */
class ServicePackageArea extends Constraint
{
    /*
     * Any public properties become valid options for the annotation.
     * Then, use these in your validator class.
     */
    public $message = 'Sum of fields areas should not be greater than the contract area.';

    public function getTargets()
    {
        return self::CLASS_CONSTRAINT;
    }
}
