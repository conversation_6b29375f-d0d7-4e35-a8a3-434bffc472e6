<?php

namespace App\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class ServicePackageAreaValidator extends ConstraintValidator
{
    public function validate($object, Constraint $constraint)
    {
        if (!$constraint instanceof ServicePackageArea) {
            throw new UnexpectedTypeException($constraint, ServicePackageArea::class);
        }

        if (null === $object || '' === $object) {
            return;
        }

        $contractArea = $object->getContract()->getArea();
        $totalArea = 0;
        foreach ($object->getServicePackageFields() as $item) {
            $totalArea += $item->getArea();
        }
        if ($totalArea > $contractArea) {
            $this->context->buildViolation($constraint->message)
                ->addViolation();
        }
    }
}
