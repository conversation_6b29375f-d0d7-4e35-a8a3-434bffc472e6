<?php

namespace App\Validator\Constraints;

use Symfony\Component\Validator\Constraints as Assert;

class Constraints
{
    public static function allowExtraFields(Assert\Collection $constraints): Assert\Collection
    {
        $constraints->allowExtraFields = true;

        foreach ($constraints->fields as $field) {
            foreach ($field->constraints as $childField) {
                if ($childField instanceof Assert\All) {
                    foreach ($childField->constraints as $field) {
                        self::allowExtraFields($field);
                    }
                }
                if ($childField instanceof Assert\Collection) {
                    self::allowExtraFields($childField);
                }
            }
        }

        return $constraints;
    }
}
