<?php

namespace App\Helpers;

class Log
{
    public static function toFile($fileName, $data, $fileExt = 'txt')
    {
        $path = '/var/www/html/app/var/debug/logs/';
        $logfile = $path . $fileName . '.' . $fileExt;

        if (!file_exists($path)) {
            mkdir($path, 0755, true);
        }

        if (!is_scalar($data)) {
            $data = print_r($data, true);
        }

        file_put_contents(
            $logfile,
            $data . PHP_EOL . PHP_EOL . PHP_EOL,
            FILE_APPEND
        );
    }
}
