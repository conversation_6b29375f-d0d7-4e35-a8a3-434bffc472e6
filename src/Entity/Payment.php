<?php

namespace App\Entity;

use App\Entity\Contract\Price;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @ORM\Entity(repositoryClass="App\Repository\PaymentRepository")
 */
class Payment
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Contract\Price", inversedBy="payments")
     *
     * @ORM\JoinColumn(nullable=false, onDelete="CASCADE")
     *
     * @Groups({"api"})
     */
    private $price;

    /**
     * @ORM\Column(type="decimal", precision=10, scale=2)
     *
     * @Groups({"api"})
     *
     * @Assert\GreaterThan(0)
     */
    private $paid;

    /**
     * @ORM\Column(type="datetime")
     *
     * @Groups({"api"})
     */
    private $datePaid;

    /**
     * @ORM\Column(type="datetime")
     *
     * @Groups({"api"})
     */
    private $createdAt;

    /**
     * @ORM\Column(type="string", length=127, nullable=true)
     *
     * @Groups({"api"})
     */
    private $username;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPrice(): ?Price
    {
        return $this->price;
    }

    public function setPrice(?Price $price): self
    {
        $this->price = $price;

        return $this;
    }

    public function getPaid()
    {
        return $this->paid;
    }

    public function setPaid($paid): self
    {
        $this->paid = $paid;

        return $this;
    }

    public function getDatePaid(): ?DateTimeInterface
    {
        return $this->datePaid;
    }

    public function setDatePaid(DateTimeInterface $datePaid): self
    {
        $this->datePaid = $datePaid;

        return $this;
    }

    public function getCreatedAt(): ?DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUsername(): ?string
    {
        return $this->username;
    }

    public function setUsername(?string $username): self
    {
        $this->username = $username;

        return $this;
    }
}
