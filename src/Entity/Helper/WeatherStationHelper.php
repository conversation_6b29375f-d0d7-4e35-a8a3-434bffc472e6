<?php

namespace App\Entity\Helper;

use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\WeatherStationHelperRepository")
 */
class WeatherStationHelper
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="integer")
     */
    private $stationId;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $stationName;

    /**
     * @ORM\Column(type="integer")
     */
    private $organizationId;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $organizationName;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $organizationIdentNumber;

    /**
     * @ORM\Column(type="date", nullable=true)
     */
    private $stationInstallDate;

    /**
     * @ORM\Column(type="date", nullable=true)
     */
    private $stationStartDate;

    /**
     * @ORM\Column(type="date", nullable=true)
     */
    private $stationEndDate;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $stationPeriod;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getStationId(): ?int
    {
        return $this->stationId;
    }

    public function setStationId(int $stationId): self
    {
        $this->stationId = $stationId;

        return $this;
    }

    public function getStationName(): ?string
    {
        return $this->stationName;
    }

    public function setStationName(string $stationName): self
    {
        $this->stationName = $stationName;

        return $this;
    }

    public function getOrganizationId(): ?int
    {
        return $this->organizationId;
    }

    public function setOrganizationId(int $organizationId): self
    {
        $this->organizationId = $organizationId;

        return $this;
    }

    public function getOrganizationName(): ?string
    {
        return $this->organizationName;
    }

    public function setOrganizationName(string $organizationName): self
    {
        $this->organizationName = $organizationName;

        return $this;
    }

    public function getOrganizationIdentNumber(): ?string
    {
        return $this->organizationIdentNumber;
    }

    public function setOrganizationIdentNumber(?string $organizationIdentNumber): self
    {
        $this->organizationIdentNumber = $organizationIdentNumber;

        return $this;
    }

    public function getStationInstallDate(): ?DateTimeInterface
    {
        return $this->stationInstallDate;
    }

    public function setStationInstallDate(?DateTimeInterface $stationInstallDate): self
    {
        $this->stationInstallDate = $stationInstallDate;

        return $this;
    }

    public function getStationStartDate(): ?DateTimeInterface
    {
        return $this->stationStartDate;
    }

    public function setStationStartDate(?DateTimeInterface $stationStartDate): self
    {
        $this->stationStartDate = $stationStartDate;

        return $this;
    }

    public function getStationEndDate(): ?DateTimeInterface
    {
        return $this->stationEndDate;
    }

    public function setStationEndDate(?DateTimeInterface $stationEndDate): self
    {
        $this->stationEndDate = $stationEndDate;

        return $this;
    }

    public function getStationPeriod(): ?string
    {
        return $this->stationPeriod;
    }

    public function setStationPeriod(string $stationPeriod): self
    {
        $this->stationPeriod = $stationPeriod;

        return $this;
    }
}
