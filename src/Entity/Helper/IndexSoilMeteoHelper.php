<?php

namespace App\Entity\Helper;

use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\IndexSoilMeteoHelperRepository")
 *
 * @ORM\Table(name="index_soil_meteo_helper")
 */
class IndexSoilMeteoHelper
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(name="row_number", type="bigint")
     */
    private $rowNumber;

    /**
     * @ORM\Column(name="organization", type="string", length=255)
     */
    private $organization;

    /**
     * @ORM\Column(name="organization_id", type="integer")
     */
    private $organizationId;

    /**
     * @ORM\Column(name="period", type="string", length=255)
     */
    private $period;

    /**
     * @ORM\Column(name="type", type="string", length=255)
     */
    private $type;

    /**
     * @ORM\Column(name="year", type="integer")
     */
    private $year;

    /**
     * @ORM\Column(name="treatment", type="string", length=255)
     */
    private $treatment;

    /**
     * @ORM\Column(name="orders_plots", type="json")
     */
    private $ordersPlots = [];

    /**
     * @ORM\Column(name="contract_date", type="datetime")
     */
    private $contractDate;

    /**
     * @ORM\Column(name="area", type="float")
     */
    private $area;

    /**
     * @ORM\Column(name="helper_type", type="string", length=255, nullable=true)
     */
    private $helperType;

    /**
     * @ORM\Column(name="customer_identification", type="string", length=50, nullable=true)
     */
    private $customerIdentification;

    /**
     * @ORM\Column(name="for_delete", type="boolean", nullable=true)
     */
    private $forDelete;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getRowNumber(): ?int
    {
        return $this->rowNumber;
    }

    public function setRowNumber(int $rowNumber): self
    {
        $this->rowNumber = $rowNumber;

        return $this;
    }

    public function getOrganization(): ?string
    {
        return $this->organization;
    }

    public function setOrganization(string $organization): self
    {
        $this->organization = $organization;

        return $this;
    }

    public function getOrganizationId(): ?int
    {
        return $this->organizationId;
    }

    public function setOrganizationId(int $organizationId): self
    {
        $this->organizationId = $organizationId;

        return $this;
    }

    public function getPeriod(): ?string
    {
        return $this->period;
    }

    public function setPeriod(string $period): self
    {
        $this->period = $period;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getYear(): ?int
    {
        return $this->year;
    }

    public function setYear(int $year): self
    {
        $this->year = $year;

        return $this;
    }

    public function getTreatment(): ?string
    {
        return $this->treatment;
    }

    public function setTreatment(string $treatment): self
    {
        $this->treatment = $treatment;

        return $this;
    }

    public function getOrdersPlots(): ?array
    {
        return $this->ordersPlots;
    }

    public function setOrdersPlots(array $ordersPlots): self
    {
        $this->ordersPlots = $ordersPlots;

        return $this;
    }

    public function getContractDate(): ?DateTimeInterface
    {
        return $this->contractDate;
    }

    public function setContractDate(DateTimeInterface $contractDate): self
    {
        $this->contractDate = $contractDate;

        return $this;
    }

    public function getArea(): ?float
    {
        return $this->area;
    }

    public function setArea(float $area): self
    {
        $this->area = $area;

        return $this;
    }

    public function getHelperType(): ?string
    {
        return $this->helperType;
    }

    public function setHelperType(?string $helperType): self
    {
        $this->helperType = $helperType;

        return $this;
    }

    public function getCustomerIdentification(): ?string
    {
        return $this->customerIdentification;
    }

    public function setCustomerIdentification(?string $customerIdentification): self
    {
        $this->customerIdentification = $customerIdentification;

        return $this;
    }

    public function getForDelete(): ?bool
    {
        return $this->forDelete;
    }

    public function setForDelete(?bool $forDelete): self
    {
        $this->forDelete = $forDelete;

        return $this;
    }
}
