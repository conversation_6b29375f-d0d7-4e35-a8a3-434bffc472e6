<?php

namespace App\Entity;

use App\Entity\Protocol\ProtocolPackageField;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\ProtocolRepository")
 */
class Protocol
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="datetime")
     */
    private $date;

    /**
     * @ORM\Column(type="integer")
     */
    private $user_id;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Protocol\ProtocolPackageField", mappedBy="protocol", orphanRemoval=true)
     */
    private $protocolPackageFields;

    public function __construct()
    {
        $this->protocolPackageFields = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDate(): ?DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getUserId(): ?int
    {
        return $this->user_id;
    }

    public function setUserId(int $user_id): self
    {
        $this->user_id = $user_id;

        return $this;
    }

    /**
     * @return Collection|ProtocolPackageField[]
     */
    public function getProtocolPackageFields(): Collection
    {
        return $this->protocolPackageFields;
    }

    public function addProtocolPackageField(ProtocolPackageField $protocolPackageField): self
    {
        if (!$this->protocolPackageFields->contains($protocolPackageField)) {
            $this->protocolPackageFields[] = $protocolPackageField;
            $protocolPackageField->setProtocolId($this);
        }

        return $this;
    }

    public function removeProtocolPackageField(ProtocolPackageField $protocolPackageField): self
    {
        if ($this->protocolPackageFields->contains($protocolPackageField)) {
            $this->protocolPackageFields->removeElement($protocolPackageField);
            // set the owning side to null (unless already changed)
            if ($protocolPackageField->getProtocolId() === $this) {
                $protocolPackageField->setProtocolId(null);
            }
        }

        return $this;
    }
}
