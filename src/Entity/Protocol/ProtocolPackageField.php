<?php

namespace App\Entity\Protocol;

use App\Entity\Protocol;
use Doctrine\ORM\Mapping as ORM;
use InvalidArgumentException;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

/**
 * @ORM\Entity(repositoryClass="App\Repository\ProtocolPackageFieldRepository")
 *
 * @UniqueEntity(
 *     fields={"protocol", "id", "packageFieldType"}
 * )
 */
class ProtocolPackageField
{
    public const PACKAGE_FIELD_TYPE_SUBSCRIPTION = 'subscription';
    public const PACKAGE_FIELD_TYPE_SERVICE = 'service';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Protocol", inversedBy="protocolPackageFields")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $protocol;

    /**
     * @ORM\Column(type="integer")
     */
    private $packageFieldId;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $packageFieldType;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProtocol(): ?Protocol
    {
        return $this->protocol;
    }

    public function setProtocol(?Protocol $protocol): self
    {
        $this->protocol = $protocol;

        return $this;
    }

    public function getPackageFieldId(): ?int
    {
        return $this->packageFieldId;
    }

    public function setPackageFieldId(int $packageFieldId): self
    {
        $this->packageFieldId = $packageFieldId;

        return $this;
    }

    public function getPackageFieldType(): ?string
    {
        return $this->packageFieldType;
    }

    public function setPackageFieldType(string $packageFieldType): self
    {
        if (!in_array($packageFieldType, [self::PACKAGE_FIELD_TYPE_SUBSCRIPTION, self::PACKAGE_FIELD_TYPE_SERVICE])) {
            throw new InvalidArgumentException("Invalid packageFieldType (types: 'subscription', 'service')! You entered '{$packageFieldType}'. ");
        }
        $this->packageFieldType = $packageFieldType;

        return $this;
    }
}
