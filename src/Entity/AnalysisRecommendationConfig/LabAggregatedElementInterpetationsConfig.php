<?php

namespace App\Entity\AnalysisRecommendationConfig;

use App\Entity\Analysis\LabAnalysisGroupElement;
use App\Entity\ServiceProvider;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\AnalysisRecommendationConfig\LabAggregatedElementInterpetationsConfigRepository")
 */
class LabAggregatedElementInterpetationsConfig
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Analysis\LabAnalysisGroupElement", inversedBy="labAggregatedElementInterpetationsConfigs")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $element;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\ServiceProvider", inversedBy="labAggregatedElementInterpetationsConfigs")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $serviceProvider;

    /**
     * @ORM\Column(name="class_ids", type="integer[]", nullable=true)
     */
    private $classes = [];

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $commentText;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getElement(): ?LabAnalysisGroupElement
    {
        return $this->element;
    }

    public function setElement(?LabAnalysisGroupElement $element): self
    {
        $this->element = $element;

        return $this;
    }

    public function getServiceProvider(): ?ServiceProvider
    {
        return $this->serviceProvider;
    }

    public function setServiceProvider(?ServiceProvider $serviceProvider): self
    {
        $this->serviceProvider = $serviceProvider;

        return $this;
    }

    public function getClasses(): ?array
    {
        return $this->classes;
    }

    public function setClasses(?array $classes): self
    {
        $this->classes = $classes;

        return $this;
    }

    public function getCommentText(): ?string
    {
        return $this->commentText;
    }

    public function setCommentText(string $commentText): self
    {
        $this->commentText = $commentText;

        return $this;
    }
}
