<?php

namespace App\Entity\AnalysisRecommendationConfig;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\AnalysisRecommendationConfig\LabInterpretationClassesConfigRepository")
 */
class LabInterpretationClassesConfig
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=20, nullable=true)
     */
    private $slug;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $description;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\AnalysisRecommendationConfig\LabElementInterpretationsConfig", mappedBy="class", orphanRemoval=true)
     */
    private $labElementInterpretationsConfigs;

    /**
     * @ORM\Column(type="string",  columnDefinition="content_class_enum", length=255, nullable=true)
     */
    private $contentClass;

    public function __construct()
    {
        $this->labElementInterpretationsConfigs = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function setSlug(string $slug): self
    {
        $this->slug = $slug;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    /**
     * @return Collection|LabElementInterpretationsConfig[]
     */
    public function getLabElementInterpretationsConfigs(): Collection
    {
        return $this->labElementInterpretationsConfigs;
    }

    public function addLabElementInterpretationsConfig(LabElementInterpretationsConfig $labElementInterpretationsConfig): self
    {
        if (!$this->labElementInterpretationsConfigs->contains($labElementInterpretationsConfig)) {
            $this->labElementInterpretationsConfigs[] = $labElementInterpretationsConfig;
            $labElementInterpretationsConfig->setClass($this);
        }

        return $this;
    }

    public function removeLabElementInterpretationsConfig(LabElementInterpretationsConfig $labElementInterpretationsConfig): self
    {
        if ($this->labElementInterpretationsConfigs->contains($labElementInterpretationsConfig)) {
            $this->labElementInterpretationsConfigs->removeElement($labElementInterpretationsConfig);
            // set the owning side to null (unless already changed)
            if ($labElementInterpretationsConfig->getClass() === $this) {
                $labElementInterpretationsConfig->setClass(null);
            }
        }

        return $this;
    }

    public function setContentClass(string $contentClass): self
    {
        $this->contentClass = $contentClass;

        return $this;
    }

    public function getContentClass(): ?string
    {
        return $this->contentClass;
    }
}
