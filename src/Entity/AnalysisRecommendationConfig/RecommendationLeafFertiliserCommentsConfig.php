<?php

namespace App\Entity\AnalysisRecommendationConfig;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\AnalysisRecommendationConfig\RecommendationLeafFertiliserCommentsConfigRepository")
 */
class RecommendationLeafFertiliserCommentsConfig
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\AnalysisRecommendationConfig\RecommendationModelsConfig", inversedBy="recommendationLeafFertiliserCommentsConfigs")
     *
     * @ORM\JoinColumn(name="model_id", nullable=false)
     */
    private $model;

    /**
     * @ORM\Column(name="crop_ids", type="integer[]", nullable=true)
     */
    private $crops;

    /**
     * @ORM\Column(type="string", columnDefinition="result_element_enum[]", nullable=false)
     */
    private $resultElements;

    /**
     * @ORM\Column(type="numrange")
     */
    private $range;

    /**
     * @ORM\Column(type="text")
     */
    private $commentText;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getModel(): ?RecommendationModelsConfig
    {
        return $this->model;
    }

    public function setModel(?RecommendationModelsConfig $model): self
    {
        $this->model = $model;

        return $this;
    }

    public function getCrops(): ?array
    {
        return $this->crops;
    }

    public function setCrops(?array $crops): self
    {
        $this->crops = $crops;

        return $this;
    }

    public function getResultElements(): ?array
    {
        return $this->resultElements;
    }

    public function setResultElement(array $resultElements): self
    {
        $this->resultElements = $resultElements;

        return $this;
    }

    public function getRange()
    {
        return $this->range;
    }

    public function setRange($range): self
    {
        $this->range = $range;

        return $this;
    }

    public function getCommentText(): ?string
    {
        return $this->commentText;
    }

    public function setCommentText(string $commentText): self
    {
        $this->commentText = $commentText;

        return $this;
    }
}
