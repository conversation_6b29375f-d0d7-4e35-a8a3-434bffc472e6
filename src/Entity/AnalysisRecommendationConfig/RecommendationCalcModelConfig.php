<?php

namespace App\Entity\AnalysisRecommendationConfig;

use App\Entity\Analysis\LabAnalysisGroupElement;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\AnalysisRecommendationConfig\RecommendationCalcModelConfigRepository")
 */
class RecommendationCalcModelConfig
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(name="calc_model_id", type="integer", nullable=false)
     */
    private $calcModel;

    /**
     * @ORM\Column(type="string", columnDefinition="result_element_enum", nullable=false)
     */
    private $resultElement;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Analysis\LabAnalysisGroupElement")
     *
     * @ORM\JoinColumn(name="element_id", nullable=true)
     */
    private $element;

    /**
     * @ORM\Column(type="string[]", columnDefinition="recommendation_crop_model_parameter_enum[]", nullable=true)
     */
    private $params;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $visualOrder;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCalcModel(): ?int
    {
        return $this->calcModel;
    }

    public function setCalcModel(int $calcModel): self
    {
        $this->calcModel = $calcModel;

        return $this;
    }

    public function getResultElement(): ?string
    {
        return $this->resultElement;
    }

    public function setResultElement(string $resultElement): self
    {
        $this->resultElement = $resultElement;

        return $this;
    }

    public function getElement(): ?LabAnalysisGroupElement
    {
        return $this->element;
    }

    public function setElement(?LabAnalysisGroupElement $element): self
    {
        $this->element = $element;

        return $this;
    }

    public function getParams(): ?array
    {
        return $this->params;
    }

    public function setParams(?array $params): self
    {
        $this->params = $params;

        return $this;
    }

    public function getVisualOrder(): ?int
    {
        return $this->visualOrder;
    }

    public function setVisualOrder(?int $visualOrder): self
    {
        $this->visualOrder = $visualOrder;

        return $this;
    }
}
