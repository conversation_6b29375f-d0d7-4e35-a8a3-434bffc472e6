<?php

namespace App\Entity\AnalysisRecommendationConfig;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\AnalysisRecommendationConfig\RecommendationCropModelConfigValuesRepository")
 */
class RecommendationCropModelConfigValues
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\AnalysisRecommendationConfig\RecommendationModelsConfig", inversedBy="recommendationCropModelConfigValues")
     *
     * @ORM\JoinColumn(name="model_id", nullable=false)
     */
    private $model;

    /**
     * @ORM\Column(name="crop_ids", type="integer[]")
     */
    private $crops;

    /**
     * @ORM\Column(type="string", columnDefinition="recommendation_crop_model_parameter_enum", nullable=false)
     */
    private $parameter;

    /**
     * @ORM\Column(type="float", nullable=true)
     */
    private $value;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getModel(): ?RecommendationModelsConfig
    {
        return $this->model;
    }

    public function setModel(?RecommendationModelsConfig $model): self
    {
        $this->model = $model;

        return $this;
    }

    public function getCrops()
    {
        return $this->crops;
    }

    public function setCrops($crops): self
    {
        $this->crops = $crops;

        return $this;
    }

    public function getParameter(): ?string
    {
        return $this->parameter;
    }

    public function setParameter(string $parameter): self
    {
        $this->parameter = $parameter;

        return $this;
    }

    public function getValue(): ?float
    {
        return $this->value;
    }

    public function setValue(?float $value): self
    {
        $this->value = $value;

        return $this;
    }
}
