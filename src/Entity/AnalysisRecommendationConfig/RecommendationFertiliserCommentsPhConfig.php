<?php

namespace App\Entity\AnalysisRecommendationConfig;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\AnalysisRecommendationConfig\RecommendationFertiliserCommentsPhConfigRepository")
 */
class RecommendationFertiliserCommentsPhConfig
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\AnalysisRecommendationConfig\RecommendationModelsConfig", inversedBy="recommendationFertiliserCommentsPhConfigs")
     *
     * @ORM\JoinColumn(name="model_id", nullable=false)
     */
    private $model;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $fertiliserType;

    /**
     * @ORM\Column(type="string", columnDefinition="result_element_enum", nullable=false)
     */
    private $resultElement;

    /**
     * @ORM\Column(type="numrange")
     */
    private $range;

    /**
     * @ORM\Column(type="text")
     */
    private $commentText;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getModel(): ?RecommendationModelsConfig
    {
        return $this->model;
    }

    public function setModel(?RecommendationModelsConfig $model): self
    {
        $this->model = $model;

        return $this;
    }

    public function getFertiliserType(): ?string
    {
        return $this->fertiliserType;
    }

    public function setFertiliserType(?string $fertiliserType): self
    {
        $this->fertiliserType = $fertiliserType;

        return $this;
    }

    public function getResultElement(): ?string
    {
        return $this->resultElement;
    }

    public function setResultElement(?string $resultElement): self
    {
        $this->resultElement = $resultElement;

        return $this;
    }

    public function getRange()
    {
        return $this->range;
    }

    public function setRange($range): self
    {
        $this->range = $range;

        return $this;
    }

    public function getCommentText(): ?string
    {
        return $this->commentText;
    }

    public function setCommentText(string $commentText): self
    {
        $this->commentText = $commentText;

        return $this;
    }
}
