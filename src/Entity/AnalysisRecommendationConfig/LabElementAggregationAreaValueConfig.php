<?php

namespace App\Entity\AnalysisRecommendationConfig;

use App\Entity\ServiceProvider;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\AnalysisRecommendationConfig\LabElementAggregationAreaValueConfigRepository")
 */
class LabElementAggregationAreaValueConfig
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\OneToOne(targetEntity="App\Entity\ServiceProvider", inversedBy="labElementAggregationAreaValueConfig", cascade={"persist", "remove"})
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $serviceProvider;

    /**
     * @ORM\Column(type="float")
     */
    private $areaTreshold;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getServiceProvider(): ?ServiceProvider
    {
        return $this->serviceProvider;
    }

    public function setServiceProvider(ServiceProvider $serviceProvider): self
    {
        $this->serviceProvider = $serviceProvider;

        return $this;
    }

    public function getAreaTreshold(): ?float
    {
        return $this->areaTreshold;
    }

    public function setAreaTreshold(float $areaTreshold): self
    {
        $this->areaTreshold = $areaTreshold;

        return $this;
    }
}
