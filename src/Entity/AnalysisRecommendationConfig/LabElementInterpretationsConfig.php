<?php

namespace App\Entity\AnalysisRecommendationConfig;

use App\Entity\Analysis\LabAnalysisGroupElement;
use App\Entity\ServiceProvider;
use App\Model\Types\RangeType;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\AnalysisRecommendationConfig\LabElementInterpretationsConfigRepository")
 */
class LabElementInterpretationsConfig
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Analysis\LabAnalysisGroupElement", inversedBy="labElementInterpretationsConfigs")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $element;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\ServiceProvider", inversedBy="labElementInterpretationsConfigs")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $serviceProvider;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\AnalysisRecommendationConfig\LabInterpretationClassesConfig", inversedBy="labElementInterpretationsConfigs")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $class;

    /**
     * @ORM\Column(type="numrange")
     */
    private $range;

    /**
     * @ORM\Column(type="string", length=10, nullable=true)
     */
    private $color;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getElement(): ?LabAnalysisGroupElement
    {
        return $this->element;
    }

    public function setElement(?LabAnalysisGroupElement $element): self
    {
        $this->element = $element;

        return $this;
    }

    public function getServiceProvider(): ?ServiceProvider
    {
        return $this->serviceProvider;
    }

    public function setServiceProvider(?ServiceProvider $serviceProvider): self
    {
        $this->serviceProvider = $serviceProvider;

        return $this;
    }

    public function getClass(): ?LabInterpretationClassesConfig
    {
        return $this->class;
    }

    public function setClass(?LabInterpretationClassesConfig $class): self
    {
        $this->class = $class;

        return $this;
    }

    public function getRange(): RangeType
    {
        return $this->range;
    }

    public function setRange(RangeType $range): self
    {
        $this->range = $range;

        return $this;
    }

    public function getColor(): ?string
    {
        return $this->color;
    }

    public function setColor(?string $color): self
    {
        $this->color = $color;

        return $this;
    }
}
