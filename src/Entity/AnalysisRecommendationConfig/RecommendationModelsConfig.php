<?php

namespace App\Entity\AnalysisRecommendationConfig;

use App\Entity\ServiceProvider;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\AnalysisRecommendationConfig\RecommendationModelsConfigRepository")
 */
class RecommendationModelsConfig
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $name;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\ServiceProvider", inversedBy="recommendationModelsConfigs")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $serviceProvider;

    /**
     * @ORM\Column(name="calc_model_id", type="integer", nullable=false)
     */
    private $calcModel;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\AnalysisRecommendationConfig\RecommendationCropModelConfigValues", mappedBy="model")
     */
    private $recommendationCropModelConfigValues;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\AnalysisRecommendationConfig\RecommendationElementCommentsConfig", mappedBy="model", orphanRemoval=true)
     */
    private $recommendationElementCommentsConfigs;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\AnalysisRecommendationConfig\RecommendationFertiliserCommentsPhConfig", mappedBy="model", orphanRemoval=true)
     */
    private $recommendationFertiliserCommentsPhConfigs;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\AnalysisRecommendationConfig\RecommendationLeafFertiliserCommentsConfig", mappedBy="model", orphanRemoval=true)
     */
    private $recommendationLeafFertiliserCommentsConfigs;

    public function __construct()
    {
        $this->recommendationCropModelConfigValues = new ArrayCollection();
        $this->recommendationElementCommentsConfigs = new ArrayCollection();
        $this->recommendationFertiliserCommentsPhConfigs = new ArrayCollection();
        $this->recommendationLeafFertiliserCommentsConfigs = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getServiceProvider(): ?ServiceProvider
    {
        return $this->serviceProvider;
    }

    public function setServiceProvider(?ServiceProvider $serviceProvider): self
    {
        $this->serviceProvider = $serviceProvider;

        return $this;
    }

    public function getCalcModel(): ?int
    {
        return $this->calcModel;
    }

    public function setCalcModel(int $calcModel): self
    {
        $this->calcModel = $calcModel;

        return $this;
    }

    /**
     * @return Collection|RecommendationCropModelConfigValues[]
     */
    public function getRecommendationCropModelConfigValues(): Collection
    {
        return $this->recommendationCropModelConfigValues;
    }

    public function addMetaElementsGroup(RecommendationCropModelConfigValues $recommendationCropModelConfigValue): self
    {
        if (!$this->recommendationCropModelConfigValues->contains($recommendationCropModelConfigValue)) {
            $this->recommendationCropModelConfigValues[] = $recommendationCropModelConfigValue;
            $recommendationCropModelConfigValue->setModel($this);
        }

        return $this;
    }

    public function removeMetaElementsGroup(RecommendationCropModelConfigValues $recommendationCropModelConfigValue): self
    {
        if ($this->recommendationCropModelConfigValues->contains($recommendationCropModelConfigValue)) {
            $this->recommendationCropModelConfigValues->removeElement($recommendationCropModelConfigValue);
            // set the owning side to null (unless already changed)
            if ($recommendationCropModelConfigValue->getModel() === $this) {
                $recommendationCropModelConfigValue->setModel(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|RecommendationElementCommentsConfig[]
     */
    public function getRecommendationElementCommentsConfigs(): Collection
    {
        return $this->recommendationElementCommentsConfigs;
    }

    public function addRecommendationElementCommentsConfig(RecommendationElementCommentsConfig $recommendationElementCommentsConfig): self
    {
        if (!$this->recommendationElementCommentsConfigs->contains($recommendationElementCommentsConfig)) {
            $this->recommendationElementCommentsConfigs[] = $recommendationElementCommentsConfig;
            $recommendationElementCommentsConfig->setModel($this);
        }

        return $this;
    }

    public function removeRecommendationElementCommentsConfig(RecommendationElementCommentsConfig $recommendationElementCommentsConfig): self
    {
        if ($this->recommendationElementCommentsConfigs->contains($recommendationElementCommentsConfig)) {
            $this->recommendationElementCommentsConfigs->removeElement($recommendationElementCommentsConfig);
            // set the owning side to null (unless already changed)
            if ($recommendationElementCommentsConfig->getModel() === $this) {
                $recommendationElementCommentsConfig->setModel(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|RecommendationFertiliserCommentsPhConfig[]
     */
    public function getRecommendationFertiliserCommentsPhConfigs(): Collection
    {
        return $this->recommendationFertiliserCommentsPhConfigs;
    }

    public function addRecommendationFertiliserCommentsPhConfig(RecommendationFertiliserCommentsPhConfig $recommendationFertiliserCommentsPhConfig): self
    {
        if (!$this->recommendationFertiliserCommentsPhConfigs->contains($recommendationFertiliserCommentsPhConfig)) {
            $this->recommendationFertiliserCommentsPhConfigs[] = $recommendationFertiliserCommentsPhConfig;
            $recommendationFertiliserCommentsPhConfig->setModel($this);
        }

        return $this;
    }

    public function removeRecommendationFertiliserCommentsPhConfig(RecommendationFertiliserCommentsPhConfig $recommendationFertiliserCommentsPhConfig): self
    {
        if ($this->recommendationFertiliserCommentsPhConfigs->contains($recommendationFertiliserCommentsPhConfig)) {
            $this->recommendationFertiliserCommentsPhConfigs->removeElement($recommendationFertiliserCommentsPhConfig);
            // set the owning side to null (unless already changed)
            if ($recommendationFertiliserCommentsPhConfig->getModel() === $this) {
                $recommendationFertiliserCommentsPhConfig->setModel(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|RecommendationLeafFertiliserCommentsConfig[]
     */
    public function getRecommendationLeafFertiliserCommentsConfigs(): Collection
    {
        return $this->recommendationLeafFertiliserCommentsConfigs;
    }

    public function addRecommendationLeafFertiliserCommentsConfig(RecommendationLeafFertiliserCommentsConfig $recommendationLeafFertiliserCommentsConfig): self
    {
        if (!$this->recommendationLeafFertiliserCommentsConfigs->contains($recommendationLeafFertiliserCommentsConfig)) {
            $this->recommendationLeafFertiliserCommentsConfigs[] = $recommendationLeafFertiliserCommentsConfig;
            $recommendationLeafFertiliserCommentsConfig->setModel($this);
        }

        return $this;
    }

    public function removeRecommendationLeafFertiliserCommentsConfig(RecommendationLeafFertiliserCommentsConfig $recommendationLeafFertiliserCommentsConfig): self
    {
        if ($this->recommendationLeafFertiliserCommentsConfigs->contains($recommendationLeafFertiliserCommentsConfig)) {
            $this->recommendationLeafFertiliserCommentsConfigs->removeElement($recommendationLeafFertiliserCommentsConfig);
            // set the owning side to null (unless already changed)
            if ($recommendationLeafFertiliserCommentsConfig->getModel() === $this) {
                $recommendationLeafFertiliserCommentsConfig->setModel(null);
            }
        }

        return $this;
    }
}
