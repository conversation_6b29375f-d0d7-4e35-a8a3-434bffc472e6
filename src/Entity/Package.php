<?php

namespace App\Entity;

use App\Entity\Analysis\LabAnalysisPackageGroup;
use App\Entity\Contract\Service;
use App\Entity\Contract\ServicePackage;
use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Navigation\PackageMainNavigation;
use App\Entity\Package\SamplingType;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @ORM\Entity(repositoryClass="App\Repository\PackageRepository")
 */
class Package implements IProvided
{
    // Package type constants
    public const AB_HUMUS = 'AB Humus';
    public const MAPPING_3D = '3D mapping';
    public const AB_ISO_CONTROL = 'AB ISO control';
    public const WEATHER_STATIONS = 'Weather stations';
    public const AB_LEAF_SAMPLES = 'AB Leaf samples';
    public const AB_VRA_FULL = 'AB VRA full';
    public const IRRIGATION_MANAGEMENT = 'Irrigation Management';
    public const FARM_TRACK = 'Farm Track';
    public const TF_CONNECT = 'TF connect';
    public const AB_ISO_FULL = 'AB ISO full';
    public const WEATHER_DATA = 'Weather data';
    public const INDEX_VRA_N = 'Index VRA-N';
    public const AB_VRA_CONTROL = 'AB VRA control';
    public const SATELLITE_IMAGERY = 'Satellite imagery';
    public const ADMINISTRATIVE_MAP = 'Administrative map';

    // Integration type constants

    public const INTEGRATION_TYPE_TECHNOFARM = 'TF';
    public const INTEGRATION_TYPE_FARMTRACK = 'FT';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer", nullable=false)
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Package")
     *
     * @ORM\JoinColumn(name="parent_id", nullable=true, onDelete="SET NULL")
     *
     * @Groups({"api"})
     */
    private $parent;

    /**
     * @Assert\NotBlank
     *
     * @ORM\Column(type="string", length=30)
     *
     * @Groups({"api"})
     */
    private $slug;

    /**
     * @ORM\ManyToMany(targetEntity="App\Entity\Service", mappedBy="packages")
     *
     * @Groups({"api"})
     */
    private $services;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Contract\SubscriptionPackage", mappedBy="package", orphanRemoval=true)
     */
    private $subscriptionPackages;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Contract\ServicePackage", mappedBy="package", orphanRemoval=true)
     */
    private $servicePackages;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\ServiceProvider")
     */
    private $serviceProvider;

    /**
     * @ORM\Column(type="integer", nullable=false)
     */
    private int $serviceProviderId;

    /**
     * @ORM\Column(type="boolean", options={"default" = true})
     */
    private $isActive;

    /**
     * @ORM\ManyToMany(targetEntity="App\Entity\Contract\Service")
     */
    private $contracts;

    /**
     * @ORM\Column(type="boolean", nullable=true, options={"default" = false})
     *
     * @Groups({"api"})
     */
    private $containFields;

    /**
     * @ORM\Column(type="string", length=10, nullable=true)
     *
     * @Groups({"api"})
     */
    private $slugShort;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Analysis\LabAnalysisPackageGroup", mappedBy="package")
     */
    private $labAnalysisPackageGroups;

    /**
     * @ORM\Column(type="boolean", nullable=true, options={"default" = false})
     *
     * @Groups({"api"})
     */
    private $isSampling;

    /**
     * @ORM\ManyToMany(targetEntity="App\Entity\Package\SamplingType", inversedBy="package")
     *
     * @ORM\JoinTable(name="package_sampling_type")
     *
     * @Groups({"api"})
     */
    private $samplingTypes;

    /**
     * @ORM\Column(type="boolean", nullable=true, options={"default" = false})
     *
     * @Groups({"api"})
     */
    private $hasStation;

    /**
     * @ORM\Column(type="string", nullable=true, columnDefinition="integration_type_enum")
     *
     * @Groups({"api"})
     */
    private $integration;

    /**
     * @ORM\Column(name="is_vra", type="boolean", nullable=true, options={"default" = false})
     *
     * @Groups({"api"})
     */
    private $isVRA;

    /**
     * @ORM\Column(type="boolean", nullable=true, options={"default" = false})
     *
     * @Groups({"api"})
     */
    private $isFullSampling;

    /**
     * @ORM\Column(type="boolean", nullable=true, options={"default" = false})
     *
     * @Groups({"api"})
     */
    private $isSatellite;

    /**
     * @ORM\OneToMany(targetEntity=PackageMainNavigation::class, mappedBy="package")
     */
    private $packageMainNavigations;

    /**
     * @ORM\Column(type="boolean", nullable=false, options={"default" = false})
     *
     * @Groups({"api"})
     */
    private $isCountable;

    /**
     * @ORM\Column(type="jsonb", nullable=false, options={"default" = "{""color"": ""#000000"", ""backgroundColor"": ""#fafafa""}"})
     *
     * @Groups({"api"})
     */
    private $style;

    /**
     * @ORM\Column(type="jsonb", nullable=true)
     *
     * @Groups({"api"})
     */
    private $attributes = [];

    public function __construct()
    {
        $this->services = new ArrayCollection();
        $this->subscriptionPackages = new ArrayCollection();
        $this->servicePackages = new ArrayCollection();
        $this->isActive = true;
        $this->contracts = new ArrayCollection();
        $this->labAnalysisPackageGroups = new ArrayCollection();
        $this->samplingTypes = new ArrayCollection();
        $this->packageMainNavigations = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getParent(): ?self
    {
        return $this->parent;
    }

    public function setParent(Package $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function setSlug(string $slug): self
    {
        $this->slug = $slug;

        return $this;
    }

    /**
     * @return Collection|Service[]
     */
    public function getServices(): Collection
    {
        return $this->services;
    }

    public function addService(Service $service): self
    {
        if (!$this->services->contains($service)) {
            $this->services[] = $service;
            $service->addPackage($this);
        }

        return $this;
    }

    public function removeService(Service $service): self
    {
        if ($this->services->contains($service)) {
            $this->services->removeElement($service);
            $service->removePackage($this);
        }

        return $this;
    }

    /**
     * @return Collection|SubscriptionPackage[]
     */
    public function getSubscriptionPackages(): Collection
    {
        return $this->subscriptionPackages;
    }

    /**
     * @return Collection|ServicePackage[]
     */
    public function getServicePackages(): Collection
    {
        return $this->subscriptionPackages;
    }

    public function addSubscriptionPackage(SubscriptionPackage $subscriptionPackage): self
    {
        if (!$this->subscriptionPackages->contains($subscriptionPackage)) {
            $this->subscriptionPackages[] = $subscriptionPackage;
            $subscriptionPackage->setPackage($this);
        }

        return $this;
    }

    public function removeSubscriptionPackage(SubscriptionPackage $subscriptionPackage): self
    {
        if ($this->subscriptionPackages->contains($subscriptionPackage)) {
            $this->subscriptionPackages->removeElement($subscriptionPackage);
            // set the owning side to null (unless already changed)
            if ($subscriptionPackage->getPackage() === $this) {
                $subscriptionPackage->setPackage(null);
            }
        }

        return $this;
    }

    public function addServicePackage(ServicePackage $servicePackage): self
    {
        if (!$this->servicePackages->contains($servicePackage)) {
            $this->servicePackages[] = $servicePackage;
            $servicePackage->setPackage($this);
        }

        return $this;
    }

    public function removeServicePackage(ServicePackage $servicePackage): self
    {
        if ($this->servicePackages->contains($servicePackage)) {
            $this->servicePackages->removeElement($servicePackage);
            // set the owning side to null (unless already changed)
            if ($servicePackage->getPackage() === $this) {
                $servicePackage->setPackage(null);
            }
        }

        return $this;
    }

    public function getServiceProvider(): ?ServiceProvider
    {
        return $this->serviceProvider;
    }

    public function setServiceProvider(?ServiceProvider $serviceProvider): self
    {
        $this->serviceProvider = $serviceProvider;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getIsActive(): bool
    {
        return $this->isActive;
    }

    /**
     * @param mixed $isActive
     */
    public function setIsValid(bool $isActive): self
    {
        $this->isActive = $isActive;

        return $this;
    }

    /**
     * @return Collection|Service[]
     */
    public function getContracts(): Collection
    {
        return $this->contracts;
    }

    public function addContract(Service $contract): self
    {
        if (!$this->contracts->contains($contract)) {
            $this->contracts[] = $contract;
        }

        return $this;
    }

    public function removeContract(Service $contract): self
    {
        if ($this->contracts->contains($contract)) {
            $this->contracts->removeElement($contract);
        }

        return $this;
    }

    public function getContainFields(): ?bool
    {
        return $this->containFields;
    }

    public function setContainFields(?bool $containFields): self
    {
        $this->containFields = $containFields;

        return $this;
    }

    public function getSlugShort(): ?string
    {
        return $this->slugShort;
    }

    public function setSlugShort(?string $slugShort): self
    {
        $this->slugShort = $slugShort;

        return $this;
    }

    /**
     * @return Collection|LabAnalysisPackageGroup[]
     */
    public function getLabAnalysisPackageGroups(): Collection
    {
        return $this->labAnalysisPackageGroups;
    }

    public function addLabAnalysisPackageGroup(LabAnalysisPackageGroup $labAnalysisPackageGroup): self
    {
        if (!$this->labAnalysisPackageGroups->contains($labAnalysisPackageGroup)) {
            $this->labAnalysisPackageGroups[] = $labAnalysisPackageGroup;
            $labAnalysisPackageGroup->setPackage($this);
        }

        return $this;
    }

    public function removeLabAnalysisPackageGroup(LabAnalysisPackageGroup $labAnalysisPackageGroup): self
    {
        if ($this->labAnalysisPackageGroups->contains($labAnalysisPackageGroup)) {
            $this->labAnalysisPackageGroups->removeElement($labAnalysisPackageGroup);
            // set the owning side to null (unless already changed)
            if ($labAnalysisPackageGroup->getPackage() === $this) {
                $labAnalysisPackageGroup->setPackage(null);
            }
        }

        return $this;
    }

    /**
     *     * @return mixed
     */
    public function getIsSampling()
    {
        return $this->isSampling;
    }

    public function setIsSampling($isSampling): void
    {
        $this->isSampling = $isSampling;
    }

    /**
     * @return Collection|SamplingType[]
     */
    public function getSamplingTypes(): Collection
    {
        return $this->samplingTypes;
    }

    public function addSamplingType(SamplingType $samplingType): self
    {
        if (!$this->samplingTypes->contains($samplingType)) {
            $this->samplingTypes[] = $samplingType;
        }

        return $this;
    }

    public function clearSamplingTypes(): self
    {
        $this->samplingTypes = new ArrayCollection();

        return $this;
    }

    public function getHasStation(): ?bool
    {
        return $this->hasStation;
    }

    public function setHasStation(?bool $hasStation): self
    {
        $this->hasStation = $hasStation;

        return $this;
    }

    public function getIntegration(): ?string
    {
        return $this->integration;
    }

    public function setIntegration(?string $integration): self
    {
        $this->integration = $integration;

        return $this;
    }

    /**
     *     * @return bool
     */
    public function getIsVRA()
    {
        return $this->isVRA;
    }

    /**
     * @param bool $isVRA
     */
    public function setIsVRA($isVRA): void
    {
        $this->isSampling = $isVRA;
    }

    public function getIsFullSampling()
    {
        return $this->isFullSampling;
    }

    public function setIsFullSampling($isFullSampling): void
    {
        $this->isFullSampling = $isFullSampling;
    }

    public function getIsSatellite()
    {
        return $this->isSatellite;
    }

    public function setIsSatellite($isSatellite): void
    {
        $this->isSatellite = $isSatellite;
    }

    /**
     * @return Collection<int, PackageMainNavigation>
     */
    public function getPackageMainNavigations(): Collection
    {
        return $this->packageMainNavigations;
    }

    public function addPackageMainNavigation(PackageMainNavigation $packageMainNavigation): self
    {
        if (!$this->packageMainNavigations->contains($packageMainNavigation)) {
            $this->packageMainNavigations[] = $packageMainNavigation;
            $packageMainNavigation->setPackage($this);
        }

        return $this;
    }

    public function removePackageMainNavigation(PackageMainNavigation $packageMainNavigation): self
    {
        if ($this->packageMainNavigations->removeElement($packageMainNavigation)) {
            // set the owning side to null (unless already changed)
            if ($packageMainNavigation->getPackage() === $this) {
                $packageMainNavigation->setPackage(null);
            }
        }

        return $this;
    }

    public function getStyle(): array
    {
        return $this->style;
    }

    public function setStyle(array $style): self
    {
        $this->style = $style;

        return $this;
    }

    public function getAttributes(): ?array
    {
        return $this->attributes;
    }

    public function setAttributes($attributes): self
    {
        $this->attributes = $attributes;

        return $this;
    }

    public function getIsCountable(): bool
    {
        return $this->isCountable;
    }

    public function setIsCountable(bool $isCountable): void
    {
        $this->isCountable = $isCountable;
    }

    /**
     * @return int
     */
    public function getServiceProviderId()
    {
        return $this->serviceProviderId;
    }
}
