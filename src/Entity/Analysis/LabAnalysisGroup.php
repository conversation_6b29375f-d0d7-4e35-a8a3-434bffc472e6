<?php

namespace App\Entity\Analysis;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Analysis\LabAnalysisGroupRepository")
 */
class LabAnalysisGroup
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=63)
     */
    private $name;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Analysis\LabAnalysisPackageGroup", mappedBy="labAnalysisGroup", cascade={"persist", "remove"})
     */
    private $labAnalysisPackageGroups;

    public function __construct()
    {
        $this->labAnalysisPackageGroups = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection|LabAnalysisPackageGroup[]
     */
    public function getLabAnalysisPackageGroups(): Collection
    {
        return $this->labAnalysisPackageGroups;
    }
}
