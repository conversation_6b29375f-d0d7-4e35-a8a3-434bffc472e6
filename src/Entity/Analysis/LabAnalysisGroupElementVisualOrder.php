<?php

namespace App\Entity\Analysis;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass="App\Repository\LabAnalysisGroupElementVisualOrderRepository")
 */
class LabAnalysisGroupElementVisualOrder
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Analysis\LabAnalysisGroupElement", inversedBy="labAnalysisGroupElementVisualOrders")
     *
     * @ORM\JoinColumn(nullable=false)
     *
     * @Groups({"api"})
     */
    private $labAnalysisGroupElement;

    /**
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $serviceProviderId;

    /**
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $visualOrder;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLabAnalysisGroupElement(): ?LabAnalysisGroupElement
    {
        return $this->labAnalysisGroupElement;
    }

    public function setLabAnalysisGroupElement(?LabAnalysisGroupElement $labAnalysisGroupElement): self
    {
        $this->labAnalysisGroupElement = $labAnalysisGroupElement;

        return $this;
    }

    public function getServiceProviderId(): ?int
    {
        return $this->serviceProviderId;
    }

    public function setServiceProviderId(int $serviceProviderId): self
    {
        $this->serviceProviderId = $serviceProviderId;

        return $this;
    }

    public function getVisualOrder(): ?int
    {
        return $this->visualOrder;
    }

    public function setVisualOrder(int $visualOrder): self
    {
        $this->visualOrder = $visualOrder;

        return $this;
    }
}
