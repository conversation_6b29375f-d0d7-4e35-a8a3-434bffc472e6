<?php

namespace App\Entity\Analysis;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Analysis\LabElementsCalculationsRepository")
 */
class LabElementsCalculations
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(name="element", type="string", columnDefinition="elements_enum", nullable=false)
     */
    private $element;

    /**
     * @ORM\Column(type="float")
     */
    private $coefficient;

    /**
     * @ORM\Column(type="string", length=27)
     */
    private $operation;

    /**
     * @ORM\Column(type="string", length=27, nullable=true)
     */
    private $templateColumn;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getElement(): ?string
    {
        return $this->element;
    }

    public function setElement(string $element): self
    {
        $this->element = $element;

        return $this;
    }

    public function getCoefficient(): ?float
    {
        return $this->coefficient;
    }

    public function setCoefficient(float $coefficient): self
    {
        $this->coefficient = $coefficient;

        return $this;
    }

    public function getOperation(): ?string
    {
        return $this->operation;
    }

    public function setOperation(string $operation): self
    {
        $this->operation = $operation;

        return $this;
    }

    public function getTemplateColumn(): ?string
    {
        return $this->templateColumn;
    }

    public function setTemplateColumn(?string $templateColumn): self
    {
        $this->templateColumn = $templateColumn;

        return $this;
    }
}
