<?php

namespace App\Entity\Analysis;

use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Analysis\LabElementGroupRepository")
 *
 * @Gedmo\Loggable
 */
class LabElementGroup
{
    public const WORKFLOW_NAME = 'lab_element_group_state';

    public const TRANSITION_IN_PROGRESS = 'in_progress';
    public const TRANSITION_FOR_APPROVE = 'for_approve';
    public const TRANSITION_FOR_RECOMMENDATION = 'for_recommendation';
    public const TRANSITION_IN_PROGRESS_BACK = 'in_progress_back';
    public const TRANSITION_DELIVERED = 'delivered';

    public const STATE_FOR_RECOMMENDATION = 'For recommendation';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $packageId;

    /**
     * @ORM\Column(type="string", length=63)
     *
     * @Groups({"api"})
     */
    private $packageType;

    /**
     * @ORM\Column(type="string", length=63, nullable=true)
     *
     * @Groups({"api"})
     */
    private $plotUuid;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Analysis\LabAnalysisPackageGroup", inversedBy="labElementGroups")
     *
     * @Groups({"api"})
     */
    private $labAnalysisPackageGroup;

    /**
     * @ORM\Column(name="state", type="string", columnDefinition="element_group_states_enum", nullable=false, options={"default" = "Pending"})
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $state = 'Pending';

    /**
     * @ORM\Column(type="datetime")
     */
    private $stateUpdatedAt;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Analysis\LabElementsResults", mappedBy="labElementGroup", cascade={"persist"})
     *
     * @Groups({"api"})
     */
    private $labElementsResults;

    public function __construct()
    {
        $this->labElementsResults = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPackageId(): ?int
    {
        return $this->packageId;
    }

    public function setPackageId(int $packageId): self
    {
        $this->packageId = $packageId;

        return $this;
    }

    public function getPackageType(): ?string
    {
        return $this->packageType;
    }

    public function setPackageType(string $packageType): self
    {
        $this->packageType = $packageType;

        return $this;
    }

    public function getPlotUuid(): ?string
    {
        return $this->plotUuid;
    }

    public function setPlotUuid(string $plotUuid): self
    {
        $this->plotUuid = $plotUuid;

        return $this;
    }

    public function getLabAnalysisPackageGroup(): ?LabAnalysisPackageGroup
    {
        return $this->labAnalysisPackageGroup;
    }

    public function setLabAnalysisPackageGroup(?LabAnalysisPackageGroup $labAnalysisPackageGroup): self
    {
        $this->labAnalysisPackageGroup = $labAnalysisPackageGroup;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getStateUpdatedAt(): ?DateTimeInterface
    {
        return $this->stateUpdatedAt;
    }

    public function setStateUpdatedAt(DateTimeInterface $stateUpdatedAt): self
    {
        $this->stateUpdatedAt = $stateUpdatedAt;

        return $this;
    }

    /**
     * @return Collection|LabElementsResults[]
     */
    public function getLabElementsResults(): Collection
    {
        return $this->labElementsResults;
    }

    public function addLabElementsResult(LabElementsResults $labElementsResult): self
    {
        if (!$this->labElementsResults->contains($labElementsResult)) {
            $this->labElementsResults[] = $labElementsResult;
            $labElementsResult->setLabElementGroup($this);
        }

        return $this;
    }

    public function removeLabElementsResult(LabElementsResults $labElementsResult): self
    {
        if ($this->labElementsResults->contains($labElementsResult)) {
            $this->labElementsResults->removeElement($labElementsResult);
            // set the owning side to null (unless already changed)
            if ($labElementsResult->getLabElementGroup() === $this) {
                $labElementsResult->setLabElementGroup(null);
            }
        }

        return $this;
    }
}
