<?php

namespace App\Entity\Analysis;

use App\Entity\Package;
use App\Entity\Package\SamplingType;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Analysis\LabAnalysisPackageGroupRepository")
 */
class LabAnalysisPackageGroup
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Package", inversedBy="labAnalysisPackageGroups")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $package;

    /**
     * @ORM\Column(type="string", length=63)
     */
    private $packageType;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Package\SamplingType")
     */
    private $samplingType;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Analysis\LabAnalysisGroup")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $labAnalysisGroup;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPackage(): ?Package
    {
        return $this->package;
    }

    public function setPackage(?Package $package): self
    {
        $this->package = $package;

        return $this;
    }

    public function getPackageType(): ?string
    {
        return $this->packageType;
    }

    public function setPackageType(string $packageType): self
    {
        $this->packageType = $packageType;

        return $this;
    }

    public function getSamplingType(): SamplingType
    {
        return $this->samplingType;
    }

    public function setSamplingType(SamplingType $samplingType): self
    {
        $this->samplingType = $samplingType;

        return $this;
    }

    public function getLabAnalysisGroup(): ?LabAnalysisGroup
    {
        return $this->labAnalysisGroup;
    }

    public function setLabAnalysisGroup(?LabAnalysisGroup $labAnalysisGroup): self
    {
        $this->labAnalysisGroup = $labAnalysisGroup;

        return $this;
    }
}
