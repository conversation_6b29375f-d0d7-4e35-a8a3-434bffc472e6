<?php

namespace App\Entity\Analysis;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Analysis\MetaElementsGroupsRepository")
 */
class MetaElementsGroups
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(name="element", type="string", columnDefinition="elements_enum", nullable=false)
     */
    private $element;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Analysis\MetaGroups", inversedBy="metaElementsGroups")
     *
     * @ORM\JoinColumn(name="group_id", referencedColumnName="id")
     */
    private $metaGroups;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getElement(): ?string
    {
        return $this->element;
    }

    public function setElement(string $element): self
    {
        $this->element = $element;

        return $this;
    }

    public function getMetaGroups(): ?MetaGroups
    {
        return $this->metaGroups;
    }

    public function setMetaGroups(?MetaGroups $metaGroups): self
    {
        $this->metaGroups = $metaGroups;

        return $this;
    }
}
