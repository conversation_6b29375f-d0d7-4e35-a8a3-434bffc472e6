<?php

namespace App\Entity\Analysis;

use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Analysis\LabElementsResultsRepository")
 *
 * @Gedmo\Loggable
 */
class LabElementsResults
{
    public const WORKFLOW_NAME = 'lab_elements_results_state';

    public const TRANSITION_FOR_ANALYSIS = 'for_analysis';
    public const TRANSITION_ANALYSED = 'analysed';
    public const TRANSITION_APPROVED = 'approved';
    public const TRANSITION_FOR_REANALYSIS = 'for_reanalysis';
    public const TRANSITION_ANALYSED_AGAIN = 'analysed_again';
    public const TRANSITION_NOT_ANALYSED = 'not_analysed';

    public const STATE_FOR_ANALYSIS = 'For Analysis';
    public const STATE_FOR_REANALYSIS = 'For reanalysis';
    public const STATE_ANALYSED = 'Analysed';
    public const STATE_APPROVED = 'Approved';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @ORM\Column(name="element", type="string", columnDefinition="elements_enum", nullable=true)
     *
     * @Groups({"api"})
     */
    private $element;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Analysis\LabElementGroup", inversedBy="labElementsResults")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $labElementGroup;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Analysis\PackageGridPoints", inversedBy="labElementsResults")
     *
     * @ORM\JoinColumn(nullable=false, onDelete="CASCADE")
     */
    private $packageGridPoints;

    /**
     * @ORM\Column(type="float", nullable=true)
     *
     * @Groups({"api"})
     */
    private $value;

    /**
     * @ORM\Column(name="state", type="string", columnDefinition="element_result_states_enum", nullable=true, options={"default" = "Pending"})
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $state = 'Pending';

    /**
     * @ORM\Column(type="datetime")
     */
    private $stateUpdatedAt;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Analysis\LabElementsResultsRaw", inversedBy="labElementsResults")
     */
    private $labElementsResultsRaw;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getElement(): ?string
    {
        return $this->element;
    }

    public function setElement(string $element): self
    {
        $this->element = $element;

        return $this;
    }

    public function getLabElementGroup(): ?LabElementGroup
    {
        return $this->labElementGroup;
    }

    public function setLabElementGroup(?LabElementGroup $labElementGroup): self
    {
        $this->labElementGroup = $labElementGroup;

        return $this;
    }

    public function getPackageGridPoints(): ?PackageGridPoints
    {
        return $this->packageGridPoints;
    }

    public function setPackageGridPoints(?PackageGridPoints $packageGridPoints): self
    {
        $this->packageGridPoints = $packageGridPoints;

        return $this;
    }

    public function getValue(): ?float
    {
        return $this->value;
    }

    public function setValue(?float $value): self
    {
        $this->value = $value;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getStateUpdatedAt(): ?DateTimeInterface
    {
        return $this->stateUpdatedAt;
    }

    public function setStateUpdatedAt(DateTimeInterface $stateUpdatedAt): self
    {
        $this->stateUpdatedAt = $stateUpdatedAt;

        return $this;
    }

    public function getLabElementsResultsRaw(): ?LabElementsResultsRaw
    {
        return $this->labElementsResultsRaw;
    }

    public function setLabElementsResultsRaw(?LabElementsResultsRaw $labElementsResultsRaw): self
    {
        $this->labElementsResultsRaw = $labElementsResultsRaw;

        return $this;
    }
}
