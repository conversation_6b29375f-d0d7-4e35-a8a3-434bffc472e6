<?php

namespace App\Entity\Analysis;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Analysis\MetaGroupsRepository")
 */
class MetaGroups
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"api"})
     */
    private $name;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Analysis\MetaElementsGroups", mappedBy="metaGroups")
     */
    private $metaElementsGroups;

    public function __construct()
    {
        $this->metaElementsGroups = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection|MetaElementsGroups[]
     */
    public function getMetaElementsGroups(): Collection
    {
        return $this->metaElementsGroups;
    }

    public function addMetaElementsGroup(MetaElementsGroups $metaElementsGroup): self
    {
        if (!$this->metaElementsGroups->contains($metaElementsGroup)) {
            $this->metaElementsGroups[] = $metaElementsGroup;
            $metaElementsGroup->setMetaGroups($this);
        }

        return $this;
    }

    public function removeMetaElementsGroup(MetaElementsGroups $metaElementsGroup): self
    {
        if ($this->metaElementsGroups->contains($metaElementsGroup)) {
            $this->metaElementsGroups->removeElement($metaElementsGroup);
            // set the owning side to null (unless already changed)
            if ($metaElementsGroup->getMetaGroups() === $this) {
                $metaElementsGroup->setMetaGroups(null);
            }
        }

        return $this;
    }
}
