<?php

namespace App\Entity\Analysis;

use App\Entity\AnalysisRecommendationConfig\LabAggregatedElementInterpetationsConfig;
use App\Entity\AnalysisRecommendationConfig\LabElementInterpretationsConfig;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Analysis\LabAnalysisGroupElementRepository")
 */
class LabAnalysisGroupElement
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Analysis\LabAnalysisGroup")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $labAnalysisGroup;

    /**
     * @ORM\Column(name="element", type="string", columnDefinition="elements_enum", nullable=false)
     */
    private $element;

    /**
     * @ORM\Column(name="unit", type="string", nullable=true)
     */
    private $unit;

    /**
     * @ORM\Column(name="method", type="string", nullable=true)
     */
    private $method;

    /**
     * @ORM\Column(name="has_soil_map", type="boolean", nullable=true)
     */
    private $hasSoilMap;

    /**
     * @ORM\Column(type="string", length=10, nullable=true)
     */
    private $color;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\AnalysisRecommendationConfig\LabElementInterpretationsConfig", mappedBy="element", orphanRemoval=true)
     */
    private $labElementInterpretationsConfigs;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\AnalysisRecommendationConfig\LabAggregatedElementInterpetationsConfig", mappedBy="element", orphanRemoval=true)
     */
    private $labAggregatedElementInterpetationsConfigs;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Analysis\LabAnalysisGroupElementVisualOrder", mappedBy="labAnalysisGroupElement")
     */
    private $labAnalysisGroupElementVisualOrders;

    public function __construct()
    {
        $this->labElementInterpretationsConfigs = new ArrayCollection();
        $this->labAggregatedElementInterpetationsConfigs = new ArrayCollection();
        $this->labAnalysisGroupElementVisualOrders = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLabAnalysisGroup(): ?LabAnalysisGroup
    {
        return $this->labAnalysisGroup;
    }

    public function setLabAnalysisGroup(?LabAnalysisGroup $labAnalysisGroup): self
    {
        $this->labAnalysisGroup = $labAnalysisGroup;

        return $this;
    }

    public function getElement(): ?string
    {
        return $this->element;
    }

    public function setElement(string $element): self
    {
        $this->element = $element;

        return $this;
    }

    public function geUnit(): ?string
    {
        return $this->unit;
    }

    public function setUnit(string $unit): self
    {
        $this->unit = $unit;

        return $this;
    }

    public function getMethod(): ?string
    {
        return $this->method;
    }

    public function setMethod(string $method): self
    {
        $this->method = $method;

        return $this;
    }

    /**
     * @return Collection|LabElementInterpretationsConfig[]
     */
    public function getLabElementInterpretationsConfigs(): Collection
    {
        return $this->labElementInterpretationsConfigs;
    }

    public function addLabElementInterpretationsConfig(LabElementInterpretationsConfig $labElementInterpretationsConfig): self
    {
        if (!$this->labElementInterpretationsConfigs->contains($labElementInterpretationsConfig)) {
            $this->labElementInterpretationsConfigs[] = $labElementInterpretationsConfig;
            $labElementInterpretationsConfig->setElement($this);
        }

        return $this;
    }

    public function removeLabElementInterpretationsConfig(LabElementInterpretationsConfig $labElementInterpretationsConfig): self
    {
        if ($this->labElementInterpretationsConfigs->contains($labElementInterpretationsConfig)) {
            $this->labElementInterpretationsConfigs->removeElement($labElementInterpretationsConfig);
            // set the owning side to null (unless already changed)
            if ($labElementInterpretationsConfig->getElement() === $this) {
                $labElementInterpretationsConfig->setElement(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|LabAggregatedElementInterpetationsConfig[]
     */
    public function getLabAggregatedElementInterpetationsConfigs(): Collection
    {
        return $this->labAggregatedElementInterpetationsConfigs;
    }

    public function addLabAggregatedElementInterpetationsConfig(LabAggregatedElementInterpetationsConfig $labAggregatedElementInterpetationsConfig): self
    {
        if (!$this->labAggregatedElementInterpetationsConfigs->contains($labAggregatedElementInterpetationsConfig)) {
            $this->labAggregatedElementInterpetationsConfigs[] = $labAggregatedElementInterpetationsConfig;
            $labAggregatedElementInterpetationsConfig->setElement($this);
        }

        return $this;
    }

    public function removeLabAggregatedElementInterpetationsConfig(LabAggregatedElementInterpetationsConfig $labAggregatedElementInterpetationsConfig): self
    {
        if ($this->labAggregatedElementInterpetationsConfigs->contains($labAggregatedElementInterpetationsConfig)) {
            $this->labAggregatedElementInterpetationsConfigs->removeElement($labAggregatedElementInterpetationsConfig);
            // set the owning side to null (unless already changed)
            if ($labAggregatedElementInterpetationsConfig->getElement() === $this) {
                $labAggregatedElementInterpetationsConfig->setElement(null);
            }
        }

        return $this;
    }

    public function getHasSoilMap()
    {
        return $this->hasSoilMap;
    }

    /**
     * @return $this
     */
    public function setHasSoilMap(bool $hasSoilMap): self
    {
        $this->hasSoilMap = $hasSoilMap;

        return $this;
    }

    public function getColor(): ?string
    {
        return $this->color;
    }

    public function setColor(?string $color): self
    {
        $this->color = $color;

        return $this;
    }

    /**
     * @return Collection|LabAnalysisGroupElementVisualOrder[]
     */
    public function getLabAnalysisGroupElementVisualOrders(): Collection
    {
        return $this->labAnalysisGroupElementVisualOrders;
    }

    public function addLabAnalysisGroupElementVisualOrder(LabAnalysisGroupElementVisualOrder $labAnalysisGroupElementVisualOrder): self
    {
        if (!$this->labAnalysisGroupElementVisualOrders->contains($labAnalysisGroupElementVisualOrder)) {
            $this->labAnalysisGroupElementVisualOrders[] = $labAnalysisGroupElementVisualOrder;
            $labAnalysisGroupElementVisualOrder->setLabAnalysisGroupElement($this);
        }

        return $this;
    }

    public function removeLabAnalysisGroupElementVisualOrder(LabAnalysisGroupElementVisualOrder $labAnalysisGroupElementVisualOrder): self
    {
        if ($this->labAnalysisGroupElementVisualOrders->contains($labAnalysisGroupElementVisualOrder)) {
            $this->labAnalysisGroupElementVisualOrders->removeElement($labAnalysisGroupElementVisualOrder);
            // set the owning side to null (unless already changed)
            if ($labAnalysisGroupElementVisualOrder->getLabAnalysisGroupElement() === $this) {
                $labAnalysisGroupElementVisualOrder->setLabAnalysisGroupElement(null);
            }
        }

        return $this;
    }
}
