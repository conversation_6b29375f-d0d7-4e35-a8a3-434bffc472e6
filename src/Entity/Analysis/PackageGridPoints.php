<?php

namespace App\Entity\Analysis;

use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Package\SamplingType;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use <PERSON>ed<PERSON>\Mapping\Annotation as Gedmo;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Analysis\PackageGridPointsRepository")
 *
 * @ORM\Table(indexes={@ORM\Index(name="package_grid_points_lab_number_idx", columns={"lab_number"}), @ORM\Index(name="package_grid_points_package_id_idx", columns={"package_id"})})
 *
 * @Gedmo\Loggable
 */
class PackageGridPoints
{
    public const WORKFLOW_NAME = 'package_grid_points_state';

    public const TRANSITION_PENDING = 'pending';
    public const TRANSITION_NOT_SAMPLED = 'not_sampled';
    public const TRANSITION_FOR_SAMPLING = 'for_sampling';
    public const TRANSITION_SAMPLING = 'sampling';
    public const TRANSITION_SAMPLED = 'sampled';
    public const TRANSITION_RECEIVED_IN_LAB = 'received_in_lab';

    public const STATE_FOR_SAMPLING = 'For sampling';
    public const STATE_NOT_SAMPLED = 'Not sampled';
    public const STATE_PENDING = 'Pending';
    public const STATE_SAMPLED = 'Sampled';
    public const STATE_SAMPLING = 'Sampling';
    public const STATE_RECEIVED_IN_LAB = 'ReceivedInLab';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=63, nullable=true)
     */
    private $pointUuid;

    /**
     * @ORM\Column(type="integer")
     */
    private $sampleId;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Package\SamplingType")
     */
    private $samplingType;

    /**
     * @ORM\Column(type="string", length=63, nullable=true)
     */
    private $plotUuid;

    /**
     * @ORM\Column(type="integer")
     */
    private $packageId;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Contract\SubscriptionPackage")
     */
    private $package;

    /**
     * @ORM\Column(type="string", length=63)
     */
    private $packageType;

    /**
     * @ORM\Column(type="string", length=63, nullable=true)
     */
    private $gridType;

    /**
     * @ORM\Column(type="string", length=127, nullable=true)
     */
    private $barcode;

    /**
     * @ORM\Column(type="string", length=63, nullable=true)
     */
    private $labNumber;

    /**
     * @ORM\Column(name="state", type="string", columnDefinition="point_states_enum", nullable=false, options={"default" = "Pending"})
     *
     * @Gedmo\Versioned
     */
    private $state = 'Pending';

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $stateUpdatedAt;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Analysis\LabElementsResults", mappedBy="packageGridPoints")
     */
    private $labElementsResults;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPointUuid(): ?string
    {
        return $this->pointUuid;
    }

    public function setPointUuid(string $pointUuid): self
    {
        $this->pointUuid = $pointUuid;

        return $this;
    }

    public function getSampleId(): ?int
    {
        return $this->sampleId;
    }

    public function setSampleId(int $sampleId): self
    {
        $this->sampleId = $sampleId;

        return $this;
    }

    public function getSamplingType(): ?SamplingType
    {
        return $this->samplingType;
    }

    public function setSamplingType(SamplingType $samplingType): self
    {
        $this->samplingType = $samplingType;

        return $this;
    }

    public function getPlotUuid(): ?string
    {
        return $this->plotUuid;
    }

    public function setPlotUuid(string $plotUuid): self
    {
        $this->plotUuid = $plotUuid;

        return $this;
    }

    public function getPackageId(): ?int
    {
        return $this->packageId;
    }

    public function setPackageId(int $packageId): self
    {
        $this->packageId = $packageId;

        return $this;
    }

    public function getPackage(): ?SubscriptionPackage
    {
        return $this->package;
    }

    public function setPackage(SubscriptionPackage $package): self
    {
        $this->package = $package;

        return $this;
    }

    public function getPackageType(): ?string
    {
        return $this->packageType;
    }

    public function setPackageType(string $packageType): self
    {
        $this->packageType = $packageType;

        return $this;
    }

    public function getGridType(): ?string
    {
        return $this->gridType;
    }

    public function setGridType(?string $gridType): self
    {
        $this->gridType = $gridType;

        return $this;
    }

    public function getBarcode(): ?string
    {
        return $this->barcode;
    }

    public function setBarcode(?string $barcode): self
    {
        $this->barcode = $barcode;

        return $this;
    }

    public function getLabNumber(): ?string
    {
        return $this->labNumber;
    }

    public function setLabNumber(?string $labNumber): self
    {
        $this->labNumber = $labNumber;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getStateUpdatedAt(): ?DateTimeInterface
    {
        return $this->stateUpdatedAt;
    }

    public function setStateUpdatedAt(?DateTimeInterface $stateUpdatedAt): self
    {
        $this->stateUpdatedAt = $stateUpdatedAt;

        return $this;
    }
}
