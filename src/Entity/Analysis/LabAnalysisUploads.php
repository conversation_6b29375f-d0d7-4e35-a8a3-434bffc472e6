<?php

namespace App\Entity\Analysis;

use App\Entity\ServiceProvider;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Analysis\LabAnalysisUploadsRepository")
 *
 * @Gedmo\Loggable
 */
class LabAnalysisUploads
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $id;

    /**
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $userId;

    /**
     * @ORM\Column(type="datetime")
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $date;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $filePath;

    /**
     * @ORM\Column(name="state", type="string", columnDefinition="analysis_uploads_states_enum", nullable=false, options={"default" = "Success"})
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $state = 'Success';

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $records;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Analysis\LabElementsResultsRaw", mappedBy="labAnalisysUploads")
     */
    private $labElementsResultsRaws;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\ServiceProvider")
     */
    private $serviceProvider;

    public function __construct()
    {
        $this->labElementsResultsRaws = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getDate(): ?DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getFilePath(): ?string
    {
        return $this->filePath;
    }

    public function setFilePath(string $filePath): self
    {
        $this->filePath = $filePath;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getRecords(): ?int
    {
        return $this->records;
    }

    public function setRecords(?int $records): self
    {
        $this->records = $records;

        return $this;
    }

    /**
     * @return Collection|LabElementsResultsRaw[]
     */
    public function getLabElementsResultsRaws(): Collection
    {
        return $this->labElementsResultsRaws;
    }

    public function addLabElementsResultsRaw(LabElementsResultsRaw $labElementsResultsRaw): self
    {
        if (!$this->labElementsResultsRaws->contains($labElementsResultsRaw)) {
            $this->labElementsResultsRaws[] = $labElementsResultsRaw;
            $labElementsResultsRaw->setLabAnalisysUploads($this);
        }

        return $this;
    }

    public function removeLabElementsResultsRaw(LabElementsResultsRaw $labElementsResultsRaw): self
    {
        if ($this->labElementsResultsRaws->contains($labElementsResultsRaw)) {
            $this->labElementsResultsRaws->removeElement($labElementsResultsRaw);
            // set the owning side to null (unless already changed)
            if ($labElementsResultsRaw->getLabAnalisysUploads() === $this) {
                $labElementsResultsRaw->setLabAnalisysUploads(null);
            }
        }

        return $this;
    }

    public function getServiceProvider(): ?ServiceProvider
    {
        return $this->serviceProvider;
    }

    public function setServiceProvider(?ServiceProvider $serviceProvider): self
    {
        $this->serviceProvider = $serviceProvider;

        return $this;
    }
}
