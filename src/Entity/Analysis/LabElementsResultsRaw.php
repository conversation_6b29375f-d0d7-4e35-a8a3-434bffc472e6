<?php

namespace App\Entity\Analysis;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Analysis\LabElementsResultsRawRepository")
 *
 * @ORM\Table(indexes={@ORM\Index(name="lab_elements_results_raw_lab_number_idx", columns={"lab_number"})})
 */
class LabElementsResultsRaw
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=27)
     *
     * @Groups({"api"})
     */
    private $labNumber;

    /**
     * @ORM\Column(name="element", type="string", columnDefinition="elements_enum", nullable=true)
     *
     * @Groups({"api"})
     */
    private $element;

    /**
     * @ORM\Column(type="float")
     *
     * @Groups({"api"})
     */
    private $value;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Analysis\LabAnalysisUploads", inversedBy="labElementsResultsRaws")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $labAnalisysUploads;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Analysis\LabElementsResults", mappedBy="labElementsResultsRaw")
     */
    private $labElementsResults;

    public function __construct()
    {
        $this->labElementsResults = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLabNumber(): ?string
    {
        return $this->labNumber;
    }

    public function setLabNumber(string $labNumber): self
    {
        $this->labNumber = $labNumber;

        return $this;
    }

    public function getElement(): ?string
    {
        return $this->element;
    }

    public function setElement(string $element): self
    {
        $this->element = $element;

        return $this;
    }

    public function getValue(): ?float
    {
        return $this->value;
    }

    public function setValue(float $value): self
    {
        $this->value = $value;

        return $this;
    }

    public function getLabAnalisysUploads(): ?LabAnalysisUploads
    {
        return $this->labAnalisysUploads;
    }

    public function setLabAnalisysUploads(?LabAnalysisUploads $labAnalisysUploads): self
    {
        $this->labAnalisysUploads = $labAnalisysUploads;

        return $this;
    }

    /**
     * @return Collection|LabElementsResults[]
     */
    public function getLabElementsResults(): Collection
    {
        return $this->labElementsResults;
    }

    public function addLabElementsResult(LabElementsResults $labElementsResult): self
    {
        if (!$this->labElementsResults->contains($labElementsResult)) {
            $this->labElementsResults[] = $labElementsResult;
            $labElementsResult->setLabElementsResultsRaw($this);
        }

        return $this;
    }

    public function removeLabElementsResult(LabElementsResults $labElementsResult): self
    {
        if ($this->labElementsResults->contains($labElementsResult)) {
            $this->labElementsResults->removeElement($labElementsResult);
            // set the owning side to null (unless already changed)
            if ($labElementsResult->getLabElementsResultsRaw() === $this) {
                $labElementsResult->setLabElementsResultsRaw(null);
            }
        }

        return $this;
    }
}
