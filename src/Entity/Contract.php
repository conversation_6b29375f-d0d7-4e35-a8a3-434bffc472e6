<?php

namespace App\Entity;

use App\Entity\Contract\Price;
use App\Entity\Contract\ResponsibleUser;
use DateTime;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\DiscriminatorMap;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @ORM\Entity(repositoryClass="App\Repository\ContractRepository")
 *
 * @ORM\InheritanceType("JOINED")
 *
 * @ORM\DiscriminatorColumn(name="type", type="string")
 *
 * @ORM\DiscriminatorMap({
 *     "service" = "App\Entity\Contract\Service",
 *     "subscription" = "App\Entity\Contract\Subscription"
 * })
 *
 * @DiscriminatorMap(typeProperty="type", mapping={"service" = "App\Entity\Contract\Service", "subscription" = "App\Entity\Contract\Subscription"})
 *
 * @ORM\HasLifecycleCallbacks
 *
 * @Gedmo\Loggable
 */
abstract class Contract implements IPackageable, IProvided
{
    public const TYPE_SUBSCRIPTION = 'subscription';
    public const TYPE_SERVICE = 'service';

    public const PACKAGE_STATUS_IN_PROGRESS = 'In progress';
    public const PACKAGE_STATUS_WAITING_FOR_PLOTS = 'Waiting for plots';

    public const STATUS_NEW = 'New';
    public const STATUS_ACTIVE = 'Active';

    public const TYPES = [
        self::TYPE_SUBSCRIPTION,
        self::TYPE_SERVICE,
    ];

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @ORM\Column(name="status", type="string", columnDefinition="contract_statuses_enum", nullable=false, options={"default" = "New"})
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $status = 'New';

    /**
     * @ORM\Column(type="date", nullable=true)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $contractDate;

    /**
     * @Assert\NotBlank
     *
     * @ORM\Column(type="datetime")
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $startDate;

    /**
     * @Assert\NotBlank
     *
     * @ORM\Column(type="datetime")
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $endDate;

    /**
     * @ORM\Column(type="datetime")
     *
     * @Groups({"api"})
     */
    private $createdAt;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     *
     * @Groups({"api"})
     */
    private $modifiedAt;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Currency", inversedBy="contracts", cascade={"persist", "remove"})
     *
     * @ORM\JoinColumn(nullable=false)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $currency;

    /**
     * @Assert\Type("boolean")
     *
     * @ORM\Column(type="boolean")
     *
     * @Groups({"api"})
     */
    private $isAnnex;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Contract")
     *
     * @Groups({"api"})
     */
    private $parent;

    /**
     * @var string
     *
     * @ORM\Column(name="number", type="string", length=50, nullable=true)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $number;

    /**
     * @Assert\NotBlank
     *
     * @Assert\Type("numeric")
     *
     * @Assert\GreaterThanOrEqual(0)
     *
     * @var string
     *
     * @ORM\Column(name="area", type="decimal", precision=10, scale=4, nullable=true)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $area;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\ServiceProvider")
     */
    private $serviceProvider;

    /**
     * @ORM\Column(type="integer", nullable=false)
     */
    private int $serviceProviderId;

    /**
     * @Assert\NotBlank
     *
     * @var string
     *
     * @ORM\Column(name="customer_identification", type="string", length=50, nullable=true)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $customerIdentification;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Contract\ResponsibleUser", mappedBy="contract", orphanRemoval=true, cascade={"persist", "remove"})
     *
     * @Groups({"api"})
     */
    private $responsibleUsers;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Contract\Price", mappedBy="contract", orphanRemoval=true, cascade={"persist", "remove"})
     *
     * @Groups({"api"})
     */
    private $prices;

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $organizationId;

    public function __construct()
    {
        $this->createdAt = new DateTime();
        $this->isAnnex = false;
        $this->responsibleUsers = new ArrayCollection();
        $this->prices = new ArrayCollection();
    }

    abstract public function getPackages(): iterable;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getContractDate(): ?DateTimeInterface
    {
        return $this->contractDate;
    }

    public function setContractDate(DateTimeInterface $contractDate): self
    {
        $this->contractDate = $contractDate;

        return $this;
    }

    public function getStartDate(): ?DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(DateTimeInterface $startDate): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    public function getEndDate(): ?DateTimeInterface
    {
        return $this->endDate;
    }

    public function setEndDate(DateTimeInterface $endDate): self
    {
        $this->endDate = $endDate;

        return $this;
    }

    public function getCreatedAt(): ?DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getModifiedAt(): ?DateTimeInterface
    {
        return $this->modifiedAt;
    }

    public function setModifiedAt(?DateTimeInterface $modifiedAt): self
    {
        $this->modifiedAt = $modifiedAt;

        return $this;
    }

    public function getCurrency(): ?Currency
    {
        return $this->currency;
    }

    public function setCurrency(?Currency $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getIsAnnex(): ?bool
    {
        return $this->isAnnex;
    }

    public function getParent(): ?self
    {
        return $this->parent;
    }

    public function setParent(?self $parent): self
    {
        $this->parent = $parent;
        if (!empty($parent)) {
            $this->isAnnex = true;
        }

        return $this;
    }

    public function getNumber(): string
    {
        return $this->number;
    }

    public function setNumber(string $number): Contract
    {
        $this->number = $number;

        return $this;
    }

    /**
     * @return string
     */
    public function getArea()
    {
        return $this->area;
    }

    public function setArea(string $area): Contract
    {
        $this->area = $area;

        return $this;
    }

    /**
     * @ORM\PreUpdate
     */
    public function onPreUpdate()
    {
        $this->modifiedAt = new DateTime();
    }

    public function getServiceProviderId(): int
    {
        return $this->serviceProviderId;
    }

    public function setServiceProvider(?ServiceProvider $serviceProvider): self
    {
        $this->serviceProvider = $serviceProvider;

        return $this;
    }

    public function getServiceProvider(): ?ServiceProvider
    {
        return $this->serviceProvider;
    }

    /**
     * @return string
     */
    public function getCustomerIdentification()
    {
        return $this->customerIdentification;
    }

    public function setCustomerIdentification(string $customerIdentification): Contract
    {
        $this->customerIdentification = $customerIdentification;

        return $this;
    }

    /**
     * @return Collection|ResponsibleUser[]
     */
    public function getResponsibleUsers(): Collection
    {
        return $this->responsibleUsers;
    }

    public function addResponsibleUser(ResponsibleUser $responsibleUser): self
    {
        if (!$this->responsibleUsers->contains($responsibleUser)) {
            $this->responsibleUsers[] = $responsibleUser;
            $responsibleUser->setContract($this);
        }

        return $this;
    }

    public function removeResponsibleUser(ResponsibleUser $responsibleUser): self
    {
        if ($this->responsibleUsers->contains($responsibleUser)) {
            $this->responsibleUsers->removeElement($responsibleUser);
            // set the owning side to null (unless already changed)
            if ($responsibleUser->getContract() === $this) {
                $responsibleUser->setContract(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Price[]
     */
    public function getPrices(): Collection
    {
        return $this->prices;
    }

    public function addPrice(Price $price): self
    {
        if (!$this->prices->contains($price)) {
            $this->prices[] = $price;
            $price->setContract($this);
        }

        return $this;
    }

    public function removePrice(Price $price): self
    {
        if ($this->prices->contains($price)) {
            $this->prices->removeElement($price);
            // set the owning side to null (unless already changed)
            if ($price->getContract() === $this) {
                $price->setContract(null);
            }
        }

        return $this;
    }

    public function getOrganizationId()
    {
        return $this->organizationId;
    }

    public function setOrganizationId($organizationId): self
    {
        $this->organizationId = $organizationId;

        return $this;
    }

    public function canEdit(): bool
    {
        $packages = $this->getPackages();

        if ($packages) {
            /** @var Package $package */
            foreach ($packages as $package) {
                $fields = $package->getPackageFields();

                if (count($fields) > 0) {
                    return false;
                }
            }
        }

        return true;
    }

    public function canDelete(): bool
    {
        $packages = $this->getPackages();
        $canDelete = true;

        if ($packages) {
            /** @var Package $package */
            foreach ($packages as $package) {
                $fields = $package->getPackageFields();
                $containFields = $package->getPackage()->getContainFields();
                $canDelete = $canDelete && $containFields && 0 === count($fields);
            }
        }

        return $canDelete;
    }
}
