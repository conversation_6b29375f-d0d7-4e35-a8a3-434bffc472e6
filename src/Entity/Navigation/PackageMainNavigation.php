<?php

namespace App\Entity\Navigation;

use App\Entity\Package;
use App\Repository\Navigation\PackageMainNavigationRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=PackageMainNavigationRepository::class)
 */
class PackageMainNavigation
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Package::class, inversedBy="packageMainNavigations")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $package;

    /**
     * @ORM\ManyToOne(targetEntity=MainNavigation::class, inversedBy="packageMainNavigations")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $mainNavigation;

    /**
     * @ORM\Column(type="integer")
     */
    private $visualOrder;

    /**
     * @ORM\Column(type="string", columnDefinition="link_target_enum", nullable=false, options={"default" = "_self"})
     */
    private $target;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPackage(): ?Package
    {
        return $this->package;
    }

    public function setPackage(?Package $package): self
    {
        $this->package = $package;

        return $this;
    }

    public function getMainNavigation(): ?MainNavigation
    {
        return $this->mainNavigation;
    }

    public function setMainNavigation(?MainNavigation $mainNavigation): self
    {
        $this->mainNavigation = $mainNavigation;

        return $this;
    }

    public function getVisualOrder(): ?int
    {
        return $this->visualOrder;
    }

    public function setVisualOrder(int $visualOrder): self
    {
        $this->visualOrder = $visualOrder;

        return $this;
    }

    public function getTarget(): ?string
    {
        return $this->target;
    }

    public function setTarget(string $target): self
    {
        $this->target = $target;

        return $this;
    }
}
