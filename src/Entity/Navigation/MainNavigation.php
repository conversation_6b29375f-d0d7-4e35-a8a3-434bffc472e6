<?php

namespace App\Entity\Navigation;

use App\Repository\Navigation\MainNavigationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=MainNavigationRepository::class)
 */
class MainNavigation
{
    public const WEB_INSTANCE = 'web';
    public const WEB_LEGACY_INSTANCE = 'web-legacy';
    public const MOBILE_INSTANCE = 'mobile';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $labelEn;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $labelBg;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $labelRo;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $labelUa;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $labelIt;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $url;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $noDataUrl;

    /**
     * @ORM\Column(type="xml", length=255)
     */
    private $icon;

    /**
     * @ORM\Column(type="ltree")
     */
    private $path;

    /**
     * @ORM\Column(name="instance", type="string", columnDefinition="main_navigation_instance_enum", nullable=false, options={"default" = "web"})
     */
    private string $instance = 'web';

    /**
     * @ORM\OneToMany(targetEntity=PackageMainNavigation::class, mappedBy="mainNavigation")
     */
    private $packageMainNavigations;

    public function __construct()
    {
        $this->packageMainNavigations = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function setUrl(string $url): self
    {
        $this->url = $url;

        return $this;
    }

    public function getNoDataUrl(): ?string
    {
        return $this->noDataUrl;
    }

    public function setNoDataUrl(string $noDataUrl): self
    {
        $this->noDataUrl = $noDataUrl;

        return $this;
    }

    public function getIcon(): ?string
    {
        return $this->icon;
    }

    public function setIcon(string $icon): self
    {
        $this->icon = $icon;

        return $this;
    }

    public function getPath(): ?string
    {
        return $this->path;
    }

    public function setPath(?self $path): self
    {
        $this->path = $path;

        return $this;
    }

    /**
     * @return Collection<int, PackageMainNavigation>
     */
    public function getPackageMainNavigations(): Collection
    {
        return $this->packageMainNavigations;
    }

    public function addPackageMainNavigation(PackageMainNavigation $packageMainNavigation): self
    {
        if (!$this->packageMainNavigations->contains($packageMainNavigation)) {
            $this->packageMainNavigations[] = $packageMainNavigation;
            $packageMainNavigation->setMainNavigation($this);
        }

        return $this;
    }

    public function removePackageMainNavigation(PackageMainNavigation $packageMainNavigation): self
    {
        if ($this->packageMainNavigations->removeElement($packageMainNavigation)) {
            // set the owning side to null (unless already changed)
            if ($packageMainNavigation->getMainNavigation() === $this) {
                $packageMainNavigation->setMainNavigation(null);
            }
        }

        return $this;
    }

    public function getLabelEn()
    {
        return $this->labelEn;
    }

    public function setLabelEn($labelEn): void
    {
        $this->labelEn = $labelEn;
    }

    public function getLabelRo()
    {
        return $this->labelRo;
    }

    public function setLabelRo($labelRo): void
    {
        $this->labelRo = $labelRo;
    }

    public function getLabelUa()
    {
        return $this->labelUa;
    }

    public function setLabelUa($labelUa): void
    {
        $this->labelUa = $labelUa;
    }

    public function getLabelBg()
    {
        return $this->labelBg;
    }

    public function setLabelBg($labelBg): void
    {
        $this->labelBg = $labelBg;
    }

    public function getLabelIt()
    {
        return $this->labelIt;
    }

    public function setLabelIt($labelIt): void
    {
        $this->labelIt = $labelIt;
    }

    public function getInstance(): string
    {
        return $this->instance;
    }

    public function setInstance(string $instance): void
    {
        $this->instance = $instance;
    }

    public static function getAvailableInstance(): array
    {
        return [
            self::WEB_INSTANCE,
            self::WEB_LEGACY_INSTANCE,
            self::MOBILE_INSTANCE,
        ];
    }
}
