<?php

namespace App\Entity\Log\Recommendation;

use App\Repository\Log\Recommendation\RecommendationVraOrdersLogRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Table(
 *     name="loggable.recommendations_vra_orders_log",
 *     options={"row_format" = "DYNAMIC"},
 *     indexes={
 *
 *         @ORM\Index(name="recommendations_vra_orders_log_class_lookup_idx", columns={"object_class"}),
 *         @ORM\Index(name="recommendations_vra_orders_log_date_lookup_idx", columns={"logged_at"}),
 *         @ORM\Index(name="recommendations_vra_orders_log_user_lookup_idx", columns={"username"}),
 *         @ORM\Index(name="recommendations_vra_orders_log_version_lookup_idx", columns={"object_id", "object_class", "version"})
 *     }
 * )
 *
 * @ORM\Entity(repositoryClass=RecommendationVraOrdersLogRepository::class)
 */
class RecommendationVraOrdersLog extends RecommendationLogEntry
{
    // All required columns are mapped through inherited superclass
}
