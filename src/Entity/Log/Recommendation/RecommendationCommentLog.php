<?php

namespace App\Entity\Log\Recommendation;

use App\Repository\Log\Recommendation\RecommendationCommentLogRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Table(
 *     name="loggable.recommendation_comments_log",
 *     options={"row_format" = "DYNAMIC"},
 *     indexes={
 *
 *         @ORM\Index(name="recommendation_comments_log_class_lookup_idx", columns={"object_class"}),
 *         @ORM\Index(name="recommendation_comments_log_date_lookup_idx", columns={"logged_at"}),
 *         @ORM\Index(name="recommendation_comments_log_user_lookup_idx", columns={"username"}),
 *         @ORM\Index(name="recommendation_comments_log_version_lookup_idx", columns={"object_id", "object_class", "version"})
 *     }
 * )
 *
 * @ORM\Entity(repositoryClass=RecommendationCommentLogRepository::class)
 */
class RecommendationCommentLog extends RecommendationLogEntry
{
    // All required columns are mapped through inherited superclass
}
