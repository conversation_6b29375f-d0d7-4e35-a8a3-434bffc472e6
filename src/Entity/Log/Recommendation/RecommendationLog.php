<?php

namespace App\Entity\Log\Recommendation;

use App\Repository\Log\Recommendation\RecommendationLogRepository;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Loggable\Entity\MappedSuperclass\AbstractLogEntry;

/**
 * @ORM\Table(
 *     name="loggable.recommendations_log",
 *     options={"row_format" = "DYNAMIC"},
 *     indexes={
 *
 *         @ORM\Index(name="recommendations_log_class_lookup_idx", columns={"object_class"}),
 *         @ORM\Index(name="recommendations_log_date_lookup_idx", columns={"logged_at"}),
 *         @ORM\Index(name="recommendations_log_user_lookup_idx", columns={"username"}),
 *         @ORM\Index(name="recommendations_log_version_lookup_idx", columns={"object_id", "object_class", "version"})
 *     }
 * )
 *
 * @ORM\Entity(repositoryClass=RecommendationLogRepository::class)
 */
class RecommendationLog extends AbstractLogEntry
{
    // All required columns are mapped through inherited superclass
}
