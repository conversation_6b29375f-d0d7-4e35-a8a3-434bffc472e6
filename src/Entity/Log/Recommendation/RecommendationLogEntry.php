<?php

namespace App\Entity\Log\Recommendation;

use Doctrine\ORM\Mapping as ORM;
use Gedmo\Loggable\Entity\MappedSuperclass\AbstractLogEntry;

abstract class RecommendationLogEntry extends AbstractLogEntry
{
    /**
     * @var null|int
     *
     * @ORM\Column(type="integer", name="recommendation_id", nullable=true)
     */
    protected $recommendationId;

    public function getRecommendationId(): ?int
    {
        return $this->recommendationId;
    }

    public function setRecommendationId(?int $recommendationId): self
    {
        $this->recommendationId = $recommendationId;

        return $this;
    }
}
