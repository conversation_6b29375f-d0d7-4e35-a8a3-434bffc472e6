<?php

namespace App\Entity\Contract;

use App\Entity\IProvided;
use App\Entity\ServiceProvider;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Contract\DurationTypeRepository")
 */
class DurationType implements IProvided
{
    public const DURATION_TYPE_MONTH_SLUG = 'month';
    public const DURATION_TYPE_YEAR_SLUG = 'year';
    public const DURATION_TYPE_DAY_SLUG = 'day';
    public const DURATION_TYPE_FARMING_YEAR = 'farming_year';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @Assert\NotBlank
     *
     * @ORM\Column(type="string", length=20)
     *
     * @Groups({"api"})
     */
    private $slug;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\ServiceProvider")
     */
    private $serviceProvider;

    /**
     * @ORM\Column(type="integer", nullable=false)
     */
    private int $serviceProviderId;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isCalendarPeriod;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $startDay;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $endDay;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $startMonth;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $endMonth;

    /**
     * @ORM\Column(type="string", length=20, nullable=true)
     */
    private $format;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function setSlug(string $slug): self
    {
        $this->slug = $slug;

        return $this;
    }

    public function getServiceProvider(): ?ServiceProvider
    {
        return $this->serviceProvider;
    }

    public function setServiceProvider(?ServiceProvider $serviceProvider): self
    {
        $this->serviceProvider = $serviceProvider;

        return $this;
    }

    public function getIsCalendarPeriod(): ?bool
    {
        return $this->isCalendarPeriod;
    }

    public function setIsCalendarPeriod(?bool $isCalendarPeriod): self
    {
        $this->isCalendarPeriod = $isCalendarPeriod;

        return $this;
    }

    public function getStartMonth(): ?int
    {
        return $this->startMonth;
    }

    public function setStartMonth(?int $startMonth): self
    {
        $this->startMonth = $startMonth;

        return $this;
    }

    public function getEndMonth(): ?int
    {
        return $this->endMonth;
    }

    public function setEndMonth(?int $endMonth): self
    {
        $this->endMonth = $endMonth;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getStartDay(): ?int
    {
        return $this->startDay;
    }

    public function setStartDay($startDay): self
    {
        $this->startDay = $startDay;

        return $this;
    }

    public function getEndDay()
    {
        return $this->endDay;
    }

    /**
     * @return DurationType
     */
    public function setEndDay($endDay)
    {
        $this->endDay = $endDay;

        return $this;
    }

    public function getFormat(): ?string
    {
        return $this->format;
    }

    public function setFormat(?string $format): self
    {
        $this->format = $format;

        return $this;
    }

    /**
     * @return int
     */
    public function getServiceProviderId()
    {
        return $this->serviceProviderId;
    }
}
