<?php

namespace App\Entity\Contract;

use App\Entity\Package;
use App\Validator\Constraints\ServicePackageArea;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use <PERSON>ed<PERSON>\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Contract\ServicePackageRepository")
 *
 * @ORM\Table(name="service_contract_packages")
 *
 * @ServicePackageArea
 *
 * @Gedmo\Loggable
 */
class ServicePackage implements IFieldable
{
    public const STATUS_IN_PROGRESS = 'In progress';
    public const STATUS_WAITING_FOR_PLOTS = 'Waiting for plots';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Contract\Service", inversedBy="servicePackages")
     *
     * @ORM\JoinColumn(nullable=false, onDelete="CASCADE")
     *
     * @Gedmo\Versioned
     */
    private $contract;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Package")
     *
     * @ORM\JoinColumn(nullable=false)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $package;

    /**
     * @ORM\Column(name="status", type="string", columnDefinition="package_statuses_enum", nullable=false, options={"default" = "New"})
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $status = 'New';

    /**
     * @ORM\Column(name="state", type="string", columnDefinition="package_states_enum", nullable=false, options={"default" = "New"})
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $state = 'New';

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Contract\ServicePackageField", mappedBy="servicePackage", orphanRemoval=true, cascade={"persist", "remove"})
     *
     * @Groups({"api"})
     */
    private $servicePackageFields;

    /**
     * @ORM\Column(type="decimal", precision=10, scale=2, nullable=true)
     *
     * @Groups({"api"})
     *
     * @Assert\GreaterThan(0)
     */
    private $amount;

    public function __construct()
    {
        $this->servicePackageFields = new ArrayCollection();
    }

    public function getPackageFields(): iterable
    {
        return $this->servicePackageFields;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getContract(): ?Service
    {
        return $this->contract;
    }

    public function setContract(?Service $contract): self
    {
        $this->contract = $contract;

        return $this;
    }

    public function getPackage(): ?Package
    {
        return $this->package;
    }

    public function setPackage(?Package $package): self
    {
        $this->package = $package;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(?string $state): self
    {
        $this->state = $state;

        return $this;
    }

    /**
     * @return Collection|ServicePackageField[]
     */
    public function getServicePackageFields(): Collection
    {
        return $this->servicePackageFields;
    }

    public function addServicePackageField(ServicePackageField $servicePackageField): self
    {
        if (!$this->servicePackageFields->contains($servicePackageField)) {
            $this->servicePackageFields[] = $servicePackageField;
            $servicePackageField->setServicePackage($this);
        }

        return $this;
    }

    public function removeServicePackageField(ServicePackageField $servicePackageField): self
    {
        if ($this->servicePackageFields->contains($servicePackageField)) {
            $this->servicePackageFields->removeElement($servicePackageField);
            // set the owning side to null (unless already changed)
            if ($servicePackageField->getServicePackage() === $this) {
                $servicePackageField->setServicePackage(null);
            }
        }

        return $this;
    }

    public function getAmount()
    {
        return $this->amount;
    }

    public function setAmount($amount): self
    {
        $this->amount = $amount;

        return $this;
    }
}
