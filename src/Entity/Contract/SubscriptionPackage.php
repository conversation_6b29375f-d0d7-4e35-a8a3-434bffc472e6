<?php

namespace App\Entity\Contract;

use App\Entity\Analysis\PackageGridPoints;
use App\Entity\IProvided;
use App\Entity\Package;
use App\Entity\ServiceProvider;
use App\Validator\Constraints\SubscriptionPackageArea;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Contract\SubscriptionPackageRepository")
 *
 * @SubscriptionPackageArea
 *
 * @Gedmo\Loggable
 */
class SubscriptionPackage implements IFieldable, IProvided
{
    public const STATUS_NEW = 'New';
    public const STATUS_ACTIVE = 'Active';
    public const STATUS_EXPIRED = 'Expired';

    public const STATUS_IN_PROGRESS = 'In progress';
    public const STATUS_WAITING_FOR_PLOTS = 'Waiting for plots';

    public const TRANSITION_WAITING_FOR_PLOT_STATE = 'waiting_for_plot_state';
    public const TRANSITION_ADD_STATIONS_STATE = 'add_stations_state';
    public const TRANSITION_ADD_DEVICES_STATE = 'add_devices_state';
    public const TRANSITION_IN_PROGRESS_STATE = 'in_progress_state';
    public const TRANSITION_DONE_STATE = 'done_state';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @Assert\NotBlank
     *
     * @Assert\Type("integer")
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $period;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Contract\Subscription", inversedBy="subscriptionPackages", cascade={"persist", "remove"})
     *
     * @ORM\JoinColumn(nullable=false, onDelete="CASCADE")
     *
     * @Gedmo\Versioned
     */
    private $contract;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Package", inversedBy="subscriptionPackages", cascade={"persist", "remove"})
     *
     * @ORM\JoinColumn(nullable=false)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $package;

    /**
     * @ORM\Column(name="status", type="string", columnDefinition="package_statuses_enum", nullable=false, options={"default" = "New"})
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $status = 'New';

    /**
     * @ORM\Column(name="state", type="string", columnDefinition="package_states_enum", nullable=false, options={"default" = "New"})
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $state = 'New';

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\ServiceProvider", cascade={"persist", "remove"})
     */
    private $serviceProvider;

    /**
     * @ORM\Column(type="integer", nullable=false)
     */
    private int $serviceProviderId;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Contract\SubscriptionPackageField", mappedBy="subscriptionPackage", orphanRemoval=true, cascade={"persist", "remove"})
     *
     * @Groups({"api"})
     */
    private $subscriptionPackageFields;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Analysis\PackageGridPoints", mappedBy="package", cascade={"persist", "remove"})
     */
    private $packageGridPoints;

    /**
     * @ORM\Column(type="decimal", precision=10, scale=2, nullable=true)
     *
     * @Groups({"api"})
     *
     * @Assert\GreaterThan(0)
     */
    private $amount;

    /**
     * @ORM\Column(type="datetime")
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $startDate;

    /**
     * @ORM\Column(type="datetime")
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $endDate;

    public function __construct()
    {
        $this->subscriptionPackageFields = new ArrayCollection();
        $this->packageGridPoints = new ArrayCollection();
    }

    public function getPackageFields(): iterable
    {
        return $this->subscriptionPackageFields;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPeriod(): ?int
    {
        return $this->period;
    }

    public function setPeriod(int $period): self
    {
        $this->period = $period;

        return $this;
    }

    public function getContract(): ?Subscription
    {
        return $this->contract;
    }

    public function setContract(?Subscription $contract): self
    {
        $this->contract = $contract;

        return $this;
    }

    public function getPackage(): ?Package
    {
        return $this->package;
    }

    public function setPackage(?Package $package): self
    {
        $this->package = $package;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(?string $state): self
    {
        $this->state = $state;

        return $this;
    }

    /**
     * @return Collection|PackageGridPoints[]
     */
    public function getPackageGridPoints(): Collection
    {
        return $this->packageGridPoints;
    }

    public function addPackageGridPoint(PackageGridPoints $packageGridPoint)
    {
        $this->packageGridPoints[] = $packageGridPoint;

        return $this;
    }

    public function getServiceProvider(): ?ServiceProvider
    {
        return $this->serviceProvider;
    }

    public function setServiceProvider(?ServiceProvider $serviceProvider): self
    {
        $this->serviceProvider = $serviceProvider;

        return $this;
    }

    /**
     * @return Collection|SubscriptionPackageField[]
     */
    public function getSubscriptionPackageFields(): Collection
    {
        return $this->subscriptionPackageFields;
    }

    public function addSubscriptionPackageField(SubscriptionPackageField $subscriptionPackageField): self
    {
        if (!$this->subscriptionPackageFields->contains($subscriptionPackageField)) {
            $this->subscriptionPackageFields[] = $subscriptionPackageField;
            $subscriptionPackageField->setSubscriptionPackage($this);
        }

        return $this;
    }

    public function removeSubscriptionPackageField(SubscriptionPackageField $subscriptionPackageField): self
    {
        if ($this->subscriptionPackageFields->contains($subscriptionPackageField)) {
            $this->subscriptionPackageFields->removeElement($subscriptionPackageField);
            // set the owning side to null (unless already changed)
            if ($subscriptionPackageField->getSubscriptionPackage() === $this) {
                $subscriptionPackageField->setSubscriptionPackage(null);
            }
        }

        return $this;
    }

    public function getAmount()
    {
        return $this->amount;
    }

    public function setAmount($amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getStartDate(): ?DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(DateTimeInterface $startDate): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    public function getEndDate(): ?DateTimeInterface
    {
        return $this->endDate;
    }

    public function setEndDate(DateTimeInterface $endDate): self
    {
        $this->endDate = $endDate;

        return $this;
    }

    /**
     * @return int
     */
    public function getServiceProviderId()
    {
        return $this->serviceProviderId;
    }
}
