<?php

namespace App\Entity\Contract;

use App\Entity\Contract;
use Doctrine\ORM\Mapping as ORM;
use <PERSON>ed<PERSON>\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Contract\ResponsibleUserRepository")
 *
 * @Gedmo\Loggable
 */
class ResponsibleUser
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Contract", inversedBy="responsibleUsers")
     *
     * @ORM\JoinColumn(nullable=false, onDelete="CASCADE")
     *
     * @Gedmo\Versioned
     */
    private $contract;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $username;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $role;

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $userId;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getContract(): ?Contract
    {
        return $this->contract;
    }

    public function setContract(?Contract $contract): self
    {
        $this->contract = $contract;

        return $this;
    }

    public function getUsername(): ?string
    {
        return $this->username;
    }

    public function setUsername(string $username): self
    {
        $this->username = $username;

        return $this;
    }

    public function getRole(): ?string
    {
        return $this->role;
    }

    public function setRole(string $role): self
    {
        $this->role = $role;

        return $this;
    }

    public function getUserId()
    {
        return $this->userId;
    }

    public function setUserId($userId): self
    {
        $this->userId = $userId;

        return $this;
    }
}
