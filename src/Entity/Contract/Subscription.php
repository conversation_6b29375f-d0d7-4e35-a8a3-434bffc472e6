<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entity\Contract;

use App\Entity\Contract;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class Subscription.
 *
 * @ORM\Entity(repositoryClass="App\Repository\Contract\SubscriptionRepository")
 *
 * @ORM\Table(name="subscription_contracts")
 *
 * @Gedmo\Loggable
 */
class Subscription extends Contract
{
    /**
     * @Assert\NotBlank
     *
     * @Assert\Type("integer")
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $duration;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Contract\DurationType", cascade={"persist", "remove"})
     *
     * @ORM\JoinColumn(nullable=false)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $durationType;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Contract\SubscriptionPackage", mappedBy="contract", orphanRemoval=true, cascade={"persist", "remove"})
     *
     * @Groups({"api"})
     */
    private $subscriptionPackages;

    public function __construct()
    {
        parent::__construct();
        $this->subscriptionPackages = new ArrayCollection();
    }

    public function getPackages(): iterable
    {
        return $this->subscriptionPackages;
    }

    public function getDuration(): ?int
    {
        return $this->duration;
    }

    public function setDuration(int $duration): self
    {
        $this->duration = $duration;

        return $this;
    }

    public function getDurationType(): ?DurationType
    {
        return $this->durationType;
    }

    public function setDurationType(?DurationType $durationType): self
    {
        $this->durationType = $durationType;

        return $this;
    }

    /**
     * @return Collection|SubscriptionPackage[]
     */
    public function getSubscriptionPackages(): Collection
    {
        return $this->subscriptionPackages;
    }

    public function addSubscriptionPackage(SubscriptionPackage $subscriptionPackage): self
    {
        if (!$this->subscriptionPackages->contains($subscriptionPackage)) {
            $this->subscriptionPackages[] = $subscriptionPackage;
            $subscriptionPackage->setContract($this);
        }

        return $this;
    }

    public function removeSubscriptionPackage(SubscriptionPackage $subscriptionPackage): self
    {
        if ($this->subscriptionPackages->contains($subscriptionPackage)) {
            $this->subscriptionPackages->removeElement($subscriptionPackage);
            // set the owning side to null (unless already changed)
            if ($subscriptionPackage->getContract() === $this) {
                $subscriptionPackage->setContract(null);
            }
        }

        return $this;
    }
}
