<?php

namespace App\Entity\Contract;

use App\Entity\Contract;
use App\Entity\IProvided;
use App\Entity\Payment;
use App\Entity\ServiceProvider;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Contract\PriceRepository")
 *
 * @Gedmo\Loggable
 */
class Price implements IProvided
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @Assert\Type("integer")
     *
     * @Assert\GreaterThan(0)
     *
     * @ORM\Column(type="integer", nullable=true)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $period;

    /**
     * @Assert\NotBlank
     *
     * @Assert\Type("numeric")
     *
     * @Assert\GreaterThan(0)
     *
     * @ORM\Column(type="decimal", precision=10, scale=2, nullable=true)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $amount;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\ServiceProvider")
     */
    private $serviceProvider;

    /**
     * @ORM\Column(type="integer", nullable=false)
     */
    private int $serviceProviderId;

    /**
     * @Assert\NotBlank
     *
     * @ORM\Column(name="status", type="string", columnDefinition="price_status_enum", nullable=false, options={"default" = "None"})
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $status = 'None';

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Contract", inversedBy="prices")
     *
     * @ORM\JoinColumn(nullable=false, onDelete="CASCADE")
     */
    private $contract;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Payment", mappedBy="price")
     *
     * @Groups({"api"})
     */
    private $payments;

    public function __construct()
    {
        $this->payments = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPeriod(): ?int
    {
        return $this->period;
    }

    public function setPeriod(int $period): self
    {
        $this->period = $period;

        return $this;
    }

    public function getAmount()
    {
        return $this->amount;
    }

    public function setAmount($amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getServiceProvider(): ?ServiceProvider
    {
        return $this->serviceProvider;
    }

    public function setServiceProvider(?ServiceProvider $serviceProvider): self
    {
        $this->serviceProvider = $serviceProvider;

        return $this;
    }

    public function getStatus()
    {
        return $this->status;
    }

    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    public function getContract(): ?Contract
    {
        return $this->contract;
    }

    public function setContract(?Contract $contract): self
    {
        $this->contract = $contract;

        return $this;
    }

    /**
     * @return Collection|Payment[]
     */
    public function getPayments(): Collection
    {
        return $this->payments;
    }

    public function addPayment(Payment $payment): self
    {
        if (!$this->payments->contains($payment)) {
            $this->payments[] = $payment;
            $payment->setPrice($this);
        }

        return $this;
    }

    public function removePayment(Payment $payment): self
    {
        if ($this->payments->contains($payment)) {
            $this->payments->removeElement($payment);
            // set the owning side to null (unless already changed)
            if ($payment->getPrice() === $this) {
                $payment->setPrice(null);
            }
        }

        return $this;
    }

    /**
     * @return int
     */
    public function getServiceProviderId()
    {
        return $this->serviceProviderId;
    }
}
