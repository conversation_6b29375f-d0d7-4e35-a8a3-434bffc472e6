<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entity\Contract;

use App\Entity\Contract;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * Class Service.
 *
 * @ORM\Entity(repositoryClass="App\Repository\Contract\ServiceRepository")
 *
 * @ORM\Table(name="service_contracts")
 *
 * @Gedmo\Loggable
 */
class Service extends Contract
{
    /**
     * /**
     * @ORM\OneToMany(targetEntity="App\Entity\Contract\ServicePackage", mappedBy="contract", orphanRemoval=true, cascade={"persist", "remove"})
     *
     * @Groups({"api"})
     */
    private $servicePackages;

    public function __construct()
    {
        parent::__construct();
        $this->servicePackages = new ArrayCollection();
    }

    public function getPackages(): iterable
    {
        return $this->servicePackages;
    }

    /**
     * @return Collection|ServicePackage[]
     */
    public function getServicePackages(): Collection
    {
        return $this->servicePackages;
    }

    public function addServicePackage(ServicePackage $servicePackage): self
    {
        if (!$this->servicePackages->contains($servicePackage)) {
            $this->servicePackages[] = $servicePackage;
            $servicePackage->setContract($this);
        }

        return $this;
    }

    public function removeServicePackage(ServicePackage $servicePackage): self
    {
        if ($this->servicePackages->contains($servicePackage)) {
            $this->servicePackages->removeElement($servicePackage);
            // set the owning side to null (unless already changed)
            if ($servicePackage->getContract() === $this) {
                $servicePackage->setContract(null);
            }
        }

        return $this;
    }
}
