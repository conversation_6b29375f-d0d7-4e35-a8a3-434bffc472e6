<?php

namespace App\Entity\Contract;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Contract\SubscriptionPackageFieldRepository")
 *
 * @Gedmo\Loggable
 */
class SubscriptionPackageField
{
    public const WORKFLOW_NAME = 'subscription_field_state';

    public const TRANSITION_PLOT_ACTIVE = 'plot_active';
    public const TRANSITION_PLOT_GRIDDED = 'plot_gridded';
    public const TRANSITION_PLOT_CELLS_SELECTED = 'plot_cells_selected';
    public const TRANSITION_PLOT_FOR_SAMPLING = 'plot_for_sampling';
    public const TRANSITION_PLOT_SAMPLING = 'plot_sampling';
    public const TRANSITION_PLOT_SAMPLED = 'plot_sampled';
    public const TRANSITION_PLOT_IN_PROGRESS = 'plot_in_progress';
    public const TRANSITION_PLOT_FOR_RECOMMENDATION = 'plot_for_recommendation';
    public const TRANSITION_PLOT_DELIVERED = 'plot_delivered';
    public const TRANSITION_PLOT_EXPIRED = 'plot_expired';

    public const STATE_GRIDDED = 'Gridded';
    public const STATE_CELLS_SELECTED = 'Cells selected';
    public const STATE_FOR_SAMPLING = 'For sampling';
    public const STATE_SAMPLING = 'Sampling';
    public const STATE_SAMPLED = 'Sampled';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Contract\SubscriptionPackageField")
     *
     * @ORM\JoinColumn(name="parent_id", nullable=true, onDelete="SET NULL")
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $parent;

    /**
     * @ORM\Column(type="string", length=63, nullable=true)
     *
     * @Groups({"api"})
     */
    private $plotUuid;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Contract\SubscriptionPackage", inversedBy="subscriptionPackageFields")
     *
     * @ORM\JoinColumn(nullable=false)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $subscriptionPackage;

    /**
     * @ORM\Column(name="field_state", type="string", columnDefinition="field_states_enum", nullable=false, options={"default" = "New"})
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $fieldState = 'New';

    /**
     * @ORM\Column(type="decimal", precision=20, scale=5)
     *
     * @Groups({"api"})
     */
    private $area;

    /**
     * @ORM\Column(type="string", length=63, nullable=true)
     *
     * @Groups({"api"})
     */
    private $orderUuid;

    /**
     * @ORM\Column(type="integer", nullable=false)
     *
     * @Groups({"api"})
     */
    private int $farmId;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getParent(): ?self
    {
        return $this->parent;
    }

    public function setParent(?SubscriptionPackageField $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    public function getPlotUuid(): ?string
    {
        return $this->plotUuid;
    }

    public function setPlotUuid(string $plotUuid): self
    {
        $this->plotUuid = $plotUuid;

        return $this;
    }

    public function getSubscriptionPackage(): ?SubscriptionPackage
    {
        return $this->subscriptionPackage;
    }

    public function setSubscriptionPackage(?SubscriptionPackage $subscriptionPackage): self
    {
        $this->subscriptionPackage = $subscriptionPackage;

        return $this;
    }

    public function getFieldState(): ?string
    {
        return $this->fieldState;
    }

    public function setFieldState(?string $fieldState): self
    {
        $this->fieldState = $fieldState;

        return $this;
    }

    public function getArea()
    {
        return $this->area;
    }

    public function setArea($area): self
    {
        $this->area = $area;

        return $this;
    }

    public function getOrderUuid(): ?string
    {
        return $this->orderUuid;
    }

    public function setOrderUuid(string $orderUuid): self
    {
        $this->orderUuid = $orderUuid;

        return $this;
    }

    public function getFarmId(): ?int
    {
        return $this->farmId;
    }

    public function setFarmId(int $farmId): self
    {
        $this->farmId = $farmId;

        return $this;
    }
}
