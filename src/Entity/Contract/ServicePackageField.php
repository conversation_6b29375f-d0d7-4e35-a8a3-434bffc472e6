<?php

namespace App\Entity\Contract;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Contract\ServicePackageFieldRepository")
 *
 * @Gedmo\Loggable
 */
class ServicePackageField
{
    public const STATE_GRIDDED = 'Gridded';
    public const STATE_CELLS_SELECTED = 'Cells selected';
    public const STATE_FOR_SAMPLING = 'For sampling';
    public const STATE_SAMPLING = 'Sampling';
    public const STATE_SAMPLED = 'Sampled';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Contract\ServicePackage", inversedBy="servicePackageFields")
     *
     * @ORM\JoinColumn(nullable=false)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $servicePackage;

    /**
     * @ORM\Column(type="string", length=63, nullable=true)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $plotUuid;

    /**
     * @ORM\Column(type="decimal", precision=20, scale=5)
     *
     * @Groups({"api"})
     */
    private $area;

    /**
     * @ORM\Column(type="string", length=63, nullable=true)
     *
     * @Groups({"api"})
     *
     * @Gedmo\Versioned
     */
    private $orderUuid;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getServicePackage(): ?ServicePackage
    {
        return $this->servicePackage;
    }

    public function setServicePackage(?ServicePackage $servicePackage): self
    {
        $this->servicePackage = $servicePackage;

        return $this;
    }

    public function getPlotUuid(): ?string
    {
        return $this->plotUuid;
    }

    public function setPlotUuid(string $plotUuid): self
    {
        $this->plotUuid = $plotUuid;

        return $this;
    }

    public function getArea()
    {
        return $this->area;
    }

    public function setArea($area): self
    {
        $this->area = $area;

        return $this;
    }

    public function getOrderUuid(): ?string
    {
        return $this->orderUuid;
    }

    public function setOrderUuid(string $orderUuid): self
    {
        $this->orderUuid = $orderUuid;

        return $this;
    }
}
