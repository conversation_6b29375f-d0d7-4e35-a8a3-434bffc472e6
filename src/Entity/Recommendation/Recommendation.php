<?php

namespace App\Entity\Recommendation;

use App\Entity\AnalysisRecommendationConfig\RecommendationModelsConfig;
use App\Entity\Log\Recommendation\RecommendationLog;
use DateTime;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Loggable\Loggable;
use Gedmo\Mapping\Annotation as Gedmo;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Recommendation\RecommendationRepository")
 *
 * @ORM\Table(name="recommendations")
 *
 * @ORM\HasLifecycleCallbacks
 *
 * @Gedmo\Loggable(logEntryClass=RecommendationLog::class)
 */
class Recommendation implements Loggable
{
    public const WORKFLOW_NAME = 'recommendation_status';

    public const STATUS_FOR_APPROVE = 'For approve';
    public const STATUS_DELIVERED = 'Delivered';
    public const STATUS_DECLINED = 'Declined';
    public const NO_RECOMMENDATION = 'No recommendation';

    public const TRANSITION_COMPLETE_RECOMMENDATION = 'complete_recommendation';
    public const TRANSITION_DECLINE_RECOMMENDATION = 'decline_recommendation';
    public const TRANSITION_FOR_APPROVE_RECOMMENDATION = 'for_approve_recommendation';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="integer", name="package_id", nullable=false)
     */
    private $package;

    /**
     * @ORM\Column(type="string", length=63, nullable=false)
     */
    private $packageType;

    /**
     * @ORM\Column(type="string", length=63, nullable=false)
     */
    private $plotUuid;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $plotName;

    /**
     * @ORM\Column(name="crop_id", type="integer", nullable=true)
     *
     * @Gedmo\Versioned
     */
    private $crop;

    /**
     * @ORM\Column(type="string", columnDefinition="recommendation_status_enum", nullable=false, options={"default" = "For approve"})
     *
     * @Gedmo\Versioned
     */
    private $status = self::STATUS_FOR_APPROVE;

    /**
     * @ORM\Column(type="float", nullable=true)
     *
     * @Gedmo\Versioned
     */
    private $targetYield;

    /**
     * @ORM\Column(type="date")
     *
     * @Gedmo\Versioned
     */
    private $validFrom;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\AnalysisRecommendationConfig\RecommendationModelsConfig")
     *
     * @ORM\JoinColumn(name="model_id", nullable=true)
     *
     * @Gedmo\Versioned
     */
    private $model;

    /**
     * @ORM\Column(type="datetime", nullable=false)
     */
    private $createdAt;

    /**
     * @ORM\Column(type="datetime", nullable=false)
     */
    private $modifiedAt;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Recommendation\RecommendationElementsResults", mappedBy="recommendation", orphanRemoval=true)
     */
    private $recommendationElementsResults;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Recommendation\RecommendationElementsSusces", mappedBy="recommendation", orphanRemoval=true)
     */
    private $recommendationElementsSusces;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Recommendation\RecommendationComment", mappedBy="recommendation", orphanRemoval=true)
     */
    private $recommendationComments;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\Recommendation\RecommendationVraOrders", mappedBy="recommendation", orphanRemoval=true)
     */
    private $recommendationVraOrders;

    /**
     * @ORM\Column(type="float", nullable=false)
     *
     * @Gedmo\Versioned
     */
    private $humus = 1;

    /**
     * @ORM\Column(type="integer[]", nullable=false)
     *
     * @Gedmo\Versioned
     */
    private $samplingTypeIds;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Gedmo\Versioned
     */
    private $declineReason;

    public function __construct()
    {
        $this->createdAt = new DateTime();
        $this->recommendationElementsResults = new ArrayCollection();
        $this->recommendationComments = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPackage(): ?int
    {
        return $this->package;
    }

    public function setPackage(?int $package): self
    {
        $this->package = $package;

        return $this;
    }

    public function getPackageType(): ?string
    {
        return $this->packageType;
    }

    public function setPackageType(?string $packageType): self
    {
        $this->packageType = $packageType;

        return $this;
    }

    public function getPlotUuid(): ?string
    {
        return $this->plotUuid;
    }

    public function setPlotUuid(?string $plotUuid): self
    {
        $this->plotUuid = $plotUuid;

        return $this;
    }

    public function getPlotName(): ?string
    {
        return $this->plotName;
    }

    public function setPlotName(string $plotName): self
    {
        $this->plotName = $plotName;

        return $this;
    }

    public function getCrop(): ?int
    {
        return $this->crop;
    }

    public function setCrop(?int $crop): self
    {
        $this->crop = $crop;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getTargetYield(): ?float
    {
        return $this->targetYield;
    }

    public function setTargetYield(float $targetYield): self
    {
        $this->targetYield = $targetYield;

        return $this;
    }

    public function getValidFrom(): DateTime
    {
        return $this->validFrom;
    }

    public function setValidFrom(DateTime $validFrom): self
    {
        if ($this->validFrom == $validFrom) {
            return $this;
        }

        $this->validFrom = $validFrom;

        return $this;
    }

    public function getModel(): ?RecommendationModelsConfig
    {
        return $this->model;
    }

    public function setModel(?RecommendationModelsConfig $model): self
    {
        $this->model = $model;

        return $this;
    }

    public function getCreatedAt(): ?DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getModifiedAt(): ?DateTimeInterface
    {
        return $this->modifiedAt;
    }

    public function setModifiedAt(DateTimeInterface $modifiedAt): self
    {
        $this->modifiedAt = $modifiedAt;

        return $this;
    }

    public function getDeclineReason(): ?string
    {
        return $this->declineReason;
    }

    public function setDeclineReason(?string $declineReason): self
    {
        $this->declineReason = $declineReason;

        return $this;
    }

    /**
     * @return Collection|RecommendationElementsResults[]
     */
    public function getRecommendationElementsResults(): Collection
    {
        return $this->recommendationElementsResults;
    }

    public function addRecommendationElementsResult(RecommendationElementsResults $recommendationElementsResult): self
    {
        if (!$this->recommendationElementsResults->contains($recommendationElementsResult)) {
            $this->recommendationElementsResults[] = $recommendationElementsResult;
            $recommendationElementsResult->setRecommendation($this);
        }

        return $this;
    }

    /**
     * @return Collection|RecommendationElementsSusces[]
     */
    public function getRecommendationElementsSusces(): Collection
    {
        return $this->recommendationElementsSusces;
    }

    public function addRecommendationElementsSusces(RecommendationElementsSusces $recommendationElementsSusces): self
    {
        if (!$this->recommendationElementsSusces->contains($recommendationElementsSusces)) {
            $this->recommendationElementsSusces[] = $recommendationElementsSusces;
            $recommendationElementsSusces->setRecommendation($this);
        }

        return $this;
    }

    public function removeRecommendationElementsResult(RecommendationElementsResults $recommendationElementsResult): self
    {
        if ($this->recommendationElementsResults->contains($recommendationElementsResult)) {
            $this->recommendationElementsResults->removeElement($recommendationElementsResult);
            // set the owning side to null (unless already changed)
            if ($recommendationElementsResult->getRecommendation() === $this) {
                $recommendationElementsResult->setRecommendation(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|RecommendationComment[]
     */
    public function getRecommendationComments(): Collection
    {
        return $this->recommendationComments;
    }

    public function addRecommendationComment(RecommendationComment $recommendationComment): self
    {
        if (!$this->recommendationComments->contains($recommendationComment)) {
            $this->recommendationComments[] = $recommendationComment;
            $recommendationComment->setRecommendation($this);
        }

        return $this;
    }

    public function removeRecommendationComment(RecommendationComment $recommendationComment): self
    {
        if ($this->recommendationComments->contains($recommendationComment)) {
            $this->recommendationComments->removeElement($recommendationComment);
            // set the owning side to null (unless already changed)
            if ($recommendationComment->getRecommendation() === $this) {
                $recommendationComment->setRecommendation(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|RecommendationVraOrders[]
     */
    public function getRecommendationVraOrders(): Collection
    {
        return $this->recommendationVraOrders;
    }

    public function addRecommendationRecommendationVraOrder(RecommendationVraOrders $recommendationVraOrder): self
    {
        if (!$this->recommendationVraOrders->contains($recommendationVraOrder)) {
            $this->recommendationVraOrders[] = $recommendationVraOrder;
            $recommendationVraOrder->setRecommendation($this);
        }

        return $this;
    }

    public function removeRecommendationRecommendationVraOrder(RecommendationVraOrders $recommendationVraOrder): self
    {
        if ($this->recommendationVraOrders->contains($recommendationVraOrder)) {
            $this->recommendationVraOrders->removeElement($recommendationVraOrder);
            // set the owning side to null (unless already changed)
            if ($recommendationVraOrder->getRecommendation() === $this) {
                $recommendationVraOrder->setRecommendation(null);
            }
        }

        return $this;
    }

    public function getHumus(): ?float
    {
        return $this->humus;
    }

    public function setHumus(float $humus): self
    {
        $this->humus = $humus;

        return $this;
    }

    public function getSamplingTypeIds(): ?array
    {
        return $this->samplingTypeIds;
    }

    public function setSamplingTypeIds(array $samplingTypeIds): self
    {
        $this->samplingTypeIds = $samplingTypeIds;

        return $this;
    }

    /**
     * Gets triggered only on insert.
     *
     * @ORM\PrePersist
     */
    public function onPrePersist()
    {
        $this->createdAt = new DateTime('now');
        $this->modifiedAt = new DateTime('now');
    }

    /**
     * Gets triggered every time on update.
     *
     * @ORM\PreUpdate
     */
    public function onPreUpdate()
    {
        $this->modifiedAt = new DateTime('now');
    }
}
