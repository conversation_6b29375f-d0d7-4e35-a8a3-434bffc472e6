<?php

namespace App\Entity\Recommendation;

use App\Entity\Log\Recommendation\RecommendationElementsResultsLog;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Loggable\Loggable;
use <PERSON>ed<PERSON>\Mapping\Annotation as Gedmo;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Recommendation\RecommendationElementsResultsRepository")
 *
 * @Gedmo\Loggable(logEntryClass=RecommendationElementsResultsLog::class)
 */
class RecommendationElementsResults implements Loggable
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Recommendation\Recommendation", inversedBy="recommendationElementsResults")
     *
     * @ORM\JoinColumn(nullable=false)
     *
     * @Gedmo\Versioned
     */
    private $recommendation;

    /**
     * @ORM\Column(type="string", columnDefinition="result_element_enum", nullable=false)
     *
     * @Gedmo\Versioned
     */
    private $resultElement;

    /**
     * @ORM\Column(type="float", nullable=true)
     *
     * @Gedmo\Versioned
     */
    private $value;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getRecommendation(): ?Recommendation
    {
        return $this->recommendation;
    }

    public function setRecommendation(?Recommendation $recommendation): self
    {
        $this->recommendation = $recommendation;

        return $this;
    }

    public function getResultElement(): ?string
    {
        return $this->resultElement;
    }

    public function setResultElement(string $resultElement): self
    {
        $this->resultElement = $resultElement;

        return $this;
    }

    public function getValue(): ?float
    {
        return $this->value;
    }

    public function setValue(?float $value): self
    {
        $this->value = $value;

        return $this;
    }
}
