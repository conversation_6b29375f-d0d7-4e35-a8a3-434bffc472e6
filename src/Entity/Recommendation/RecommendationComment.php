<?php

namespace App\Entity\Recommendation;

use App\Entity\Log\Recommendation\RecommendationCommentLog;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Loggable\Loggable;
use Ged<PERSON>\Mapping\Annotation as Gedmo;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Recommendation\RecommendationCommentRepository")
 *
 * @Gedmo\Loggable(logEntryClass=RecommendationCommentLog::class)
 */
class RecommendationComment implements Loggable
{
    public const COMMENT_TYPE_ELEMENT = 'element';
    public const COMMENT_TYPE_CUSTOM = 'custom';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Recommendation\Recommendation", inversedBy="recommendationComments")
     *
     * @ORM\JoinColumn(nullable=false)
     *
     * @Gedmo\Versioned
     */
    private $recommendation;

    /**
     * @ORM\Column(type="string", columnDefinition="recommendation_comment_type_enum", nullable=false)
     *
     * @Gedmo\Versioned
     */
    private $commentType;

    /**
     * @ORM\Column(type="string", columnDefinition="result_element_enum", nullable=true)
     *
     * @Gedmo\Versioned
     */
    private $param;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Gedmo\Versioned
     */
    private $value;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getRecommendation(): ?Recommendation
    {
        return $this->recommendation;
    }

    public function setRecommendation(?Recommendation $recommendation): self
    {
        $this->recommendation = $recommendation;

        return $this;
    }

    public function getCommentType(): ?string
    {
        return $this->commentType;
    }

    public function setCommentType(string $commentType): self
    {
        $this->commentType = $commentType;

        return $this;
    }

    public function getParam(): ?string
    {
        return $this->param;
    }

    public function setParam(?string $param): self
    {
        $this->param = $param;

        return $this;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(?string $value): self
    {
        $this->value = $value;

        return $this;
    }
}
