<?php

namespace App\Entity\Recommendation;

use App\Entity\Log\Recommendation\RecommendationVraOrdersLog;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Loggable\Loggable;
use Ged<PERSON>\Mapping\Annotation as Gedmo;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Recommendation\RecommendationVraOrdersRepository")
 *
 * @ORM\Table(name="recommendations_vra_orders")
 *
 * @Gedmo\Loggable(logEntryClass=RecommendationVraOrdersLog::class)
 */
class RecommendationVraOrders implements Loggable
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="integer", nullable=false)
     *
     * @Gedmo\Versioned
     */
    private $vraOrderId;

    /**
     * @ORM\Column(type="string", columnDefinition="vra_order_type_enum", nullable=false, options={"default" = "soil_vra"})
     *
     * @Gedmo\Versioned
     */
    private $vraOrderType;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Recommendation\Recommendation")
     *
     * @ORM\JoinColumn(name="recommendation_id", nullable=false)
     *
     * @Gedmo\Versioned
     */
    private $recommendation;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getVraOrderId(): ?int
    {
        return $this->vraOrderId;
    }

    public function setVraOrderId(int $vraOrderId): self
    {
        $this->vraOrderId = $vraOrderId;

        return $this;
    }

    public function getVraOrderType(): ?string
    {
        return $this->vraOrderType;
    }

    public function setVraOrderType(string $vraOrderType): self
    {
        $this->vraOrderType = $vraOrderType;

        return $this;
    }

    public function getRecommendation(): ?Recommendation
    {
        return $this->recommendation;
    }

    public function setRecommendation(?Recommendation $recommendation): self
    {
        $this->recommendation = $recommendation;

        return $this;
    }
}
