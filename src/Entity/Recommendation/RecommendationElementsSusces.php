<?php

namespace App\Entity\Recommendation;

use App\Entity\Log\Recommendation\RecommendationElementsSuscesLog;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Loggable\Loggable;
use <PERSON>ed<PERSON>\Mapping\Annotation as Gedmo;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Recommendation\RecommendationElementsSuscesRepository")
 *
 * @Gedmo\Loggable(logEntryClass=RecommendationElementsSuscesLog::class)
 */
class RecommendationElementsSusces implements Loggable
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Recommendation\Recommendation", inversedBy="recommendationElementsResults")
     *
     * @ORM\JoinColumn(nullable=false)
     *
     * @Gedmo\Versioned
     */
    private $recommendation;

    /**
     * @ORM\Column(name="element", type="string", columnDefinition="elements_enum", nullable=false, length=255)
     *
     * @Gedmo\Versioned
     */
    private $element;

    /**
     * @ORM\Column(name="value", type="boolean", nullable=false)
     *
     * @Gedmo\Versioned
     */
    private $value;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getRecommendation(): ?Recommendation
    {
        return $this->recommendation;
    }

    public function setRecommendation(?Recommendation $recommendation): self
    {
        $this->recommendation = $recommendation;

        return $this;
    }

    public function getElement(): ?string
    {
        return $this->element;
    }

    public function setElement(string $element): self
    {
        $this->element = $element;

        return $this;
    }

    public function getValue(): ?bool
    {
        return $this->value;
    }

    public function setValue(bool $value): self
    {
        $this->value = $value;

        return $this;
    }
}
