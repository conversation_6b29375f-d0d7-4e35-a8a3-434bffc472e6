<?php

namespace App\Entity\Service;

use App\Entity\IProvided;
use App\Entity\ServiceProvider;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @ORM\Entity(repositoryClass="App\Repository\Service\StatusRepository")
 *
 * @ORM\Table(name="service_statuses")
 */
class Status implements IProvided
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"api"})
     */
    private $id;

    /**
     * @Assert\NotBlank
     *
     * @ORM\Column(type="string", length=20)
     *
     * @Groups({"api"})
     */
    private $slug;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\ServiceProvider")
     */
    private $serviceProvider;

    /**
     * @ORM\Column(type="integer", nullable=false)
     */
    private int $serviceProviderId;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function setSlug(string $slug): self
    {
        $this->slug = $slug;

        return $this;
    }

    public function getServiceProvider(): ?ServiceProvider
    {
        return $this->serviceProvider;
    }

    public function setServiceProvider(?ServiceProvider $serviceProvider): self
    {
        $this->serviceProvider = $serviceProvider;

        return $this;
    }

    /**
     * @return int
     */
    public function getServiceProviderId()
    {
        return $this->serviceProviderId;
    }
}
