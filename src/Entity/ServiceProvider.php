<?php

namespace App\Entity;

use App\Entity\AnalysisRecommendationConfig\LabAggregatedElementInterpetationsConfig;
use App\Entity\AnalysisRecommendationConfig\LabElementAggregationAreaValueConfig;
use App\Entity\AnalysisRecommendationConfig\LabElementInterpretationsConfig;
use App\Entity\AnalysisRecommendationConfig\RecommendationModelsConfig;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\ServiceProviderRepository")
 *
 * @deprecated
 */
class ServiceProvider
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="IDENTITY")
     *
     * @ORM\Column(type="integer", nullable=false)
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=100)
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=100)
     */
    private $slug;

    /**
     * @ORM\Column(type="boolean")
     */
    private $fromSync;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $lastSync;

    /**
     * @ORM\Column(type="string", length=15, nullable=false, options={"default" = "BG"})
     */
    private $countryCode = 'BG';

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\AnalysisRecommendationConfig\LabElementInterpretationsConfig", mappedBy="serviceProvider", orphanRemoval=true)
     */
    private $labElementInterpretationsConfigs;

    /**
     * @ORM\OneToOne(targetEntity="App\Entity\AnalysisRecommendationConfig\LabElementAggregationAreaValueConfig", mappedBy="serviceProvider", cascade={"persist", "remove"})
     */
    private $labElementAggregationAreaValueConfig;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\AnalysisRecommendationConfig\LabAggregatedElementInterpetationsConfig", mappedBy="serviceProvider", orphanRemoval=true)
     */
    private $labAggregatedElementInterpetationsConfigs;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\AnalysisRecommendationConfig\RecommendationModelsConfig", mappedBy="serviceProvider", orphanRemoval=true)
     */
    private $recommendationModelsConfigs;

    private $packages;

    public function __construct()
    {
        $this->labElementInterpretationsConfigs = new ArrayCollection();
        $this->labAggregatedElementInterpetationsConfigs = new ArrayCollection();
        $this->recommendationModelsConfigs = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getFromSync(): ?bool
    {
        return $this->fromSync;
    }

    public function setFromSync(bool $fromSync): self
    {
        $this->fromSync = $fromSync;

        return $this;
    }

    public function getLastSync(): ?DateTimeInterface
    {
        return $this->lastSync;
    }

    public function setLastSync(?DateTimeInterface $lastSync): self
    {
        $this->lastSync = $lastSync;

        return $this;
    }

    public function getCountryCode(): ?string
    {
        return $this->countryCode;
    }

    public function setCountryCode(string $countryCode): self
    {
        $this->countryCode = $countryCode;

        return $this;
    }

    public function getSlug()
    {
        return $this->slug;
    }

    public function setSlug($slug): void
    {
        $this->slug = $slug;
    }

    /**
     * @return Collection|LabElementInterpretationsConfig[]
     */
    public function getLabElementInterpretationsConfigs(): Collection
    {
        return $this->labElementInterpretationsConfigs;
    }

    public function addLabElementInterpretationsConfig(LabElementInterpretationsConfig $labElementInterpretationsConfig): self
    {
        if (!$this->labElementInterpretationsConfigs->contains($labElementInterpretationsConfig)) {
            $this->labElementInterpretationsConfigs[] = $labElementInterpretationsConfig;
            $labElementInterpretationsConfig->setServiceProvider($this);
        }

        return $this;
    }

    public function removeLabElementInterpretationsConfig(LabElementInterpretationsConfig $labElementInterpretationsConfig): self
    {
        if ($this->labElementInterpretationsConfigs->contains($labElementInterpretationsConfig)) {
            $this->labElementInterpretationsConfigs->removeElement($labElementInterpretationsConfig);
            // set the owning side to null (unless already changed)
            if ($labElementInterpretationsConfig->getServiceProvider() === $this) {
                $labElementInterpretationsConfig->setServiceProvider(null);
            }
        }

        return $this;
    }

    public function getLabElementAggregationAreaValueConfig(): ?LabElementAggregationAreaValueConfig
    {
        return $this->labElementAggregationAreaValueConfig;
    }

    public function setLabElementAggregationAreaValueConfig(LabElementAggregationAreaValueConfig $labElementAggregationAreaValueConfig): self
    {
        $this->labElementAggregationAreaValueConfig = $labElementAggregationAreaValueConfig;

        // set the owning side of the relation if necessary
        if ($labElementAggregationAreaValueConfig->getServiceProvider() !== $this) {
            $labElementAggregationAreaValueConfig->setServiceProvider($this);
        }

        return $this;
    }

    /**
     * @return Collection|LabAggregatedElementInterpetationsConfig[]
     */
    public function getLabAggregatedElementInterpetationsConfigs(): Collection
    {
        return $this->labAggregatedElementInterpetationsConfigs;
    }

    public function addLabAggregatedElementInterpetationsConfig(LabAggregatedElementInterpetationsConfig $labAggregatedElementInterpetationsConfig): self
    {
        if (!$this->labAggregatedElementInterpetationsConfigs->contains($labAggregatedElementInterpetationsConfig)) {
            $this->labAggregatedElementInterpetationsConfigs[] = $labAggregatedElementInterpetationsConfig;
            $labAggregatedElementInterpetationsConfig->setServiceProvider($this);
        }

        return $this;
    }

    public function removeLabAggregatedElementInterpetationsConfig(LabAggregatedElementInterpetationsConfig $labAggregatedElementInterpetationsConfig): self
    {
        if ($this->labAggregatedElementInterpetationsConfigs->contains($labAggregatedElementInterpetationsConfig)) {
            $this->labAggregatedElementInterpetationsConfigs->removeElement($labAggregatedElementInterpetationsConfig);
            // set the owning side to null (unless already changed)
            if ($labAggregatedElementInterpetationsConfig->getServiceProvider() === $this) {
                $labAggregatedElementInterpetationsConfig->setServiceProvider(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|RecommendationModelsConfig[]
     */
    public function getRecommendationModelsConfigs(): Collection
    {
        return $this->recommendationModelsConfigs;
    }

    public function addRecommendationModelsConfig(RecommendationModelsConfig $recommendationModelsConfig): self
    {
        if (!$this->recommendationModelsConfigs->contains($recommendationModelsConfig)) {
            $this->recommendationModelsConfigs[] = $recommendationModelsConfig;
            $recommendationModelsConfig->setServiceProvider($this);
        }

        return $this;
    }

    public function removeRecommendationModelsConfig(RecommendationModelsConfig $recommendationModelsConfig): self
    {
        if ($this->recommendationModelsConfigs->contains($recommendationModelsConfig)) {
            $this->recommendationModelsConfigs->removeElement($recommendationModelsConfig);
            // set the owning side to null (unless already changed)
            if ($recommendationModelsConfig->getServiceProvider() === $this) {
                $recommendationModelsConfig->setServiceProvider(null);
            }
        }

        return $this;
    }
}
