<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Listener\WorkflowEntered;

use App\Service\Workflow\SubscriptionPackageService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class DeactivateSubscriptionPackageListener implements EventSubscriberInterface
{
    private $workflowSubscriptionPackageService;

    public function __construct(SubscriptionPackageService $workflowSubscriptionPackageService)
    {
        $this->workflowSubscriptionPackageService = $workflowSubscriptionPackageService;
    }

    /**
     * When a contract is deactivated, the packages to it must also be deactivated.
     */
    public function enteredReview(Event $event)
    {
        /** @var App\Entity\Contract\Subscription $contract */
        $contract = $event->getSubject();
        $this->workflowSubscriptionPackageService->deactivateSubscriptionPackagesByContractId($contract->getId());
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.contract_subscription_state.entered.Expired' => ['enteredReview'],
        ];
    }
}
