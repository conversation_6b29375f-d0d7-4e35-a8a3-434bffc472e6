<?php
/**
 * <AUTHOR> Atanasov <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Listener\WorkflowEntered;

use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Package;
use App\Service\Technofarm\ModuleRightService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class DeactivateTechnofarmModulesRightsListener implements EventSubscriberInterface
{
    private $moduleRightService;
    private $em;

    public function __construct(ModuleRightService $moduleRightService, EntityManagerInterface $entityManager)
    {
        $this->moduleRightService = $moduleRightService;
        $this->em = $entityManager;
    }

    /**
     * When a contract is deactivated, the packages to it must also be deactivated.
     */
    public function onExprire(Event $event)
    {
        /** @var App\Entity\Contract\SubscriptionPackage $contract */
        $subscriptionPackage = $event->getSubject();
        if (Package::INTEGRATION_TYPE_TECHNOFARM === $subscriptionPackage->getPackage()->getIntegration() && false === $this->doesFuturePackageWithSameTypeExist($subscriptionPackage)) {
            $this->moduleRightService->revokeModuleRights($subscriptionPackage);
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_package_status.entered.Expired' => ['onExprire'],
        ];
    }

    /**
     * Check if there is a future package with the same type.
     */
    public function doesFuturePackageWithSameTypeExist(SubscriptionPackage $subscriptionPackage): bool
    {
        $subscriptionPackageRepository = $this->em->getRepository(SubscriptionPackage::class);

        $currentDate = date('Y-m-d');
        $result = $subscriptionPackageRepository->getActiveSubscriptions(
            [
                'organizationId' => $subscriptionPackage->getContract()->getOrganizationId(),
                'integration' => Package::INTEGRATION_TYPE_TECHNOFARM,
                'packageId' => $subscriptionPackage->getPackage()->getId(),
                'endDate' => $currentDate,
            ]
        );

        return (bool) (count($result) > 0);
    }
}
