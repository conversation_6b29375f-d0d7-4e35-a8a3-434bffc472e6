<?php

namespace App\Listener\WorkflowEntered\Analysis;

use App\Entity\Analysis\LabElementGroup;
use App\Service\Workflow\Analysis\LabElementGroupWorkflowService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class LabElementsResultsApprovedListener implements EventSubscriberInterface
{
    private $labElementGroupWorkflowService;

    public function __construct(LabElementGroupWorkflowService $labElementGroupWorkflowService)
    {
        $this->labElementGroupWorkflowService = $labElementGroupWorkflowService;
    }

    /**
     * When All LabElementGroups in Delivered set Plot to Delivered - depends on guard.
     */
    public function enteredReview(Event $event)
    {
        /** @var App\Entity\Analysis\LabElementsResults $labElementsResults */
        $labElementsResults = $event->getSubject();

        $params[] = ['field' => 'id', 'value' => $labElementsResults->getLabElementGroup()->getId(), 'type' => '='];
        $this->labElementGroupWorkflowService->updateState(LabElementGroup::class, LabElementGroup::WORKFLOW_NAME, $params, LabElementGroup::TRANSITION_FOR_RECOMMENDATION);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.lab_elements_results_state.entered.Approved' => ['enteredReview'],
        ];
    }
}
