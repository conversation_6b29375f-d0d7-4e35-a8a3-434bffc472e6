<?php

namespace App\Listener\WorkflowEntered\Analysis;

use App\Entity\Contract\SubscriptionPackageField;
use App\Service\Workflow\SubscriptionFieldService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class PackageGridPointReceivedInLabListener implements EventSubscriberInterface
{
    private $workflowSubscriptionFieldService;

    public function __construct(SubscriptionFieldService $workflowSubscriptionFieldService)
    {
        $this->workflowSubscriptionFieldService = $workflowSubscriptionFieldService;
    }

    /**
     * When All points are in state ReceivedInLab set Plot to in_progress - depends on guard:.
     */
    public function enteredReview(Event $event)
    {
        /** @var App\Entity\Analysis\PackageGridPoints $packageGridPoint */
        $packageGridPoint = $event->getSubject();

        $params[] = ['field' => 'subscriptionPackage', 'value' => $packageGridPoint->getPackageId(), 'type' => '='];
        $params[] = ['field' => 'plotUuid', 'value' => $packageGridPoint->getPlotUuid(), 'type' => '='];
        $this->workflowSubscriptionFieldService->updateSubscriptionPackageFieldStateByParams($params, SubscriptionPackageField::TRANSITION_PLOT_IN_PROGRESS);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.package_grid_points_state.entered.ReceivedInLab' => ['enteredReview'],
        ];
    }
}
