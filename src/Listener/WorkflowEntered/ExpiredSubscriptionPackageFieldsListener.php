<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2020 TechnoFarm Ltd.
 */

namespace App\Listener\WorkflowEntered;

use App\Entity\Contract\SubscriptionPackageField;
use App\Service\Workflow\SubscriptionFieldService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class ExpiredSubscriptionPackageFieldsListener implements EventSubscriberInterface
{
    private $workflowSubscriptionPackageFieldService;

    public function __construct(SubscriptionFieldService $workflowSubscriptionPackageFieldService)
    {
        $this->workflowSubscriptionPackageFieldService = $workflowSubscriptionPackageFieldService;
    }

    public function enteredReview(Event $event)
    {
        /** @var App\Entity\Contract\SubscriptionPackage $subscriptionPackage */
        $subscriptionPackage = $event->getSubject();
        $subscriptionPackageFields = $subscriptionPackage->getSubscriptionPackageFields();
        foreach ($subscriptionPackageFields as $field) {
            $this->workflowSubscriptionPackageFieldService->changeSubscriptionPackageFiledState($field, SubscriptionPackageField::TRANSITION_PLOT_EXPIRED);
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_package_status.entered.Expired' => ['enteredReview'],
        ];
    }
}
