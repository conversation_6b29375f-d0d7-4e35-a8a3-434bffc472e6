<?php
/**
 * Created by PhpStorm.
 * User: l.nonchev
 * Date: 3/11/2021
 * Time: 8:21 AM.
 */

namespace App\Listener\WorkflowEntered;

use App\Service\Workflow\SubscriptionFieldService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class SubscriptionPackageDoneListener implements EventSubscriberInterface
{
    private $workflowSubscriptionFieldService;

    public function __construct(SubscriptionFieldService $workflowSubscriptionFieldService)
    {
        $this->workflowSubscriptionFieldService = $workflowSubscriptionFieldService;
    }

    /**
     * When SubscriptionPackageField in Delivered set Plot to Delivered - depends on guard.
     */
    public function enteredReview(Event $event)
    {
        /** @var App\Entity\Contract\SubscriptionPackage $subscriptionPackage */
        $subscriptionPackage = $event->getSubject();

        $params[] = ['field' => 'subscriptionPackage', 'type' => '=', 'value' => $subscriptionPackage->getId()];
        $this->workflowSubscriptionFieldService->updatePlotsEditableStatus($params);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_package_states.entered.Done' => ['enteredReview'],
        ];
    }
}
