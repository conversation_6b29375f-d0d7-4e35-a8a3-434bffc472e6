<?php

namespace App\Listener\WorkflowEntered\Recommendation;

use App\Entity\Analysis\LabElementGroup;
use App\Entity\Contract\SubscriptionPackageField;
use App\Entity\Recommendation\Recommendation;
use App\Service\Recommendation\RecommendationMailerService;
use App\Service\Recommendation\RecommendationService;
use App\Service\Workflow\Analysis\LabElementGroupWorkflowService;
use App\Service\Workflow\SubscriptionFieldService;
use Exception;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class RecommendationDeliveredStatusListener implements EventSubscriberInterface
{
    private $labElementGroupWorkflowService;

    private $recommendationMailerService;

    private $recommendationService;

    private $workflowSubscriptionFieldService;

    public function __construct(
        LabElementGroupWorkflowService $labElementGroupWorkflowService,
        RecommendationMailerService $recommendationMailerService,
        RecommendationService $recommendationService,
        SubscriptionFieldService $fieldService
    ) {
        $this->labElementGroupWorkflowService = $labElementGroupWorkflowService;
        $this->recommendationMailerService = $recommendationMailerService;
        $this->recommendationService = $recommendationService;
        $this->workflowSubscriptionFieldService = $fieldService;
    }

    /**
     * When Recommendation in Delivered set Plot and Element group to Delivered.
     */
    public function enteredReview(Event $event)
    {
        /** @var Recommendation $recommendation */
        $recommendation = $event->getSubject();

        // Update element groups state to Delivered
        $paramsElGr[] = ['field' => 'packageId', 'value' => $recommendation->getPackage(), 'type' => '='];
        $paramsElGr[] = ['field' => 'packageType', 'value' => $recommendation->getPackageType(), 'type' => '='];
        $paramsElGr[] = ['field' => 'plotUuid', 'value' => $recommendation->getPlotUuid(), 'type' => '='];
        $this->labElementGroupWorkflowService->updateState(LabElementGroup::class, LabElementGroup::WORKFLOW_NAME, $paramsElGr, LabElementGroup::TRANSITION_DELIVERED);

        // Update subscription packages field field_state to Delivered
        $paramsPlot[] = ['field' => 'subscriptionPackage', 'value' => $recommendation->getPackage(), 'type' => '='];
        $paramsPlot[] = ['field' => 'plotUuid', 'value' => $recommendation->getPlotUuid(), 'type' => '='];
        $this->workflowSubscriptionFieldService->updateState(SubscriptionPackageField::class, SubscriptionPackageField::WORKFLOW_NAME, $paramsPlot, SubscriptionPackageField::TRANSITION_PLOT_DELIVERED);

        try {
            $contract = $this->recommendationService->getContractByRecommendation($recommendation->getId());
            $farmingYear = $this->recommendationService->getRecommendationFarmingYear($recommendation->getId());

            $this->recommendationMailerService->sendClientRecommendationDeliveredMessage($recommendation, $contract, $farmingYear);
        } catch (Exception $e) {
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.recommendation_status.entered.Delivered' => ['enteredReview'],
        ];
    }
}
