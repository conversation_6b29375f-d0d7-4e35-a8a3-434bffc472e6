<?php

namespace App\Listener\WorkflowEntered\Recommendation;

use App\Entity\Recommendation\Recommendation;
use App\Service\Recommendation\RecommendationMailerService;
use App\Service\Recommendation\RecommendationService;
use App\Service\Workflow\Analysis\LabElementGroupWorkflowService;
use Exception;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class RecommendationForApproveStatusListener implements EventSubscriberInterface
{
    /**
     * @var RecommendationMailerService
     */
    private $mailerService;

    /**
     * @var LabElementGroupWorkflowService
     */
    private $labElementGroupWorkflowService;

    /**
     * @var RecommendationService
     */
    private $recommendationService;

    /**
     * RecommendationForApproveStatusListener constructor.
     */
    public function __construct(
        RecommendationMailerService $mailerService,
        LabElementGroupWorkflowService $labElementGroupWorkflowService,
        RecommendationService $recommendationService
    ) {
        $this->labElementGroupWorkflowService = $labElementGroupWorkflowService;
        $this->mailerService = $mailerService;
        $this->recommendationService = $recommendationService;
    }

    public function enteredReview(Event $event)
    {
        /** @var Recommendation $recommendation */
        $recommendation = $event->getSubject();

        try {
            $this->mailerService->sendChangeRecommendationStatusMessage($recommendation);
        } catch (Exception $e) {
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.recommendation_status.entered.For approve' => ['enteredReview'],
            'workflow.recommendation_status.entered.Declined' => ['enteredReview'],
        ];
    }
}
