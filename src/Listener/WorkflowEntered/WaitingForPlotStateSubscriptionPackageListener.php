<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Listener\WorkflowEntered;

use App\Entity\Contract\SubscriptionPackage;
use App\Service\Workflow\SubscriptionPackageService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class WaitingForPlotStateSubscriptionPackageListener implements EventSubscriberInterface
{
    private $workflowSubscriptionPackageService;

    public function __construct(SubscriptionPackageService $workflowSubscriptionPackageService)
    {
        $this->workflowSubscriptionPackageService = $workflowSubscriptionPackageService;
    }

    /**
     * When a contract and package is activated, the packages state must be set to Waiting for plots.
     */
    public function changeState(Event $event)
    {
        /** @var App\Entity\Contract\SubscriptionPackage $subscriptionPackage */
        $subscriptionPackage = $event->getSubject();
        $this->workflowSubscriptionPackageService->changeSubscriptionPackageState($subscriptionPackage, SubscriptionPackage::TRANSITION_WAITING_FOR_PLOT_STATE);
        $this->workflowSubscriptionPackageService->changeSubscriptionPackageState($subscriptionPackage, SubscriptionPackage::TRANSITION_ADD_STATIONS_STATE);
        $this->workflowSubscriptionPackageService->changeSubscriptionPackageState($subscriptionPackage, SubscriptionPackage::TRANSITION_ADD_DEVICES_STATE);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_package_status.entered.Active' => ['changeState'],
        ];
    }
}
