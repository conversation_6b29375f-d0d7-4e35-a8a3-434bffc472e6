<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Listener\WorkflowEntered;

use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Contract\SubscriptionPackageField;
use App\Service\Workflow\SubscriptionPackageService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class SubsPackExpiredAndSubsFieldPackDeliverListener implements EventSubscriberInterface
{
    private $workflowSubscriptionPackageService;

    public function __construct(SubscriptionPackageService $workflowSubscriptionPackageService)
    {
        $this->workflowSubscriptionPackageService = $workflowSubscriptionPackageService;
    }

    /**
     * Case 1: When a contract and subscription package is deactivated, the package state must be set to Done.
     *
     * Case 2: When all subscription packages fields is in state delivered, the package state must be set to Done.
     */
    public function enteredReview(Event $event)
    {
        $transition = $event->getTransition();

        if (SubscriptionPackageField::TRANSITION_PLOT_EXPIRED === $transition->getName()) {
            /** @var App\Entity\Contract\SubscriptionPackage $subscriptionPackage */
            $subscriptionPackage = $event->getSubject();
        }

        if (SubscriptionPackageField::TRANSITION_PLOT_DELIVERED === $transition->getName()) {
            /** @var App\Entity\Contract\SubscriptionPackageField $subscriptionPackageField */
            $subscriptionPackageField = $event->getSubject();

            $subscriptionPackage = $subscriptionPackageField->getSubscriptionPackage();
        }

        if (isset($subscriptionPackage)) {
            $this->workflowSubscriptionPackageService->changeSubscriptionPackageState($subscriptionPackage, SubscriptionPackage::TRANSITION_DONE_STATE);
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_package_status.entered.Expired' => ['enteredReview'],
            'workflow.subscription_field_state.entered.Delivered' => ['enteredReview'],
        ];
    }
}
