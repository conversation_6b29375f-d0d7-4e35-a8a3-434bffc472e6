<?php

namespace App\Listener\Loggable;

use App\Entity\Log\Recommendation\RecommendationLogEntry;
use Gedmo\Loggable\LogEntryInterface;
use Gedmo\Loggable\LoggableListener;

class ExtendedLoggableListener extends LoggableListener
{
    protected function prePersistLogEntry($logEntry, $object)
    {
        if ($this->isRecommendationLogEntry($logEntry, $object)) {
            $this->handleRecommendationLogEntry($logEntry, $object);
        }
    }

    private function isRecommendationLogEntry(LogEntryInterface $logEntry, $object): bool
    {
        return $logEntry instanceof RecommendationLogEntry && method_exists($object, 'getRecommendation');
    }

    private function handleRecommendationLogEntry(LogEntryInterface $logEntry, $object)
    {
        /** @var Recommendation */
        $recommendation = $object->getRecommendation();

        /** @var RecommendationLogEntry $logEntry */
        $logEntry->setRecommendationId($recommendation->getId());
    }
}
