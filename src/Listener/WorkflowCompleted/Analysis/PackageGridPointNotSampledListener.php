<?php

namespace App\Listener\WorkflowCompleted\Analysis;

use App\Entity\Analysis\PackageGridPoints;
use App\Service\Workflow\Analysis\PackageGridPointsWorkflowService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class PackageGridPointNotSampledListener implements EventSubscriberInterface
{
    private PackageGridPointsWorkflowService $packageGridPointsWorkflowService;

    public function __construct(PackageGridPointsWorkflowService $packageGridPointsWorkflowService)
    {
        $this->packageGridPointsWorkflowService = $packageGridPointsWorkflowService;
    }

    /**
     * When package grid point(pgp) change state to 'Not sampled', state for all pgp needs to be changed to "Pending".
     */
    public function changeState(Event $event)
    {
        /** @var App\Entity\Analysis\PackageGridPoints $packageGridPoint */
        $packageGridPoint = $event->getSubject();
        $params[] = ['field' => 'packageId', 'value' => $packageGridPoint->getPackageId(), 'type' => '='];
        $params[] = ['field' => 'plotUuid', 'value' => $packageGridPoint->getPlotUuid(), 'type' => '='];
        $this->packageGridPointsWorkflowService->flushChanges();
        $this->packageGridPointsWorkflowService->updateState(PackageGridPoints::class, PackageGridPoints::WORKFLOW_NAME, $params, PackageGridPoints::TRANSITION_PENDING);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.package_grid_points_state.completed.not_sampled' => ['changeState'],
        ];
    }
}
