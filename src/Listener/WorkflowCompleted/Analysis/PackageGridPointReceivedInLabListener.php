<?php

namespace App\Listener\WorkflowCompleted\Analysis;

use App\Entity\Analysis\LabElementGroup;
use App\Entity\Analysis\LabElementsResults;
use App\Service\Workflow\Analysis\LabElementGroupWorkflowService;
use App\Service\Workflow\Analysis\LabElementsResultsWorkflowService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class PackageGridPointReceivedInLabListener implements EventSubscriberInterface
{
    private LabElementsResultsWorkflowService $labElementsResultsWorkflowService;
    private LabElementGroupWorkflowService $labElementGroupWorkflowService;

    public function __construct(
        LabElementsResultsWorkflowService $labElementsResultsWorkflowService,
        LabElementGroupWorkflowService $labElementGroupWorkflowService
    ) {
        $this->labElementsResultsWorkflowService = $labElementsResultsWorkflowService;
        $this->labElementGroupWorkflowService = $labElementGroupWorkflowService;
    }

    /**
     * When package grid point change state to 'ReceivedInLab', lab element group and lab elements results state must be changed.
     */
    public function changeState(Event $event)
    {
        /** @var App\Entity\Analysis\PackageGridPoints $packageGridPoint */
        $packageGridPoint = $event->getSubject();

        $arrRelationDataLabElRes[] = ['field' => 'packageGridPoints', 'value' => $packageGridPoint->getId(), 'type' => '='];
        $this->labElementsResultsWorkflowService->updateState(LabElementsResults::class, LabElementsResults::WORKFLOW_NAME, $arrRelationDataLabElRes, LabElementsResults::TRANSITION_FOR_ANALYSIS);

        $labElementGroupIds = $this->labElementGroupWorkflowService->getLabElementGroupIdsByPackageGridPoint($packageGridPoint);
        $arrRelationDataLabElGr[] = ['field' => 'id', 'value' => $labElementGroupIds, 'type' => 'in'];

        $this->labElementGroupWorkflowService->updateState(LabElementGroup::class, LabElementGroup::WORKFLOW_NAME, $arrRelationDataLabElGr, LabElementGroup::TRANSITION_IN_PROGRESS);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.package_grid_points_state.completed.received_in_lab' => ['changeState'],
        ];
    }
}
