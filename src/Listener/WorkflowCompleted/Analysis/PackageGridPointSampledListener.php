<?php

namespace App\Listener\WorkflowCompleted\Analysis;

use App\Entity\Contract\SubscriptionPackageField;
use App\Service\Workflow\SubscriptionFieldService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class PackageGridPointSampledListener implements EventSubscriberInterface
{
    private SubscriptionFieldService $workflowSubscriptionFieldService;

    public function __construct(
        SubscriptionFieldService $workflowSubscriptionFieldService
    ) {
        $this->workflowSubscriptionFieldService = $workflowSubscriptionFieldService;
    }

    /**
     * When package grid point change state to 'Sampled', change subscription package field state to 'Sampled'.
     */
    public function changeState(Event $event)
    {
        /** @var App\Entity\Analysis\PackageGridPoints $packageGridPoint */
        $packageGridPoint = $event->getSubject();

        $params[] = ['field' => 'plotUuid', 'value' => $packageGridPoint->getPlotUuid(), 'type' => 'IN'];
        $params[] = ['field' => 'subscriptionPackage', 'value' => $packageGridPoint->getPackageId(), 'type' => 'IN'];
        $this->workflowSubscriptionFieldService->flushChanges();
        $this->workflowSubscriptionFieldService->updateState(SubscriptionPackageField::class, SubscriptionPackageField::WORKFLOW_NAME, $params, SubscriptionPackageField::TRANSITION_PLOT_SAMPLED);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.package_grid_points_state.completed.sampled' => ['changeState'],
        ];
    }
}
