<?php

namespace App\Listener\WorkflowCompleted\Analysis;

use App\Entity\Contract\SubscriptionPackageField;
use App\Service\Analysis\LabElementGroupService;
use App\Service\Workflow\SubscriptionFieldService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class LabElementGroupForRecommendationListener implements EventSubscriberInterface
{
    private LabElementGroupService $labElementGroupService;
    private SubscriptionFieldService $workflowSubscriptionFieldService;

    public function __construct(LabElementGroupService $labElementGroupService, SubscriptionFieldService $workflowSubscriptionFieldService)
    {
        $this->labElementGroupService = $labElementGroupService;
        $this->workflowSubscriptionFieldService = $workflowSubscriptionFieldService;
    }

    /**
     * When All LabElementGroups is set for_recommendation - send SoilMapData.
     */
    public function enteredReview(Event $event)
    {
        /** @var App\Entity\Analysis\LabElementGroup $labElementGroup */
        $labElementGroup = $event->getSubject();

        $this->labElementGroupService->sendSoilMapData($labElementGroup);

        $params[] = ['field' => 'subscriptionPackage', 'value' => $labElementGroup->getPackageId(), 'type' => '='];
        $params[] = ['field' => 'plotUuid', 'value' => $labElementGroup->getPlotUuid(), 'type' => '='];
        $this->workflowSubscriptionFieldService->updateState(SubscriptionPackageField::class, SubscriptionPackageField::WORKFLOW_NAME, $params, SubscriptionPackageField::TRANSITION_PLOT_FOR_RECOMMENDATION);
        $this->workflowSubscriptionFieldService->flushChanges();
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.lab_element_group_state.completed.for_recommendation' => ['enteredReview'],
        ];
    }
}
