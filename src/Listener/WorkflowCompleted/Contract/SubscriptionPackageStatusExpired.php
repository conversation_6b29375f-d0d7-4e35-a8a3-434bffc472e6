<?php

namespace App\Listener\WorkflowCompleted\Contract;

use App\Service\Contract\SubscriptionPackageService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class SubscriptionPackageStatusExpired implements EventSubscriberInterface
{
    private $subscriptionPackageService;

    public function __construct(SubscriptionPackageService $subscriptionPackageService)
    {
        $this->subscriptionPackageService = $subscriptionPackageService;
    }

    /**
     * When All LabElementGroups in Delivered set Plot to Delivered - depends on guard.
     */
    public function enteredReview(Event $event)
    {
        /** @var \App\Entity\Contract\SubscriptionPackage $subscriptionPackage */
        $subscriptionPackage = $event->getSubject();

        // Call Laravel - Deactivate Station
        $this->subscriptionPackageService->deactivateStation($subscriptionPackage);

        // Call Laravel - Deactivate Integration
        $this->subscriptionPackageService->deactivateIntegration($subscriptionPackage);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_package_status.completed.deactivate_package' => ['enteredReview'],
        ];
    }
}
