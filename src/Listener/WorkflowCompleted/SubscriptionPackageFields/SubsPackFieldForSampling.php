<?php

namespace App\Listener\WorkflowCompleted\SubscriptionPackageFields;

use App\Entity\Contract;
use App\Service\Workflow\SubscriptionFieldService;
use Exception;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class SubsPackFieldForSampling implements EventSubscriberInterface
{
    private SubscriptionFieldService $workflowSubscriptionFieldService;

    public function __construct(
        SubscriptionFieldService $workflowSubscriptionFieldService
    ) {
        $this->workflowSubscriptionFieldService = $workflowSubscriptionFieldService;
    }

    /**
     * When package subscription package field complete For sampling state, create elements group and elements.
     *
     * @throws Exception
     */
    public function changeState(Event $event)
    {
        /** @var App\Entity\Contract\SubscriptionPackageField $subscriptionPackageField */
        $subscriptionPackageField = $event->getSubject();
        $subscriptionPackage = $subscriptionPackageField->getSubscriptionPackage();
        $package = $subscriptionPackage->getPackage();

        if ($package->getIsFullSampling()) {
            return;
        }

        $params = [
            'packageId' => $subscriptionPackage->getId(),
            'packageType' => Contract::TYPE_SUBSCRIPTION,
            'plotUuids' => [$subscriptionPackageField->getPlotUuid()],
        ];

        $this->workflowSubscriptionFieldService->createElementsGroupAndElementsResults($params);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_field_state.completed.plot_for_sampling' => ['changeState'],
        ];
    }
}
