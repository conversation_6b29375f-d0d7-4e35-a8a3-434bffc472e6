<?php

namespace App\Listener\WorkflowGuard;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class AddStationsStateSubscriptionPackageGuard implements EventSubscriberInterface
{
    public const STATUS_ACTIVE = 'Active';

    public function guardReview(GuardEvent $event)
    {
        /** @var App\Entity\Contract\SubscriptionPackage $subscriptionPackage */
        $package = $event->getSubject();

        $transition = $event->getTransition();
        $metaData = $event->getWorkflow()->getMetadataStore()->getTransitionMetadata($transition);
        $packageName = $package->getPackage()->getSlug();

        if (
            (empty($package->getContract()->getContractDate()) && self::STATUS_ACTIVE !== $package->getContract()->getStatus())
            || !(in_array($packageName, $metaData['packages_add_stations_state']))
        ) {
            // Block the transition "add_stations_state"
            $event->setBlocked(true);
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_package_states.guard.add_stations_state' => ['guardReview'],
        ];
    }
}
