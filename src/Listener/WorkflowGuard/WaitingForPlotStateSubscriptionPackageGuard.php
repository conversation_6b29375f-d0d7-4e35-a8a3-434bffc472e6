<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Listener\WorkflowGuard;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class WaitingForPlotStateSubscriptionPackageGuard implements EventSubscriberInterface
{
    public const STATUS_ACTIVE = 'Active';

    public function guardReview(GuardEvent $event)
    {
        /** @var App\Entity\Contract\SubscriptionPackage $subscriptionPackage */
        $package = $event->getSubject();
        $packageFields = $package->getPackageFields()->toArray();

        if (
            (empty($package->getContract()->getContractDate()) && self::STATUS_ACTIVE !== $package->getContract()->getStatus())
            || !$package->getPackage()->getContainFields()
            || count($packageFields) > 0
        ) {
            // Block the transition "waiting_for_plot_state"
            $event->setBlocked(true);
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_package_states.guard.waiting_for_plot_state' => ['guardReview'],
        ];
    }
}
