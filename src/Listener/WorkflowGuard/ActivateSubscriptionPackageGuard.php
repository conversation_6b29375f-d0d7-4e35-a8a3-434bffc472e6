<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Listener\WorkflowGuard;

use <PERSON>ymfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class ActivateSubscriptionPackageGuard implements EventSubscriberInterface
{
    public function guardReview(GuardEvent $event)
    {
        /** @var App\Entity\Contract\SubscriptionPackage $subscriptionPackage */
        $package = $event->getSubject();

        if (empty($package->getContract()->getContractDate())) {
            // Block the transition "activate_package" if the contract date has no set
            $event->setBlocked(true);
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_package_status.guard.activate_package' => ['guardReview'],
        ];
    }
}
