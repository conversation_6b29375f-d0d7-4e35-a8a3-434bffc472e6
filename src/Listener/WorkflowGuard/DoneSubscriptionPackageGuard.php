<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Listener\WorkflowGuard;

use App\Entity\Contract\SubscriptionPackage;
use App\Entity\Contract\SubscriptionPackageField;
use App\Service\Workflow\SubscriptionPackageService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class DoneSubscriptionPackageGuard implements EventSubscriberInterface
{
    private $subscriptionPackageService;

    public function __construct(SubscriptionPackageService $subscriptionPackageService)
    {
        $this->subscriptionPackageService = $subscriptionPackageService;
    }

    public function guardReview(GuardEvent $event)
    {
        /** @var App\Entity\Contract\SubscriptionPackage $package */
        $package = $event->getSubject();
        $transition = $event->getTransition();
        $metaData = $event->getWorkflow()->getMetadataStore()->getTransitionMetadata($transition);
        $packageName = $package->getPackage()->getSlug();

        if (SubscriptionPackage::STATUS_EXPIRED === $package->getStatus() && !(in_array($packageName, $metaData['done_packages_when_contact_exp']))) {
            // Block the transition "done_state" if the package state is Expired and the package does not fit into a specific set of packages
            $event->setBlocked(true);
        }

        if (SubscriptionPackage::STATUS_ACTIVE === $package->getStatus()) {
            $params[] = ['field' => 'subscriptionPackage', 'type' => '=', 'value' => $package->getId()];
            $isAllInState = $this->subscriptionPackageService->isAllInState('Delivered', SubscriptionPackageField::class, $params, false, 'fieldState');
            $event->setBlocked(!$isAllInState);
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_package_states.guard.done_state' => ['guardReview'],
        ];
    }
}
