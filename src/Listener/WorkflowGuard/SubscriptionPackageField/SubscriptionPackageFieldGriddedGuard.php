<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2020 TechnoFarm Ltd.
 */

namespace App\Listener\WorkflowGuard\SubscriptionPackageField;

use App\Entity\Analysis\PackageGridPoints;
use App\Entity\Contract\SubscriptionPackageField;
use App\Service\Workflow\Analysis\PackageGridPointsWorkflowService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class SubscriptionPackageFieldGriddedGuard implements EventSubscriberInterface
{
    private PackageGridPointsWorkflowService $packageGridPointsWorkflowService;

    public function __construct(PackageGridPointsWorkflowService $packageGridPointsWorkflowService)
    {
        $this->packageGridPointsWorkflowService = $packageGridPointsWorkflowService;
    }

    public function guardReview(GuardEvent $event)
    {
        /** @var App\Entity\Contract\SubscriptionPackageField $subPackageField */
        $subscriptionPackageField = $event->getSubject();
        $package = $subscriptionPackageField->getSubscriptionPackage()->getPackage();
        $packageId = $subscriptionPackageField->getSubscriptionPackage()->getId();
        $plotUuid = $subscriptionPackageField->getPlotUuid();

        if (!$package->getContainFields() || ($package->getContainFields() && !$package->getIsSampling())) {
            $event->setBlocked(true);
        }

        if ($package->getContainFields() && $package->getIsSampling() && !$package->getIsFullSampling()
            && SubscriptionPackageField::STATE_CELLS_SELECTED === $subscriptionPackageField->getFieldState()) {
            $params[] = ['field' => 'plotUuid', 'type' => '=', 'value' => $plotUuid];
            $params[] = ['field' => 'packageId', 'type' => '=', 'value' => $packageId];
            $isAllInState = $this->packageGridPointsWorkflowService->isAllInState(PackageGridPoints::STATE_PENDING, PackageGridPoints::class, $params, true);
            $event->setBlocked(!$isAllInState);
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_field_state.guard.plot_gridded' => ['guardReview'],
        ];
    }
}
