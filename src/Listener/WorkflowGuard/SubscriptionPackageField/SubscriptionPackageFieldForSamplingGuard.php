<?php

namespace App\Listener\WorkflowGuard\SubscriptionPackageField;

use App\Entity\Contract\SubscriptionPackageField;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class SubscriptionPackageFieldForSamplingGuard implements EventSubscriberInterface
{
    public function guardReview(GuardEvent $event)
    {
        $isBlockState = true;
        /** @var App\Entity\Contract\SubscriptionPackageField $subPackageField */
        $subPackageField = $event->getSubject();
        $package = $subPackageField->getSubscriptionPackage()->getPackage();

        if ($package->getContainFields() && $package->getIsSampling() && $package->getIsFullSampling()
            && SubscriptionPackageField::STATE_GRIDDED === $subPackageField->getFieldState()) {
            $isBlockState = false;
        }

        if ($package->getContainFields() && $package->getIsSampling() && !$package->getIsFullSampling()
            && SubscriptionPackageField::STATE_CELLS_SELECTED === $subPackageField->getFieldState()) {
            $isBlockState = false;
        }

        $event->setBlocked($isBlockState);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_field_state.guard.plot_for_sampling' => ['guardReview'],
        ];
    }
}
