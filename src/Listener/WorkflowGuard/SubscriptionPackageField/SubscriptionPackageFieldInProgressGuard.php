<?php

namespace App\Listener\WorkflowGuard\SubscriptionPackageField;

use App\Entity\Analysis\PackageGridPoints;
use App\Service\Workflow\Analysis\PackageGridPointsWorkflowService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class SubscriptionPackageFieldInProgressGuard implements EventSubscriberInterface
{
    private PackageGridPointsWorkflowService $packageGridPointsWorkflowService;

    public function __construct(PackageGridPointsWorkflowService $packageGridPointsWorkflowService)
    {
        $this->packageGridPointsWorkflowService = $packageGridPointsWorkflowService;
    }

    public function guardReview(GuardEvent $event)
    {
        $isAllInState = false;
        /** @var App\Entity\SubscriptionPackageField $subscriptionPackageField */
        $subscriptionPackageField = $event->getSubject();
        $subscriptionPackage = $subscriptionPackageField->getSubscriptionPackage();
        $package = $subscriptionPackage->getPackage();
        $params[] = ['field' => 'plotUuid', 'type' => '=', 'value' => $subscriptionPackageField->getPlotUuid()];
        $params[] = ['field' => 'packageId', 'type' => '=', 'value' => $subscriptionPackage->getId()];

        if ($package->getIsFullSampling()) {
            $isAllInState = $this->packageGridPointsWorkflowService->isAllInState(PackageGridPoints::STATE_RECEIVED_IN_LAB, PackageGridPoints::class, $params);
        }

        if (!$package->getIsFullSampling()) {
            $isAllInState = $this->packageGridPointsWorkflowService->isAllInStates([PackageGridPoints::STATE_RECEIVED_IN_LAB, PackageGridPoints::STATE_NOT_SAMPLED], PackageGridPoints::class, $params);
        }

        $event->setBlocked(!$isAllInState);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_field_state.guard.plot_in_progress' => ['guardReview'],
        ];
    }
}
