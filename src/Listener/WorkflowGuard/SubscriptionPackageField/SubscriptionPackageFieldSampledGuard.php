<?php

namespace App\Listener\WorkflowGuard\SubscriptionPackageField;

use App\Entity\Analysis\PackageGridPoints;
use App\Service\Workflow\Analysis\PackageGridPointsWorkflowService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class SubscriptionPackageFieldSampledGuard implements EventSubscriberInterface
{
    private PackageGridPointsWorkflowService $packageGridPointsWorkflowService;

    public function __construct(PackageGridPointsWorkflowService $packageGridPointsWorkflowService)
    {
        $this->packageGridPointsWorkflowService = $packageGridPointsWorkflowService;
    }

    public function guardReview(GuardEvent $event)
    {
        $isAllInState = false;

        /** @var App\Entity\SubscriptionPackageField $subscriptionPackageField */
        $subscriptionPackageField = $event->getSubject();
        $package = $subscriptionPackageField->getSubscriptionPackage()->getPackage();
        $subscriptionPackageId = $subscriptionPackageField->getSubscriptionPackage()->getId();
        $plotUuid = $subscriptionPackageField->getPlotUuid();

        $params[] = ['field' => 'plotUuid', 'type' => '=', 'value' => $plotUuid];
        $params[] = ['field' => 'packageId', 'type' => '=', 'value' => $subscriptionPackageId];

        if ($package->getContainFields() && $package->getIsSampling() && $package->getIsFullSampling()) {
            $isAllInState = $this->packageGridPointsWorkflowService->isAllInStates([PackageGridPoints::STATE_SAMPLED, PackageGridPoints::STATE_RECEIVED_IN_LAB], PackageGridPoints::class, $params, true);
        }

        if ($package->getContainFields() && $package->getIsSampling() && !$package->getIsFullSampling()) {
            $isAllInState = $this->packageGridPointsWorkflowService->isAllInStates([PackageGridPoints::STATE_SAMPLED, PackageGridPoints::STATE_NOT_SAMPLED, PackageGridPoints::STATE_RECEIVED_IN_LAB], PackageGridPoints::class, $params, true);
        }

        $event->setBlocked(!$isAllInState);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_field_state.guard.plot_sampled' => ['guardReview'],
        ];
    }
}
