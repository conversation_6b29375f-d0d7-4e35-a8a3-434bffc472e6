<?php

namespace App\Listener\WorkflowGuard\SubscriptionPackageField;

use App\Entity\Analysis\PackageGridPoints;
use App\Service\Workflow\Analysis\PackageGridPointsWorkflowService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class SubscriptionPackageFieldCellsSelectedGuard implements EventSubscriberInterface
{
    private PackageGridPointsWorkflowService $packageGridPointsWorkflowService;

    public function __construct(PackageGridPointsWorkflowService $packageGridPointsWorkflowService)
    {
        $this->packageGridPointsWorkflowService = $packageGridPointsWorkflowService;
    }

    public function guardReview(GuardEvent $event)
    {
        $isBlockTransition = true;

        /** @var App\Entity\SubscriptionPackageField $subscriptionPackageField */
        $subscriptionPackageField = $event->getSubject();
        $package = $subscriptionPackageField->getSubscriptionPackage()->getPackage();
        $subscriptionPackageId = $subscriptionPackageField->getSubscriptionPackage()->getId();
        $plotUuid = $subscriptionPackageField->getPlotUuid();

        $params[] = ['field' => 'plotUuid', 'type' => '=', 'value' => $plotUuid];
        $params[] = ['field' => 'packageId', 'type' => '=', 'value' => $subscriptionPackageId];
        $isAllInState = $this->packageGridPointsWorkflowService->isAllInStates([PackageGridPoints::STATE_FOR_SAMPLING, PackageGridPoints::STATE_NOT_SAMPLED], PackageGridPoints::class, $params, true);
        if ($package->getContainFields() && $package->getIsSampling() && !$package->getIsFullSampling() && $isAllInState) {
            $isBlockTransition = false;
        }

        $event->setBlocked($isBlockTransition);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_field_state.guard.plot_cells_selected' => ['guardReview'],
        ];
    }
}
