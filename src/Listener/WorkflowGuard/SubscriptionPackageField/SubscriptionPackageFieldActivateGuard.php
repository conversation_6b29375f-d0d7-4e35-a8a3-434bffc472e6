<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2020 TechnoFarm Ltd.
 */

namespace App\Listener\WorkflowGuard\SubscriptionPackageField;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class SubscriptionPackageFieldActivateGuard implements EventSubscriberInterface
{
    public function guardReview(GuardEvent $event)
    {
        /** @var App\Entity\Contract\SubscriptionPackageField $subPackageField */
        $subPackageField = $event->getSubject();
        $package = $subPackageField->getSubscriptionPackage()->getPackage();

        if (!$package->getContainFields() || ($package->getContainFields() && $package->getIsSampling())) {
            $event->setBlocked(true);
        }
    }

    public static function getSubscribedE<PERSON>s(): array
    {
        return [
            'workflow.subscription_field_state.guard.plot_active' => ['guardReview'],
        ];
    }
}
