<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Listener\WorkflowGuard;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class ActivateSubscriptionContractGuard implements EventSubscriberInterface
{
    public function guardReview(GuardEvent $event)
    {
        /** @var App\Entity\Contract\Subscription $contract */
        $contract = $event->getSubject();

        if (empty($contract->getContractDate())) {
            // Block the transition "activate_contract" if the contract date has no set
            $event->setBlocked(true);
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.contract_subscription_state.guard.activate_contract' => ['guardReview'],
        ];
    }
}
