<?php

namespace App\Listener\WorkflowGuard\Analysis;

use App\Entity\Analysis\PackageGridPoints;
use App\Entity\Contract\SubscriptionPackageField;
use App\Service\Workflow\Analysis\PackageGridPointsWorkflowService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class PackageGridPointSamplingGuard implements EventSubscriberInterface
{
    private PackageGridPointsWorkflowService $gridPointsWorkflowService;

    public function __construct(PackageGridPointsWorkflowService $gridPointsWorkflowService)
    {
        $this->gridPointsWorkflowService = $gridPointsWorkflowService;
    }

    public function guardReview(GuardEvent $event)
    {
        $isBlockTransition = true;
        /** @var App\Entity\Analysis\PackageGridPoints $packageGridPoint */
        $packageGridPoint = $event->getSubject();
        $subscriptionPackage = $this->gridPointsWorkflowService->findSubscriptionPackageById($packageGridPoint->getPackageId());
        $subscriptionPackageField = $this->gridPointsWorkflowService->findSubscriptionPackageField($packageGridPoint->getPackageId(), $packageGridPoint->getPlotUuid());
        $package = $subscriptionPackage->getPackage();

        if ($package->getIsFullSampling() && PackageGridPoints::STATE_PENDING === $packageGridPoint->getState()) {
            $isBlockTransition = false;
        }

        if (!$package->getIsFullSampling() && PackageGridPoints::STATE_FOR_SAMPLING === $packageGridPoint->getState()) {
            $isBlockTransition = false;
        }

        if (!$package->getIsFullSampling()
            && PackageGridPoints::STATE_NOT_SAMPLED === $packageGridPoint->getState()
            && SubscriptionPackageField::STATE_SAMPLING === $subscriptionPackageField->getFieldState()
        ) {
            $isBlockTransition = false;
        }

        $event->setBlocked($isBlockTransition);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.package_grid_points_state.guard.sampling' => ['guardReview'],
        ];
    }
}
