<?php

namespace App\Listener\WorkflowGuard\Analysis;

use App\Entity\Analysis\LabElementsResults;
use App\Service\Workflow\Analysis\LabElementGroupWorkflowService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class LabElementsGroupForRecommendationGuard implements EventSubscriberInterface
{
    private LabElementGroupWorkflowService $labElementGroupWorkflowService;

    public function __construct(LabElementGroupWorkflowService $labElementGroupWorkflowService)
    {
        $this->labElementGroupWorkflowService = $labElementGroupWorkflowService;
    }

    public function guardReview(GuardEvent $event)
    {
        /** @var App\Entity\LabElementGroup $labElementGroup */
        $labElementGroup = $event->getSubject();

        $params[] = ['field' => 'labElementGroup', 'type' => '=', 'value' => $labElementGroup->getId()];
        $isAllInState = $this->labElementGroupWorkflowService->isAllInState(LabElementsResults::STATE_APPROVED, LabElementsResults::class, $params);

        $event->setBlocked(!$isAllInState);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.lab_element_group_state.guard.for_recommendation' => ['guardReview'],
        ];
    }
}
