<?php

namespace App\Listener\WorkflowGuard\Analysis;

use App\Service\Workflow\Analysis\PackageGridPointsWorkflowService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class PackageGridPointForSamplingGuard implements EventSubscriberInterface
{
    private PackageGridPointsWorkflowService $gridPointsWorkflowService;

    public function __construct(PackageGridPointsWorkflowService $gridPointsWorkflowService)
    {
        $this->gridPointsWorkflowService = $gridPointsWorkflowService;
    }

    public function guardReview(GuardEvent $event)
    {
        /** @var App\Entity\Analysis\PackageGridPoints $packageGridPoint */
        $packageGridPoint = $event->getSubject();
        $subscriptionPackage = $this->gridPointsWorkflowService->findSubscriptionPackageById($packageGridPoint->getPackageId());
        $package = $subscriptionPackage->getPackage();

        if ($package->getIsFullSampling()) {
            $event->setBlocked(true);
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.package_grid_points_state.guard.for_sampling' => ['guardReview'],
        ];
    }
}
