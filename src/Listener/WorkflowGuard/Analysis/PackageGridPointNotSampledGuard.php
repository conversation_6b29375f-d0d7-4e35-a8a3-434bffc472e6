<?php

namespace App\Listener\WorkflowGuard\Analysis;

use App\Entity\Analysis\PackageGridPoints;
use App\Entity\Contract\SubscriptionPackageField;
use App\Service\Workflow\Analysis\PackageGridPointsWorkflowService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class PackageGridPointNotSampledGuard implements EventSubscriberInterface
{
    private PackageGridPointsWorkflowService $gridPointsWorkflowService;

    public function __construct(PackageGridPointsWorkflowService $gridPointsWorkflowService)
    {
        $this->gridPointsWorkflowService = $gridPointsWorkflowService;
    }

    public function guardReview(GuardEvent $event)
    {
        $isBlockTransition = true;
        /** @var App\Entity\Analysis\PackageGridPoints $packageGridPoint */
        $packageGridPoint = $event->getSubject();
        $subscriptionPackage = $this->gridPointsWorkflowService->findSubscriptionPackageById($packageGridPoint->getPackageId());
        $subscriptionPackageField = $this->gridPointsWorkflowService->findSubscriptionPackageField($packageGridPoint->getPackageId(), $packageGridPoint->getPlotUuid());
        $package = $subscriptionPackage->getPackage();

        $params[] = ['field' => 'plotUuid', 'type' => '=', 'value' => $packageGridPoint->getPlotUuid()];
        $params[] = ['field' => 'packageId', 'type' => '=', 'value' => $packageGridPoint->getPackageId()];
        $atLeastOneAtSamplingState = $this->gridPointsWorkflowService->atLeastOneAtState(PackageGridPoints::STATE_SAMPLING, PackageGridPoints::class, $params, false);
        $atLeastOneAtForSamplingState = $this->gridPointsWorkflowService->atLeastOneAtState(PackageGridPoints::STATE_FOR_SAMPLING, PackageGridPoints::class, $params, false);

        if (!$package->getIsFullSampling()
            && in_array($subscriptionPackageField->getFieldState(), [
                SubscriptionPackageField::STATE_GRIDDED,
                SubscriptionPackageField::STATE_CELLS_SELECTED,
            ])
            && PackageGridPoints::STATE_SAMPLING !== $packageGridPoint->getState()
        ) {
            $isBlockTransition = false;
        }

        if (!$package->getIsFullSampling()
            && SubscriptionPackageField::STATE_FOR_SAMPLING === $subscriptionPackageField->getFieldState()
            && PackageGridPoints::STATE_FOR_SAMPLING === $packageGridPoint->getState()
            && $atLeastOneAtForSamplingState
        ) {
            $isBlockTransition = false;
        }

        if (!$package->getIsFullSampling()
            && SubscriptionPackageField::STATE_SAMPLING === $subscriptionPackageField->getFieldState()
            && PackageGridPoints::STATE_SAMPLING === $packageGridPoint->getState()
            && $atLeastOneAtSamplingState
        ) {
            $isBlockTransition = false;
        }

        $event->setBlocked($isBlockTransition);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.package_grid_points_state.guard.not_sampled' => ['guardReview'],
        ];
    }
}
