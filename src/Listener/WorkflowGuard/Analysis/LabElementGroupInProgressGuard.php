<?php

namespace App\Listener\WorkflowGuard\Analysis;

use App\Entity\Analysis\PackageGridPoints;
use App\Service\Workflow\Analysis\LabElementGroupWorkflowService;
use App\Service\Workflow\Analysis\PackageGridPointsWorkflowService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class LabElementGroupInProgressGuard implements EventSubscriberInterface
{
    private LabElementGroupWorkflowService $labElementGroupWorkflowService;
    private PackageGridPointsWorkflowService $packageGridPointsWorkflowService;

    public function __construct(
        LabElementGroupWorkflowService $labElementGroupWorkflowService,
        PackageGridPointsWorkflowService $packageGridPointsWorkflowService
    ) {
        $this->labElementGroupWorkflowService = $labElementGroupWorkflowService;
        $this->packageGridPointsWorkflowService = $packageGridPointsWorkflowService;
    }

    public function guardReview(GuardEvent $event)
    {
        $isAllInState = false;
        /** @var App\Entity\Analysis\LabElementGroup $labElementGroup */
        $labElementGroup = $event->getSubject();
        $subscriptionPackage = $this->labElementGroupWorkflowService->findSubscriptionPackageById($labElementGroup->getPackageId());
        $package = $subscriptionPackage->getPackage();

        $packageGridPointIds = $this->packageGridPointsWorkflowService->getPackageGridPointIdsByLabElementGroup($labElementGroup);
        $params[] = ['field' => 'id', 'value' => $packageGridPointIds, 'type' => 'in'];

        if ($package->getIsFullSampling()) {
            $isAllInState = $this->packageGridPointsWorkflowService->isAllInState(PackageGridPoints::STATE_RECEIVED_IN_LAB, PackageGridPoints::class, $params, true);
        }

        if (!$package->getIsFullSampling()) {
            $isAllInState = $this->packageGridPointsWorkflowService->isAllInStates([PackageGridPoints::STATE_RECEIVED_IN_LAB, PackageGridPoints::STATE_NOT_SAMPLED], PackageGridPoints::class, $params, true);
        }

        $event->setBlocked(!$isAllInState);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.lab_element_group_state.guard.in_progress' => ['guardReview'],
        ];
    }
}
