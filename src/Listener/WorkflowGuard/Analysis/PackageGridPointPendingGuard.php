<?php

namespace App\Listener\WorkflowGuard\Analysis;

use App\Entity\Analysis\PackageGridPoints;
use App\Service\Workflow\Analysis\PackageGridPointsWorkflowService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\GuardEvent;

class PackageGridPointPendingGuard implements EventSubscriberInterface
{
    private PackageGridPointsWorkflowService $packageGridPointsWorkflowService;

    public function __construct(PackageGridPointsWorkflowService $packageGridPointsWorkflowService)
    {
        $this->packageGridPointsWorkflowService = $packageGridPointsWorkflowService;
    }

    public function guardReview(GuardEvent $event)
    {
        /** @var App\Entity\Analysis\PackageGridPoints $packageGridPoint */
        $packageGridPoint = $event->getSubject();

        $params[] = ['field' => 'plotUuid', 'type' => '=', 'value' => $packageGridPoint->getPlotUuid()];
        $params[] = ['field' => 'packageId', 'type' => '=', 'value' => $packageGridPoint->getPackageId()];
        $isAllInState = $this->packageGridPointsWorkflowService->isAllInStates([PackageGridPoints::STATE_NOT_SAMPLED, PackageGridPoints::STATE_PENDING], PackageGridPoints::class, $params, true);

        $event->setBlocked(!$isAllInState);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.package_grid_points_state.guard.pending' => ['guardReview'],
        ];
    }
}
