<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Listener;

use App\Entity\IProvided;
use App\Entity\ServiceProvider;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Symfony\Component\Security\Core\Security;

class DoctrinePrePersistListener
{
    private $em;
    private $security;

    public function __construct(Security $security, EntityManagerInterface $entityManager)
    {
        $this->security = $security;
        $this->em = $entityManager;
    }

    public function prePersist(LifecycleEventArgs $args)
    {
        $entity = $args->getObject();
        if ($entity instanceof IProvided && !$entity->getServiceProvider()) {
            $entity->setServiceProvider(
                $this->em->getReference(
                    ServiceProvider::class,
                    $this->security->getUser()->getServiceProvider()->getId()
                )
            );
        }
    }
}
