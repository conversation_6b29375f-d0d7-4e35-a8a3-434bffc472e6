<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Listener;

use App\Security\User\SystemUser;
use App\Service\SentryService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\Security\Core\Security;

class BeforeRequestListener
{
    private $em;
    private $security;
    private $sentryService;

    public function __construct(EntityManagerInterface $entityManager, Security $security, SentryService $sentryService)
    {
        $this->em = $entityManager;
        $this->security = $security;
        $this->sentryService = $sentryService;
    }

    public function onKernelRequest(RequestEvent $event)
    {
        $tokenUser = $this->security->getUser();
        if (!empty($tokenUser) && !($tokenUser instanceof SystemUser)) {
            $filter = $this->em->getFilters()->enable('service_provider');
            $filter->setParameter('serviceProvider', $tokenUser->getServiceProvider()->getId());
            $this->sentryService->configureScope($tokenUser);
        }
    }
}
