<?php

namespace App\Listener;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Psr\Log\LoggerInterface;

class ExceptionListener
{
    private $params;
    private $logger;

    public function __construct(ParameterBagInterface $params, LoggerInterface $logger)
    {
        $this->params = $params;
        $this->logger = $logger;
    }

    public function onKernelException(ExceptionEvent $event)
    {
        $exception = $event->getThrowable();
        $request = $event->getRequest();
        
        // Only handle JSON API requests in debug mode
        if (!$this->params->get('kernel.debug') || !$this->isJsonRequest($request)) {
            return;
        }

        // Log the exception for debugging
        $this->logger->error('Exception caught in ExceptionListener', [
            'exception' => $exception,
            'request_uri' => $request->getRequestUri(),
            'request_method' => $request->getMethod(),
        ]);

        // Create detailed error response for development
        $statusCode = $exception instanceof HttpExceptionInterface ? $exception->getStatusCode() : 500;
        
        $errorData = [
            'type' => 'https://tools.ietf.org/html/rfc2616#section-10',
            'title' => 'An error occurred',
            'status' => $statusCode,
            'detail' => $exception->getMessage(),
            'debug' => [
                'exception_class' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'code' => $exception->getCode(),
                'trace' => $this->formatTrace($exception->getTrace()),
                'request' => [
                    'method' => $request->getMethod(),
                    'uri' => $request->getRequestUri(),
                    'headers' => $request->headers->all(),
                    'content' => $request->getContent(),
                    'query' => $request->query->all(),
                    'request' => $request->request->all(),
                ]
            ]
        ];

        // Add previous exception if exists
        if ($exception->getPrevious()) {
            $previous = $exception->getPrevious();
            $errorData['debug']['previous'] = [
                'exception_class' => get_class($previous),
                'message' => $previous->getMessage(),
                'file' => $previous->getFile(),
                'line' => $previous->getLine(),
                'code' => $previous->getCode(),
                'trace' => $this->formatTrace($previous->getTrace()),
            ];
        }

        $response = new JsonResponse($errorData, $statusCode);
        $event->setResponse($response);
    }

    private function isJsonRequest($request): bool
    {
        return in_array('application/json', $request->getAcceptableContentTypes()) ||
               $request->headers->get('Content-Type') === 'application/json' ||
               $request->getPathInfo() !== '/' && strpos($request->getPathInfo(), '/api') === 0;
    }

    private function formatTrace(array $trace): array
    {
        $formattedTrace = [];
        
        foreach ($trace as $index => $frame) {
            $formattedTrace[] = [
                'file' => $frame['file'] ?? 'unknown',
                'line' => $frame['line'] ?? 'unknown',
                'function' => $frame['function'] ?? 'unknown',
                'class' => $frame['class'] ?? null,
                'type' => $frame['type'] ?? null,
                'args' => isset($frame['args']) ? $this->formatArgs($frame['args']) : [],
            ];
        }
        
        return $formattedTrace;
    }

    private function formatArgs(array $args): array
    {
        $formattedArgs = [];
        
        foreach ($args as $arg) {
            if (is_object($arg)) {
                $formattedArgs[] = get_class($arg) . ' (object)';
            } elseif (is_array($arg)) {
                $formattedArgs[] = 'Array(' . count($arg) . ')';
            } elseif (is_string($arg)) {
                $formattedArgs[] = '"' . (strlen($arg) > 100 ? substr($arg, 0, 100) . '...' : $arg) . '"';
            } elseif (is_numeric($arg)) {
                $formattedArgs[] = $arg;
            } elseif (is_bool($arg)) {
                $formattedArgs[] = $arg ? 'true' : 'false';
            } elseif (is_null($arg)) {
                $formattedArgs[] = 'null';
            } else {
                $formattedArgs[] = gettype($arg);
            }
        }
        
        return $formattedArgs;
    }
}
