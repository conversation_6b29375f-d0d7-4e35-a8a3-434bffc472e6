<?php

namespace App\Listener\WorkflowEnter\SubscriptionPackageFields;

use App\Entity\Analysis\PackageGridPoints;
use App\Service\Workflow\Analysis\PackageGridPointsWorkflowService;
use Exception;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class SubsPackFieldSamplingListener implements EventSubscriberInterface
{
    private PackageGridPointsWorkflowService $workflowPackageGridPointsService;

    public function __construct(
        PackageGridPointsWorkflowService $workflowPackageGridPointsService
    ) {
        $this->workflowPackageGridPointsService = $workflowPackageGridPointsService;
    }

    /**
     * When subscription package field complete Sampling state, change package grid points state to Sampling for this package.
     *
     * @throws Exception
     */
    public function changeState(Event $event)
    {
        /** @var App\Entity\Contract\SubscriptionPackageField $subscriptionPackageField */
        $subscriptionPackageField = $event->getSubject();
        $subscriptionPackage = $subscriptionPackageField->getSubscriptionPackage();

        $params[] = ['field' => 'packageId', 'value' => $subscriptionPackage->getId(), 'type' => 'IN'];
        $params[] = ['field' => 'plotUuid', 'value' => $subscriptionPackageField->getPlotUuid(), 'type' => 'IN'];

        $this->workflowPackageGridPointsService->updateState(PackageGridPoints::class, PackageGridPoints::WORKFLOW_NAME, $params, PackageGridPoints::TRANSITION_SAMPLING);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.subscription_field_state.enter.Sampling' => ['changeState'],
        ];
    }
}
