<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Factory;

use App\Entity\Contract\Service;
use App\Entity\Contract\Subscription;
use Exception;

class ContractFactory
{
    public function get($type)
    {
        $instance = null;

        switch ($type) {
            case 'subscription':
                $instance = new Subscription();

                break;
            case 'service':
                $instance = new Service();

                break;
            default:
                throw new Exception('Invalid type');
        }

        return $instance;
    }
}
