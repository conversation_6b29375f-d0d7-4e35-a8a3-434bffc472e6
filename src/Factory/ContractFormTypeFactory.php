<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Factory;

use App\Entity\Contract;
use App\Entity\Contract\Service;
use App\Entity\Contract\Subscription;
use App\Form\Contract\ServiceType;
use App\Form\Contract\SubscriptionType;
use Exception;

class ContractFormTypeFactory
{
    public function get($type)
    {
        $instance = null;

        switch ($type) {
            case 'subscription':
                $instance = SubscriptionType::class;

                break;
            case 'service':
                $instance = ServiceType::class;

                break;
            default:
                throw new Exception('Invalid type');
        }

        return $instance;
    }

    public function getByObject(Contract $object)
    {
        $instance = null;

        switch (true) {
            case ($object instanceof Subscription):
                $instance = SubscriptionType::class;

                break;
            case ($object instanceof Service):
                $instance = ServiceType::class;

                break;
            default:
                throw new Exception('Invalid type');
        }

        return $instance;
    }
}
